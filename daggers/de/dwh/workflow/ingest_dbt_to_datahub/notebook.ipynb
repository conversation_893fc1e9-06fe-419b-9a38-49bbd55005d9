{"cells": [{"cell_type": "code", "execution_count": null, "id": "7eb0edbc-79a9-4a9b-9802-a82dc7d64bde", "metadata": {"tags": []}, "outputs": [], "source": ["!pip uninstall decore pencilbox -y"]}, {"cell_type": "code", "execution_count": null, "id": "b963fd68-422f-4906-aadf-a138a675974d", "metadata": {}, "outputs": [], "source": ["!pip install acryl-datahub[dbt]\n", "!pip install acryl-datahub[datahub-kafka]\n", "!pip install confluent-kafka==2.6.1\n", "!pip install dbt-core==1.6.1\n", "!pip install dbt-trino==1.6.1\n", "!pip install pydantic_core==2.6.1\n", "!pip install pydantic==1.10.22"]}, {"cell_type": "code", "execution_count": null, "id": "52125a60-6856-4de3-a581-75059453aeae", "metadata": {}, "outputs": [], "source": ["import subprocess\n", "import os\n", "import yaml\n", "import logging"]}, {"cell_type": "code", "execution_count": null, "id": "76adb0d6-e13c-4ab1-a2fa-55ed7821e2d5", "metadata": {}, "outputs": [], "source": ["cwd = \"/usr/local/airflow/dags/repo/dags/de/dwh/workflow/ingest_dbt_to_datahub\"\n", "os.ch<PERSON>(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "f4d7fd9a-51a5-4dd0-aa53-fee7e3a782bd", "metadata": {}, "outputs": [], "source": ["from utils import get_secret"]}, {"cell_type": "code", "execution_count": null, "id": "550508dc-da53-484b-b242-ffad42cca616", "metadata": {}, "outputs": [], "source": ["def get_http_endpoint(host, port):\n", "    return \"http://\" + host + \":\" + port"]}, {"cell_type": "code", "execution_count": null, "id": "0933279a-ed2c-4c29-a40c-57cdde14d04d", "metadata": {}, "outputs": [], "source": ["logging.basicConfig(\n", "    level=logging.INFO,\n", "    format=\"%(asctime)s - %(name)s - %(levelname)s - %(message)s\",\n", "    handlers=[logging.StreamHandler()],\n", ")\n", "\n", "logger = logging.getLogger(__name__)"]}, {"cell_type": "code", "execution_count": null, "id": "51099782-5d53-4f48-8a74-18e5e6a523e4", "metadata": {}, "outputs": [], "source": ["SECRETS_PATH = \"dse/services/datahub/api_key\"\n", "SECRETS_PATH_KAFKA = \"dse/services/datahub/kafka_broker\"\n", "SECRETS_PATH_SCHEMA_REGISTRY = \"dse/services/datahub/kafka_schema_registry\"\n", "DATAHUB_DOMAIN_NAME = \"urn:li:domain:blinkit\"\n", "secret = get_secret(SECRETS_PATH)\n", "secret_kafka = get_secret(SECRETS_PATH_KAFKA)\n", "secret_schema_registry = get_secret(SECRETS_PATH_SCHEMA_REGISTRY)"]}, {"cell_type": "code", "execution_count": null, "id": "4edc93ec-1a59-4e6c-8186-656203238339", "metadata": {}, "outputs": [], "source": ["DBT_PROJECT_DIR = \"/dbt-models/trino_dwh\"\n", "DBT_TARGET_DIR = f\"{DBT_PROJECT_DIR}/target\"\n", "PROFILE_FILE_DIR = DBT_PROJECT_DIR\n", "DATAHUB_SERVER_URL = get_http_endpoint(secret[\"host\"], secret[\"port\"])\n", "DATAHUB_SCHEMA_REGISTRY_URL = get_http_endpoint(\n", "    secret_schema_registry[\"host\"], secret_schema_registry[\"port\"]\n", ")\n", "DATAHUB_BOOTSTRAP = secret_kafka[\"host\"] + \":\" + secret_kafka[\"port\"]\n", "DATAHUB_TOKEN = secret[\"token\"]"]}, {"cell_type": "code", "execution_count": null, "id": "ba021654-88f6-4034-bbee-99e496c13c67", "metadata": {}, "outputs": [], "source": ["# generating dbt docs\n", "os.chdir(DBT_PROJECT_DIR)\n", "print(os.getcwd())\n", "try:\n", "    logger.info(\"Starting dbt docs generation process...\")\n", "    result = subprocess.run(\n", "        [\n", "            \"dbt\",\n", "            \"docs\",\n", "            \"generate\",\n", "            \"--target\",\n", "            \"prod\",\n", "            \"--profiles-dir\",\n", "            \"/root/.dbt\",\n", "        ],\n", "        check=True,\n", "        capture_output=True,\n", "        text=True,\n", "    )\n", "    print(f\"DBT docs generated successfully : {result.stdout}\")\n", "except subprocess.CalledProcessError as e:\n", "    if e.stdout:\n", "        logger.info(f\"STDOUT:\\n {e.stdout}\")\n", "    if e.stderr:\n", "        logger.error(f\"STDERR:\\n {e.stderr}\")\n", "except Exception as e:\n", "    logger.error(f\"Unexpected error: {str(e)}\")\n", "    import traceback\n", "\n", "    logger.error(traceback.format_exc())"]}, {"cell_type": "code", "execution_count": null, "id": "302d28ad-cecb-4266-bd76-de9cb0f43f33", "metadata": {}, "outputs": [], "source": ["pipeline_config = {\n", "    \"source\": {\n", "        \"type\": \"dbt\",\n", "        \"config\": {\n", "            \"manifest_path\": f\"{DBT_TARGET_DIR}/manifest.json\",\n", "            \"catalog_path\": f\"{DBT_TARGET_DIR}/catalog.json\",\n", "            \"target_platform\": \"trino\",\n", "        },\n", "    },\n", "    \"transformers\": [\n", "        {\n", "            \"type\": \"simple_add_dataset_domain\",\n", "            \"config\": {\"semantics\": \"OVERWRITE\", \"domains\": [DATAHUB_DOMAIN_NAME]},\n", "        }\n", "    ],\n", "    \"datahub_api\": {\n", "        \"server\": DATAHUB_SERVER_URL,\n", "        \"token\": DATAHUB_TOKEN,\n", "    },\n", "    \"sink\": {\n", "        \"type\": \"datahub-kafka\",\n", "        \"config\": {\n", "            \"connection\": {\n", "                \"bootstrap\": DATAHUB_BOOTSTRAP,\n", "                \"schema_registry_url\": DATAHUB_SCHEMA_REGISTRY_URL,\n", "            }\n", "        },\n", "    },\n", "}\n", "\n", "yaml_file_path = DBT_TARGET_DIR + \"/config.yml\"\n", "\n", "with open(yaml_file_path, \"w\") as yaml_file:\n", "    yaml.dump(pipeline_config, yaml_file, default_flow_style=False)\n", "print(f\"Configuration saved to {yaml_file_path}\")"]}, {"cell_type": "code", "execution_count": null, "id": "21412bf7-9f83-40b9-ba3c-db1a147b8000", "metadata": {}, "outputs": [], "source": ["# ingesting data using recipe file\n", "try:\n", "    logger.info(\"Starting datahub ingestion process .... \")\n", "    result = subprocess.run(\n", "        [\"datahub\", \"ingest\", \"-c\", \"target/config.yml\"],\n", "        check=True,\n", "        capture_output=True,\n", "        text=True,\n", "    )\n", "    print(\"Ingestion completed successfully : \", result.stdout)\n", "except subprocess.CalledProcessError as e:\n", "    if e.stdout:\n", "        logger.info(f\"STDOUT:\\n {e.stdout}\")\n", "    if e.stderr:\n", "        logger.error(f\"STDERR:\\n {e.stderr}\")\n", "    # logger.error(\"An error occurred while ingesting to datahub: \", e.stderr)"]}, {"cell_type": "code", "execution_count": null, "id": "921edb39-3133-44b4-8881-7936d097fa07", "metadata": {}, "outputs": [], "source": ["!pip install decore pencilbox --index-url https://pypi.grofer.io/simple"]}, {"cell_type": "code", "execution_count": null, "id": "d8986111", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "\n", "pb.to_s3(\n", "    f\"{DBT_TARGET_DIR}/manifest.json\",\n", "    \"grofers-test-dse-singapore\",\n", "    \"neelesh/dbt-models/manifest.json\",\n", ")\n", "pb.to_s3(\n", "    f\"{DBT_TARGET_DIR}/catalog.json\",\n", "    \"grofers-test-dse-singapore\",\n", "    \"neelesh/dbt-models/catalog.json\",\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 5}