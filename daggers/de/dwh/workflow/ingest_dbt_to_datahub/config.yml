alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: ingest_dbt_to_datahub
dag_type: workflow
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de/dbt:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow-dags
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
    - mountPath: /usr/local/dbt-models
      name: airflow-dags
      subPath: dbt-models
    - mountPath: /dbt-models
      name: dbt-models
      subPath: dbt-models
  type: kubernetes
namespace: de
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U079CFQGS5T
path: de/dwh/workflow/ingest_dbt_to_datahub
paused: false
pool: de_pool
project_name: dwh
schedule:
  end_date: '2025-10-05T00:00:00'
  interval: 35 10,21 * * *
  start_date: '2025-04-28T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files:
- utils.py
tags: []
template_name: notebook
version: 2
