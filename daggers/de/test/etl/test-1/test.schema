schema_name: 'de_etls'
table_name: 'sqlflow_test_table'
table_description: 'Test Table'
primary_key: ['view_name']
partition_key: ['last_refresh_ts_ist']
incremental_key: 'sql_file_path'
load_type: 'upsert'
force_upsert_without_increment_check: False
column_descriptions:
- name: view_name
  type: VARCHAR
  description: 'Test'
- name: sql_file_path
  type: VARCHAR
  description: 'Test'
- name: security
  type: VARCHAR
  description: 'Test'
- name: owner_email
  type: VARCHAR
  description: 'Test'
- name: slack_id
  type: VARCHAR
  description: 'Test'
- name: reviewer_email
  type: VARCHAR
  description: 'Test'
- name: version
  type: INTEGER
  description: 'Test'
- name: etl_snapshot_ts_ist
  type: TIMESTAMP
  description: 'Test'
- name: end_date
  type: DATE
  description: 'Test'
- name: partition_columns
  type: ARRAY(VARCHAR)
  description: 'Test'
- name: materialized_view
  type: BOOLEAN
  description: 'Test'
- name: last_refresh_ts_ist
  type: TIMESTAMP
  description: 'Test'
- name: refresh_interval_utc
  type: VARCHAR
  description: 'Test'
- name: rls
  type: VARCHAR
  description: 'Test'
- name: priority
  type: VARCHAR
  description: 'Test'
