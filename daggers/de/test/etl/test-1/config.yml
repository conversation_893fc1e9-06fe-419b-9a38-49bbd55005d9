alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: test-1
dag_type: etl
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-primary-eks-role
  type: kubernetes
namespace: de
owner:
  email: <EMAIL>
  slack_id: U08FD0L7L5V
path: example
paused: false
pool: test
project_name: test
schedule:
  end_date: '2025-09-30T00:00:00'
  interval: 45 22 * * *
  start_date: '2023-01-27T02:30:00'
schedule_type: fixed
sla: 10 minutes
tags: []
template_name: trino_sql
version: 1
trino_sql:
- name: test
  tag: l1
