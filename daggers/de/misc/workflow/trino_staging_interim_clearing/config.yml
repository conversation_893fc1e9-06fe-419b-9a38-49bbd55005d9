alert_configs:
  slack:
  - channel: bl-data-alerts-p1
dag_name: trino_staging_interim_clearing
dag_type: workflow
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    node_type: spot
    load_type: tiny
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow-dags
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow-de/plugins
  type: kubernetes
namespace: de
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ
path: de/misc/workflow/trino_staging_interim_clearing
paused: false
project_name: misc
schedule:
  interval: '30 21 * * 0'
  start_date: '2024-01-25T00:00:00'
  end_date: '2026-08-16T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags:
- trino_staging_interim_clearing
template_name: notebook
version: 1
