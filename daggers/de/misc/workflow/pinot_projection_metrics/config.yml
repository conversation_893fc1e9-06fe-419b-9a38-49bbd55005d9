alert_configs:
  slack:
  - channel: bl-data-alerts-p1
dag_name: pinot_projection_metrics
dag_type: workflow
escalation_priority: low
executor:
  config:
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    node_type: spot
    load_type: tiny
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
  type: kubernetes
namespace: de
notebook:
  retries: 3
  retry_delay_in_seconds: 5
  parameters: 
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ
path: de/misc/workflow/pinot_projection_metrics
paused: false
project_name: misc
schedule:
  interval: '0 5 * * *'
  start_date: '2024-03-10T06:00:00'
  end_date: '2026-08-16T00:00:00'
schedule_type: fixed
sla: 62 minutes
support_files: []
tags:
- pinot_projection_metrics
template_name: notebook
version: 1
