alert_configs:
  slack:
  - channel: bl-data-alerts-p1
dag_name: sonar_access_mapping_dag
dag_type: workflow
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: de
notebook:
  parameters: 
owner:
  email: <EMAIL>
  slack_id: U03RZ16CUUA
path: de/misc/workflow/sonar_access_mapping_dag
paused: false
project_name: misc
schedule:
  end_date: '2025-11-01T00:00:00'
  interval: 0 18 * * *
  start_date: '2025-03-19T00:00:00'
schedule_type: fixed
sla: 361 minutes
support_files: []
tags: []
template_name: notebook
version: 1
