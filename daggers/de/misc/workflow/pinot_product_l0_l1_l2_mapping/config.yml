dag_name: pinot_product_l0_l1_l2_mapping
dag_type: workflow
escalation_priority: low
executor:
  config:
    node_type: spot
    load_type: tiny
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow-de/plugins
  type: kubernetes
namespace: de
notebook:
  parameters: 
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ
path: de/misc/workflow/pinot_product_l0_l1_l2_mapping
paused: false
project_name: misc
schedule:
  interval: '50 */1 * * *'
  start_date: '2022-10-14T06:00:00'
  end_date: '2026-08-15T00:00:00'
schedule_type: fixed
sla: 60 minutes
support_files: []
tags:
- pinot_product_l0_l1_l2_mapping
template_name: notebook
version: 2
