dag_name: transform_vector_backend_logs_backfill
dag_type: workflow
escalation_priority: high
namespace: de
executor:
  config:    
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    node_type : spot
    load_type : high-mem
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow-de/plugins
  type : kubernetes
notebooks:
- name: notebook
  tag: parallel
  parameters:
    ds: '2025-08-09'
    SOURCE_BUCKET: prod-dse-backend-events-raw
    SOURCE_SPLIT: daily
    SINK_BUCKET: prod-dse-backend-events
    SUBPREFIX: application-logs/dragonstone_api-intent_empty_result/,application-logs/dragonstone_api_canary-intent_empty_result/,application-logs/dragonstone_api_primary-intent_empty_result/
    TABLE_NAME: dragonstone_api_intent_empty_result
    MAX_ROWS_PER_FILE: 2000000
  retries: 3
  executor_config:
    node_type : spot
    load_type : high-mem
  alias: dragonstone_api_intent_empty_result
- name: notebook
  tag: parallel
  parameters:
    ds: '2025-08-09'
    SOURCE_BUCKET: prod-dse-backend-events-raw
    SOURCE_SPLIT: daily
    SINK_BUCKET: prod-dse-backend-events
    SUBPREFIX: application-logs/dragonstone_api-autocomplete_empty_result/,application-logs/dragonstone_api_canary-autocomplete_empty_result/,application-logs/dragonstone_api_primary-autocomplete_empty_result/
    TABLE_NAME: dragonstone_api_autocomplete_empty_result
    MAX_ROWS_PER_FILE: 250000
  executor_config:
    node_type : spot
    load_type : high-mem
  retries: 3
  alias: dragonstone_api_autocomplete_empty_result
- name: notebook
  tag: parallel
  parameters:
    ds: '2025-08-09'
    SOURCE_BUCKET: prod-dse-backend-events-raw
    SOURCE_SPLIT: daily
    SINK_BUCKET: prod-dse-backend-events
    SUBPREFIX: application-logs/dragonstone_api_primary-spell_corrected_result/,application-logs/dragonstone_api_canary-spell_corrected_result/,application-logs/dragonstone_api_secondary-spell_corrected_result/
    TABLE_NAME: dragonstone_api_spell_corrected_result
    MAX_ROWS_PER_FILE: 2000000
  executor_config:
    node_type : spot
    load_type : high-mem
  retries: 3
  alias: dragonstone_api_spell_corrected_result
- name: notebook
  tag: parallel
  parameters:
    ds: '2025-08-09'
    SOURCE_BUCKET: prod-dse-backend-events-raw
    SOURCE_SPLIT: daily
    SINK_BUCKET: prod-dse-backend-events
    SUBPREFIX: application-logs/dragonstone_api-entity_recognition_result/,application-logs/dragonstone_api_canary-entity_recognition_result/,application-logs/dragonstone_api_primary-entity_recognition_result/,application-logs/dragonstone_api_secondary-entity_recognition_result/
    TABLE_NAME: dragonstone_api_entity_recognition_result
    MAX_ROWS_PER_FILE: 250000
  executor_config:
    node_type : spot
    load_type : ultra-high-mem
  retries: 3
  alias: dragonstone_api_entity_recognition_result
- name: notebook
  tag: parallel
  parameters:
    ds: '2025-08-09'
    SOURCE_BUCKET: prod-dse-backend-events-raw
    SOURCE_SPLIT: daily
    SINK_BUCKET: prod-dse-backend-events
    SUBPREFIX: application-logs/dragonstone_api_primary-high_confidence_query_issue/,application-logs/dragonstone_api_canary-high_confidence_query_issue/,dragonstone_api_secondary-high_confidence_query_issue
    TABLE_NAME: dragonstone_api_high_confidence_query_issue
    MAX_ROWS_PER_FILE: 1500000
  executor_config:
    node_type : spot
    load_type : high-mem
  retries: 3
  alias: dragonstone_api_high_confidence_query_issue
- name: notebook
  tag: parallel
  parameters:
    ds: '2025-08-09'
    SOURCE_BUCKET: prod-dse-backend-events-raw
    SOURCE_SPLIT: daily
    SINK_BUCKET: prod-dse-backend-events
    SUBPREFIX: application-logs/dragonstone_api_primary-autosuggest_response_event/,application-logs/dragonstone_api_canary-autosuggest_response_event/,application-logs/dragonstone_api_secondary-autosuggest_response_event/,application-logs/product_discovery_service_api-autosuggest_response_event/
    TABLE_NAME: dragonstone_api_autosuggest_response_event
    MAX_ROWS_PER_FILE: 200000
  retries: 3
  executor_config:
    node_type : spot
    load_type : high-mem
  alias: dragonstone_api_autosuggest_response_event
- name: notebook
  tag: parallel
  parameters:
    ds: '2025-08-09'
    SOURCE_BUCKET: prod-dse-backend-events-raw
    SOURCE_SPLIT: daily
    SINK_BUCKET: prod-dse-backend-events
    SUBPREFIX: application-logs/track_order-track_order_eta_details/
    TABLE_NAME: track_order_eta_details
    MAX_ROWS_PER_FILE: 2000000
  retries: 3
  executor_config:
    node_type : spot
    load_type : high-mem
  alias: track_order_eta_details
- name: notebook
  tag: parallel
  parameters:
    ds: '2025-08-09'
    SOURCE_BUCKET: prod-dse-backend-events-raw
    SOURCE_SPLIT: daily
    SINK_BUCKET: prod-dse-backend-events
    SUBPREFIX: application-logs/track_order-track_order_delay_components/
    TABLE_NAME: track_order_delay_components
    MAX_ROWS_PER_FILE: 2000000
  executor_config:
    node_type : spot
    load_type : high-mem
  retries: 3
  alias: track_order_delay_components
# - name: notebook
#   tag: parallel
#   parameters:
#     ds: '2025-08-09'
#     SOURCE_BUCKET: prod-dse-backend-events-raw
#     SOURCE_SPLIT: daily
#     SINK_BUCKET: prod-dse-backend-events
#     SUBPREFIX: application-logs/dragonstone_api_primary-previous_buy_result/,application-logs/dragonstone_api_canary-previous_buy_result/
#     TABLE_NAME: dragonstone_api_previous_buy_result
#     MAX_ROWS_PER_FILE: 250000
#   retries: 3
#   alias: dragonstone_api_previous_buy_result
- name: notebook
  tag: parallel
  parameters:
    ds: '2025-08-09'
    SOURCE_BUCKET: prod-dse-backend-events-raw
    SOURCE_SPLIT: daily
    SINK_BUCKET: prod-dse-backend-events
    SUBPREFIX: application-logs/serviceability_notebooks_celery-manpower_reopen_prediction/
    TABLE_NAME: serviceability_manpower_reopen_prediction
    MAX_ROWS_PER_FILE: 2000000
  retries: 3
  executor_config:
    node_type : spot
    load_type : high-mem
  alias: serviceability_manpower_reopen_prediction
- name: notebook
  tag: parallel
  parameters:
    ds: '2025-08-09'
    SOURCE_BUCKET: prod-dse-backend-events-raw
    SOURCE_SPLIT: daily
    SINK_BUCKET: prod-dse-backend-events
    SUBPREFIX: application-logs/crm_api-lifeline_api_event_logs/
    TABLE_NAME: crm_lifeline_api_event_logs
    MAX_ROWS_PER_FILE: 100000
  retries: 3
  executor_config:
    node_type : spot
    load_type : high-mem
  alias: crm_lifeline_api_event_logs
- name: notebook
  tag: parallel
  parameters:
    ds: '2025-08-09'
    SOURCE_BUCKET: prod-dse-backend-events-raw
    SOURCE_SPLIT: daily
    SINK_BUCKET: prod-dse-backend-events
    SUBPREFIX: application-logs/crm_api_celery_bulk-/
    TABLE_NAME: crm_api_bulk_upload_event_logs
    MAX_ROWS_PER_FILE: 2000000
  retries: 3
  executor_config:
    node_type : spot
    load_type : high-mem
  alias: crm_api_bulk_upload_event_logs
- name: notebook
  tag: parallel
  parameters:
    ds: '2025-08-09'
    SOURCE_BUCKET: prod-dse-backend-events-raw
    SOURCE_SPLIT: daily
    SINK_BUCKET: prod-dse-backend-events
    SUBPREFIX: application-logs/merchant_visibility-merchant_visibility_info/
    TABLE_NAME: merchant_visibility_info
    MAX_ROWS_PER_FILE: 2000000
  retries: 3
  executor_config:
    node_type : spot
    load_type : high-mem
  alias: merchant_visibility_info
- name: notebook
  tag: parallel
  parameters:
    ds: '2025-08-09'
    SOURCE_BUCKET: prod-dse-backend-events-raw
    SOURCE_SPLIT: daily
    SINK_BUCKET: prod-dse-backend-events
    SUBPREFIX: application-logs/checkout_service_canary-cart_checkout/,application-logs/checkout_service_primary-cart_checkout/
    TABLE_NAME: cart_checkout
    MAX_ROWS_PER_FILE: 5000000
  retries: 3
  executor_config:
    node_type : spot
    load_type : high-mem
  alias: cart_checkout
- name: notebook
  tag: parallel
  parameters:
    ds: '2025-08-09'
    SOURCE_BUCKET: prod-dse-backend-events-raw
    SOURCE_SPLIT: daily
    SINK_BUCKET: prod-dse-backend-events
    SUBPREFIX: application-logs/print_engine-printing_forecast/,application-logs/printing_service_api-printing_forecast/
    TABLE_NAME: printing_forecast
    MAX_ROWS_PER_FILE: 100000
  retries: 3
  executor_config:
    node_type : spot
    load_type : high-mem
  alias: printing_forecast
# - name: notebook
#   tag: parallel
#   parameters:
#     ds: '2025-08-09'
#     SOURCE_BUCKET: prod-dse-backend-events-raw
#     SOURCE_SPLIT: daily
#     SINK_BUCKET: prod-dse-backend-events
#     SUBPREFIX: application-logs/espina_api_canary-api_logs/,application-logs/espina_api-api_logs/
#     TABLE_NAME: espina_api_logs
#     MAX_ROWS_PER_FILE: 500000
#   retries: 3
#   alias: espina_api_logs
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ
path: de/misc/workflow/transform_vector_backend_logs_backfill
paused: false
project_name: misc
schedule:
  interval: '0 0 1 1 1'
  start_date: '2022-03-29T10:00:00'
  end_date: '2025-08-15T00:00:00'
schedule_type: fixed
sla: 1440 minutes
slack_alert_configs:
- channel: bl-data-alerts-p1
support_files: []
tags: []
template_name: multi_notebook
version: 1