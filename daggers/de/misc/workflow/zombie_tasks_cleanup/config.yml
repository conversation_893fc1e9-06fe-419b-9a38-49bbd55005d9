alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: zombie_tasks_cleanup
dag_type: workflow
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
  type: kubernetes
namespace: de
notebooks:
- alias: prep
  executor_config:
    load_type: tiny
    node_type: spot
  name: cleanup
  parameters:
    MAX_RUNNING_TIME_HOURS: 24
    env: prep
    vault_path: dse/services/airflow/users/airflow-prep/admin
  tag: parallel
- alias: common
  executor_config:
    load_type: tiny
    node_type: spot
  name: cleanup
  parameters:
    MAX_RUNNING_TIME_HOURS: 24
    env: common
    vault_path: dse/services/airflow/users/airflow-common/admin
  tag: parallel
- alias: de
  executor_config:
    load_type: tiny
    node_type: spot
  name: cleanup
  parameters:
    MAX_RUNNING_TIME_HOURS: 24
    env: de
    vault_path: dse/services/airflow/users/airflow-de/api_user
  tag: parallel
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ
path: de/misc/workflow/zombie_tasks_cleanup
paused: false
project_name: misc
schedule:
  end_date: '2026-09-06T00:00:00'
  interval: 30 1,13 * * *
  start_date: '2023-01-16T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: multi_notebook
version: 1
