alert_configs:
  slack:
  - channel: bl-data-alerts-p1
dag_name: pinot_wtd_mtd_metrics
dag_type: workflow
escalation_priority: low
execution_timeout: 60
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: de
notebook:
  parameters: 
owner:
  email: <EMAIL>
  slack_id: S03TEACQKDW
path: de/misc/workflow/pinot_wtd_mtd_metrics
paused: false
pool: de_pool
project_name: misc
schedule:
  interval: '0,30 1-3 * * *'
  start_date: '2025-02-01T00:00:00'
  end_date: '2025-12-10T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
