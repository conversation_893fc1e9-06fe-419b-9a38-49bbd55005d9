# Workflows that require low SLA but have low throughput
dag_name: transform_backend_logs_every_2hrs
dag_type: workflow
escalation_priority: high
namespace: de
notebooks:
- name: notebook
  executor_config:
    load_type: medium
    node_type: spot
  tag: parallel
  parameters:
    ds: '{{ ds }}'
    SOURCE_BUCKET: prod-dse-backend-events-raw
    SINK_BUCKET: prod-dse-backend-events
    SUBPREFIX: application-logs/checkout_service_primary-abandoned_cart_notif_sent/,application-logs/checkout_service_canary-abandoned_cart_notif_sent/
    TABLE_NAME: checkout_service_abandoned_cart_notifs
    MAX_ROWS_PER_FILE: 1500000
  retries: 2
  alias: checkout_service_abandoned_cart_notifs
- name: notebook
  executor_config:
    load_type: medium
    node_type: spot
  tag: parallel
  parameters:
    ds: '{{ ds }}'
    SOURCE_BUCKET: prod-dse-backend-events-raw
    SINK_BUCKET: prod-dse-backend-events
    SUBPREFIX: application-logs/checkout_service_primary-eligible_abanoned_cart/,application-logs/checkout_service_canary-eligible_abanoned_cart/
    TABLE_NAME: checkout_service_eligible_abanoned_cart
    MAX_ROWS_PER_FILE: 1500000
  retries: 2
  alias: checkout_service_eligible_abanoned_cart
- name: notebook
  executor_config:
    load_type: medium
    node_type: spot
  tag: parallel
  parameters:
    ds: '{{ ds }}'
    SOURCE_BUCKET: prod-dse-backend-events-raw
    SINK_BUCKET: prod-dse-backend-events
    SUBPREFIX: application-logs/serviceability_notification_celery-serviceability_sent_notification/
    TABLE_NAME: serviceability_sent_notification
    MAX_ROWS_PER_FILE: 1500000
  retries: 2
  alias: serviceability_sent_notification
- name: notebook
  executor_config:
    load_type: medium
    node_type: spot
  tag: parallel
  parameters:
    ds: '{{ ds }}'
    SOURCE_BUCKET: prod-dse-backend-events-raw
    SINK_BUCKET: prod-dse-backend-events
    SUBPREFIX: application-logs/checkout_service_primary-null_karma_score/,application-logs/checkout_service_canary-null_karma_score/
    TABLE_NAME: null_karma_score
    MAX_ROWS_PER_FILE: 1500000
  retries: 2
  alias: null_karma_score
- name: notebook
  executor_config:
    load_type: medium
    node_type: spot
  tag: parallel
  parameters:
    ds: '{{ ds }}'
    SOURCE_BUCKET: prod-dse-backend-events-raw
    SINK_BUCKET: prod-dse-backend-events
    SUBPREFIX: application-logs/checkout_service_primary-rewards_applicability/,application-logs/checkout_service_canary-rewards_applicability/
    TABLE_NAME: rewards_applicability
    MAX_ROWS_PER_FILE: 1500000
  retries: 2
  alias: rewards_applicability
- name: notebook
  executor_config:
    load_type: high-mem
    node_type: spot
  tag: parallel
  parameters:
    ds: '{{ ds }}'
    SOURCE_BUCKET: prod-dse-backend-events-raw
    SINK_BUCKET: prod-dse-backend-events
    SUBPREFIX: application-logs/bots_api-user_bot_journey/,application-logs/bots_api_wimo_cron-user_bot_journey/
    TABLE_NAME: bots_api-user_bot_journey
    MAX_ROWS_PER_FILE: 25000
  retries: 3
  alias: bots_api_user_bot_journey
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ
path: de/misc/workflow/transform_backend_logs_every_2hrs
paused: false
project_name: misc
schedule:
  interval: '0 */2 * * *'
  start_date: '2022-12-07T11:00:00'
  end_date: '2026-01-15T00:00:00'
schedule_type: fixed
sla: 120 minutes
slack_alert_configs:
- channel: bl-data-alerts-p1
- channel: bl-cd-analytics-internal
support_files: []
tags: []
template_name: multi_notebook
version: 1
executor:
  config:
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    load_type: medium
    node_type: spot
  type: kubernetes
