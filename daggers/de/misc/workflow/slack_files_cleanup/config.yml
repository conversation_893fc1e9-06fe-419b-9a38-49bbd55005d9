alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: slack_files_cleanup
dag_type: workflow
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow-de/plugins
  type: kubernetes
namespace: de
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U04UN5M83RQ
path: de/misc/workflow/slack_files_cleanup
paused: false
pool: de_pool
project_name: misc
schedule:
  end_date: '2026-08-17T00:00:00'
  interval: 30 17 * * *
  start_date: '2025-03-19T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
