alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: user_slack_info
dag_type: workflow
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: de
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ
path: de/misc/workflow/user_slack_info
paused: false
pool: de_pool
project_name: misc
schedule:
  end_date: '2026-03-13T00:00:00'
  interval: 30 6 * * *
  start_date: '2024-12-12T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
