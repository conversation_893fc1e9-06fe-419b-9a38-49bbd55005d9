alert_configs:
  slack:
    - channel: bl-data-alerts-p1
dag_name: redash_queue_alert
dag_type: workflow
escalation_priority: low
execution_timeout: 50
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    volume_mounts:
      - mountPath: /usr/local/airflow/plugins
        name: airflow-dags
        subPath: airflow-de/plugins
  type: kubernetes
namespace: de
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: S03TEACQKDW
path: de/misc/workflow/redash_queue_alert
paused: false
project_name: misc
schedule:
  end_date: "2026-08-15T00:00:00"
  interval: 0 5 * * *
  start_date: "2024-08-17T06:00:00"
schedule_type: fixed
sla: 60 minutes
support_files:
  - slack_client.py
tags:
  - redash_queue_alert
template_name: notebook
version: 1
