from slack_sdk import WebClient
from slack_sdk.errors import SlackApiError

class SlackClient:
    def __init__(self, slack_bot_token):
        self.slack_client = WebClient(token=slack_bot_token)

    def fetch_slack_user_info(self, email):
        if not len(email):
            raise Exception("user email cannot be null")

        try:
            response = self.slack_client.users_lookupByEmail(email=email)
            return response.data["user"]
        except Exception as e:
            assert e.response["ok"] is False
            assert e.response["error"]  # str like 'invalid_auth', 'channel_not_found'
            print(f"Got an error: {e.response['error']}")