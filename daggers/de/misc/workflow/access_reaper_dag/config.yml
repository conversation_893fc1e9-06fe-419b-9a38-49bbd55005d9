alert_configs:
  slack:
  - channel: bl-data-alerts-p1
dag_name: access_reaper_dag
dag_type: workflow
escalation_priority: low
executor:
  config:
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    memory:
      limit: 4G
      request: 500M
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow-de/plugins
  type: kubernetes
namespace: de
notebooks:
- alias: sonar_cleanup
  name: sonar
  parameters: null
  tag: parallel
  executor_config:
    load_type: tiny
    node_type: spot
- alias: redash_cleanup
  name: redash
  parameters: null
  tag: parallel
  executor_config:
    load_type: tiny
    node_type: spot
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ
path: de/misc/workflow/access_reaper_dag
paused: false
project_name: misc
schedule:
  interval: 30 11 * * *
  start_date: '2023-04-24T00:00:00'
  end_date: '2026-08-15T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: ["access_reaper", "cleanup"]
template_name: multi_notebook
version: 1
