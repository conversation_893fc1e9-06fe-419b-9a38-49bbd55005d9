{"cells": [{"cell_type": "code", "execution_count": null, "id": "89270324-a49e-4556-a7b9-1bae25fd1315", "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "\n", "import ast\n", "import pandas as pd\n", "import pencilbox as pb\n", "import requests"]}, {"cell_type": "code", "execution_count": null, "id": "decaf7d7-e537-40f2-b3f6-27beb87e16bc", "metadata": {}, "outputs": [], "source": ["REPORTS_API_KEY = pb.get_secret(\"data/services/redash-reports/access_management/api_keys\")[\"key\"]\n", "QUERIES_API_KEY = pb.get_secret(\"data/services/redash-queries/access_management/api_keys\")[\"key\"]\n", "REDASH_REPORTS_URL = pb.get_secret(\"data/services/redash-reports/access_management/api_keys\")[\n", "    \"internal_url\"\n", "]\n", "REDASH_QUERIES_URL = pb.get_secret(\"data/services/redash-queries/access_management/api_keys\")[\n", "    \"internal_url\"\n", "]\n", "\n", "IMMORTAL_USERS = ast.literal_eval(\n", "    pb.get_secret(\"data/services/access_management/users\")[\"immortal_users\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "6a7d0f38-12bf-4ffa-829b-dfa831a01240", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"WITH \n", "queries_users AS\n", "  (SELECT u.id AS queries_user_id,\n", "          u.email AS queries_email,\n", "          FORMAT_DATETIME(FROM_UNIXTIME(u.disabled_at/1000000) AT TIME ZONE 'Asia/Kolkata','yyyy-MM-dd HH:mm:ss.') AS queries_disabled_at,\n", "          'redash-queries.grofer.io' AS platform_queries,\n", "          CASE\n", "              WHEN u.disabled_at IS NOT NULL THEN 'Inactive'\n", "              ELSE 'Active'\n", "          END AS queries_is_active,\n", "          MAX(e.created_at) AS last_time_queries_used\n", "   FROM lake_redash_queries.users u\n", "   JOIN lake_events.redash_queries_events e ON u.id = e.user_id\n", "   AND e.at_date_ist > CURRENT_DATE - INTERVAL '1' year\n", "   GROUP BY u.id, u.email, u.disabled_at\n", "    ),\n", "            \n", "reports_users AS\n", "  (SELECT u.id AS reports_user_id,\n", "          u.email AS reports_email,\n", "          FORMAT_DATETIME(FROM_UNIXTIME(u.disabled_at/1000000) AT TIME ZONE 'Asia/Kolkata','yyyy-MM-dd HH:mm:ss.') AS reports_disabled_at,\n", "          'reports.grofer.io' AS platform_reports,\n", "          CASE\n", "              WHEN u.disabled_at IS NOT NULL THEN 'Inactive'\n", "              ELSE 'Active'\n", "          END AS reports_is_active,\n", "          MAX(e.created_at) AS last_time_reports_used\n", "   FROM lake_redash_reports.users u\n", "   JOIN lake_events.redash_reports_events e ON u.id = e.user_id\n", "   AND e.at_date_ist > CURRENT_DATE - INTERVAL '1' year\n", "   GROUP BY u.id, u.email, u.disabled_at\n", "    ),\n", "            \n", "total_users AS\n", "  ( SELECT *\n", "   FROM queries_users\n", "   FULL OUTER JOIN reports_users ON queries_users.queries_email = reports_users.reports_email \n", "  ),\n", "\n", "both_platforms_inactive AS\n", "  ( SELECT *\n", "    FROM total_users\n", "    WHERE (total_users.queries_user_id IS NOT NULL AND total_users.last_time_queries_used < CURRENT_TIMESTAMP - INTERVAL '60' day)\n", "    AND (total_users.reports_user_id IS NOT NULL AND total_users.last_time_reports_used < CURRENT_TIMESTAMP - INTERVAL '60' day)\n", "   ),\n", "\n", "individual_platform_inactive AS\n", "  ( SELECT *\n", "    FROM total_users\n", "    WHERE ((total_users.queries_user_id IS NOT NULL AND total_users.last_time_queries_used < CURRENT_TIMESTAMP - INTERVAL '60' day)\n", "           AND (total_users.reports_user_id IS NOT NULL AND total_users.last_time_reports_used >= CURRENT_TIMESTAMP - INTERVAL '60' day))\n", "    OR ((total_users.reports_user_id IS NOT NULL AND total_users.last_time_reports_used < CURRENT_TIMESTAMP - INTERVAL '60' day)\n", "        AND (total_users.queries_user_id IS NOT NULL AND total_users.last_time_queries_used >= CURRENT_TIMESTAMP - INTERVAL '60' day))\n", "   ),\n", "\n", "single_only_platform_inactive AS\n", "  ( SELECT *\n", "    FROM total_users\n", "    WHERE (total_users.queries_user_id IS NULL AND total_users.last_time_reports_used < CURRENT_TIMESTAMP - INTERVAL '60' day)\n", "    OR (total_users.reports_user_id IS NULL AND total_users.last_time_queries_used < CURRENT_TIMESTAMP - INTERVAL '60' day)\n", "   ),\n", "          \n", "all_inactive_users AS\n", "  ( SELECT *, 'both_platforms_inactive' as case_type FROM both_platforms_inactive\n", "    UNION ALL\n", "    SELECT *, 'individual_platform_inactive' as case_type FROM individual_platform_inactive\n", "    UNION ALL\n", "    SELECT *, 'single_only_platform_inactive' as case_type FROM single_only_platform_inactive\n", "  )\n", "  \n", "SELECT A.*, \n", "       B.deleted as deactivated_slack_account,\n", "       CASE \n", "           WHEN A.queries_user_id IS NOT NULL AND A.last_time_queries_used < CURRENT_TIMESTAMP - INTERVAL '60' day\n", "           THEN 'remove_groups'\n", "           ELSE 'keep_access'\n", "       END AS queries_action,\n", "       CASE \n", "           WHEN A.reports_user_id IS NOT NULL AND A.last_time_reports_used < CURRENT_TIMESTAMP - INTERVAL '60' day\n", "           THEN 'remove_groups'\n", "           ELSE 'keep_access'\n", "       END AS reports_action,\n", "       CASE \n", "           WHEN B.deleted = true THEN 'disable_account'\n", "           ELSE 'remove_groups_only'\n", "       END AS account_action\n", "FROM all_inactive_users A\n", "LEFT JOIN de_etls.user_slack_info B ON COALESCE(A.queries_email, A.reports_email) = B.email\"\"\"\n", "con = pb.get_connection(\"[Warehouse] Trino\")\n", "df = pd.read_sql_query(sql=sql, con=con)"]}, {"cell_type": "code", "execution_count": null, "id": "3e7f03e8-a9a0-4710-9113-b453ebe6138b", "metadata": {}, "outputs": [], "source": ["df[\"queries_user_id\"] = df[\"queries_user_id\"].astype(\"Int64\")\n", "df[\"reports_user_id\"] = df[\"reports_user_id\"].astype(\"Int64\")\n", "df = df[~(df[\"reports_email\"].isin(IMMORTAL_USERS) | df[\"queries_email\"].isin(IMMORTAL_USERS))]"]}, {"cell_type": "code", "execution_count": null, "id": "5caf3e00-97a3-476a-90a5-3beb5e48a67a", "metadata": {}, "outputs": [], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "id": "d614a61a-643d-4650-b786-b42f51387b1b", "metadata": {}, "outputs": [], "source": ["filtered_df = df[((df[\"queries_is_active\"] == \"Active\") | (df[\"reports_is_active\"] == \"Active\"))]"]}, {"cell_type": "code", "execution_count": null, "id": "847f4282-50c0-4ea0-a161-ed24df2492ba", "metadata": {}, "outputs": [], "source": ["filtered_df = filtered_df.reset_index(drop=True)\n", "filtered_df"]}, {"cell_type": "code", "execution_count": null, "id": "82ea203c-a04a-45bd-9241-d8f084f04583", "metadata": {}, "outputs": [], "source": ["def get_platform_id_dict(df):\n", "    result_dict = {}\n", "    for index, row in df.iterrows():\n", "        queries_email = row[\"queries_email\"]\n", "        reports_email = row[\"reports_email\"]\n", "        queries_user_id = row[\"queries_user_id\"]\n", "        reports_user_id = row[\"reports_user_id\"]\n", "        queries_action = row[\"queries_action\"]\n", "        reports_action = row[\"reports_action\"]\n", "        account_action = row[\"account_action\"]\n", "        case_type = row[\"case_type\"]\n", "\n", "        queries_id_to_process = None\n", "        reports_id_to_process = None\n", "\n", "        if queries_action == \"remove_groups\" and pd.notna(queries_user_id):\n", "            queries_id_to_process = queries_user_id\n", "\n", "        if reports_action == \"remove_groups\" and pd.notna(reports_user_id):\n", "            reports_id_to_process = reports_user_id\n", "\n", "        if pd.notnull(queries_email):\n", "            result_dict[queries_email] = {\n", "                \"queries_user_id\": queries_id_to_process,\n", "                \"reports_user_id\": reports_id_to_process,\n", "                \"queries_action\": queries_action,\n", "                \"reports_action\": reports_action,\n", "                \"account_action\": account_action,\n", "                \"case_type\": case_type,\n", "            }\n", "\n", "        if pd.notnull(reports_email) and reports_email != queries_email:\n", "            result_dict[reports_email] = {\n", "                \"queries_user_id\": queries_id_to_process,\n", "                \"reports_user_id\": reports_id_to_process,\n", "                \"queries_action\": queries_action,\n", "                \"reports_action\": reports_action,\n", "                \"account_action\": account_action,\n", "                \"case_type\": case_type,\n", "            }\n", "\n", "    return result_dict"]}, {"cell_type": "code", "execution_count": null, "id": "112fa40e-bf95-492e-90d4-f77815154c2c", "metadata": {}, "outputs": [], "source": ["inactive_users_platform_id_dict = get_platform_id_dict(filtered_df)"]}, {"cell_type": "code", "execution_count": null, "id": "087eaab9-683f-42e9-a37e-10eb18434a24", "metadata": {}, "outputs": [], "source": ["inactive_users_platform_id_dict"]}, {"cell_type": "code", "execution_count": null, "id": "e7a192ac-1975-4560-84d4-8cf5b078d2df", "metadata": {}, "outputs": [], "source": ["def remove_user_from_all_groups(platform, user_id):\n", "    group_mapping = {\n", "        \"queries\": {\"full_access\": 1059, \"view_only\": 1058},\n", "        \"reports\": {\"full_access\": 1255, \"view_only\": 1254},\n", "    }\n", "\n", "    if platform == \"queries\":\n", "        platform_name = \"redash_queries\"\n", "        api_key = QUERIES_API_KEY\n", "        base_url = REDASH_QUERIES_URL\n", "    elif platform == \"reports\":\n", "        platform_name = \"redash_reports\"\n", "        api_key = REPORTS_API_KEY\n", "        base_url = REDASH_REPORTS_URL\n", "\n", "    headers = {\"Authorization\": f\"Key {api_key}\"}\n", "\n", "    platform_groups = group_mapping[platform]\n", "\n", "    groups_removed = []\n", "\n", "    for access_type, group_id in platform_groups.items():\n", "        try:\n", "            # API call to remove user from group\n", "            remove_url = f\"{base_url}/api/groups/{group_id}/members/{user_id}\"\n", "            response = requests.delete(remove_url, headers=headers, verify=False)\n", "\n", "            if response.status_code == 200:\n", "                groups_removed.append(f\"{access_type} (ID: {group_id})\")\n", "                print(f\"Successfully removed user {user_id} from {platform} group: {access_type}\")\n", "            elif response.status_code == 404:\n", "                print(f\"User {user_id} was not in {platform} group: {access_type}\")\n", "            else:\n", "                print(\n", "                    f\"Failed to remove user {user_id} from {platform} group: {access_type}. Status: {response.status_code}\"\n", "                )\n", "\n", "        except Exception as e:\n", "            print(f\"Error removing user {user_id} from {platform} group {access_type}: {str(e)}\")\n", "\n", "    if groups_removed:\n", "        print(f\"User {user_id} removed from {platform} groups: {', '.join(groups_removed)}\")\n", "    else:\n", "        print(f\"No groups removed for user {user_id} on {platform} platform\")"]}, {"cell_type": "code", "execution_count": null, "id": "5a08275b-ce2e-4de3-9675-fc62df17551a", "metadata": {"tags": []}, "outputs": [], "source": ["def disable_users(platform_id_user_dict):\n", "    for email, data in platform_id_user_dict.items():\n", "        resp_count = 0\n", "        from_email = \"<EMAIL>\"\n", "        to_email = [email]\n", "        subject = \"Redash Access Blocked\"\n", "\n", "        platform_actions = []\n", "        account_disabled = False\n", "\n", "        # If Slack is deactivated, disable account on ALL platforms the user has access to\n", "        if data[\"account_action\"] == \"disable_account\":\n", "            if data[\"queries_user_id\"] is not None:\n", "                queries_disable_endpoint = (\n", "                    f\"{REDASH_QUERIES_URL}/api/users/{data['queries_user_id']}/disable\"\n", "                )\n", "                queries_headers = {\"Authorization\": f\"Key {QUERIES_API_KEY}\"}\n", "                queries_response = requests.post(\n", "                    queries_disable_endpoint, headers=queries_headers, verify=False\n", "                )\n", "                if queries_response.status_code == 200:\n", "                    platform_actions.append(\"redash-queries.grofer.io (account disabled)\")\n", "                    resp_count += 1\n", "                    account_disabled = True\n", "                    print(\n", "                        f\"Successfully disabled queries account for user {data['queries_user_id']}\"\n", "                    )\n", "                else:\n", "                    print(\n", "                        f\"Failed to disable queries account for user {data['queries_user_id']}. Status: {queries_response.status_code}\"\n", "                    )\n", "\n", "            if data[\"reports_user_id\"] is not None:\n", "                reports_disable_endpoint = (\n", "                    f\"{REDASH_REPORTS_URL}/api/users/{data['reports_user_id']}/disable\"\n", "                )\n", "                reports_headers = {\"Authorization\": f\"Key {REPORTS_API_KEY}\"}\n", "                reports_response = requests.post(\n", "                    reports_disable_endpoint, headers=reports_headers, verify=False\n", "                )\n", "                if reports_response.status_code == 200:\n", "                    platform_actions.append(\"reports.grofer.io (account disabled)\")\n", "                    resp_count += 1\n", "                    account_disabled = True\n", "                    print(\n", "                        f\"Successfully disabled reports account for user {data['reports_user_id']}\"\n", "                    )\n", "                else:\n", "                    print(\n", "                        f\"Failed to disable reports account for user {data['reports_user_id']}. Status: {reports_response.status_code}\"\n", "                    )\n", "\n", "        else:\n", "            # Only remove groups from inactive platforms\n", "            if data[\"queries_action\"] == \"remove_groups\" and data[\"queries_user_id\"] is not None:\n", "                remove_user_from_all_groups(\"queries\", data[\"queries_user_id\"])\n", "                platform_actions.append(\"redash-queries.grofer.io (groups removed)\")\n", "                resp_count += 1\n", "\n", "            if data[\"reports_action\"] == \"remove_groups\" and data[\"reports_user_id\"] is not None:\n", "                remove_user_from_all_groups(\"reports\", data[\"reports_user_id\"])\n", "                platform_actions.append(\"reports.grofer.io (groups removed)\")\n", "                resp_count += 1\n", "\n", "        if resp_count > 0:\n", "            platform_str = \"<li>{}</li>\".format(\"</li><li>\".join(platform_actions))\n", "\n", "            if account_disabled:\n", "                html_content = f\"\"\"Your Account has been disabled due to inactivity for more than 60 days and your Slack account being deactivated.\n", "                <br><br>Platforms affected:\n", "                <ul>{platform_str}</ul>\n", "                <br>If you need access restored, please contact the data team.\"\"\"\n", "            else:\n", "                html_content = f\"\"\"Your Access has been blocked due to inactivity for more than 60 days.\n", "                <br><br>Actions taken:\n", "                <ul>{platform_str}</ul>\n", "                <br>You still have access to active platforms. If you need access restored, please contact the data team.\"\"\"\n", "\n", "            try:\n", "                pb.send_email(from_email, to_email, subject, html_content)\n", "                print(f\"Email sent to {email}\")\n", "            except Exception as e:\n", "                print(f\"Email sending failed for {email}: {str(e)}\")\n", "                print(\"Continuing with other operations...\")\n", "\n", "            print(f\"Processed {email}: {', '.join(platform_actions)}\")"]}, {"cell_type": "code", "execution_count": null, "id": "fc42988c-6727-4ce0-a5fa-e548a203395e", "metadata": {}, "outputs": [], "source": ["inactive_users_platform_id_dict"]}, {"cell_type": "code", "execution_count": null, "id": "82c2ef24-1fbf-441a-82ac-475caba6c3af", "metadata": {}, "outputs": [], "source": ["disable_users(inactive_users_platform_id_dict)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}