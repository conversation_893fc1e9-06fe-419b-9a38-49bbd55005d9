alert_configs:
  slack:
  - channel: bl-data-alerts-p0
dag_name: zppl_table_location_updater
dag_type: etl
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
  type: kubernetes
namespace: de
notebook:
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  retry_exponential_backoff: true
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ
path: de/misc/etl/zppl_table_location_updater
paused: false
project_name: misc
schedule:
  end_date: '2025-10-15T00:00:00'
  interval: 30 23 * * *
  start_date: '2023-07-17T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
