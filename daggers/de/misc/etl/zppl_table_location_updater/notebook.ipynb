{"cells": [{"cell_type": "code", "execution_count": null, "id": "6e4c0295-2f47-4f1d-b516-83619106ddf8", "metadata": {}, "outputs": [], "source": ["!pip install PyHive[hive]==0.6.4 --quiet"]}, {"cell_type": "code", "execution_count": null, "id": "63622b98-2391-4394-9f93-5bdfb10455e9", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "from datetime import datetime, timedelta\n", "from dateutil.tz import gettz\n", "from pyhive import hive"]}, {"cell_type": "code", "execution_count": null, "id": "62503819-a83f-447b-9424-b6d82ff3e8c9", "metadata": {}, "outputs": [], "source": ["def hive_connection():\n", "    hive_conn_params = pb.get_secret(\"dse/hive/hive-server/airflow_hive_sync\")\n", "    hive_conn = hive.Connection(\n", "        host=hive_conn_params[\"host\"],\n", "        port=10000,\n", "        username=hive_conn_params[\"hive_user\"],\n", "        password=hive_conn_params[\"hive_password\"],\n", "        auth=\"CUSTOM\",\n", "    )\n", "    return hive_conn"]}, {"cell_type": "code", "execution_count": null, "id": "7e5ebc51-5e8d-4c56-af69-814c0011f984", "metadata": {}, "outputs": [], "source": ["hive_conn = hive_connection()\n", "cur = hive_conn.cursor()\n", "date_partition_format = \"%Y%m%d\""]}, {"cell_type": "code", "execution_count": null, "id": "0dcf9810-0374-4901-9089-146a3b6220b1", "metadata": {}, "outputs": [], "source": ["dt = datetime.now(tz=gettz(\"Asia/Kolkata\")).date().strftime(date_partition_format)"]}, {"cell_type": "code", "execution_count": null, "id": "52f903ef-63f7-40b1-8d41-67ef28e7b850", "metadata": {}, "outputs": [], "source": ["table_names = [\n", "    \"billing_gateway_settlements\",\n", "    \"billing_breakups\",\n", "    \"user_cards\",\n", "    \"user_card_external_tokens\",\n", "    \"card_bins_v2\",\n", "    \"payment_gateways\",\n", "    \"wallets\",\n", "]\n", "\n", "for table in table_names:\n", "    sql = f\"\"\"alter table btransactions.{table} set location 's3://blinkit-data-pipeline/zppl/mysql_sync/ztransactions/{table}/{dt}/'\"\"\"\n", "    print(sql)\n", "    cur.execute(sql)"]}, {"cell_type": "code", "execution_count": null, "id": "0359fe1b-834e-4ae6-a6d7-5c5dbb32a5d2", "metadata": {}, "outputs": [], "source": ["cur.close()\n", "hive_conn.close()"]}, {"cell_type": "code", "execution_count": null, "id": "9fdf4cda-3cfe-446e-9cc1-9225cbd2c11c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}