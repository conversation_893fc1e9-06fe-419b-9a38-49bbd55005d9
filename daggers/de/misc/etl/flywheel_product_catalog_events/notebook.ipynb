{"cells": [{"cell_type": "code", "execution_count": null, "id": "12ed9d4c-f912-466a-83ac-cc9f445505a4", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import sys, time, json\n", "from datetime import datetime, timezone\n", "import pytz\n", "import os"]}, {"cell_type": "code", "execution_count": null, "id": "00e702ac-4389-4e8b-b287-e43fff88915d", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["cwd = \"./\""]}, {"cell_type": "code", "execution_count": null, "id": "be7dc697-b1cb-47f9-9d21-3a3645f432a5", "metadata": {}, "outputs": [], "source": ["sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "32816889-141d-4981-99ae-5d1af916c2a9", "metadata": {}, "outputs": [], "source": ["from columns_metadata import *\n", "from utils import *"]}, {"cell_type": "markdown", "id": "f1538f81-d954-4381-be66-b6603584f91c", "metadata": {}, "source": ["### Initializations"]}, {"cell_type": "code", "execution_count": null, "id": "96a23224-473c-4e56-a83e-9bb370edc7b7", "metadata": {}, "outputs": [], "source": ["prod_bridge_warpstream_conn_id = \"[Warpstream] prod-bridge-kafka\"\n", "if os.getenv(\"JUPYTERHUB_USER\"):\n", "    prod_bridge_warpstream_conn_id = \"[Kafka] test-data-common\"\n", "warpstream_sink_topic = \"blinkit.de.product_catalog_events\""]}, {"cell_type": "code", "execution_count": null, "id": "9485c1aa-940c-41e7-a854-530115959a60", "metadata": {}, "outputs": [], "source": ["TRINO_GRACE_PERIOD_MINS = \"15\"  # to include records missing due to checkpoint moving forward due to inserts in the base table\n", "INVENTORY_UPDATE_EVENT_NAME = \"INVENTORY_UPDATE\"\n", "PRICING_UPDATE_EVENT_NAME = \"PRICING_UPDATE\"\n", "PRODUCT_DELETE_EVENT_NAME = \"PRODUCT_DELETE\"\n", "PRODUCT_CREATE_EVENT_NAME = \"PRODUCT_CREATE\""]}, {"cell_type": "code", "execution_count": null, "id": "d1f37d60", "metadata": {}, "outputs": [], "source": ["current_date = datetime.now(pytz.timezone(\"Asia/Kolkata\")).strftime(\"%Y-%m-%d\")\n", "print(current_date)"]}, {"cell_type": "code", "execution_count": null, "id": "eaa557d1", "metadata": {}, "outputs": [], "source": ["validation_columns = [\"price\", \"mrp_price\", \"brand_id\"]"]}, {"cell_type": "code", "execution_count": null, "id": "719f43f9-c269-41b5-bc60-a89a3fc88258", "metadata": {}, "outputs": [], "source": ["trino_connection = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "markdown", "id": "4c6664f5-b33b-4d22-828d-02801a0692c7", "metadata": {}, "source": ["### Run maintenance on merchant-product flat table (runs whenever needed)"]}, {"cell_type": "code", "execution_count": null, "id": "880c00f6-1d0a-41d1-821c-fc3a2f6f4c3e", "metadata": {}, "outputs": [], "source": ["run_maintenance(table_name=merchant_product_mapping_flat_table_name)"]}, {"cell_type": "markdown", "id": "cad79211-a1c3-4160-b8f7-2aff6343ce78", "metadata": {"tags": []}, "source": ["### Getting last checkpoints"]}, {"cell_type": "code", "execution_count": null, "id": "869a7ffc-f78e-4603-a720-05bd9397e1d6", "metadata": {}, "outputs": [], "source": ["checkpoints = pd.read_sql_query(\n", "    f\"SELECT event_name, latest_checkpoint_ts FROM {checkpoint_table_schema}.{checkpoint_table_name}\",\n", "    trino_connection,\n", ")\n", "checkpoints_dict = {\n", "    row[\"event_name\"]: row[\"latest_checkpoint_ts\"] for index, row in checkpoints.iterrows()\n", "}\n", "checkpoints_dict"]}, {"cell_type": "markdown", "id": "08598bc5-fc61-49ab-9c95-1076c43f1d8d", "metadata": {"tags": []}, "source": ["### Process inventory update events (product-merchant level)"]}, {"cell_type": "code", "execution_count": null, "id": "a22144e9-1890-49d5-b259-ca61f929a4ac", "metadata": {"tags": []}, "outputs": [], "source": ["inventory_updates_sql = f\"\"\"\n", "WITH\n", "inventory_updates AS (\n", "    SELECT\n", "        'INVENTORY_UPDATE' AS event_type,\n", "        CAST(product_id AS BIGINT) AS product_id,\n", "        CAST(merchant_id AS BIGINT) AS merchant_id,\n", "        \n", "        CASE\n", "            WHEN inventory_status IN ('IN_STOCK', 'OUT_OF_STOCK')\n", "            THEN inventory_status\n", "            ELSE ''\n", "        END AS availability,\n", "        \n", "        DATE_FORMAT(DATE_ADD('minute', -330, FROM_UNIXTIME(CAST(update_ts AS BIGINT) / 1000)), '%%Y-%%m-%%d %%H:%%i:%%s') AS inventory_update_ts,\n", "        arroyo_ingestion_ts,\n", "        \n", "        ROW_NUMBER() OVER(\n", "            PARTITION BY \n", "                product_id, \n", "                merchant_id \n", "            ORDER BY \n", "                DATE_ADD('minute', -330, FROM_UNIXTIME(CAST(update_ts AS BIGINT) / 1000))\n", "                    DESC\n", "        ) AS rank_\n", "    FROM\n", "        lake_events.inventory_update_events\n", "    WHERE\n", "        at_date BETWEEN CURRENT_DATE - INTERVAL '1' DAY AND CURRENT_DATE\n", "        \n", "        AND from_iso8601_timestamp(arroyo_ingestion_ts) > from_iso8601_timestamp(\n", "            '{checkpoints_dict[INVENTORY_UPDATE_EVENT_NAME]}'\n", "        ) - INTERVAL '{TRINO_GRACE_PERIOD_MINS}' MINUTE\n", "),\n", "\n", "new_checkpoint_ts AS (\n", "    SELECT\n", "        MAX(arroyo_ingestion_ts) AS new_checkpoint_ts\n", "    FROM\n", "        inventory_updates\n", "),\n", "\n", "latest_inventory_update AS (\n", "    SELECT\n", "        event_type,\n", "        product_id,\n", "        merchant_id,\n", "        availability,\n", "        inventory_update_ts\n", "    FROM\n", "        inventory_updates\n", "    WHERE\n", "        rank_ = 1\n", "),\n", "\n", "enriched_inventory_update_data AS (\n", "    SELECT\n", "        i.event_type,\n", "        i.product_id,\n", "        i.merchant_id,\n", "        COALESCE(i.availability, 'OUT_OF_STOCK') AS availability,\n", "        i.inventory_update_ts,\n", "        COALESCE(COALESCE(NULLIF(pdp.price, 0), mpm.price),0) AS price,\n", "        COALESCE(COALESCE(pd.brand_id, dp.brand_id), 0) AS brand_id,\n", "        COALESCE(COALESCE(NULLIF(pdp.mrp, 0), mpm.mrp_price), 0) AS mrp_price,\n", "        mpm.enabled_flag,\n", "        mpm.pricing_update_ts,\n", "        mpm.mapping_disable_ts,\n", "        mpm.tags,\n", "        CASE\n", "            WHEN fm.is_bistro_outlet=true\n", "            THEN 'bistro'\n", "            ELS<PERSON> 'blinkit'\n", "        END AS tenant,\n", "        current_date as dt\n", "    FROM\n", "        latest_inventory_update\n", "            AS i\n", "    LEFT JOIN\n", "        {flat_table_schema}.{merchant_product_mapping_flat_table_name}\n", "            AS mpm\n", "            ON mpm.product_id = i.product_id\n", "            AND mpm.merchant_id = i.merchant_id\n", "    LEFT JOIN\n", "        {flat_table_schema}.{product_details_flat_table_name}\n", "            AS pd\n", "            ON pd.product_id = i.product_id\n", "    LEFT JOIN\n", "        {pricing_details_lake_table}\n", "            AS pdp\n", "            ON pdp.cms_product_id = i.product_id\n", "            AND pdp.frontend_id = i.merchant_id\n", "    LEFT JOIN\n", "        {merchant_outlet_facility_mapping_table}\n", "        AS fm\n", "        ON fm.frontend_merchant_id = i.merchant_id\n", "        AND fm.is_current = TRUE\n", "        AND fm.is_mapping_enabled = TRUE\n", "        AND fm.is_current_mapping_active = TRUE\n", "    LEFT JOIN\n", "        {dwh_dim_product}\n", "        AS dp\n", "        ON dp.product_id = i.product_id\n", "        AND dp.is_current = TRUE\n", "    WHERE\n", "        i.availability <> mpm.availability\n", "        OR (mpm.availability IS NULL AND i.availability <> '')\n", "    GROUP BY\n", "        1,2,3,4,5,6,7,8,9,10,11,12,13\n", "\n", "),\n", "\n", "enriched_with_row_number AS (\n", "    SELECT *,\n", "           ROW_NUMBER() OVER (\n", "               PARTITION BY product_id, merchant_id, tenant\n", "               ORDER BY inventory_update_ts DESC\n", "           ) AS row_num\n", "    FROM enriched_inventory_update_data\n", ")\n", "\n", "SELECT\n", "    *\n", "FROM\n", "    enriched_with_row_number,\n", "    new_checkpoint_ts\n", "WHERE \n", "    row_num = 1\n", "\"\"\"\n", "# print(inventory_updates_sql)\n", "inventory_updates = query_trino(inventory_updates_sql, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "7b292e22", "metadata": {}, "outputs": [], "source": ["inventory_update_kafka_df, inventory_update_logging_df = split_and_log_invalid(\n", "    inventory_updates, validation_columns\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a8bafe9b-c48a-46e9-a949-ecbf5b60f717", "metadata": {}, "outputs": [], "source": ["if not inventory_update_kafka_df.empty:\n", "    new_checkpoint_ts = inventory_update_kafka_df[\"new_checkpoint_ts\"].unique().tolist()[0]\n", "    inventory_updates_kafka_msg = inventory_update_kafka_df.copy()\n", "    inventory_updates_kafka_msg = filter_by_tenant(inventory_updates_kafka_msg, \"blinkit\")\n", "    inventory_updates_kafka_msg[\"product_details\"] = inventory_updates_kafka_msg.apply(\n", "        lambda row: {\n", "            \"id\": row[\"product_id\"],\n", "            \"brand_id\": row[\"brand_id\"],\n", "            \"mrp_price\": row[\"mrp_price\"],\n", "        },\n", "        axis=1,\n", "    )\n", "    inventory_updates_kafka_msg = inventory_updates_kafka_msg[\n", "        [\"event_type\", \"product_id\", \"merchant_id\", \"availability\", \"price\", \"product_details\"]\n", "    ]\n", "    print(inventory_updates_kafka_msg.head())\n", "else:\n", "    print(\"No inventory updates to process.\")"]}, {"cell_type": "markdown", "id": "4b3ffce6-35eb-4aa1-9286-361984d4bf67", "metadata": {}, "source": ["#### Push above data to flywheel kafka & then to the flat table"]}, {"cell_type": "code", "execution_count": null, "id": "60a3f884-bf67-4fcd-84b8-2e9d8776a5e6", "metadata": {}, "outputs": [], "source": ["if not inventory_updates.empty:\n", "    to_kafka_batch(\n", "        inventory_updates_kafka_msg, prod_bridge_warpstream_conn_id, warpstream_sink_topic\n", "    )\n", "    push_flat_table_data(inventory_updates)\n", "\n", "    table_update_sql = checkpoint_update_sql.format(INVENTORY_UPDATE_EVENT_NAME, new_checkpoint_ts)\n", "    print(table_update_sql)\n", "    pb.to_trino(table_update_sql, **checkpoint_table_kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "bce022d9", "metadata": {}, "outputs": [], "source": ["del inventory_updates_kafka_msg\n", "del inventory_updates"]}, {"cell_type": "markdown", "id": "3c1945c6-2995-4c33-beb4-c9425f9c46b4", "metadata": {"tags": []}, "source": ["### Process pricing update events (product-merchant level)"]}, {"cell_type": "code", "execution_count": null, "id": "1ae355c7-baf4-4085-90a5-1bc7d1809fa4", "metadata": {}, "outputs": [], "source": ["pricing_updates_sql = f\"\"\"\n", "WITH\n", "pricing_updates AS (\n", "    SELECT\n", "        'PRICING_UPDATE' AS event_type,\n", "        CAST(product_id AS BIGINT) AS product_id,\n", "        CAST(merchant_id AS BIGINT) AS merchant_id,\n", "        COALESCE(price, 0) AS price,\n", "        COALESCE(mrp, 0) AS mrp_price,\n", "        \n", "        DATE_FORMAT(DATE_ADD('minute', -330, FROM_UNIXTIME(CAST(update_ts AS BIGINT))), '%%Y-%%m-%%d %%H:%%i:%%s') AS pricing_update_ts,\n", "        \n", "        arroyo_ingestion_ts,\n", "        \n", "        ROW_NUMBER() OVER(\n", "            PARTITION BY \n", "                product_id, \n", "                merchant_id \n", "            ORDER BY \n", "                DATE_ADD('minute', -330, FROM_UNIXTIME(CAST(update_ts AS BIGINT)))\n", "                    DESC\n", "        ) AS rank_\n", "    FROM\n", "        lake_events.price_update_events\n", "    WHERE\n", "        at_date BETWEEN CURRENT_DATE - INTERVAL '1' DAY AND CURRENT_DATE\n", "        AND from_iso8601_timestamp(arroyo_ingestion_ts) > from_iso8601_timestamp(\n", "            '{checkpoints_dict[PRICING_UPDATE_EVENT_NAME]}'\n", "        ) - INTERVAL '{TRINO_GRACE_PERIOD_MINS}' MINUTE\n", "),\n", "\n", "new_checkpoint_ts AS (\n", "    SELECT\n", "        MAX(arroyo_ingestion_ts) AS new_checkpoint_ts\n", "    FROM\n", "        pricing_updates\n", "),\n", "\n", "latest_pricing_update AS (\n", "    SELECT\n", "        event_type,\n", "        product_id,\n", "        merchant_id,\n", "        price,\n", "        mrp_price,\n", "        pricing_update_ts\n", "    FROM\n", "        pricing_updates\n", "    WHERE\n", "        rank_ = 1\n", "),\n", "\n", "enriched_pricing_update_data AS (\n", "    SELECT\n", "        p.event_type,\n", "        p.product_id,\n", "        p.merchant_id,\n", "        p.price,\n", "        p.mrp_price,\n", "        p.pricing_update_ts,\n", "        COALESCE(mpm.availability, 'OUT_OF_STOCK') AS availability,\n", "        mpm.enabled_flag,\n", "        mpm.mapping_disable_ts,\n", "        mpm.inventory_update_ts,\n", "        mpm.tags,\n", "        COALESCE(COALESCE(pd.brand_id, dp.brand_id),0) AS brand_id,\n", "        CASE\n", "            WHEN fm.is_bistro_outlet=true\n", "            THEN 'bistro'\n", "            ELS<PERSON> 'blinkit'\n", "        END AS tenant,\n", "        current_date as dt\n", "    FROM\n", "        latest_pricing_update\n", "            AS p\n", "    LEFT JOIN\n", "        {flat_table_schema}.{merchant_product_mapping_flat_table_name}\n", "            AS mpm\n", "            ON mpm.product_id = p.product_id\n", "            AND mpm.merchant_id = p.merchant_id\n", "    LEFT JOIN\n", "        {flat_table_schema}.{product_details_flat_table_name}\n", "            AS pd\n", "            ON pd.product_id = p.product_id\n", "    LEFT JOIN\n", "        {merchant_outlet_facility_mapping_table}\n", "        AS fm\n", "        ON fm.frontend_merchant_id = p.merchant_id\n", "        AND fm.is_current = TRUE\n", "        AND fm.is_mapping_enabled = TRUE\n", "        AND fm.is_current_mapping_active = TRUE\n", "    LEFT JOIN\n", "        {dwh_dim_product}\n", "        AS dp\n", "        ON dp.product_id = p.product_id\n", "        AND dp.is_current = TRUE\n", "    WHERE\n", "        p.price <> mpm.price\n", "        OR p.mrp_price <> mpm.mrp_price\n", "        OR (mpm.price IS NULL AND p.price > 0)\n", "        OR (mpm.mrp_price IS NULL AND p.mrp_price > 0)\n", "    GROUP BY\n", "        1,2,3,4,5,6,7,8,9,10,11,12,13\n", "),\n", "\n", "enriched_with_row_number AS (\n", "    SELECT *,\n", "           ROW_NUMBER() OVER (\n", "               PARTITION BY product_id, merchant_id, tenant\n", "               ORDER BY pricing_update_ts DESC\n", "           ) AS row_num\n", "    FROM enriched_pricing_update_data\n", ")\n", "\n", "SELECT\n", "    *\n", "FROM\n", "    enriched_with_row_number,\n", "    new_checkpoint_ts\n", "WHERE row_num = 1\n", "\"\"\"\n", "\n", "pricing_updates = query_trino(pricing_updates_sql, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "9b751c0d-b279-4b1d-a246-ea7c23a5396e", "metadata": {}, "outputs": [], "source": ["pricing_updates_kafka_df, pricing_updates_logging_df = split_and_log_invalid(\n", "    pricing_updates, validation_columns\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "420d56fa-1669-442b-b579-b79d89ce21e9", "metadata": {}, "outputs": [], "source": ["if not pricing_updates_kafka_df.empty:\n", "    new_checkpoint_ts = pricing_updates_kafka_df[\"new_checkpoint_ts\"].unique().tolist()[0]\n", "    pricing_updates_kafka_msg = pricing_updates_kafka_df.copy()\n", "    pricing_updates_kafka_msg = filter_by_tenant(pricing_updates_kafka_msg, \"blinkit\")\n", "    pricing_updates_kafka_msg[\"product_details\"] = pricing_updates_kafka_msg.apply(\n", "        lambda row: {\n", "            \"id\": row[\"product_id\"],\n", "            \"brand_id\": row[\"brand_id\"],\n", "            \"mrp_price\": row[\"mrp_price\"],\n", "        },\n", "        axis=1,\n", "    )\n", "    pricing_updates_kafka_msg = pricing_updates_kafka_msg[\n", "        [\"event_type\", \"product_id\", \"merchant_id\", \"availability\", \"price\", \"product_details\"]\n", "    ]\n", "    print(pricing_updates_kafka_msg.head())\n", "else:\n", "    print(\"No pricing updates to process.\")"]}, {"cell_type": "markdown", "id": "c0cf2196-4b6b-4b8f-8a43-7ca98710975c", "metadata": {}, "source": ["#### Push above data to flywheel kafka & then to the flat table"]}, {"cell_type": "code", "execution_count": null, "id": "bb8b7132-d9b6-4734-97b3-9de9b8e7d287", "metadata": {}, "outputs": [], "source": ["if not pricing_updates.empty:\n", "    to_kafka_batch(pricing_updates_kafka_msg, prod_bridge_warpstream_conn_id, warpstream_sink_topic)\n", "    push_flat_table_data(pricing_updates)\n", "\n", "    table_update_sql = checkpoint_update_sql.format(PRICING_UPDATE_EVENT_NAME, new_checkpoint_ts)\n", "    print(table_update_sql)\n", "    pb.to_trino(table_update_sql, **checkpoint_table_kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "a6b87c98", "metadata": {}, "outputs": [], "source": ["del pricing_updates_kafka_msg\n", "del pricing_updates"]}, {"cell_type": "markdown", "id": "877a6d52-b829-42db-8498-e7e9cdb221f1", "metadata": {}, "source": ["### Process product delete and create event (product-merchant level)"]}, {"cell_type": "code", "execution_count": null, "id": "1e89af9d-92e8-4de7-ac96-bc0b133b3175", "metadata": {"tags": []}, "outputs": [], "source": ["product_creates_deletes_sql = f\"\"\"\n", "WITH\n", "mapping_disabled_updates AS (\n", "    SELECT\n", "        CASE \n", "            WHEN enabled_flag = 'false' THEN 'PRODUCT_DELETE'\n", "            WHEN enabled_flag = 'true' THEN 'PRODUCT_CREATE'\n", "        END AS event_type,\n", "        CAST(product_id AS BIGINT) AS product_id,\n", "        CAST(merchant_id AS BIGINT) AS merchant_id,\n", "        CAST(enabled_flag AS BOOLEAN) AS enabled_flag,\n", "        DATE_FORMAT(DATE_ADD('minute', -330, FROM_UNIXTIME(CAST(update_ts AS BIGINT))), '%%Y-%%m-%%d %%H:%%i:%%s') AS mapping_disable_ts,\n", "        arroyo_ingestion_ts,\n", "        tags,\n", "        \n", "        ROW_NUMBER() OVER(\n", "            PARTITION BY \n", "                product_id, \n", "                merchant_id \n", "            ORDER BY \n", "                DATE_ADD('minute', -330, FROM_UNIXTIME(CAST(update_ts AS BIGINT)))\n", "                    DESC\n", "        ) AS rank_\n", "    FROM\n", "        lake_events.product_merchant_mapping_disabled_events\n", "    WHERE\n", "        at_date BETWEEN CURRENT_DATE - INTERVAL '1' DAY AND CURRENT_DATE\n", "        AND from_iso8601_timestamp(arroyo_ingestion_ts) > from_iso8601_timestamp(\n", "            '{checkpoints_dict[PRODUCT_DELETE_EVENT_NAME]}'\n", "        ) - INTERVAL '{TRINO_GRACE_PERIOD_MINS}' MINUTE\n", "),\n", "\n", "new_checkpoint_ts AS (\n", "    SELECT\n", "        MAX(arroyo_ingestion_ts) AS new_checkpoint_ts\n", "    FROM\n", "        mapping_disabled_updates\n", "),\n", "\n", "latest_mapping_disabled_update AS (\n", "    SELECT\n", "        event_type,\n", "        product_id,\n", "        merchant_id,\n", "        enabled_flag,\n", "        mapping_disable_ts,\n", "        tags\n", "    FROM\n", "        mapping_disabled_updates\n", "    WHERE\n", "        rank_ = 1\n", "),\n", "\n", "gtin_info AS (\n", "    SELECT \n", "        pp2.item_id, pp2.upc, product_id\n", "    FROM rpc.product_product pp2\n", "    INNER JOIN dwh.dim_item_product_offer_mapping o ON o.item_id = pp2.item_id \n", "    WHERE pp2.id IN \n", "    (\n", "        SELECT max(id) AS id FROM rpc.product_product pp WHERE pp.active=1 AND pp.approved=1  GROUP BY item_id\n", "    )\n", "    GROUP BY 1,2,3\n", "),\n", "\n", "enriched_mapping_disabled_data AS (\n", "    SELECT\n", "        d.event_type,\n", "        d.product_id,\n", "        d.merchant_id,\n", "        d.enabled_flag,\n", "        d.mapping_disable_ts,\n", "        d.tags,\n", "        COALESCE(COALESCE(NULLIF(pdp.price, 0), mpm.price),0) AS price,\n", "        COALESCE(COALESCE(NULLIF(pdp.mrp, 0), mpm.mrp_price), 0) AS mrp_price,\n", "        mpm.pricing_update_ts,\n", "        COALESCE(mpm.availability, 'OUT_OF_STOCK') AS availability,\n", "        mpm.inventory_update_ts,\n", "        COALESCE(COALESCE(pd.brand_id, dp.brand_id),0) AS brand_id,\n", "        COALESCE(pd.product_name, '') as product_name,\n", "        COALESCE(pd.description, '') as description,\n", "        COALESCE(pd.brand_name, '') as brand_name,\n", "        COALESCE(pd.image_link, '') as image_link,\n", "        COALESCE(pd.frame_image_link, '') as frame_image_link,\n", "        json_array(\n", "               json_object(\n", "                   'type': 'l0',\n", "                   'id': COALESCE(pd.l0_category_id, 0),\n", "                   'name': COALESCE(pd.l0_category, '')\n", "               ),\n", "               json_object(\n", "                   'type': 'l1',\n", "                   'id': COALESCE(pd.l1_category_id, 0),\n", "                   'name': COALESCE(pd.l1_category, '')\n", "               ),\n", "               json_object(\n", "                   'type': 'l2',\n", "                   'id': COALESCE(pd.l2_category_id, 0),\n", "                   'name': COALESCE(pd.l2_category, '')\n", "               )\n", "        ) AS categories,\n", "       'https://blinkit.com/prn/'||lower(replace(replace(pd.product_name,' ','-'),'%%','percent'))||'/prid/'||cast(cast(pd.product_id as bigint) as varchar) as destination_link,\n", "        CASE\n", "            WHEN fm.is_bistro_outlet=true\n", "            THEN 'bistro'\n", "            ELS<PERSON> 'blinkit'\n", "        END AS tenant,\n", "        current_date as dt,\n", "        CASE WHEN ((COALESCE(pd.l0_category_id, 0) = 1487) or (COALESCE(pd.product_name, '') LIKE '%%GMC%%') or (COALESCE(pd.product_name, '') LIKE '%%GHH%%')) then '' else gi.upc end as gtin\n", "        \n", "    FROM\n", "        latest_mapping_disabled_update\n", "            AS d\n", "    LEFT JOIN\n", "        {flat_table_schema}.{merchant_product_mapping_flat_table_name}\n", "            AS mpm\n", "            ON mpm.product_id = d.product_id\n", "            AND mpm.merchant_id = d.merchant_id\n", "    LEFT JOIN\n", "        {flat_table_schema}.{product_details_flat_table_name}\n", "            AS pd\n", "            ON pd.product_id = d.product_id\n", "    LEFT JOIN\n", "        {pricing_details_lake_table}\n", "            AS pdp\n", "            ON pdp.cms_product_id = d.product_id\n", "            AND pdp.frontend_id = d.merchant_id\n", "    LEFT JOIN\n", "        {merchant_outlet_facility_mapping_table}\n", "        AS fm\n", "        ON fm.frontend_merchant_id = d.merchant_id\n", "        AND fm.is_current = TRUE\n", "        AND fm.is_mapping_enabled = TRUE\n", "        AND fm.is_current_mapping_active = TRUE\n", "    LEFT JOIN\n", "        {dwh_dim_product}\n", "        AS dp\n", "        ON dp.product_id = d.product_id\n", "        AND dp.is_current = TRUE\n", "    LEFT JOIN\n", "        gtin_info\n", "        AS gi\n", "        ON dp.product_id = gi.product_id\n", "        AND dp.is_current = TRUE\n", "    WHERE\n", "        d.enabled_flag <> mpm.enabled_flag\n", "        OR (mpm.enabled_flag IS NULL AND d.enabled_flag IS NOT NULL)\n", "    GROUP BY\n", "        1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22\n", ")\n", "\n", "SELECT\n", "    *\n", "FROM\n", "    enriched_mapping_disabled_data,\n", "    new_checkpoint_ts\n", "\"\"\"\n", "# print(product_creates_deletes_sql)\n", "product_creates_deletes = query_trino(product_creates_deletes_sql, trino_connection)"]}, {"cell_type": "code", "execution_count": null, "id": "485e5ccc-74b3-4b02-928c-f3a9cd332423", "metadata": {}, "outputs": [], "source": ["product_creates_deletes_kafka_df, product_creates_deletes_logging_df = split_and_log_invalid(\n", "    product_creates_deletes, validation_columns\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b37182a3-5653-48cc-b6eb-33640bea1ca5", "metadata": {}, "outputs": [], "source": ["def build_product_details(row):\n", "    if row[\"event_type\"] == PRODUCT_DELETE_EVENT_NAME:\n", "        return {\"id\": row[\"product_id\"], \"brand_id\": row[\"brand_id\"]}\n", "    elif row[\"event_type\"] == PRODUCT_CREATE_EVENT_NAME:\n", "        return {\n", "            \"id\": row[\"product_id\"],\n", "            \"name\": row.get(\"product_name\", \"#NA\"),\n", "            \"description\": row.get(\"description\", \"#NA\"),\n", "            \"mrp_price\": row.get(\"mrp_price\", 0),\n", "            \"brand\": row.get(\"brand_name\", \"#NA\"),\n", "            \"gtin\": row.get(\"gtin\", \"\"),\n", "            \"brand_id\": (\n", "                row.get(\"brand_id\")\n", "                if not isinstance(row.get(\"brand_id\"), pd.Series)\n", "                else row.get(\"brand_id\").iloc[0]\n", "            ),\n", "            \"image_link\": row.get(\"image_link\", \"#NA\"),\n", "            \"frame_image_link\": row.get(\"frame_image_link\", \"#NA\"),\n", "            \"categories\": (\n", "                json.loads(row[\"categories\"])\n", "                if isinstance(row[\"categories\"], str)\n", "                else row[\"categories\"]\n", "            ),\n", "            \"destination_link\": row.get(\"destination_link\", \"#NA\"),\n", "        }"]}, {"cell_type": "code", "execution_count": null, "id": "c9febdde-83aa-481c-a968-0334f6e5822d", "metadata": {}, "outputs": [], "source": ["if not product_creates_deletes_kafka_df.empty:\n", "    new_checkpoint_ts = product_creates_deletes_kafka_df[\"new_checkpoint_ts\"].unique().tolist()[0]\n", "\n", "    product_creates_deletes_kafka_msg = product_creates_deletes_kafka_df.copy()\n", "    product_creates_deletes_kafka_msg = filter_by_tenant(\n", "        product_creates_deletes_kafka_msg, \"blinkit\"\n", "    )\n", "    product_creates_deletes_kafka_msg[\"product_details\"] = product_creates_deletes_kafka_msg.apply(\n", "        build_product_details, axis=1\n", "    )\n", "\n", "    product_creates_deletes_kafka_msg = product_creates_deletes_kafka_msg[\n", "        [\"event_type\", \"product_id\", \"merchant_id\", \"product_details\"]\n", "    ]\n", "\n", "    print(product_creates_deletes_kafka_msg.head())\n", "\n", "else:\n", "    print(\"No product creates/deletes to process.\")"]}, {"cell_type": "code", "execution_count": null, "id": "91c018a5-246d-49f5-83a4-8307e5570986", "metadata": {}, "outputs": [], "source": ["# Drop the 'gtin' column, we don't need this in merchant_product_mapping\n", "product_creates_deletes = product_creates_deletes.drop(columns=[\"gtin\"])\n", "\n", "# Drop duplicate rows based on the primary key columns\n", "product_creates_deletes = product_creates_deletes.drop_duplicates(\n", "    subset=[\"product_id\", \"merchant_id\", \"tenant\"]\n", ")"]}, {"cell_type": "markdown", "id": "2e862337-0a95-4660-8172-efea0f7a48d6", "metadata": {}, "source": ["#### Push above data to flywheel kafka & then to the flat table"]}, {"cell_type": "code", "execution_count": null, "id": "7682f44c-7f42-47ce-a9b5-ed2e2ef8eb5a", "metadata": {}, "outputs": [], "source": ["if not product_creates_deletes.empty:\n", "    to_kafka_batch(\n", "        product_creates_deletes_kafka_msg, prod_bridge_warpstream_conn_id, warpstream_sink_topic\n", "    )\n", "    push_flat_table_data(product_creates_deletes)\n", "\n", "    event_names = [PRODUCT_DELETE_EVENT_NAME, PRODUCT_CREATE_EVENT_NAME]\n", "    table_update_sql = generate_checkpoint_update_sql(event_names, new_checkpoint_ts)\n", "    print(table_update_sql)\n", "    pb.to_trino(table_update_sql, **checkpoint_table_kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "f2af0b04-7ea0-4b6a-abd9-cef69e2170ac", "metadata": {}, "outputs": [], "source": ["logging_df = pd.concat(\n", "    [product_creates_deletes_logging_df, pricing_updates_logging_df, inventory_update_logging_df],\n", "    ignore_index=True,\n", "    sort=False,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "90fd200f-9f96-4165-8fef-b15f9c5f2dd4", "metadata": {}, "outputs": [], "source": ["logging_df.info()"]}, {"cell_type": "code", "execution_count": null, "id": "790e1e38-41f1-45e5-a205-a0e8a5f910f0", "metadata": {}, "outputs": [], "source": ["push_flat_table_data(logging_df, invalid_event_logging_table_name)"]}, {"cell_type": "code", "execution_count": null, "id": "99c30138-e40e-483a-98cf-c852f8ad3de2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 5}