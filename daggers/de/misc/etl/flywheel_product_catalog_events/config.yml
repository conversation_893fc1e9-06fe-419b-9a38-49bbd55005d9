alert_configs:
  slack:
  - channel: bl-data-alerts-p1
dag_name: flywheel_product_catalog_events
dag_type: etl
escalation_priority: low
execution_timeout: 60
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    load_type: medium
    node_type: spot
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: de
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ
path: de/misc/etl/flywheel_product_catalog_events
paused: false
pool: de_pool
project_name: misc
schedule:
  end_date: '2025-11-03T00:00:00'
  interval: '0 * * * *'
  start_date: '2025-04-03T00:00:00'
schedule_type: fixed
sla: 121 minutes
support_files:
- columns_metadata.py
- utils.py
tags: []
template_name: notebook
version: 1
