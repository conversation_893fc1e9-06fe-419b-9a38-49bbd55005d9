alert_configs:
  slack:
  - channel: bl-data-alerts-p1
dag_name: pending_grn_events
dag_type: etl
escalation_priority: low
execution_timeout: 45
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    node_type: spot
    load_type: tiny
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow-dags
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow-de/plugins
  type: kubernetes
namespace: de
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ
path: de/misc/etl/pending_grn_events
paused: false
pool: de_pool
project_name: misc
schedule:
  end_date: '2026-09-10T00:00:00'
  interval: '*/15 * * * *'
  start_date: '2024-10-09T00:00:00'
schedule_type: fixed
sla: 121 minutes
support_files: []
tags: []
template_name: notebook
version: 1
