alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
  - channel: bl-data-alerts-p1
dag_name: automatic_brand_segments
dag_type: etl
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    load_type: very-high-mem
    node_type: spot
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
  type: kubernetes
namespace: de
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U04T965Q4DB
path: de/misc/etl/automatic_brand_segments
paused: true
project_name: misc
schedule:
  end_date: '2025-12-21T00:00:00'
  interval: 22 1 * * *
  start_date: '2023-08-20T00:00:00'
schedule_type: fixed
sla: 24 hours
support_files:
- feature_store_utils.py
- kafka_utils.py
tags: []
template_name: notebook
version: 1
