dag_name: oms_bifrost_3
dag_type: etl
escalation_priority: low

alert_configs:
  slack:
    - channel: bl-data-alerts-p1
  error_channel_mapping:
    terminated_with_errors: bl-data-spot-interruptions
    spot: bl-data-spot-interruptions

executor:
  config:
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    cpu:
      limit: 1
      request: 0.3
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    memory:
      limit: 500M
      request: 250M
    node_selectors:
      nodetype: airflow-srp-spot
    tolerations:
      - effect: NoSchedule
        key: service
        operator: Equal
        value: airflow-srp
    volume_mounts:
      - mountPath: /usr/local/airflow/plugins
        name: airflow-dags
        subPath: airflow-de/plugins
  type: celery
namespace: de
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ

path: de/replicate/etl/oms_bifrost_3
paused: false
project_name: replicate
schedule:
  interval: "8,23,38,53 * * * *"
  start_date: "2021-08-18T10:00:00"
  end_date: '2026-08-15T00:00:00'
schedule_type: fixed
sla: 60 minutes
task_concurrency: 8
poke_interval: 180
redshift_sla_seconds: 3600
emr_sensor: {}
nessie_dag_split: true
spark:
  aws_conn_id: aws_default
  mode: emr
  nessie_jar_version: 2.1.2
  cluster_reuse: true
  emr_version: 6.11.1
support_files: []
tables:
  - flow_id: oms_bifrost.oms_cart_event.v1
    topic_name: postgres.oms_bifrost.public.oms_cart_event
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: created_by_id
          type: int4
        - name: updated_by_id
          type: int4
        - name: extra
          type: varchar(max)
        - name: cart_id
          type: int8
        - name: event_type_key
          type: varchar(32)
        - name: reason_key
          type: varchar(64)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.oms_bifrost.public.oms_cart_event/
      schema: lake_oms_bifrost
      sortkey: []
      table: oms_cart_event
    source:
      database: oms_bifrost
      table: oms_cart_event
  - flow_id: oms_bifrost.oms_order.v3
    topic_name: postgres.oms_bifrost.public.oms_order
    nessie_jar_version: 2.1.2-high-resources
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: prefix
          type: varchar(max)
        - name: external_id
          type: varchar(16)
        - name: customer_id
          type: int4
        - name: slot_start
          type: int4
        - name: slot_end
          type: int4
        - name: slot_type
          type: varchar(max)
        - name: slot_properties
          type: varchar(max)
        - name: current_status
          type: varchar(50)
        - name: total_cost
          type: float8
        - name: delivery_cost
          type: float8
        - name: discount
          type: float8
        - name: net_cost
          type: float8
        - name: procurement_amount
          type: float8
        - name: type
          type: varchar(63)
        - name: direction
          type: varchar(31)
        - name: station
          type: varchar(64)
        - name: cart_id
          type: int8
        - name: merchant_id
          type: int4
        - name: shipment_id
          type: varchar(32)
        - name: wallet_amount
          type: float8
        - name: shipment_split_key
          type: varchar(128)
        - name: additional_charges_amount
          type: float8
        - name: additional_charges_data
          type: varchar(max)
        - name: meta
          type: varchar(max)
        - name: is_archived
          type: boolean
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.oms_bifrost.public.oms_order/
      schema: lake_oms_bifrost
      sortkey: [install_ts]
      table: oms_order
    source:
      database: oms_bifrost
      table: oms_order
  - flow_id: oms_bifrost.oms_suborder.v3
    topic_name: postgres.oms_bifrost.public.oms_suborder
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: prefix
          type: varchar(max)
        - name: external_id
          type: varchar(20)
        - name: current_status
          type: varchar(50)
        - name: slot_start
          type: int4
        - name: slot_end
          type: int4
        - name: slot_type
          type: varchar(max)
        - name: slot_properties
          type: varchar(max)
        - name: procurement_amount
          type: float8
        - name: backend_merchant_id
          type: int4
        - name: order_id
          type: int4
        - name: type
          type: varchar(63)
        - name: sort_criteria_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.oms_bifrost.public.oms_suborder/
      schema: lake_oms_bifrost
      sortkey: [install_ts]
      table: oms_suborder
    source:
      database: oms_bifrost
      table: oms_suborder
tags:
  - de
  - replicate
  - oms_bifrost_3
template_name: nessie
version: 1
