dag_name: kitchen_1
dag_type: etl
escalation_priority: low
alert_configs:
  slack:
    - channel: bl-data-alerts-p1
  error_channel_mapping:
    terminated_with_errors: bl-data-spot-interruptions
    user_request: bl-data-spot-interruptions
    spot: bl-data-spot-interruptions
executor:
  config:
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    cpu:
      limit: 1
      request: 0.3
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    memory:
      limit: 500M
      request: 250M
    node_selectors:
      nodetype: airflow-srp-spot
    tolerations:
      - effect: NoSchedule
        key: service
        operator: Equal
        value: airflow-srp
    volume_mounts:
      - mountPath: /usr/local/airflow/plugins
        name: airflow-dags
        subPath: airflow-de/plugins
  type: celery
namespace: de
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ
path: de/replicate/etl/kitchen_1
paused: false
project_name: replicate
schedule:
  end_date: "2026-06-08T00:00:00"
  interval: 0 * * * *
  start_date: "2025-06-13T00:00:00"
schedule_type: fixed
sla: 180 minutes
task_concurrency: 15
poke_interval: 180
redshift_sla_seconds: 10800
emr_sensor: {}
spark:
  aws_conn_id: aws_default
  mode: emr
  nessie_jar_version: 2.1.2
  cluster_reuse: true
  emr_version: 6.11.1
support_files: []
tables:
  - flow_id: kitchen.activity_logs.v1
    topic_name: postgres.kitchen.public.activity_logs
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: entity_type
          type: varchar(64)
        - name: entity_id
          type: varchar(255)
        - name: action
          type: varchar(64)
        - name: field_name
          type: varchar(64)
        - name: previous_value
          type: varchar(255)
        - name: new_value
          type: varchar(255)
        - name: description
          type: varchar(max)
        - name: source
          type: varchar(255)
        - name: created_by
          type: varchar(64)
        - name: created_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.kitchen.public.activity_logs/
      schema: lake_kitchen
      sortkey: []
      table: activity_logs
    source:
      database: kitchen
      table: activity_logs
  - flow_id: kitchen.attendance_summary.v1
    topic_name: postgres.kitchen.public.attendance_summary
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: employee_id
          type: varchar(32)
        - name: store_id
          type: int8
        - name: date
          type: date
        - name: first_in
          type: timestamp
        - name: last_out
          type: timestamp
        - name: total_active_time
          type: int8
        - name: overtime_duration
          type: int8
        - name: granted_overtime
          type: int8
        - name: is_overtime_granted
          type: boolean
        - name: attendance_status
          type: varchar(64)
        - name: is_active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(32)
        - name: updated_by
          type: varchar(32)
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.kitchen.public.attendance_summary/
      schema: lake_kitchen
      sortkey: []
      table: attendance_summary
    source:
      database: kitchen
      table: attendance_summary
  - flow_id: kitchen.attribute.v1
    topic_name: postgres.kitchen.public.attribute
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: name
          type: varchar(64)
        - name: display_name
          type: varchar(64)
        - name: data_type
          type: varchar(64)
        - name: is_active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(64)
        - name: updated_by
          type: varchar(64)
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.kitchen.public.attribute/
      schema: lake_kitchen
      sortkey: []
      table: attribute
    source:
      database: kitchen
      table: attribute
  - flow_id: kitchen.dish.v1
    topic_name: postgres.kitchen.public.dish
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: product_id
          type: int8
        - name: product_name
          type: varchar(255)
        - name: status
          type: varchar(40)
        - name: created_by
          type: varchar(8)
        - name: created_at
          type: timestamp
        - name: updated_by
          type: varchar(8)
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.kitchen.public.dish/
      schema: lake_kitchen
      sortkey: []
      table: dish
    source:
      database: kitchen
      table: dish
  - flow_id: kitchen.entity.v1
    topic_name: postgres.kitchen.public.entity
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: name
          type: varchar(255)
        - name: description
          type: varchar(255)
        - name: is_active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(30)
        - name: updated_by
          type: varchar(30)
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.kitchen.public.entity/
      schema: lake_kitchen
      sortkey: []
      table: entity
    source:
      database: kitchen
      table: entity
  - flow_id: kitchen.entity_attribute_mapping.v1
    topic_name: postgres.kitchen.public.entity_attribute_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: entity_id
          type: int8
        - name: entity_instance_id
          type: int8
        - name: attribute_id
          type: int8
        - name: value
          type: varchar(max)
        - name: is_active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(64)
        - name: updated_by
          type: varchar(64)
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.kitchen.public.entity_attribute_mapping/
      schema: lake_kitchen
      sortkey: []
      table: entity_attribute_mapping
    source:
      database: kitchen
      table: entity_attribute_mapping
  - flow_id: kitchen.entity_schedule.v1
    topic_name: postgres.kitchen.public.entity_schedule
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: entity_id
          type: int8
        - name: entity_instance_id
          type: int8
        - name: schedule_type
          type: varchar(64)
        - name: day_of_week
          type: int8
        - name: start_date
          type: date
        - name: end_date
          type: date
        - name: start_time
          type: time
        - name: end_time
          type: time
        - name: is_active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(30)
        - name: updated_by
          type: varchar(30)
        - name: updated_at
          type: timestamp
      copy_params:
        - ESCAPE
        - REMOVEQUOTES
        - DELIMITER ','
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.kitchen.public.entity_schedule/
      schema: lake_kitchen
      sortkey: []
      table: entity_schedule
    source:
      database: kitchen
      table: entity_schedule
  - flow_id: kitchen.foodware_mapping.v1
    topic_name: postgres.kitchen.public.foodware_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: product_id
          type: int8
        - name: reference_id
          type: int8
        - name: association_type
          type: varchar(255)
        - name: mapping_type
          type: varchar(255)
        - name: foodware_id
          type: int8
        - name: store_id
          type: int8
        - name: quantity
          type: int8
        - name: is_active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(30)
        - name: updated_by
          type: varchar(30)
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.kitchen.public.foodware_mapping/
      schema: lake_kitchen
      sortkey: []
      table: foodware_mapping
    source:
      database: kitchen
      table: foodware_mapping
  - flow_id: kitchen.ingredient.v1
    topic_name: postgres.kitchen.public.ingredient
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: sub_product_id
          type: int8
        - name: ingredient_product_id
          type: int8
        - name: station
          type: varchar(64)
        - name: name
          type: varchar(255)
        - name: mapping_type
          type: varchar(64)
        - name: process_type
          type: varchar(64)
        - name: quantity
          type: int4
        - name: prep_time
          type: int8
        - name: sop
          type: varchar(max)
        - name: status
          type: varchar(40)
        - name: created_by
          type: varchar(8)
        - name: created_at
          type: timestamp
        - name: updated_by
          type: varchar(8)
        - name: updated_at
          type: timestamp
        - name: station_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.kitchen.public.ingredient/
      schema: lake_kitchen
      sortkey: []
      table: ingredient
    source:
      database: kitchen
      table: ingredient
  - flow_id: kitchen.item_mapping.v1
    topic_name: postgres.kitchen.public.item_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: product_id
          type: int8
        - name: item_id
          type: int8
        - name: variant_id
          type: varchar(64)
        - name: created_by
          type: varchar(30)
        - name: created_at
          type: timestamp
        - name: updated_by
          type: varchar(30)
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.kitchen.public.item_mapping/
      schema: lake_kitchen
      sortkey: []
      table: item_mapping
    source:
      database: kitchen
      table: item_mapping
  - flow_id: kitchen.order_states.v1
    topic_name: postgres.kitchen.public.order_states
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: order_id
          type: int8
        - name: store_id
          type: int8
        - name: prev_order_id
          type: int8
        - name: status
          type: varchar(40)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.kitchen.public.order_states/
      schema: lake_kitchen
      sortkey: []
      table: order_states
    source:
      database: kitchen
      table: order_states
  - flow_id: kitchen.packaging_bags.v1
    topic_name: postgres.kitchen.public.packaging_bags
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: product_id
          type: int8
        - name: product_name
          type: varchar(255)
        - name: external_order_id
          type: int8
        - name: quantity
          type: int4
        - name: created_by
          type: varchar(30)
        - name: created_at
          type: timestamp
        - name: updated_by
          type: varchar(30)
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.kitchen.public.packaging_bags/
      schema: lake_kitchen
      sortkey: []
      table: packaging_bags
    source:
      database: kitchen
      table: packaging_bags
  - flow_id: kitchen.preparation_task_log.v1
    topic_name: postgres.kitchen.public.preparation_task_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: store_id
          type: int8
        - name: station
          type: varchar(64)
        - name: product_id
          type: int8
        - name: description
          type: varchar(255)
        - name: quantity
          type: int8
        - name: status
          type: varchar(64)
        - name: task_type
          type: varchar(64)
        - name: prepared_product_id
          type: int8
        - name: prepared_quantity
          type: int8
        - name: expected_prepared_quantity
          type: int8
        - name: expected_start_time
          type: timestamp
        - name: expected_end_time
          type: timestamp
        - name: start_time
          type: timestamp
        - name: end_time
          type: timestamp
        - name: preparation_time
          type: int8
        - name: location
          type: varchar(255)
        - name: initiated_by
          type: varchar(64)
        - name: picked_by
          type: varchar(64)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: station_id
          type: int8
        - name: expected_expired_at_time
          type: timestamp
        - name: projection_factor
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.kitchen.public.preparation_task_log/
      schema: lake_kitchen
      sortkey: []
      table: preparation_task_log
    source:
      database: kitchen
      table: preparation_task_log
  - flow_id: kitchen.product.v1
    topic_name: postgres.kitchen.public.product
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: product_id
          type: int8
        - name: name
          type: varchar(255)
        - name: product_type
          type: varchar(255)
        - name: is_active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(30)
        - name: updated_by
          type: varchar(30)
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.kitchen.public.product/
      schema: lake_kitchen
      sortkey: []
      table: product
    source:
      database: kitchen
      table: product
  - flow_id: kitchen.product_mapping.v1
    topic_name: postgres.kitchen.public.product_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: parent_product_id
          type: int8
        - name: child_product_id
          type: int8
        - name: association_type
          type: varchar(255)
        - name: mapping_type
          type: varchar(255)
        - name: quantity
          type: int8
        - name: is_active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(30)
        - name: updated_by
          type: varchar(30)
        - name: updated_at
          type: timestamp
        - name: is_condiment
          type: boolean
        - name: store_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.kitchen.public.product_mapping/
      schema: lake_kitchen
      sortkey: []
      table: product_mapping
    source:
      database: kitchen
      table: product_mapping
  - flow_id: kitchen.product_transformation.v1
    topic_name: postgres.kitchen.public.product_transformation
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: store_id
          type: int8
        - name: product_id
          type: int8
        - name: transformed_product_id
          type: int8
        - name: transformation_type
          type: varchar(40)
        - name: prep_time
          type: int8
        - name: station
          type: varchar(64)
        - name: name
          type: varchar(255)
        - name: conversion_factor
          type: numeric(10, 2)
        - name: quantity
          type: int4
        - name: sop
          type: varchar(max)
        - name: status
          type: varchar(40)
        - name: created_by
          type: varchar(30)
        - name: created_at
          type: timestamp
        - name: updated_by
          type: varchar(30)
        - name: updated_at
          type: timestamp
        - name: station_id
          type: int8
        - name: parallel_capacity
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.kitchen.public.product_transformation/
      schema: lake_kitchen
      sortkey: []
      table: product_transformation
    source:
      database: kitchen
      table: product_transformation
  - flow_id: kitchen.return_order.v1
    topic_name: postgres.kitchen.public.return_order
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: external_order_id
          type: int8
        - name: rider_order_id
          type: int8
        - name: store_id
          type: int8
        - name: status
          type: varchar(64)
        - name: type
          type: varchar(64)
        - name: reason
          type: varchar(255)
        - name: partner_name
          type: varchar(64)
        - name: partner_phone_number
          type: varchar(64)
        - name: partner_employee_id
          type: varchar(64)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.kitchen.public.return_order/
      schema: lake_kitchen
      sortkey: []
      table: return_order
    source:
      database: kitchen
      table: return_order
  - flow_id: kitchen.roster_planner.v1
    topic_name: postgres.kitchen.public.roster_planner
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: employee_id
          type: varchar(32)
        - name: store_id
          type: int8
        - name: entity_id
          type: int8
        - name: entity_instance_id
          type: int8
        - name: date
          type: date
        - name: start_time
          type: timestamp
        - name: end_time
          type: timestamp
        - name: on_leave
          type: boolean
        - name: is_active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(32)
        - name: updated_by
          type: varchar(32)
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.kitchen.public.roster_planner/
      schema: lake_kitchen
      sortkey: []
      table: roster_planner
    source:
      database: kitchen
      table: roster_planner
  - flow_id: kitchen.shift_record.v1
    topic_name: postgres.kitchen.public.shift_record
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: employee_id
          type: varchar(32)
        - name: store_id
          type: int8
        - name: date
          type: date
        - name: shift_start_time
          type: timestamp
        - name: shift_end_time
          type: timestamp
        - name: shift_end_source
          type: varchar(64)
        - name: attendance_id
          type: int8
        - name: is_active
          type: int4
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: meta
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.kitchen.public.shift_record/
      schema: lake_kitchen
      sortkey: []
      table: shift_record
    source:
      database: kitchen
      table: shift_record
  - flow_id: kitchen.station.v1
    topic_name: postgres.kitchen.public.station
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: station_name
          type: varchar(64)
        - name: is_deleted
          type: int4
        - name: created_by
          type: varchar(30)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: varchar(30)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.kitchen.public.station/
      schema: lake_kitchen
      sortkey: []
      table: station
    source:
      database: kitchen
      table: station
  - flow_id: kitchen.station_product_mapping.v1
    topic_name: postgres.kitchen.public.station_product_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: store_id
          type: int8
        - name: mapping_id
          type: int8
        - name: station_id
          type: int8
        - name: sop
          type: varchar(max)
        - name: prep_time
          type: int8
        - name: is_active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(30)
        - name: updated_by
          type: varchar(30)
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.kitchen.public.station_product_mapping/
      schema: lake_kitchen
      sortkey: []
      table: station_product_mapping
    source:
      database: kitchen
      table: station_product_mapping
  - flow_id: kitchen.store_product_mapping.v1
    topic_name: postgres.kitchen.public.store_product_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: store_id
          type: int8
        - name: product_id
          type: int8
        - name: is_active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(30)
        - name: updated_by
          type: varchar(30)
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.kitchen.public.store_product_mapping/
      schema: lake_kitchen
      sortkey: []
      table: store_product_mapping
    source:
      database: kitchen
      table: store_product_mapping
  - flow_id: kitchen.store_station.v1
    topic_name: postgres.kitchen.public.store_station
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: store_id
          type: int4
        - name: station_type
          type: varchar(64)
        - name: status
          type: varchar(64)
        - name: reason
          type: varchar(max)
        - name: created_by
          type: varchar(30)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: varchar(30)
        - name: station_capacity
          type: int8
        - name: station_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.kitchen.public.store_station/
      schema: lake_kitchen
      sortkey: []
      table: store_station
    source:
      database: kitchen
      table: store_station
  - flow_id: kitchen.subdish.v1
    topic_name: postgres.kitchen.public.subdish
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: parent_product_id
          type: int8
        - name: sub_product_id
          type: int8
        - name: name
          type: varchar(255)
        - name: mapping_type
          type: varchar(64)
        - name: quantity
          type: int4
        - name: station
          type: varchar(64)
        - name: prep_time
          type: int8
        - name: sop
          type: varchar(max)
        - name: status
          type: varchar(40)
        - name: created_by
          type: varchar(8)
        - name: created_at
          type: timestamp
        - name: updated_by
          type: varchar(8)
        - name: updated_at
          type: timestamp
        - name: station_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.kitchen.public.subdish/
      schema: lake_kitchen
      sortkey: []
      table: subdish
    source:
      database: kitchen
      table: subdish
  - flow_id: kitchen.user_entity_mapping.v1
    topic_name: postgres.kitchen.public.user_entity_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: employee_id
          type: varchar(32)
        - name: entity_id
          type: int8
        - name: entity_instance_id
          type: int8
        - name: is_active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(32)
        - name: updated_by
          type: varchar(32)
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.kitchen.public.user_entity_mapping/
      schema: lake_kitchen
      sortkey: []
      table: user_entity_mapping
    source:
      database: kitchen
      table: user_entity_mapping
tags:
  - de
  - replicate
  - kitchen_1
template_name: nessie
version: 1
