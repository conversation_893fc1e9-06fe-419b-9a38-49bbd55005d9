dag_name: pricing_v3_1
dag_type: etl
escalation_priority: low

alert_configs:
  slack:
    - channel: bl-data-alerts-p1
  error_channel_mapping:
    terminated_with_errors: bl-data-spot-interruptions
    spot: bl-data-spot-interruptions

executor:
  config:
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    cpu:
      limit: 1
      request: 0.3
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    memory:
      limit: 500M
      request: 250M
    node_selectors:
      nodetype: airflow-srp-spot
    tolerations:
      - effect: NoSchedule
        key: service
        operator: Equal
        value: airflow-srp
    volume_mounts:
      - mountPath: /usr/local/airflow/plugins
        name: airflow-dags
        subPath: airflow-de/plugins
  type: celery
namespace: de
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ

path: de/replicate/etl/pricing_v3_1
paused: false
project_name: replicate
schedule:
  interval: 18,48 * * * *
  start_date: "2021-08-18T10:00:00"
  end_date: '2026-08-15T00:00:00'
schedule_type: fixed
sla: 120 minutes
task_concurrency: 15
poke_interval: 180
redshift_sla_seconds: 7200
emr_sensor: {}
nessie_dag_split: true
spark:
  aws_conn_id: aws_default
  mode: emr
  nessie_jar_version: 2.1.2
  cluster_reuse: true
  emr_version: 6.11.1
support_files: []
tables:
  - flow_id: pricing_v3.approval_domain_rulesapproval.v1
    topic_name: postgres.pricing_v3.public.approval_domain_rulesapproval
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: retail_item_id
          type: int4
        - name: config
          type: varchar(max)
        - name: rule_type
          type: varchar(100)
        - name: start_ts
          type: timestamp
        - name: end_ts
          type: timestamp
        - name: approval_status
          type: varchar(32)
        - name: approved_by
          type: varchar(max)
        - name: comments
          type: varchar(max)
        - name: row_number
          type: int4
        - name: entity_id
          type: int4
        - name: entity_type_id
          type: int4
        - name: product_id
          type: int4
        - name: sheet_id
          type: int4
        - name: least_accepted_approval
          type: varchar(32)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.approval_domain_rulesapproval/
      schema: lake_pricing_v3
      sortkey: []
      table: approval_domain_rulesapproval
    source:
      database: pricing_v3
      table: approval_domain_rulesapproval
  - flow_id: pricing_v3.approval_domain_sheetfile.v1
    topic_name: postgres.pricing_v3.public.approval_domain_sheetfile
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: file
          type: varchar(100)
        - name: rule_type
          type: varchar(100)
        - name: state
          type: varchar(50)
        - name: error_file
          type: varchar(100)
        - name: user_email
          type: varchar(max)
        - name: upload_source
          type: varchar(32)
        - name: meta
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.approval_domain_sheetfile/
      schema: lake_pricing_v3
      sortkey: []
      table: approval_domain_sheetfile
    source:
      database: pricing_v3
      table: approval_domain_sheetfile
  - flow_id: pricing_v3.attribute_management_allocationgrouprulemap.v1
    topic_name: postgres.pricing_v3.public.attribute_management_allocationgrouprulemap
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: group_id
          type: int8
        - name: object_id
          type: int4
        - name: content_type_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.attribute_management_allocationgrouprulemap/
      schema: lake_pricing_v3
      sortkey: []
      table: attribute_management_allocationgrouprulemap
    source:
      database: pricing_v3
      table: attribute_management_allocationgrouprulemap
  - flow_id: pricing_v3.attribute_management_attributemasterrule.v1
    topic_name: postgres.pricing_v3.public.attribute_management_attributemasterrule
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: created_by_id
          type: int4
        - name: updated_by_id
          type: int4
        - name: brandfund_config
          type: varchar(max)
        - name: attribute_type
          type: varchar(32)
        - name: start_ts
          type: timestamp
        - name: end_ts
          type: timestamp
        - name: retail_item_id
          type: int4
        - name: status
          type: varchar(50)
        - name: sheet_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.attribute_management_attributemasterrule/
      schema: lake_pricing_v3
      sortkey: []
      table: attribute_management_attributemasterrule
    source:
      database: pricing_v3
      table: attribute_management_attributemasterrule
  - flow_id: pricing_v3.attribute_management_brandsfundallocationgroup.v1
    topic_name: postgres.pricing_v3.public.attribute_management_brandsfundallocationgroup
    redshift_replication: false
    sink:
      column_dtypes:
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: id
          type: int8
        - name: object_id
          type: int4
        - name: group_number
          type: int4
        - name: brand_fund_allocation_amount
          type: int4
        - name: sales_allocation_qty
          type: int4
        - name: current_sales
          type: float8
        - name: current_bf_utilised
          type: float8
        - name: status
          type: varchar(32)
        - name: content_type_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.attribute_management_brandsfundallocationgroup/
      schema: lake_pricing_v3
      sortkey: []
      table: attribute_management_brandsfundallocationgroup
    source:
      database: pricing_v3
      table: attribute_management_brandsfundallocationgroup
  - flow_id: pricing_v3.attribute_management_brandsfundapproval.v1
    topic_name: postgres.pricing_v3.public.attribute_management_brandsfundapproval
    nessie_jar_version: 2.1.2-high-resources
    redshift_replication: false
    sink:
      column_dtypes:
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: id
          type: int4
        - name: retail_item_id
          type: int4
        - name: frontend_id
          type: int4
        - name: start_ts
          type: timestamp
        - name: end_ts
          type: timestamp
        - name: brandfund_value
          type: float8
        - name: funding_type
          type: varchar(32)
        - name: distributor_id
          type: varchar(255)
        - name: row_number
          type: int4
        - name: approval_status
          type: varchar(32)
        - name: approved_by
          type: varchar(64)
        - name: comments
          type: varchar(max)
        - name: city_id
          type: int4
        - name: product_id
          type: int4
        - name: sheet_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.attribute_management_brandsfundapproval/
      schema: lake_pricing_v3
      sortkey: []
      table: attribute_management_brandsfundapproval
    source:
      database: pricing_v3
      table: attribute_management_brandsfundapproval
  - flow_id: pricing_v3.attribute_management_brandsfundcityapproval.v1
    topic_name: postgres.pricing_v3.public.attribute_management_brandsfundcityapproval
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: retail_item_id
          type: int4
        - name: start_ts
          type: timestamp
        - name: end_ts
          type: timestamp
        - name: brandfund_value
          type: float8
        - name: funding_type
          type: varchar(32)
        - name: row_number
          type: int4
        - name: approval_status
          type: varchar(32)
        - name: approved_by
          type: varchar(64)
        - name: comments
          type: varchar(max)
        - name: city_id
          type: int4
        - name: product_id
          type: int4
        - name: sheet_id
          type: int4
        - name: is_pan_india
          type: boolean
        - name: meta
          type: varchar(max)
        - name: superstore_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.attribute_management_brandsfundcityapproval/
      schema: lake_pricing_v3
      sortkey: []
      table: attribute_management_brandsfundcityapproval
    source:
      database: pricing_v3
      table: attribute_management_brandsfundcityapproval
  - flow_id: pricing_v3.attribute_management_brandsfundsummary.v1
    topic_name: postgres.pricing_v3.public.attribute_management_brandsfundsummary
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: date
          type: date
        - name: city
          type: varchar(255)
        - name: product_id
          type: int4
        - name: item_id
          type: int4
        - name: multiplier
          type: int4
        - name: offer_type
          type: varchar(255)
        - name: manufacturer_id
          type: int4
        - name: item_mrp
          type: float8
        - name: item_selling_price
          type: float8
        - name: brandfund_value
          type: float8
        - name: total_mrp
          type: float8
        - name: total_selling_price
          type: float8
        - name: qty_sold
          type: int4
        - name: total_brand_fund
          type: float8
        - name: product_name
          type: varchar(255)
        - name: l0_category_name
          type: varchar(255)
        - name: l0_category_id
          type: int4
        - name: l1_category_name
          type: varchar(255)
        - name: l2_category_name
          type: varchar(255)
        - name: brand_name
          type: varchar(255)
        - name: brand_id
          type: int4
        - name: p_type
          type: varchar(255)
        - name: manufacturer
          type: varchar(255)
        - name: seller_legal_name
          type: varchar(255)
        - name: seller_cin
          type: varchar(255)
        - name: qty_type
          type: varchar(255)
        - name: tax_type
          type: varchar(255)
        - name: grn_quantity
          type: int4
        - name: final_claimable_amount
          type: float8
        - name: system_sheet_id
          type: int4
        - name: input_bf_value
          type: float8
        - name: input_funding_type
          type: varchar(255)
        - name: is_active
          type: boolean
        - name: brandfund_absolute_input_value
          type: float8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.attribute_management_brandsfundsummary/
      schema: lake_pricing_v3
      sortkey: []
      table: attribute_management_brandsfundsummary
    source:
      database: pricing_v3
      table: attribute_management_brandsfundsummary
  - flow_id: pricing_v3.attribute_management_brandssheetfile.v1
    topic_name: postgres.pricing_v3.public.attribute_management_brandssheetfile
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: file
          type: varchar(100)
        - name: state
          type: varchar(50)
        - name: error_file
          type: varchar(100)
        - name: upload_source
          type: varchar(32)
        - name: user_email
          type: varchar(254)
        - name: manufacturer_id_new
          type: int4
        - name: manufacturer_id
          type: varchar(max)
        - name: attribute_type
          type: varchar(64)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.attribute_management_brandssheetfile/
      schema: lake_pricing_v3
      sortkey: []
      table: attribute_management_brandssheetfile
    source:
      database: pricing_v3
      table: attribute_management_brandssheetfile
  - flow_id: pricing_v3.attribute_management_itembrandfundagendaattribute.v1
    topic_name: postgres.pricing_v3.public.attribute_management_itembrandfundagendaattribute
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: created_by_id
          type: int4
        - name: updated_by_id
          type: int4
        - name: start_ts
          type: timestamp
        - name: end_ts
          type: timestamp
        - name: retail_item_id
          type: int4
        - name: superstore_id
          type: int4
        - name: brand_fund
          type: float8
        - name: funding_type
          type: varchar(32)
        - name: master_attribute_id
          type: int4
        - name: sheet_id
          type: int4
        - name: sheet_row_city
          type: varchar(255)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.attribute_management_itembrandfundagendaattribute/
      schema: lake_pricing_v3
      sortkey: []
      table: attribute_management_itembrandfundagendaattribute
    source:
      database: pricing_v3
      table: attribute_management_itembrandfundagendaattribute
  - flow_id: pricing_v3.attribute_management_kvibrandfundagendaattribute.v3
    topic_name: postgres.pricing_v3.public.attribute_management_kvibrandfundagendaattribute
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: created_by_id
          type: int4
        - name: updated_by_id
          type: int4
        - name: start_ts
          type: timestamp
        - name: end_ts
          type: timestamp
        - name: retail_item_id
          type: int4
        - name: superstore_id
          type: int4
        - name: brand_fund
          type: float8
        - name: funding_type
          type: varchar(32)
        - name: master_attribute_id
          type: int4
        - name: sheet_id
          type: int4
        - name: sheet_row_city
          type: varchar(255)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.attribute_management_kvibrandfundagendaattribute/
      schema: lake_pricing_v3
      sortkey: []
      table: attribute_management_kvibrandfundagendaattribute
    source:
      database: pricing_v3
      table: attribute_management_kvibrandfundagendaattribute
  - flow_id: pricing_v3.attribute_management_manufactureremail.v1
    topic_name: postgres.pricing_v3.public.attribute_management_manufactureremail
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: manufacturer_id
          type: int4
        - name: manufacturer_name
          type: varchar(max)
        - name: manufacturer_emails
          type: varchar(max)
        - name: is_active
          type: boolean
        - name: manufacturer_type
          type: varchar(32)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.attribute_management_manufactureremail/
      schema: lake_pricing_v3
      sortkey: []
      table: attribute_management_manufactureremail
    source:
      database: pricing_v3
      table: attribute_management_manufactureremail
  - flow_id: pricing_v3.bundles_and_combos_approval.v1
    topic_name: postgres.pricing_v3.public.bundles_and_combos_approval
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: frontend_id
          type: int4
        - name: start_ts
          type: timestamp
        - name: end_ts
          type: timestamp
        - name: brandfund_value
          type: float8
        - name: funding_type
          type: varchar(32)
        - name: approval_status
          type: varchar(32)
        - name: distributor_cin
          type: varchar(255)
        - name: row_number
          type: int4
        - name: approved_by
          type: varchar(64)
        - name: comments
          type: varchar(max)
        - name: config
          type: varchar(max)
        - name: city_id
          type: int4
        - name: product_id
          type: int4
        - name: sheet_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.bundles_and_combos_approval/
      schema: lake_pricing_v3
      sortkey: []
      table: bundles_and_combos_approval
    source:
      database: pricing_v3
      table: bundles_and_combos_approval
  - flow_id: pricing_v3.bundles_and_combos_city_approval.v1
    topic_name: postgres.pricing_v3.public.bundles_and_combos_city_approval
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: start_ts
          type: timestamp
        - name: end_ts
          type: timestamp
        - name: brandfund_value
          type: float8
        - name: funding_type
          type: varchar(32)
        - name: approval_status
          type: varchar(32)
        - name: row_number
          type: int4
        - name: approved_by
          type: varchar(64)
        - name: comments
          type: varchar(max)
        - name: config
          type: varchar(max)
        - name: city_id
          type: int4
        - name: product_id
          type: int4
        - name: sheet_id
          type: int4
        - name: is_pan_india
          type: boolean
        - name: meta
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.bundles_and_combos_city_approval/
      schema: lake_pricing_v3
      sortkey: []
      table: bundles_and_combos_city_approval
    source:
      database: pricing_v3
      table: bundles_and_combos_city_approval
  - flow_id: pricing_v3.bundles_and_combos_domain_bundlebrandfundsheetfile.v1
    topic_name: postgres.pricing_v3.public.bundles_and_combos_domain_bundlebrandfundsheetfile
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: file
          type: varchar(100)
        - name: bundle_type
          type: varchar(32)
        - name: item_multiplier_count
          type: int4
        - name: state
          type: varchar(50)
        - name: error_file
          type: varchar(100)
        - name: meta
          type: varchar(max)
        - name: user_email
          type: varchar(254)
        - name: upload_source
          type: varchar(32)
        - name: manufacturer_id_new
          type: int4
        - name: manufacturer_id
          type: varchar(max)
        - name: attribute_type
          type: varchar(64)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.bundles_and_combos_domain_bundlebrandfundsheetfile/
      schema: lake_pricing_v3
      sortkey: []
      table: bundles_and_combos_domain_bundlebrandfundsheetfile
    source:
      database: pricing_v3
      table: bundles_and_combos_domain_bundlebrandfundsheetfile
  - flow_id: pricing_v3.catalog_domain_kvi_item.v1
    topic_name: postgres.pricing_v3.public.catalog_domain_kvi_item
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: retail_item_id
          type: int4
        - name: cms_city_id
          type: int4
        - name: active
          type: boolean
        - name: status
          type: varchar(50)
        - name: cms_store_id
          type: int4
        - name: source
          type: varchar(32)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.catalog_domain_kvi_item/
      schema: lake_pricing_v3
      sortkey: []
      table: catalog_domain_kvi_item
    source:
      database: pricing_v3
      table: catalog_domain_kvi_item
  - flow_id: pricing_v3.catalog_domain_kvitagging.v1
    topic_name: postgres.pricing_v3.public.catalog_domain_kvitagging
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: created_by_id
          type: int4
        - name: updated_by_id
          type: int4
        - name: retail_item_id
          type: int4
        - name: kvitag_config
          type: varchar(max)
        - name: start_ts
          type: timestamp
        - name: end_ts
          type: timestamp
        - name: status
          type: varchar(50)
        - name: sheet_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.catalog_domain_kvitagging/
      schema: lake_pricing_v3
      sortkey: []
      table: catalog_domain_kvitagging
    source:
      database: pricing_v3
      table: catalog_domain_kvitagging
  - flow_id: pricing_v3.competitor_domain_competitorconfiguration.v1
    topic_name: postgres.pricing_v3.public.competitor_domain_competitorconfiguration
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: cms_product_id
          type: int4
        - name: competitor_name
          type: varchar(255)
        - name: expiry_days
          type: int4
        - name: is_active
          type: boolean
        - name: delta
          type: float8
        - name: city_id
          type: int4
        - name: delta_type
          type: varchar(32)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.competitor_domain_competitorconfiguration/
      schema: lake_pricing_v3
      sortkey: []
      table: competitor_domain_competitorconfiguration
    source:
      database: pricing_v3
      table: competitor_domain_competitorconfiguration
  - flow_id: pricing_v3.competitor_domain_competitormaster.v1
    topic_name: postgres.pricing_v3.public.competitor_domain_competitormaster
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: created_by_id
          type: int4
        - name: updated_by_id
          type: int4
        - name: cms_product_id
          type: int4
        - name: cms_city_id
          type: int4
        - name: competitor_name
          type: varchar(32)
        - name: competitor_mrp
          type: float8
        - name: competitor_sp
          type: float8
        - name: multiplier
          type: float8
        - name: end_ts
          type: timestamp
        - name: sheet_id
          type: int4
        - name: start_ts
          type: timestamp
        - name: status
          type: varchar(50)
        - name: superstore_config
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.competitor_domain_competitormaster/
      schema: lake_pricing_v3
      sortkey: []
      table: competitor_domain_competitormaster
    source:
      database: pricing_v3
      table: competitor_domain_competitormaster
  - flow_id: pricing_v3.cron_tasks.v1
    topic_name: postgres.pricing_v3.public.cron_tasks
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: payload
          type: varchar(max)
        - name: state
          type: varchar(50)
        - name: task_type
          type: varchar(255)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.cron_tasks/
      schema: lake_pricing_v3
      sortkey: []
      table: cron_tasks
    source:
      database: pricing_v3
      table: cron_tasks
  - flow_id: pricing_v3.event_management_domain_pricingeventerrors.v2
    topic_name: postgres.pricing_v3.public.event_management_domain_pricingeventerrors
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: retail_item_id
          type: int4
        - name: superstore
          type: int4
        - name: error
          type: varchar(max)
        - name: source
          type: varchar(512)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.event_management_domain_pricingeventerrors/
      schema: lake_pricing_v3
      sortkey: []
      table: event_management_domain_pricingeventerrors
    source:
      database: pricing_v3
      table: event_management_domain_pricingeventerrors
  - flow_id: pricing_v3.pricing_domain_city.v3
    topic_name: postgres.pricing_v3.public.pricing_domain_city
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: created_by_id
          type: int4
        - name: updated_by_id
          type: int4
        - name: retail_city_id
          type: int4
        - name: city_name
          type: varchar(32)
        - name: active
          type: boolean
        - name: is_frontend
          type: boolean
        - name: user_email
          type: varchar(255)
        - name: weight
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.pricing_domain_city/
      schema: lake_pricing_v3
      sortkey: []
      table: pricing_domain_city
    source:
      database: pricing_v3
      table: pricing_domain_city
  - flow_id: pricing_v3.pricing_domain_citycluster.v1
    topic_name: postgres.pricing_v3.public.pricing_domain_citycluster
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: created_by_id
          type: int4
        - name: updated_by_id
          type: int4
        - name: cluster_class
          type: varchar(32)
        - name: city_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.pricing_domain_citycluster/
      schema: lake_pricing_v3
      sortkey: []
      table: pricing_domain_citycluster
    source:
      database: pricing_v3
      table: pricing_domain_citycluster
  - flow_id: pricing_v3.pricing_domain_cmscity.v3
    topic_name: postgres.pricing_v3.public.pricing_domain_cmscity
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: created_by_id
          type: int4
        - name: updated_by_id
          type: int4
        - name: cms_city_id
          type: int4
        - name: city_name
          type: varchar(32)
        - name: active
          type: boolean
        - name: city_id
          type: int4
        - name: sbc_active
          type: boolean
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.pricing_domain_cmscity/
      schema: lake_pricing_v3
      sortkey: []
      table: pricing_domain_cmscity
    source:
      database: pricing_v3
      table: pricing_domain_cmscity
  - flow_id: pricing_v3.pricing_domain_outlet.v3
    topic_name: postgres.pricing_v3.public.pricing_domain_outlet
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: created_by_id
          type: int4
        - name: updated_by_id
          type: int4
        - name: retail_outlet_id
          type: int4
        - name: active
          type: boolean
        - name: city_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.pricing_domain_outlet/
      schema: lake_pricing_v3
      sortkey: []
      table: pricing_domain_outlet
    source:
      database: pricing_v3
      table: pricing_domain_outlet
  - flow_id: pricing_v3.pricing_domain_pricerecommendation.v1
    topic_name: postgres.pricing_v3.public.pricing_domain_pricerecommendation
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: created_by_id
          type: int4
        - name: updated_by_id
          type: int4
        - name: cms_product_id
          type: int4
        - name: retail_outlet_id
          type: int4
        - name: backend_id
          type: int4
        - name: frontend_id
          type: int4
        - name: retail_item_id
          type: int4
        - name: mrp
          type: float8
        - name: price
          type: float8
        - name: distributor_price
          type: float8
        - name: sbc_price
          type: float8
        - name: is_kvi
          type: boolean
        - name: state
          type: varchar(50)
        - name: error
          type: varchar(max)
        - name: force_update
          type: boolean
        - name: wlp
          type: float8
        - name: distributor_id
          type: int4
        - name: approval_status
          type: boolean
        - name: redis_price_flag
          type: boolean
        - name: item_brandfund_id
          type: int4
        - name: kvi_brandfund_id
          type: int4
        - name: latest_log_id
          type: int8
        - name: least_competitor_id
          type: int4
        - name: rule_id
          type: int4
        - name: sbc_rule_id
          type: int4
        - name: brand_id
          type: int4
        - name: city_name
          type: varchar(32)
        - name: group_id
          type: int4
        - name: l2_category
          type: int4
        - name: mapping_id
          type: int4
        - name: p_type
          type: int4
        - name: positive_variants
          type: varchar(max)
        - name: pricing_comment
          type: varchar(32)
        - name: sbc_enabled
          type: boolean
        - name: distributor_email
          type: varchar(255)
        - name: product_name
          type: varchar(255)
        - name: brandfund_value
          type: int4
        - name: is_liquidation_on
          type: boolean
        - name: is_distributor_price
          type: boolean
        - name: is_deep_discount
          type: boolean
        - name: max_quantity_allowed_in_deep_discount
          type: int4
        - name: distributor_cin
          type: varchar(32)
        - name: promotion_tags_list
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.pricing_domain_pricerecommendation/
      schema: lake_pricing_v3
      sortkey: []
      table: pricing_domain_pricerecommendation
    source:
      database: pricing_v3
      table: pricing_domain_pricerecommendation
  - flow_id: pricing_v3.pricing_domain_prices.v3
    topic_name: postgres.pricing_v3.public.pricing_domain_prices
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: created_by_id
          type: int4
        - name: updated_by_id
          type: int4
        - name: cms_product_id
          type: int4
        - name: frontend_id
          type: int4
        - name: mrp
          type: int4
        - name: price
          type: int4
        - name: sbc_price
          type: int4
        - name: latest_log_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.pricing_domain_prices/
      schema: lake_pricing_v3
      sortkey: []
      table: pricing_domain_prices
    source:
      database: pricing_v3
      table: pricing_domain_prices
  - flow_id: pricing_v3.pricing_domain_product.v4
    topic_name: postgres.pricing_v3.public.pricing_domain_product
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: created_by_id
          type: int4
        - name: updated_by_id
          type: int4
        - name: cms_product_id
          type: int4
        - name: retail_outlet_id
          type: int4
        - name: retail_item_id
          type: int4
        - name: mrp
          type: float8
        - name: price
          type: float8
        - name: item_brandfund_id
          type: int4
        - name: rule_id
          type: int4
        - name: is_kvi
          type: boolean
        - name: kvi_brandfund_id
          type: int4
        - name: least_competitor_id
          type: int4
        - name: sbc_price
          type: float8
        - name: sbc_rule_id
          type: int4
        - name: error
          type: varchar(max)
        - name: latest_log_id
          type: int8
        - name: state
          type: varchar(50)
        - name: backend_id
          type: int4
        - name: frontend_id
          type: int4
        - name: wlp
          type: float8
        - name: force_update
          type: boolean
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.pricing_domain_product/
      schema: lake_pricing_v3
      sortkey: []
      table: pricing_domain_product
    source:
      database: pricing_v3
      table: pricing_domain_product
  - flow_id: pricing_v3.pricing_domain_productchangelog.v3
    topic_name: postgres.pricing_v3.public.pricing_domain_productchangelog
    nessie_jar_version: 2.1.2-high-resources
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: retail_outlet_id
          type: int4
        - name: retail_item_id
          type: int4
        - name: cms_store_id
          type: int4
        - name: cms_product_id
          type: int4
        - name: old_price
          type: float8
        - name: new_price
          type: float8
        - name: old_mrp
          type: float8
        - name: new_mrp
          type: float8
        - name: source
          type: varchar(512)
        - name: rule_type
          type: varchar(32)
        - name: evaluation_log
          type: varchar(max)
        - name: product_store_id
          type: int4
        - name: rule_id
          type: int4
        - name: new_sbc_price
          type: float8
        - name: old_sbc_price
          type: float8
        - name: sbc_evaluation_log
          type: varchar(max)
        - name: sbc_rule_id
          type: int4
        - name: sbc_rule_type
          type: varchar(32)
        - name: product_id
          type: int4
        - name: frontend_id
          type: int4
        - name: new_wlp
          type: float8
        - name: old_wlp
          type: float8
        - name: recommended_product_id
          type: int4
        - name: distributor_id
          type: int4
        - name: product_name
          type: varchar(255)
        - name: is_liquidation_on
          type: boolean
        - name: price_source
          type: varchar(255)
        - name: brand_fund_value
          type: float8
        - name: recommended_price
          type: float8
        - name: is_deep_discount
          type: boolean
        - name: max_quantity_allowed_in_deep_discount
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.pricing_domain_productchangelog/
      schema: lake_pricing_v3
      sortkey: []
      table: pricing_domain_productchangelog
    source:
      database: pricing_v3
      table: pricing_domain_productchangelog
  - flow_id: pricing_v3.pricing_domain_superstore.v3
    topic_name: postgres.pricing_v3.public.pricing_domain_superstore
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: created_by_id
          type: int4
        - name: updated_by_id
          type: int4
        - name: superstore_id
          type: int4
        - name: active
          type: boolean
        - name: city_id
          type: int4
        - name: sbc_active
          type: boolean
        - name: b2b
          type: boolean
        - name: drop_shipment
          type: boolean
        - name: franchise
          type: boolean
        - name: is_deprecated
          type: boolean
        - name: is_frontend
          type: boolean
        - name: express
          type: boolean
        - name: offline_store_id
          type: int4
        - name: is_offline_store
          type: boolean
        - name: outlet_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.pricing_domain_superstore/
      schema: lake_pricing_v3
      sortkey: []
      table: pricing_domain_superstore
    source:
      database: pricing_v3
      table: pricing_domain_superstore
  - flow_id: pricing_v3.replication_domain_itemcityreplicationchangelog.v1
    topic_name: postgres.pricing_v3.public.replication_domain_itemcityreplicationchangelog
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: item_id
          type: int4
        - name: city_id
          type: int4
        - name: source_city_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.replication_domain_itemcityreplicationchangelog/
      schema: lake_pricing_v3
      sortkey: []
      table: replication_domain_itemcityreplicationchangelog
    source:
      database: pricing_v3
      table: replication_domain_itemcityreplicationchangelog
  - flow_id: pricing_v3.rule_management_agendarulekvialignedrm.v1
    topic_name: postgres.pricing_v3.public.rule_management_agendarulekvialignedrm
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: created_by_id
          type: int4
        - name: updated_by_id
          type: int4
        - name: start_ts
          type: timestamp
        - name: end_ts
          type: timestamp
        - name: item_id
          type: int4
        - name: superstore
          type: int4
        - name: master_rule_id
          type: int4
        - name: sheet_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.rule_management_agendarulekvialignedrm/
      schema: lake_pricing_v3
      sortkey: []
      table: rule_management_agendarulekvialignedrm
    source:
      database: pricing_v3
      table: rule_management_agendarulekvialignedrm
  - flow_id: pricing_v3.rule_management_agendarulekviinternal.v1
    topic_name: postgres.pricing_v3.public.rule_management_agendarulekviinternal
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: created_by_id
          type: int4
        - name: updated_by_id
          type: int4
        - name: start_ts
          type: timestamp
        - name: end_ts
          type: timestamp
        - name: item_id
          type: int4
        - name: superstore
          type: int4
        - name: master_rule_id
          type: int4
        - name: sheet_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.rule_management_agendarulekviinternal/
      schema: lake_pricing_v3
      sortkey: []
      table: rule_management_agendarulekviinternal
    source:
      database: pricing_v3
      table: rule_management_agendarulekviinternal
  - flow_id: pricing_v3.rule_management_agendarulekvirm.v1
    topic_name: postgres.pricing_v3.public.rule_management_agendarulekvirm
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: created_by_id
          type: int4
        - name: updated_by_id
          type: int4
        - name: start_ts
          type: timestamp
        - name: end_ts
          type: timestamp
        - name: item_id
          type: int4
        - name: superstore
          type: int4
        - name: master_rule_id
          type: int4
        - name: sheet_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.rule_management_agendarulekvirm/
      schema: lake_pricing_v3
      sortkey: []
      table: rule_management_agendarulekvirm
    source:
      database: pricing_v3
      table: rule_management_agendarulekvirm
  - flow_id: pricing_v3.rule_management_agendarulerm.v1
    topic_name: postgres.pricing_v3.public.rule_management_agendarulerm
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: created_by_id
          type: int4
        - name: updated_by_id
          type: int4
        - name: start_ts
          type: timestamp
        - name: end_ts
          type: timestamp
        - name: p_type
          type: int4
        - name: brand_id
          type: int4
        - name: l2_category
          type: int4
        - name: manufacturer_id
          type: int4
        - name: superstore
          type: int4
        - name: master_rule_id
          type: int4
        - name: sheet_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.rule_management_agendarulerm/
      schema: lake_pricing_v3
      sortkey: []
      table: rule_management_agendarulerm
    source:
      database: pricing_v3
      table: rule_management_agendarulerm
  - flow_id: pricing_v3.rule_management_agendarulermitemid.v1
    topic_name: postgres.pricing_v3.public.rule_management_agendarulermitemid
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: created_by_id
          type: int4
        - name: updated_by_id
          type: int4
        - name: start_ts
          type: timestamp
        - name: end_ts
          type: timestamp
        - name: item_id
          type: int4
        - name: superstore
          type: int4
        - name: master_rule_id
          type: int4
        - name: sheet_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.rule_management_agendarulermitemid/
      schema: lake_pricing_v3
      sortkey: []
      table: rule_management_agendarulermitemid
    source:
      database: pricing_v3
      table: rule_management_agendarulermitemid
  - flow_id: pricing_v3.rule_management_agendarulesku.v1
    topic_name: postgres.pricing_v3.public.rule_management_agendarulesku
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: created_by_id
          type: int4
        - name: updated_by_id
          type: int4
        - name: start_ts
          type: timestamp
        - name: end_ts
          type: timestamp
        - name: item_id
          type: int4
        - name: superstore
          type: int4
        - name: master_rule_id
          type: int4
        - name: sheet_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.rule_management_agendarulesku/
      schema: lake_pricing_v3
      sortkey: []
      table: rule_management_agendarulesku
    source:
      database: pricing_v3
      table: rule_management_agendarulesku
  - flow_id: pricing_v3.rule_management_kviinternalbenchmarkingladder.v1
    topic_name: postgres.pricing_v3.public.rule_management_kviinternalbenchmarkingladder
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: item_ids
          type: varchar(max)
        - name: city_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.rule_management_kviinternalbenchmarkingladder/
      schema: lake_pricing_v3
      sortkey: []
      table: rule_management_kviinternalbenchmarkingladder
    source:
      database: pricing_v3
      table: rule_management_kviinternalbenchmarkingladder
  - flow_id: pricing_v3.rule_management_masterrule.v3
    topic_name: postgres.pricing_v3.public.rule_management_masterrule
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: created_by_id
          type: int4
        - name: updated_by_id
          type: int4
        - name: config
          type: varchar(max)
        - name: rule_type
          type: varchar(32)
        - name: start_ts
          type: timestamp
        - name: end_ts
          type: timestamp
        - name: sheet_id
          type: int4
        - name: status
          type: varchar(50)
        - name: automated_stage_rule_id
          type: int4
        - name: stage_rule_id
          type: int4
        - name: brand_id
          type: int4
        - name: ptype
          type: int4
        - name: retail_item_id
          type: int4
        - name: approval_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.rule_management_masterrule/
      schema: lake_pricing_v3
      sortkey: []
      table: rule_management_masterrule
    source:
      database: pricing_v3
      table: rule_management_masterrule
  - flow_id: pricing_v3.rule_management_pricingconfiguration.v1
    topic_name: postgres.pricing_v3.public.rule_management_pricingconfiguration
    redshift_replication: false
    sink:
      column_dtypes:
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: created_by_id
          type: int4
        - name: updated_by_id
          type: int4
        - name: id
          type: int8
        - name: item_id
          type: int4
        - name: configuration_type
          type: varchar(32)
        - name: configuration
          type: varchar(max)
        - name: city_id
          type: int4
        - name: sheet_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.rule_management_pricingconfiguration/
      schema: lake_pricing_v3
      sortkey: []
      table: rule_management_pricingconfiguration
    source:
      database: pricing_v3
      table: rule_management_pricingconfiguration
  - flow_id: pricing_v3.rule_management_pricingconfigurationsheetfile.v1
    topic_name: postgres.pricing_v3.public.rule_management_pricingconfigurationsheetfile
    redshift_replication: false
    sink:
      column_dtypes:
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: id
          type: int8
        - name: file
          type: varchar(100)
        - name: state
          type: varchar(50)
        - name: user_email
          type: varchar(max)
        - name: configuration_type
          type: varchar(255)
        - name: error_file
          type: varchar(100)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.rule_management_pricingconfigurationsheetfile/
      schema: lake_pricing_v3
      sortkey: []
      table: rule_management_pricingconfigurationsheetfile
    source:
      database: pricing_v3
      table: rule_management_pricingconfigurationsheetfile
  - flow_id: pricing_v3.rule_management_promotiontags.v1
    topic_name: postgres.pricing_v3.public.rule_management_promotiontags
    redshift_replication: false
    sink:
      column_dtypes:
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: id
          type: int8
        - name: tag_name
          type: varchar(255)
        - name: start_ts
          type: timestamp
        - name: end_ts
          type: timestamp
        - name: created_by
          type: varchar(max)
        - name: updated_by
          type: varchar(max)
        - name: validation_config
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.rule_management_promotiontags/
      schema: lake_pricing_v3
      sortkey: []
      table: rule_management_promotiontags
    source:
      database: pricing_v3
      table: rule_management_promotiontags
  - flow_id: pricing_v3.rule_management_sheetfile.v3
    topic_name: postgres.pricing_v3.public.rule_management_sheetfile
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: created_by_id
          type: int4
        - name: updated_by_id
          type: int4
        - name: file
          type: varchar(100)
        - name: rule_type
          type: varchar(32)
        - name: state
          type: varchar(50)
        - name: error_file
          type: varchar(100)
        - name: meta
          type: varchar(max)
        - name: retail_user_email
          type: varchar(255)
        - name: retail_user_id
          type: int4
        - name: pricing_source
          type: varchar(32)
        - name: is_express
          type: boolean
        - name: is_fnv
          type: boolean
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pricing_v3.public.rule_management_sheetfile/
      schema: lake_pricing_v3
      sortkey: []
      table: rule_management_sheetfile
    source:
      database: pricing_v3
      table: rule_management_sheetfile
tags:
  - de
  - replicate
  - pricing_v3_1
template_name: nessie
version: 2
