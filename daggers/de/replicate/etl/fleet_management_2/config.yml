dag_name: fleet_management_2
dag_type: etl
escalation_priority: low
alert_configs:
  slack:
    - channel: bl-data-alerts-p1
  error_channel_mapping:
    terminated_with_errors: bl-data-spot-interruptions
    spot: bl-data-spot-interruptions
executor:
  config:
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    cpu:
      limit: 1
      request: 0.3
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    memory:
      limit: 500M
      request: 250M
    node_selectors:
      nodetype: airflow-srp-spot
    tolerations:
      - effect: NoSchedule
        key: service
        operator: Equal
        value: airflow-srp
    volume_mounts:
      - mountPath: /usr/local/airflow/plugins
        name: airflow-dags
        subPath: airflow-de/plugins
  type: celery
namespace: de
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ
path: de/replicate/etl/fleet_management_2
paused: false
project_name: replicate
schedule:
  end_date: "2026-05-25T00:00:00"
  interval: 26,56 0-19,22 * * *
  start_date: "2025-05-30T00:00:00"
schedule_type: fixed
sla: 180 minutes
task_concurrency: 7
poke_interval: 180
redshift_sla_seconds: 10800
emr_sensor: {}
nessie_dag_split: true
spark:
  aws_conn_id: aws_default
  mode: emr
  nessie_jar_version: 2.1.2
  cluster_reuse: true
  emr_version: 6.11.1
support_files: []
tables:
  - flow_id: fleet_management.fleet_management_cost_item_attribution.v1
    topic_name: postgres.fleet_management.public.fleet_management_cost_item_attribution
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: cost_item_id
          type: int4
        - name: cost_item_reference_type
          type: varchar(30)
        - name: attribute_id
          type: varchar(255)
        - name: attribute_type
          type: varchar(255)
        - name: attributed_cost
          type: numeric(10, 2)
        - name: attributed_cost_details
          type: varchar(max)
        - name: errors
          type: varchar(max)
        - name: status
          type: varchar(50)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.fleet_management.public.fleet_management_cost_item_attribution/
      schema: lake_fleet_management
      sortkey: []
      table: fleet_management_cost_item_attribution
    source:
      database: fleet_management
      table: fleet_management_cost_item_attribution
  - flow_id: fleet_management.fleet_management_cost_item_cost.v1
    topic_name: postgres.fleet_management.public.fleet_management_cost_item_cost
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: cost_item_id
          type: int4
        - name: cost_item_reference_type
          type: varchar(30)
        - name: cost
          type: numeric(10, 2)
        - name: cost_type
          type: varchar(255)
        - name: cost_details
          type: varchar(max)
        - name: errors
          type: varchar(max)
        - name: status
          type: varchar(50)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.fleet_management.public.fleet_management_cost_item_cost/
      schema: lake_fleet_management
      sortkey: []
      table: fleet_management_cost_item_cost
    source:
      database: fleet_management
      table: fleet_management_cost_item_cost
  - flow_id: fleet_management.fleet_management_cost_item_cost_log.v1
    topic_name: postgres.fleet_management.public.fleet_management_cost_item_cost_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: previous_details
          type: varchar(max)
        - name: current_details
          type: varchar(max)
        - name: previous_cost
          type: numeric(10, 2)
        - name: current_cost
          type: numeric(10, 2)
        - name: item_cost_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.fleet_management.public.fleet_management_cost_item_cost_log/
      schema: lake_fleet_management
      sortkey: []
      table: fleet_management_cost_item_cost_log
    source:
      database: fleet_management
      table: fleet_management_cost_item_cost_log
  - flow_id: fleet_management.fleet_management_cost_item_request_mapping.v1
    topic_name: postgres.fleet_management.public.fleet_management_cost_item_request_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: cost_item_id
          type: int8
        - name: cost_request_id
          type: int8
        - name: is_active
          type: boolean
        - name: contribution_weight
          type: numeric(4, 3)
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: cost_item_reference_type
          type: varchar(30)
        - name: cost_request_reference_type
          type: varchar(30)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.fleet_management.public.fleet_management_cost_item_request_mapping/
      schema: lake_fleet_management
      sortkey: []
      table: fleet_management_cost_item_request_mapping
    source:
      database: fleet_management
      table: fleet_management_cost_item_request_mapping
  - flow_id: fleet_management.fleet_management_cost_request_log.v1
    topic_name: postgres.fleet_management.public.fleet_management_cost_request_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: previous_details
          type: varchar(max)
        - name: current_details
          type: varchar(max)
        - name: cost_request_id
          type: int4
        - name: cost_request_reference_type
          type: varchar(30)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.fleet_management.public.fleet_management_cost_request_log/
      schema: lake_fleet_management
      sortkey: []
      table: fleet_management_cost_request_log
    source:
      database: fleet_management
      table: fleet_management_cost_request_log
  - flow_id: fleet_management.fleet_management_trip_cost_item.v1
    topic_name: postgres.fleet_management.public.fleet_management_trip_cost_item
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: vehicle_number
          type: varchar(20)
        - name: vendor_id
          type: varchar(10)
        - name: source_node_id
          type: varchar(50)
        - name: tenure_start_ts
          type: timestamp
        - name: tenure_end_ts
          type: timestamp
        - name: tenure_type
          type: varchar(50)
        - name: billable_vehicle_type
          type: varchar(30)
        - name: billing_type
          type: varchar(25)
        - name: city_details
          type: varchar(max)
        - name: toll_details
          type: varchar(max)
        - name: misc_cost
          type: numeric(8, 2)
        - name: status
          type: varchar(50)
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: tenant
          type: varchar(100)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.fleet_management.public.fleet_management_trip_cost_item/
      schema: lake_fleet_management
      sortkey: []
      table: fleet_management_trip_cost_item
    source:
      database: fleet_management
      table: fleet_management_trip_cost_item
  - flow_id: fleet_management.fleet_management_trip_cost_request.v1
    topic_name: postgres.fleet_management.public.fleet_management_trip_cost_request
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: trip_id
          type: varchar(50)
        - name: distance_in_kms
          type: numeric(8, 2)
        - name: duration_in_hrs
          type: numeric(8, 2)
        - name: trip_creation_ts
          type: timestamp
        - name: trip_dispatch_ts
          type: timestamp
        - name: trip_completion_ts
          type: timestamp
        - name: source_node_id
          type: varchar(50)
        - name: vehicle_number
          type: varchar(20)
        - name: vendor_id
          type: varchar(10)
        - name: billable_vehicle_type
          type: varchar(30)
        - name: billing_type
          type: varchar(25)
        - name: misc_cost
          type: numeric(8, 2)
        - name: metadata
          type: varchar(max)
        - name: destination_details
          type: varchar(max)
        - name: status
          type: varchar(50)
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: created_by
          type: varchar(100)
        - name: updated_by
          type: varchar(100)
        - name: tenant
          type: varchar(100)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.fleet_management.public.fleet_management_trip_cost_request/
      schema: lake_fleet_management
      sortkey: []
      table: fleet_management_trip_cost_request
    source:
      database: fleet_management
      table: fleet_management_trip_cost_request
  - flow_id: fleet_management.fleet_management_vehicle_type.v1
    topic_name: postgres.fleet_management.public.fleet_management_vehicle_type
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: display_name
          type: varchar(100)
        - name: load_span
          type: varchar(100)
        - name: variant
          type: varchar(20)
        - name: load_capacity
          type: int4
        - name: fuel_type
          type: varchar(50)
        - name: refrigeration_type
          type: varchar(50)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.fleet_management.public.fleet_management_vehicle_type/
      schema: lake_fleet_management
      sortkey: []
      table: fleet_management_vehicle_type
    source:
      database: fleet_management
      table: fleet_management_vehicle_type
  - flow_id: fleet_management.fleet_management_workflow.v1
    topic_name: postgres.fleet_management.public.fleet_management_workflow
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: tenant
          type: varchar(255)
        - name: reference_id
          type: varchar(255)
        - name: reference_type
          type: varchar(100)
        - name: workflow_type
          type: varchar(100)
        - name: user_id
          type: varchar(20)
        - name: state
          type: varchar(50)
        - name: metadata
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.fleet_management.public.fleet_management_workflow/
      schema: lake_fleet_management
      sortkey: []
      table: fleet_management_workflow
    source:
      database: fleet_management
      table: fleet_management_workflow
tags:
  - de
  - replicate
  - fleet_management_2
template_name: nessie
version: 1
