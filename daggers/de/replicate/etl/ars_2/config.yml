dag_name: ars_2
dag_type: etl
escalation_priority: low

alert_configs:
  slack:
    - channel: bl-data-alerts-p1
  error_channel_mapping:
    terminated_with_errors: bl-data-spot-interruptions
    spot: bl-data-spot-interruptions

executor:
  config:
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    cpu:
      limit: 1
      request: 0.3
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    memory:
      limit: 500M
      request: 250M
    node_selectors:
      nodetype: airflow-srp-spot
    tolerations:
      - effect: NoSchedule
        key: service
        operator: Equal
        value: airflow-srp
    volume_mounts:
      - mountPath: /usr/local/airflow/plugins
        name: airflow-dags
        subPath: airflow-de/plugins
  type: celery
namespace: de
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ

path: de/replicate/etl/ars_2
paused: false
project_name: replicate
schedule:
  interval: 13,43 * * * *
  start_date: "2021-12-20T09:30:00"
  end_date: '2026-09-15T00:00:00'
schedule_type: fixed
sla: 180 minutes
task_concurrency: 15
poke_interval: 180
redshift_sla_seconds: 10800
emr_sensor: {}
nessie_dag_split: true
spark:
  aws_conn_id: aws_default
  mode: emr
  nessie_jar_version: 2.1.2
  cluster_reuse: true
  emr_version: 6.11.1
support_files: []
tables:
  - flow_id: ars.backend_facility_transfer_attributes.v3
    topic_name: mysql.ars.ars.backend_facility_transfer_attributes
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: backend_facility_id
          type: int8
        - name: from_date
          type: date
        - name: to_date
          type: date
        - name: picking_capacity_quantity
          type: int8
        - name: picking_capacity_sku
          type: int8
        - name: created_by
          type: int8
        - name: updated_by
          type: int8
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: mode
          type: varchar(32)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.backend_facility_transfer_attributes/
      schema: lake_ars
      sortkey: []
      table: backend_facility_transfer_attributes
    source:
      database: ars
      table: backend_facility_transfer_attributes
  - flow_id: ars.backend_frontend_transfer_attributes.v2
    topic_name: mysql.ars.ars.backend_frontend_transfer_attributes
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: backend_outlet_id
          type: int8
        - name: frontend_outlet_id
          type: int8
        - name: active
          type: int4
        - name: picking_capacity_quantity_morning
          type: int8
        - name: picking_capacity_sku_morning
          type: int8
        - name: truck_load_capacity_morning
          type: float8
        - name: inward_capacity_morning
          type: int8
        - name: picking_capacity_sku_evening
          type: int8
        - name: picking_capacity_quantity_evening
          type: int8
        - name: truck_load_capacity_evening
          type: float8
        - name: inward_capacity_evening
          type: int8
        - name: truncation_rule_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.backend_frontend_transfer_attributes/
      schema: lake_ars
      sortkey: []
      table: backend_frontend_transfer_attributes
    source:
      database: ars
      table: backend_frontend_transfer_attributes
  - flow_id: ars.bulk_facility_transfer_days.v2
    topic_name: mysql.ars.ars.bulk_facility_transfer_days
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: backend_outlet_id
          type: int8
        - name: frontend_outlet_id
          type: int8
        - name: transfer_days
          type: varchar(120)
        - name: transfer_tat
          type: int8
        - name: run_id
          type: varchar(128)
        - name: case_sensitivity_type
          type: int8
        - name: picking_capacity_quantity
          type: int8
        - name: picking_capacity_sku
          type: int8
        - name: truck_load_capacity
          type: float8
        - name: threshold_doi
          type: int8
        - name: inventory_value
          type: int8
        - name: case_round_up_threshold
          type: float8
        - name: min_case_round_up_threshold
          type: float8
        - name: inward_capacity
          type: int8
        - name: truncation_config_id
          type: int8
        - name: meta
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.bulk_facility_transfer_days/
      schema: lake_ars
      sortkey: []
      table: bulk_facility_transfer_days
    source:
      database: ars
      table: bulk_facility_transfer_days
  - flow_id: ars.bulk_process_bump_tracker.v1
    topic_name: mysql.ars.ars.bulk_process_bump_tracker
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: line_number
          type: int8
        - name: category_id
          type: int8
        - name: backend_facility_id
          type: int8
        - name: from_date
          type: date
        - name: to_date
          type: date
        - name: bump_percent
          type: decimal(10, 4)
        - name: category_type
          type: int8
        - name: reason
          type: varchar(2024)
        - name: status
          type: varchar(32)
        - name: action_reason
          type: varchar(2024)
        - name: cpd
          type: decimal(10, 4)
        - name: sale
          type: int8
        - name: meta
          type: varchar(max)
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: bulk_process_tracker_id
          type: int8
        - name: bump_type
          type: varchar(32)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.bulk_process_bump_tracker/
      schema: lake_ars
      sortkey: []
      table: bulk_process_bump_tracker
    source:
      database: ars
      table: bulk_process_bump_tracker
  - flow_id: ars.case_incorporation_result.v3
    topic_name: mysql.ars.ars.case_incorporation_result
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: outlet_id
          type: int8
        - name: aligned_vendor_id
          type: int8
        - name: item_id
          type: int8
        - name: ordering_quantity
          type: float8
        - name: ordering_doi
          type: float8
        - name: default_cpd
          type: float8
        - name: case_sensitivity_type
          type: int8
        - name: case_size
          type: int8
        - name: weight_in_gm
          type: int8
        - name: lc_doi
          type: float8
        - name: uc_doi
          type: float8
        - name: max_doi
          type: float8
        - name: min_doi
          type: float8
        - name: bump_doi
          type: float8
        - name: bump_loadsize
          type: float8
        - name: bump_factor
          type: float8
        - name: mrp
          type: float8
        - name: landing_price
          type: float8
        - name: max_bump_cycles
          type: int8
        - name: initial_load_size_deficit
          type: float8
        - name: initial_max_addl_load_size
          type: float8
        - name: error_id
          type: int8
        - name: error_details
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: run_id
          type: varchar(128)
        - name: holding_doi
          type: float8
        - name: base_doi
          type: float8
        - name: variant_description
          type: varchar(max)
        - name: variant_id
          type: varchar(max)
        - name: inner_case_size
          type: int8
        - name: outer_case_size
          type: int8
        - name: is_critical
          type: int8
        - name: trigger_doi
          type: float8
        - name: facility_id
          type: int8
        - name: fill_rate_buffer_doi
          type: float8
        - name: forecasting_buffer_doi
          type: float8
        - name: po_cycle_buffer_doi
          type: float8
        - name: put_away_buffer_doi
          type: float8
        - name: holding_quantity
          type: float8
        - name: avg_cpd
          type: float8
        - name: base_quantity
          type: float8
        - name: cpd_variation
          type: float8
        - name: max_ordering_qty
          type: float8
        - name: min_ordering_qty
          type: float8
        - name: trigger_doi_quantity
          type: float8
        - name: trigger_doi_date
          type: date
        - name: group_id
          type: int8
        - name: initial_load_size_deficit_percentage
          type: float8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.case_incorporation_result/
      schema: lake_ars
      sortkey: []
      table: case_incorporation_result
    source:
      database: ars
      table: case_incorporation_result
  - flow_id: ars.final_indent.v3
    topic_name: mysql.ars.ars.final_indent
    redshift_replication: false
    nessie_jar_version: 2.1.2-high-resources
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: city
          type: int8
        - name: outlet_id
          type: int8
        - name: outlet_name
          type: varchar(1024)
        - name: physical_facility_id
          type: int8
        - name: vendor_facility_identifier
          type: varchar(1024)
        - name: item_id
          type: int8
        - name: item_name
          type: varchar(1024)
        - name: manufacturer_name
          type: varchar(1024)
        - name: perishable
          type: int4
        - name: mrp
          type: float8
        - name: landing_price
          type: float8
        - name: inner_case_size
          type: int8
        - name: outer_case_size
          type: int8
        - name: case_size
          type: int8
        - name: case_sensitivity_type
          type: int8
        - name: shelf_life
          type: int8
        - name: variant_description
          type: varchar(max)
        - name: storage_type
          type: varchar(max)
        - name: cpd
          type: float8
        - name: current_inventory
          type: float8
        - name: base_quantity
          type: float8
        - name: fill_rate
          type: float8
        - name: open_po_quantity
          type: int8
        - name: open_po_quantity_post_fill_rate
          type: float8
        - name: ci_open_po_doi
          type: float8
        - name: base_doi
          type: float8
        - name: aligned_vendor_id
          type: int8
        - name: aligned_vendor_name
          type: varchar(1024)
        - name: load_size
          type: float8
        - name: load_type
          type: int8
        - name: po_cycle
          type: int8
        - name: po_days
          type: varchar(69)
        - name: max_ordering_doi
          type: float8
        - name: min_ordering_doi
          type: float8
        - name: weight_in_gm
          type: int8
        - name: trigger_doi
          type: float8
        - name: fill_rate_buffer_doi
          type: float8
        - name: forecasting_buffer_doi
          type: float8
        - name: put_away_buffer_doi
          type: float8
        - name: po_cycle_buffer_doi
          type: float8
        - name: fill_rate_buffer_updated_by
          type: varchar(69)
        - name: tat_days
          type: int8
        - name: tat_days_updated_by
          type: varchar(69)
        - name: holding_doi
          type: float8
        - name: quantity_in_case
          type: int8
        - name: final_indent_quantity
          type: int8
        - name: final_doi
          type: float8
        - name: is_critical
          type: varchar(69)
        - name: error_type
          type: varchar(69)
        - name: error_details
          type: varchar(max)
        - name: run_id
          type: varchar(128)
        - name: threshold_doi
          type: float8
        - name: cpd_before_variation
          type: float8
        - name: cpd_variation
          type: float8
        - name: city_name
          type: varchar(1024)
        - name: expected_live_date_if_raised_today
          type: date
        - name: holding_quantity
          type: float8
        - name: facility_name
          type: varchar(1024)
        - name: auto_po_eligible
          type: int4
        - name: bucket_ab
          type: varchar(max)
        - name: bucket_x
          type: varchar(max)
        - name: final_indent_value
          type: float8
        - name: open_sto_quantity
          type: int8
        - name: po_cycle_type_id
          type: int4
        - name: internal_facility_identifier
          type: varchar(1024)
        - name: next_live_date
          type: date
        - name: trigger_doi_date
          type: date
        - name: l0_category_name
          type: varchar(1024)
        - name: l0_id
          type: int8
        - name: is_transfer_sku
          type: varchar(10)
        - name: group_id
          type: int8
        - name: overall_doi
          type: float8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.final_indent/
      schema: lake_ars
      sortkey: []
      table: final_indent
    source:
      database: ars
      table: final_indent
  - flow_id: ars.fixed_indent.v1
    topic_name: mysql.ars.ars.fixed_indent
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: facility_id
          type: int8
        - name: item_id
          type: int8
        - name: forecast_date
          type: date
        - name: mode
          type: varchar(32)
        - name: quantity
          type: int8
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: facility_type
          type: varchar(32)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.fixed_indent/
      schema: lake_ars
      sortkey: []
      table: fixed_indent
    source:
      database: ars
      table: fixed_indent
  - flow_id: ars.frontend_inward_capacity.v1
    topic_name: mysql.ars.ars.frontend_inward_capacity
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: shift_key
          type: varchar(8)
        - name: frontend_outlet_id
          type: int8
        - name: inward_capacity_percent
          type: float8
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.frontend_inward_capacity/
      schema: lake_ars
      sortkey: []
      table: frontend_inward_capacity
    source:
      database: ars
      table: frontend_inward_capacity
  - flow_id: ars.growth_factor.v1
    topic_name: mysql.ars.ars.growth_factor
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: item_id
          type: int8
        - name: for_date
          type: date
        - name: mode
          type: varchar(32)
        - name: value
          type: decimal(10, 2)
        - name: location_type
          type: int8
        - name: location_id
          type: int8
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.growth_factor/
      schema: lake_ars
      sortkey: []
      table: growth_factor
    source:
      database: ars
      table: growth_factor
  - flow_id: ars.inter_facility_transfer_days.v1
    topic_name: mysql.ars.ars.inter_facility_transfer_days
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: source_outlet_id
          type: int8
        - name: destination_outlet_id
          type: int8
        - name: mode
          type: varchar(32)
        - name: transfer_days
          type: varchar(120)
        - name: transfer_tat
          type: int8
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.inter_facility_transfer_days/
      schema: lake_ars
      sortkey: []
      table: inter_facility_transfer_days
    source:
      database: ars
      table: inter_facility_transfer_days
  - flow_id: ars.inter_facility_transfer_days_log.v1
    topic_name: mysql.ars.ars.inter_facility_transfer_days_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: source_outlet_id
          type: int8
        - name: destination_outlet_id
          type: int8
        - name: mode
          type: varchar(32)
        - name: transfer_days
          type: varchar(120)
        - name: transfer_tat
          type: int8
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.inter_facility_transfer_days_log/
      schema: lake_ars
      sortkey: []
      table: inter_facility_transfer_days_log
    source:
      database: ars
      table: inter_facility_transfer_days_log
  - flow_id: ars.item_cpd_replication.v1
    topic_name: mysql.ars.ars.item_cpd_replication
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: old_item_id
          type: int8
        - name: new_item_id
          type: int8
        - name: facility_type
          type: varchar(32)
        - name: facility_id
          type: int8
        - name: end_date
          type: date
        - name: mode
          type: varchar(32)
        - name: factor
          type: decimal(10, 2)
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.item_cpd_replication/
      schema: lake_ars
      sortkey: []
      table: item_cpd_replication
    source:
      database: ars
      table: item_cpd_replication
  - flow_id: ars.job_run.v3
    topic_name: mysql.ars.ars.job_run
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: run_id
          type: varchar(128)
        - name: outlet_id
          type: int8
        - name: run_date
          type: varchar(128)
        - name: success
          type: int4
        - name: started_at
          type: timestamp
        - name: completed_at
          type: timestamp
        - name: details
          type: varchar(max)
        - name: facility_id
          type: int8
        - name: is_simulation
          type: int4
        - name: simulation_params
          type: varchar(max)
        - name: extra
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.job_run/
      schema: lake_ars
      sortkey: []
      table: job_run
    source:
      database: ars
      table: job_run
  - flow_id: ars.min_bump_algo_result.v3
    topic_name: mysql.ars.ars.min_bump_algo_result
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: outlet_id
          type: int8
        - name: aligned_vendor_id
          type: int8
        - name: item_id
          type: int8
        - name: ordering_quantity
          type: float8
        - name: ordering_doi
          type: float8
        - name: run_id
          type: varchar(128)
        - name: error_id
          type: int8
        - name: error_details
          type: varchar(max)
        - name: group_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.min_bump_algo_result/
      schema: lake_ars
      sortkey: []
      table: min_bump_algo_result
    source:
      database: ars
      table: min_bump_algo_result
  - flow_id: ars.outlet.v3
    topic_name: mysql.ars.ars.outlet
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: outlet_id
          type: int8
        - name: name
          type: varchar(1024)
        - name: city_id
          type: int8
        - name: physical_facility_id
          type: int8
        - name: created_at
          type: timestamp
        - name: run_id
          type: varchar(128)
        - name: city_name
          type: varchar(1024)
        - name: vendor_facility_identifier
          type: varchar(1024)
        - name: facility_name
          type: varchar(1024)
        - name: internal_facility_identifier
          type: varchar(1024)
        - name: is_bulk
          type: int4
        - name: inventory_outlet_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.outlet/
      schema: lake_ars
      sortkey: []
      table: outlet
    source:
      database: ars
      table: outlet
  - flow_id: ars.outlet_item_aps_derived_cpd.v1
    topic_name: mysql.ars.ars.outlet_item_aps_derived_cpd
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: outlet_id
          type: int8
        - name: item_id
          type: int8
        - name: aps_adjusted
          type: float8
        - name: bump_percentage
          type: float8
        - name: growth_projections
          type: float8
        - name: for_date
          type: date
        - name: cpd
          type: float8
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.outlet_item_aps_derived_cpd/
      schema: lake_ars
      sortkey: []
      table: outlet_item_aps_derived_cpd
    source:
      database: ars
      table: outlet_item_aps_derived_cpd
  - flow_id: ars.outlet_item_group_mapping.v1
    topic_name: mysql.ars.ars.outlet_item_group_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: outlet_id
          type: int8
        - name: group_id
          type: int8
        - name: item_id
          type: int8
        - name: created_at
          type: timestamp
        - name: run_id
          type: varchar(128)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.outlet_item_group_mapping/
      schema: lake_ars
      sortkey: []
      table: outlet_item_group_mapping
    source:
      database: ars
      table: outlet_item_group_mapping
  - flow_id: ars.outlet_item_open_sto.v2
    topic_name: mysql.ars.ars.outlet_item_open_sto
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: from_outlet_id
          type: int8
        - name: outlet_id
          type: int8
        - name: item_id
          type: int8
        - name: sto_id
          type: int8
        - name: issue_date
          type: timestamp
        - name: delivery_date
          type: timestamp
        - name: reserved_quantity
          type: int8
        - name: billed_quantity
          type: int8
        - name: inward_quantity
          type: int8
        - name: released_reserved_quantity
          type: int8
        - name: released_billed_quantity
          type: int8
        - name: current_blocked_quantity
          type: int8
        - name: in_transit_quantity
          type: int8
        - name: total_in_transit_quantity
          type: int8
        - name: created_at
          type: timestamp
        - name: sto_state_id
          type: int8
        - name: run_id
          type: varchar(128)
        - name: slot
          type: varchar(10)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.outlet_item_open_sto/
      schema: lake_ars
      sortkey: []
      table: outlet_item_open_sto
    source:
      database: ars
      table: outlet_item_open_sto
  - flow_id: ars.outlet_vendor_item_tat_days.v3
    topic_name: mysql.ars.ars.outlet_vendor_item_tat_days
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: outlet_id
          type: int8
        - name: vendor_id
          type: int8
        - name: item_id
          type: int8
        - name: tat_days
          type: int8
        - name: tat_type
          type: int8
        - name: created_at
          type: timestamp
        - name: run_id
          type: varchar(128)
        - name: tat_days_updated_by
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.outlet_vendor_item_tat_days/
      schema: lake_ars
      sortkey: []
      table: outlet_vendor_item_tat_days
    source:
      database: ars
      table: outlet_vendor_item_tat_days
  - flow_id: ars.reasons.v2
    topic_name: mysql.ars.ars.reasons
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: description
          type: varchar(max)
        - name: type
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.reasons/
      schema: lake_ars
      sortkey: []
      table: reasons
    source:
      database: ars
      table: reasons
  - flow_id: ars.slot_flushing.v1
    topic_name: mysql.ars.ars.slot_flushing
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: attribute_id
          type: int8
        - name: attribute_type
          type: varchar(32)
        - name: backend_facility_id
          type: int8
        - name: slot
          type: varchar(32)
        - name: for_date
          type: date
        - name: transfer_percent
          type: float8
        - name: transfer_absolute_value
          type: int8
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.slot_flushing/
      schema: lake_ars
      sortkey: []
      table: slot_flushing
    source:
      database: ars
      table: slot_flushing
  - flow_id: ars.sto_dispatch_time.v2
    topic_name: mysql.ars.ars.sto_dispatch_time
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: shift_key
          type: varchar(8)
        - name: frontend_outlet_id
          type: int8
        - name: dispatch_time
          type: varchar(5)
        - name: active
          type: int4
        - name: mode
          type: varchar(32)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.sto_dispatch_time/
      schema: lake_ars
      sortkey: []
      table: sto_dispatch_time
    source:
      database: ars
      table: sto_dispatch_time
  - flow_id: ars.table_updates.v3
    topic_name: mysql.ars.ars.table_updates
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: update_id
          type: varchar(128)
        - name: target_table
          type: varchar(128)
        - name: inserted
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.table_updates/
      schema: lake_ars
      sortkey: []
      table: table_updates
    source:
      database: ars
      table: table_updates
  - flow_id: ars.transfer_case_size.v2
    topic_name: mysql.ars.ars.transfer_case_size
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: backend_facility_id
          type: int8
        - name: item_id
          type: int8
        - name: case_size
          type: int8
        - name: case_flag
          type: varchar(100)
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: moq
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.transfer_case_size/
      schema: lake_ars
      sortkey: []
      table: transfer_case_size
    source:
      database: ars
      table: transfer_case_size
tags:
  - de
  - replicate
  - ars_2
template_name: nessie
version: 2
