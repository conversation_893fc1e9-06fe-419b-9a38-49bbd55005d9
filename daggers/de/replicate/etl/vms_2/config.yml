dag_name: vms_2
dag_type: etl
escalation_priority: low

alert_configs:
  slack:
    - channel: bl-data-alerts-p1
  error_channel_mapping:
    terminated_with_errors: bl-data-spot-interruptions
    spot: bl-data-spot-interruptions

executor:
  config:
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    cpu:
      limit: 1
      request: 0.3
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    memory:
      limit: 500M
      request: 250M
    node_selectors:
      nodetype: airflow-srp-spot
    tolerations:
      - effect: NoSchedule
        key: service
        operator: Equal
        value: airflow-srp
    volume_mounts:
      - mountPath: /usr/local/airflow/plugins
        name: airflow-dags
        subPath: airflow-de/plugins
  type: celery
namespace: de
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ

path: de/replicate/etl/vms_2
paused: false
project_name: replicate
schedule:
  interval: 23 */3 * * *
  start_date: "2021-09-20T00:00:00"
  end_date: '2026-08-15T00:00:00'
schedule_type: fixed
sla: 180 minutes
task_concurrency: 15
poke_interval: 180
spark:
  aws_conn_id: aws_default
  mode: emr
  nessie_jar_version: 2.1.2
  cluster_reuse: true
  emr_version: 6.11.1
support_files: []
emr_sensor: {}
redshift_sla_seconds: 10800
tables:
  - flow_id: vms.contact_model.v2
    topic_name: mysql.vms.vms.contact_model
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: name
          type: varchar(30)
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.vms.vms.contact_model/
      schema: lake_vms
      sortkey: []
      table: contact_model
    source:
      database: vms
      table: contact_model
  - flow_id: vms.contact_purpose.v2
    topic_name: mysql.vms.vms.contact_purpose
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: name
          type: varchar(30)
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.vms.vms.contact_purpose/
      schema: lake_vms
      sortkey: []
      table: contact_purpose
    source:
      database: vms
      table: contact_purpose
  - flow_id: vms.contact_type.v2
    topic_name: mysql.vms.vms.contact_type
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: name
          type: varchar(30)
        - name: model
          type: varchar(30)
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: model_type_id
          type: int8
        - name: is_unique
          type: int4
        - name: mode
          type: varchar(20)
        - name: type
          type: varchar(30)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.vms.vms.contact_type/
      schema: lake_vms
      sortkey: []
      table: contact_type
    source:
      database: vms
      table: contact_type
  - flow_id: vms.delivery_type.v2
    topic_name: mysql.vms.vms.delivery_type
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: name
          type: varchar(512)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.vms.vms.delivery_type/
      schema: lake_vms
      sortkey: []
      table: delivery_type
    source:
      database: vms
      table: delivery_type
  - flow_id: vms.external_api_call_logs.v1
    topic_name: mysql.vms.vms.external_api_call_logs
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: external_partner
          type: varchar(32)
        - name: event_type
          type: varchar(32)
        - name: status_code
          type: int8
        - name: request_id
          type: varchar(128)
        - name: payload
          type: varchar(max)
        - name: response
          type: varchar(max)
        - name: meta
          type: varchar(max)
        - name: created_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.vms.vms.external_api_call_logs/
      schema: lake_vms
      sortkey: []
      table: external_api_call_logs
    source:
      database: vms
      table: external_api_call_logs
  - flow_id: vms.facility_vendor_address.v1
    topic_name: mysql.vms.vms.facility_vendor_address
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: facility_id
          type: int8
        - name: address_type
          type: varchar(64)
        - name: address_id
          type: int8
        - name: vendor_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.vms.vms.facility_vendor_address/
      schema: lake_vms
      sortkey: []
      table: facility_vendor_address
    source:
      database: vms
      table: facility_vendor_address
  - flow_id: vms.io_max_doi_log.v2
    topic_name: mysql.vms.vms.io_max_doi_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: item_id
          type: int8
        - name: outlet_id
          type: int8
        - name: max_doi
          type: float8
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.vms.vms.io_max_doi_log/
      schema: lake_vms
      sortkey: []
      table: io_max_doi_log
    source:
      database: vms
      table: io_max_doi_log
  - flow_id: vms.iov_fill_rate_buffer_doi.v2
    topic_name: mysql.vms.vms.iov_fill_rate_buffer_doi
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: vendor_id
          type: int8
        - name: item_id
          type: int8
        - name: outlet_id
          type: int8
        - name: fill_rate_buffer_doi
          type: float8
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.vms.vms.iov_fill_rate_buffer_doi/
      schema: lake_vms
      sortkey: []
      table: iov_fill_rate_buffer_doi
    source:
      database: vms
      table: iov_fill_rate_buffer_doi
  - flow_id: vms.po_cycle.v2
    topic_name: mysql.vms.vms.po_cycle
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: name
          type: varchar(512)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.vms.vms.po_cycle/
      schema: lake_vms
      sortkey: []
      table: po_cycle
    source:
      database: vms
      table: po_cycle
  - flow_id: vms.po_cycle_types.v2
    topic_name: mysql.vms.vms.po_cycle_types
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: identifier
          type: varchar(50)
        - name: tat_working_days
          type: int8
        - name: default_days
          type: int8
        - name: min_range_days
          type: int8
        - name: max_range_days
          type: int8
        - name: forecasting_buffer
          type: int8
        - name: max_doi
          type: int8
        - name: min_doi
          type: int8
        - name: po_cycle_multiplier
          type: float8
        - name: threshold_doi
          type: int8
        - name: trigger_doi
          type: int8
        - name: fill_rate_buffer
          type: int8
        - name: put_away_buffer
          type: int8
        - name: cpd_variation
          type: decimal(10, 2)
        - name: active
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.vms.vms.po_cycle_types/
      schema: lake_vms
      sortkey: []
      table: po_cycle_types
    source:
      database: vms
      table: po_cycle_types
  - flow_id: vms.vendor_approval.v1
    topic_name: mysql.vms.vms.vendor_approval
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: user_id
          type: int8
        - name: role
          type: varchar(128)
        - name: comment
          type: varchar(128)
        - name: state
          type: varchar(128)
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: vendor_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.vms.vms.vendor_approval/
      schema: lake_vms
      sortkey: []
      table: vendor_approval
    source:
      database: vms
      table: vendor_approval
  - flow_id: vms.vendor_config.v1
    topic_name: mysql.vms.vms.vendor_config
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: vendor_id
          type: int8
        - name: config_name
          type: varchar(500)
        - name: config_value
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.vms.vms.vendor_config/
      schema: lake_vms
      sortkey: []
      table: vendor_config
    source:
      database: vms
      table: vendor_config
  - flow_id: vms.vendor_config_log.v1
    topic_name: mysql.vms.vms.vendor_config_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: vendor_id
          type: int8
        - name: config_name
          type: varchar(500)
        - name: config_value
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.vms.vms.vendor_config_log/
      schema: lake_vms
      sortkey: []
      table: vendor_config_log
    source:
      database: vms
      table: vendor_config_log
  - flow_id: vms.vendor_contact_address.v1
    topic_name: mysql.vms.vms.vendor_contact_address
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: contact_of
          type: varchar(100)
        - name: line_one
          type: varchar(256)
        - name: line_two
          type: varchar(256)
        - name: city
          type: varchar(256)
        - name: landmark
          type: varchar(256)
        - name: pin_code
          type: varchar(10)
        - name: city_id
          type: int8
        - name: state_code
          type: varchar(3)
        - name: meta
          type: varchar(max)
        - name: contact_type_id
          type: int8
        - name: vendor_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.vms.vms.vendor_contact_address/
      schema: lake_vms
      sortkey: []
      table: vendor_contact_address
    source:
      database: vms
      table: vendor_contact_address
  - flow_id: vms.vendor_item_physical_facility_attributes_log.v2
    topic_name: mysql.vms.vms.vendor_item_physical_facility_attributes_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: vendor_id
          type: int8
        - name: item_id
          type: int8
        - name: facility_id
          type: int8
        - name: case_sensitivity_type
          type: int8
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: indenting_type
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.vms.vms.vendor_item_physical_facility_attributes_log/
      schema: lake_vms
      sortkey: []
      table: vendor_item_physical_facility_attributes_log
    source:
      database: vms
      table: vendor_item_physical_facility_attributes_log
  - flow_id: vms.vms_company.v1
    topic_name: mysql.vms.vms.vms_company
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: legal_name
          type: varchar(100)
        - name: pan
          type: varchar(15)
        - name: cin
          type: varchar(25)
        - name: gstin
          type: varchar(20)
        - name: state_code
          type: int4
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: contact_address_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.vms.vms.vms_company/
      schema: lake_vms
      sortkey: []
      table: vms_company
    source:
      database: vms
      table: vms_company
  - flow_id: vms.vms_company_details.v1
    topic_name: mysql.vms.vms.vms_company_details
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: legal_name
          type: varchar(100)
        - name: pan
          type: varchar(15)
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.vms.vms.vms_company_details/
      schema: lake_vms
      sortkey: []
      table: vms_company_details
    source:
      database: vms
      table: vms_company_details
  - flow_id: vms.vms_document_type.v1
    topic_name: mysql.vms.vms.vms_document_type
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: name
          type: varchar(30)
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: enum_code
          type: varchar(30)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.vms.vms.vms_document_type/
      schema: lake_vms
      sortkey: []
      table: vms_document_type
    source:
      database: vms
      table: vms_document_type
  - flow_id: vms.vms_line_of_business.v2
    topic_name: mysql.vms.vms.vms_line_of_business
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: name
          type: varchar(50)
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: tenant_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.vms.vms.vms_line_of_business/
      schema: lake_vms
      sortkey: []
      table: vms_line_of_business
    source:
      database: vms
      table: vms_line_of_business
  - flow_id: vms.vms_manufacturer_contact_details.v2
    topic_name: mysql.vms.vms.vms_manufacturer_contact_details
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: manufacturer_id
          type: int8
        - name: manufacturer_name
          type: varchar(250)
        - name: spoc_name
          type: varchar(250)
        - name: spoc_designation
          type: varchar(250)
        - name: city_id
          type: int8
        - name: email_address
          type: varchar(max)
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: purpose_id
          type: int8
        - name: phone_number
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.vms.vms.vms_manufacturer_contact_details/
      schema: lake_vms
      sortkey: []
      table: vms_manufacturer_contact_details
    source:
      database: vms
      table: vms_manufacturer_contact_details
  - flow_id: vms.vms_ownership_types.v2
    topic_name: mysql.vms.vms.vms_ownership_types
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: name
          type: varchar(50)
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: tenant_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.vms.vms.vms_ownership_types/
      schema: lake_vms
      sortkey: []
      table: vms_ownership_types
    source:
      database: vms
      table: vms_ownership_types
  - flow_id: vms.vms_vendor_alignment_state.v2
    topic_name: mysql.vms.vms.vms_vendor_alignment_state
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: name
          type: varchar(512)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.vms.vms.vms_vendor_alignment_state/
      schema: lake_vms
      sortkey: []
      table: vms_vendor_alignment_state
    source:
      database: vms
      table: vms_vendor_alignment_state
  - flow_id: vms.vms_vendor_city_documents.v1
    topic_name: mysql.vms.vms.vms_vendor_city_documents
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: document_url
          type: varchar(max)
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: document_type_id
          type: int8
        - name: vendor_city_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.vms.vms.vms_vendor_city_documents/
      schema: lake_vms
      sortkey: []
      table: vms_vendor_city_documents
    source:
      database: vms
      table: vms_vendor_city_documents
  - flow_id: vms.vms_vendor_city_product_details.v2
    topic_name: mysql.vms.vms.vms_vendor_city_product_details
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: item_id
          type: int8
        - name: margin_percentage
          type: decimal(4, 2)
        - name: cost_price
          type: decimal(10, 2)
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: vendor_city_id
          type: int8
        - name: sales_margin_percentage
          type: decimal(4, 2)
        - name: purchase_case_size
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.vms.vms.vms_vendor_city_product_details/
      schema: lake_vms
      sortkey: []
      table: vms_vendor_city_product_details
    source:
      database: vms
      table: vms_vendor_city_product_details
  - flow_id: vms.vms_vendor_company_mapping.v1
    topic_name: mysql.vms.vms.vms_vendor_company_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: mapping_type
          type: varchar(20)
        - name: company_id
          type: int8
        - name: vendor_id
          type: int8
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.vms.vms.vms_vendor_company_mapping/
      schema: lake_vms
      sortkey: []
      table: vms_vendor_company_mapping
    source:
      database: vms
      table: vms_vendor_company_mapping
  - flow_id: vms.vms_vendor_facility_alignment_log.v1
    topic_name: mysql.vms.vms.vms_vendor_facility_alignment_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: item_id
          type: int8
        - name: facility_id
          type: int8
        - name: shipping_city_id
          type: int8
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: vendor_id
          type: int8
        - name: active
          type: int4
        - name: case_sensitivity_type
          type: int8
        - name: case_size
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.vms.vms.vms_vendor_facility_alignment_log/
      schema: lake_vms
      sortkey: []
      table: vms_vendor_facility_alignment_log
    source:
      database: vms
      table: vms_vendor_facility_alignment_log
  - flow_id: vms.vms_vendor_group_mapping.v1
    topic_name: mysql.vms.vms.vms_vendor_group_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: group_id
          type: int8
        - name: vendor_id
          type: int8
        - name: active
          type: int4
        - name: facility_id
          type: int8
        - name: bu_job_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: group_id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [group_id]
      s3_url: s3://prod-dse-nessie-output/mysql.vms.vms.vms_vendor_group_mapping/
      schema: lake_vms
      sortkey: []
      table: vms_vendor_group_mapping
    source:
      database: vms
      table: vms_vendor_group_mapping
  - flow_id: vms.vms_vendor_new_pi_logic.v1
    topic_name: mysql.vms.vms.vms_vendor_new_pi_logic
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: vendor_id
          type: int8
        - name: facility_id
          type: int8
        - name: group_id
          type: int8
        - name: po_days
          type: varchar(69)
        - name: po_cycle
          type: int8
        - name: po_cycle_type
          type: int8
        - name: load_type
          type: int8
        - name: load_size
          type: float8
        - name: tat_day
          type: int8
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: delivery_type
          type: int8
        - name: multi_grn
          type: int4
        - name: no_supply_days
          type: varchar(69)
        - name: breaking_enabled
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.vms.vms.vms_vendor_new_pi_logic/
      schema: lake_vms
      sortkey: []
      table: vms_vendor_new_pi_logic
    source:
      database: vms
      table: vms_vendor_new_pi_logic
  - flow_id: vms.vms_vendor_new_pi_logic_log.v1
    topic_name: mysql.vms.vms.vms_vendor_new_pi_logic_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: vendor_id
          type: int8
        - name: facility_id
          type: int8
        - name: group_id
          type: int8
        - name: po_days
          type: varchar(69)
        - name: po_cycle
          type: int8
        - name: po_cycle_type
          type: int8
        - name: load_type
          type: int8
        - name: load_size
          type: float8
        - name: tat_day
          type: int8
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: delivery_type
          type: int8
        - name: multi_grn
          type: int4
        - name: no_supply_days
          type: varchar(69)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.vms.vms.vms_vendor_new_pi_logic_log/
      schema: lake_vms
      sortkey: []
      table: vms_vendor_new_pi_logic_log
    source:
      database: vms
      table: vms_vendor_new_pi_logic_log
tags:
  - de
  - replicate
  - vms_2
template_name: nessie
version: 1
