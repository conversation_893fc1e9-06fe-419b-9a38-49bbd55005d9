dag_name: transit_server_2
dag_type: etl
escalation_priority: low
alert_configs:
  slack:
    - channel: bl-data-alerts-p1
  error_channel_mapping:
    terminated_with_errors: bl-data-spot-interruptions
    user_request: bl-data-spot-interruptions
    spot: bl-data-spot-interruptions
executor:
  config:
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    cpu:
      limit: 1
      request: 0.3
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    memory:
      limit: 500M
      request: 250M
    node_selectors:
      nodetype: airflow-srp-spot
    tolerations:
      - effect: NoSchedule
        key: service
        operator: Equal
        value: airflow-srp
    volume_mounts:
      - mountPath: /usr/local/airflow/plugins
        name: airflow-dags
        subPath: airflow-de/plugins
  type: celery
namespace: de
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ

path: de/replicate/etl/transit_server_2
paused: false
project_name: replicate
schedule:
  interval: 8,23,38,53 * * * *
  start_date: "2023-10-19T00:00:00"
  end_date: '2026-08-15T00:00:00'
schedule_type: fixed
sla: 180 minutes
task_concurrency: 3
poke_interval: 180
spark:
  aws_conn_id: aws_default
  mode: emr
  nessie_jar_version: 2.1.2
  cluster_reuse: true
  emr_version: 6.11.1
support_files: []
redshift_sla_seconds: 7200
emr_sensor: {}
nessie_dag_split: true
tables:
  - flow_id: transit_server.transit_consignment.v2
    topic_name: postgres.transit_server.public.transit_consignment
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: type
          type: varchar(30)
        - name: source_store_id
          type: varchar(250)
        - name: destination_store_id
          type: varchar(250)
        - name: expected_containers
          type: int4
        - name: state
          type: varchar(30)
        - name: metadata
          type: varchar(max)
        - name: created_by
          type: varchar(50)
        - name: updated_by
          type: varchar(50)
        - name: external_id
          type: varchar(30)
        - name: destination_node_type
          type: varchar(30)
        - name: source_node_type
          type: varchar(30)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_consignment/
      schema: lake_transit_server
      sortkey: []
      table: transit_consignment
    source:
      database: transit_server
      table: transit_consignment
  - flow_id: transit_server.transit_consignment_document.v2
    topic_name: postgres.transit_server.public.transit_consignment_document
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: document_type
          type: varchar(30)
        - name: external_id
          type: varchar(30)
        - name: document_s3_url
          type: varchar(250)
        - name: metadata
          type: varchar(max)
        - name: consignment_id
          type: int4
        - name: state
          type: varchar(50)
        - name: billed_destination
          type: varchar(10)
        - name: billed_source
          type: varchar(10)
        - name: is_partial
          type: boolean
        - name: billed_destination_node_type
          type: varchar(30)
        - name: billed_source_node_type
          type: varchar(30)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_consignment_document/
      schema: lake_transit_server
      sortkey: []
      table: transit_consignment_document
    source:
      database: transit_server
      table: transit_consignment_document
  - flow_id: transit_server.transit_consignment_state_logs.v1
    topic_name: postgres.transit_server.public.transit_consignment_state_logs
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: from_state
          type: varchar(30)
        - name: to_state
          type: varchar(30)
        - name: consignment_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_consignment_state_logs/
      schema: lake_transit_server
      sortkey: []
      table: transit_consignment_state_logs
    source:
      database: transit_server
      table: transit_consignment_state_logs
tags:
  - de
  - replicate
  - transit_server_2
template_name: nessie
version: 1
