dag_name: seller_1
dag_type: etl
escalation_priority: low

alert_configs:
  slack:
    - channel: bl-data-alerts-p1
  error_channel_mapping:
    terminated_with_errors: bl-data-spot-interruptions
    spot: bl-data-spot-interruptions

executor:
  config:
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    cpu:
      limit: 1
      request: 0.3
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    memory:
      limit: 500M
      request: 250M
    node_selectors:
      nodetype: airflow-srp-spot
    tolerations:
      - effect: NoSchedule
        key: service
        operator: Equal
        value: airflow-srp
    volume_mounts:
      - mountPath: /usr/local/airflow/plugins
        name: airflow-dags
        subPath: airflow-de/plugins
  type: celery
namespace: de
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ

path: de/replicate/etl/seller_1
paused: false
project_name: replicate
schedule:
  interval: 30 */1 * * *
  start_date: "2024-06-28T00:00:00"
  end_date: "2026-06-28T00:00:00"
schedule_type: fixed
sla: 180 minutes
task_concurrency: 15
poke_interval: 180
redshift_sla_seconds: 10800
emr_sensor: {}
spark:
  aws_conn_id: aws_default
  mode: emr
  nessie_jar_version: 2.1.2
  cluster_reuse: true
  emr_version: 6.11.1
support_files: []
tables:
  - flow_id: seller.apob_facility.v1
    topic_name: postgres.seller.public.apob_facility
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: state_code
          type: varchar(max)
        - name: city_id
          type: int4
        - name: city_name
          type: varchar(max)
        - name: facility_id
          type: int4
        - name: outlet_id
          type: int4
        - name: facility_type
          type: varchar(max)
        - name: polygon_mapping
          type: varchar(max)
        - name: facility_name
          type: varchar(max)
        - name: facility_address
          type: varchar(max)
        - name: is_active
          type: boolean
        - name: rent_document_url
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: status
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.seller.public.apob_facility/
      schema: lake_seller
      sortkey: []
      table: apob_facility
    source:
      database: seller
      table: apob_facility
  - flow_id: seller.campaigns.v1
    topic_name: postgres.seller.public.campaigns
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: name
          type: varchar(255)
        - name: campaign_type
          type: varchar(255)
        - name: status
          type: varchar(255)
        - name: segment_config
          type: varchar(max)
        - name: channel_config
          type: varchar(max)
        - name: scheduled_at
          type: timestamp
        - name: recurrence_rule
          type: varchar(max)
        - name: meta
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.seller.public.campaigns/
      schema: lake_seller
      sortkey: []
      table: campaigns
    source:
      database: seller
      table: campaigns
  - flow_id: seller.cart.v1
    topic_name: postgres.seller.public.cart
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: cart_type
          type: varchar(max)
        - name: data
          type: varchar(max)
        - name: status
          type: varchar(max)
        - name: partner_cart_type
          type: varchar(max)
        - name: partner_cart_id
          type: int8
        - name: created_by
          type: int8
        - name: updated_by
          type: int8
        - name: seller_id
          type: int8
        - name: checkout_time
          type: timestamp
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: meta
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.seller.public.cart/
      schema: lake_seller
      sortkey: []
      table: cart
    source:
      database: seller
      table: cart
  - flow_id: seller.cart_order_mapping.v1
    topic_name: postgres.seller.public.cart_order_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: cart_id
          type: int8
        - name: order_id
          type: int8
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.seller.public.cart_order_mapping/
      schema: lake_seller
      sortkey: []
      table: cart_order_mapping
    source:
      database: seller
      table: cart_order_mapping
  - flow_id: seller.cart_transaction_log.v1
    topic_name: postgres.seller.public.cart_transaction_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: cart_id
          type: int8
        - name: event_type
          type: varchar(max)
        - name: meta
          type: varchar(max)
        - name: created_by
          type: int8
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.seller.public.cart_transaction_log/
      schema: lake_seller
      sortkey: []
      table: cart_transaction_log
    source:
      database: seller
      table: cart_transaction_log
  - flow_id: seller.facilities.v1
    topic_name: postgres.seller.public.facilities
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: facility_id
          type: int4
        - name: outlet_id
          type: int8
        - name: state_code
          type: varchar(max)
        - name: city_id
          type: int4
        - name: facility_type
          type: varchar(max)
        - name: facility_name
          type: varchar(max)
        - name: facility_address
          type: varchar(max)
        - name: is_active
          type: boolean
        - name: attributes
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.seller.public.facilities/
      schema: lake_seller
      sortkey: []
      table: facilities
    source:
      database: seller
      table: facilities
  - flow_id: seller.faq.v1
    topic_name: postgres.seller.public.faq
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: category
          type: varchar(max)
        - name: topic
          type: varchar(max)
        - name: question
          type: varchar(max)
        - name: answer
          type: varchar(max)
        - name: priority
          type: int4
        - name: status
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.seller.public.faq/
      schema: lake_seller
      sortkey: []
      table: faq
    source:
      database: seller
      table: faq
  - flow_id: seller.item_facility_mapping.v1
    topic_name: postgres.seller.public.item_facility_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: seller_id
          type: int8
        - name: item_id
          type: int8
        - name: facility_id
          type: int4
        - name: is_active
          type: boolean
        - name: remarks
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.seller.public.item_facility_mapping/
      schema: lake_seller
      sortkey: []
      table: item_facility_mapping
    source:
      database: seller
      table: item_facility_mapping
  - flow_id: seller.item_outlet_inventory.v1
    topic_name: postgres.seller.public.item_outlet_inventory
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: seller_id
          type: int8
        - name: item_id
          type: int8
        - name: outlet_id
          type: int4
        - name: good_in_hand
          type: int4
        - name: good_in_system
          type: int4
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.seller.public.item_outlet_inventory/
      schema: lake_seller
      sortkey: []
      table: item_outlet_inventory
    source:
      database: seller
      table: item_outlet_inventory
  - flow_id: seller.product_expansion_assessment.v1
    topic_name: postgres.seller.public.product_expansion_assessment
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: spm_id
          type: int8
        - name: item_id
          type: int8
        - name: assessment_start_date
          type: timestamp
        - name: assessment_end_date
          type: timestamp
        - name: expansion_level
          type: varchar(255)
        - name: meta
          type: varchar(max)
        - name: target
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.seller.public.product_expansion_assessment/
      schema: lake_seller
      sortkey: []
      table: product_expansion_assessment
    source:
      database: seller
      table: product_expansion_assessment
  - flow_id: seller.queue_items.v1
    topic_name: postgres.seller.public.queue_items
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: queue_type
          type: varchar(max)
        - name: entity_id
          type: int8
        - name: is_active
          type: boolean
        - name: slot_expiry_time
          type: timestamp
        - name: priority_rank
          type: int8
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.seller.public.queue_items/
      schema: lake_seller
      sortkey: []
      table: queue_items
    source:
      database: seller
      table: queue_items
  - flow_id: seller.rate_card_item.v1
    topic_name: postgres.seller.public.rate_card_item
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: rate_card_id
          type: int4
        - name: rate_type
          type: varchar(max)
        - name: rate_configuration
          type: varchar(max)
        - name: entity_type
          type: varchar(max)
        - name: entity_id
          type: int4
        - name: rate_configuration_version
          type: int4
        - name: start_ts
          type: timestamp
        - name: end_ts
          type: timestamp
        - name: created_by
          type: varchar(max)
        - name: created_ts
          type: timestamp
        - name: updated_by
          type: varchar(max)
        - name: updated_ts
          type: timestamp
        - name: is_override
          type: boolean
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.seller.public.rate_card_item/
      schema: lake_seller
      sortkey: []
      table: rate_card_item
    source:
      database: seller
      table: rate_card_item
  - flow_id: seller.sales_summary.v1
    topic_name: postgres.seller.public.sales_summary
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: seller_id
          type: int8
        - name: item_id
          type: int8
        - name: state_name
          type: varchar(max)
        - name: pos_outlet_id
          type: int4
        - name: pos_outlet_name
          type: varchar(max)
        - name: total_forward_selling_price
          type: float8
        - name: total_returned_selling_price
          type: float8
        - name: total_forward_quantity
          type: int4
        - name: total_return_quantity
          type: int4
        - name: total_net_selling_price
          type: float8
        - name: total_net_quantity
          type: int4
        - name: period_type
          type: varchar(max)
        - name: period_start_date
          type: timestamp
        - name: period_end_date
          type: timestamp
        - name: sale_identifier
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.seller.public.sales_summary/
      schema: lake_seller
      sortkey: []
      table: sales_summary
    source:
      database: seller
      table: sales_summary
  - flow_id: seller.scheduled_jobs.v1
    topic_name: postgres.seller.public.scheduled_jobs
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: job_type
          type: varchar(max)
        - name: name
          type: varchar(max)
        - name: status
          type: varchar(max)
        - name: scheduled_at
          type: timestamp
        - name: data
          type: varchar(max)
        - name: recurrence_rule
          type: varchar(max)
        - name: created_by
          type: int8
        - name: updated_by
          type: int8
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: linked_entity_type
          type: varchar(255)
        - name: linked_entity_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.seller.public.scheduled_jobs/
      schema: lake_seller
      sortkey: []
      table: scheduled_jobs
    source:
      database: seller
      table: scheduled_jobs
  - flow_id: seller.seller.v1
    topic_name: postgres.seller.public.seller
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: gstin_id
          type: varchar(max)
        - name: company_name
          type: varchar(max)
        - name: is_active
          type: boolean
        - name: external_vendor_id
          type: int4
        - name: status
          type: varchar(max)
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.seller.public.seller/
      schema: lake_seller
      sortkey: []
      table: seller
    source:
      database: seller
      table: seller
  - flow_id: seller.seller_advertiser_mapping.v1
    topic_name: postgres.seller.public.seller_advertiser_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: seller_id
          type: int8
        - name: advertiser_id
          type: int8
        - name: is_active
          type: boolean
        - name: updated_by
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.seller.public.seller_advertiser_mapping/
      schema: lake_seller
      sortkey: []
      table: seller_advertiser_mapping
    source:
      database: seller
      table: seller_advertiser_mapping
  - flow_id: seller.seller_apob.v1
    topic_name: postgres.seller.public.seller_apob
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: seller_id
          type: int8
        - name: outlet_id
          type: int4
        - name: facility_id
          type: int4
        - name: arn_number
          type: varchar(max)
        - name: entity_vendor_id
          type: int4
        - name: vendor_reference_id
          type: varchar(max)
        - name: is_active
          type: boolean
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.seller.public.seller_apob/
      schema: lake_seller
      sortkey: []
      table: seller_apob
    source:
      database: seller
      table: seller_apob
  - flow_id: seller.seller_brand_detail.v1
    topic_name: postgres.seller.public.seller_brand_detail
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: seller_id
          type: int8
        - name: brand_name
          type: varchar(max)
        - name: manufacturer_name
          type: varchar(max)
        - name: brand_logo_document_id
          type: int4
        - name: trademark_document_id
          type: int4
        - name: authorisation_document_id
          type: int4
        - name: cms_brand_id
          type: int4
        - name: trademark_number
          type: varchar(max)
        - name: is_active
          type: boolean
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.seller.public.seller_brand_detail/
      schema: lake_seller
      sortkey: []
      table: seller_brand_detail
    source:
      database: seller
      table: seller_brand_detail
  - flow_id: seller.seller_document_info.v1
    topic_name: postgres.seller.public.seller_document_info
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: seller_id
          type: int4
        - name: document_type
          type: varchar(max)
        - name: document_url
          type: varchar(max)
        - name: meta
          type: varchar(max)
        - name: start_date
          type: timestamp
        - name: end_date
          type: timestamp
        - name: document_last_updated_at
          type: timestamp
        - name: document_status
          type: varchar(max)
        - name: is_active
          type: boolean
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: payout_date
          type: date
        - name: last_synced_at
          type: date
        - name: failure_reason
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.seller.public.seller_document_info/
      schema: lake_seller
      sortkey: []
      table: seller_document_info
    source:
      database: seller
      table: seller_document_info
  - flow_id: seller.seller_order_level_pos_sales_info.v1
    topic_name: postgres.seller.public.seller_order_level_pos_sales_info
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: varchar(max)
        - name: seller_id
          type: int8
        - name: invoice_id
          type: varchar(max)
        - name: order_id
          type: int8
        - name: item_id
          type: int8
        - name: order_date
          type: timestamp
        - name: supply_state
          type: varchar(max)
        - name: customer_state
          type: varchar(max)
        - name: supply_city
          type: varchar(max)
        - name: customer_city
          type: varchar(max)
        - name: product_id
          type: int8
        - name: product_name
          type: varchar(max)
        - name: l0_category
          type: varchar(max)
        - name: l1_category
          type: varchar(max)
        - name: l2_category
          type: varchar(max)
        - name: variant_description
          type: varchar(max)
        - name: order_type
          type: varchar(max)
        - name: order_status
          type: varchar(max)
        - name: hsn_code
          type: int8
        - name: igst_percentage
          type: float8
        - name: igst_value
          type: float8
        - name: cgst_percentage
          type: float8
        - name: cgst_value
          type: float8
        - name: cess_percentage
          type: float8
        - name: cess_value
          type: float8
        - name: sgst_percentage
          type: float8
        - name: sgst_value
          type: float8
        - name: quantity
          type: int4
        - name: mrp
          type: float8
        - name: selling_price
          type: float8
        - name: total_tax
          type: float8
        - name: total_bill_amount
          type: float8
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.seller.public.seller_order_level_pos_sales_info/
      schema: lake_seller
      sortkey: []
      table: seller_order_level_pos_sales_info
    source:
      database: seller
      table: seller_order_level_pos_sales_info
  - flow_id: seller.seller_order_level_sales_info.v1
    topic_name: postgres.seller.public.seller_order_level_sales_info
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: seller_id
          type: int8
        - name: invoice_id
          type: varchar(max)
        - name: order_id
          type: int8
        - name: item_id
          type: int8
        - name: order_date
          type: timestamp
        - name: supply_state
          type: varchar(max)
        - name: customer_state
          type: varchar(max)
        - name: supply_city
          type: varchar(max)
        - name: customer_city
          type: varchar(max)
        - name: product_id
          type: int8
        - name: product_name
          type: varchar(max)
        - name: l0_category
          type: varchar(max)
        - name: l1_category
          type: varchar(max)
        - name: l2_category
          type: varchar(max)
        - name: variant_description
          type: varchar(max)
        - name: order_type
          type: varchar(max)
        - name: order_status
          type: varchar(max)
        - name: hsn_code
          type: int8
        - name: igst_percentage
          type: float8
        - name: igst_value
          type: float8
        - name: cgst_percentage
          type: float8
        - name: cgst_value
          type: float8
        - name: cess_percentage
          type: float8
        - name: cess_value
          type: float8
        - name: sgst_percentage
          type: float8
        - name: sgst_value
          type: float8
        - name: quantity
          type: int4
        - name: mrp
          type: float8
        - name: selling_price
          type: float8
        - name: total_tax
          type: float8
        - name: total_bill_amount
          type: float8
        - name: order_level_sale_identifier
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: payout_date
          type: timestamp
        - name: is_active
          type: boolean
        - name: business_category
          type: varchar(max)
        - name: iro_payout_date
          type: timestamp
        - name: variant_id
          type: varchar(max)
        - name: business_gst_name
          type: varchar(max)
        - name: business_gst_number
          type: varchar(max)
        - name: customer_name
          type: varchar(max)
        - name: irn
          type: varchar(max)
        - name: state_gst
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.seller.public.seller_order_level_sales_info/
      schema: lake_seller
      sortkey: []
      table: seller_order_level_sales_info
    source:
      database: seller
      table: seller_order_level_sales_info
  - flow_id: seller.seller_payout_commission.v1
    topic_name: postgres.seller.public.seller_payout_commission
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: action
          type: varchar(max)
        - name: external_vendor_id
          type: int8
        - name: entity_vendor_id
          type: int8
        - name: external_vendor_gst
          type: varchar(max)
        - name: entity_vendor_gst
          type: varchar(max)
        - name: city_id
          type: int4
        - name: item_id
          type: int8
        - name: outlet_id
          type: int4
        - name: trx_type
          type: varchar(max)
        - name: quantity
          type: int4
        - name: amount
          type: float8
        - name: rrn
          type: varchar(max)
        - name: etl_date_ist
          type: timestamp
        - name: order_id
          type: varchar(max)
        - name: ref_message_id
          type: varchar(max)
        - name: amount_with_gst
          type: float8
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.seller.public.seller_payout_commission/
      schema: lake_seller
      sortkey: []
      table: seller_payout_commission
    source:
      database: seller
      table: seller_payout_commission
  - flow_id: seller.seller_product_mappings.v1
    topic_name: postgres.seller.public.seller_product_mappings
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: seller_id
          type: int8
        - name: product_id
          type: int8
        - name: item_id
          type: int8
        - name: selling_price
          type: float8
        - name: landing_price
          type: float8
        - name: mrp
          type: float8
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: enabled_flag
          type: boolean
        - name: current_state
          type: varchar(max)
        - name: meta
          type: varchar(max)
        - name: activation_state
          type: varchar(max)
        - name: business_category_id
          type: int8
        - name: business_category_name
          type: varchar(max)
        - name: cms_brand_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.seller.public.seller_product_mappings/
      schema: lake_seller
      sortkey: []
      table: seller_product_mappings
    source:
      database: seller
      table: seller_product_mappings
  - flow_id: seller.seller_product_request_mappings.v1
    topic_name: postgres.seller.public.seller_product_request_mappings
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: seller_id
          type: int8
        - name: product_id
          type: int8
        - name: request_id
          type: int8
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.seller.public.seller_product_request_mappings/
      schema: lake_seller
      sortkey: []
      table: seller_product_request_mappings
    source:
      database: seller
      table: seller_product_request_mappings
  - flow_id: seller.seller_t_and_c.v1
    topic_name: postgres.seller.public.seller_t_and_c
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: seller_id
          type: int8
        - name: t_and_c_id
          type: int4
        - name: install_ts
          type: timestamp
        - name: status
          type: varchar(255)
        - name: meta
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.seller.public.seller_t_and_c/
      schema: lake_seller
      sortkey: []
      table: seller_t_and_c
    source:
      database: seller
      table: seller_t_and_c
  - flow_id: seller.store_group_definitions.v1
    topic_name: postgres.seller.public.store_group_definitions
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: group_name
          type: varchar(max)
        - name: group_type
          type: varchar(max)
        - name: attributes
          type: varchar(max)
        - name: rules
          type: varchar(max)
        - name: is_active
          type: boolean
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.seller.public.store_group_definitions/
      schema: lake_seller
      sortkey: []
      table: store_group_definitions
    source:
      database: seller
      table: store_group_definitions
  - flow_id: seller.task.v1
    topic_name: postgres.seller.public.task
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: type
          type: varchar(max)
        - name: status
          type: varchar(max)
        - name: reason
          type: varchar(max)
        - name: workflow_id
          type: int8
        - name: external_ticket_id
          type: varchar(max)
        - name: is_active
          type: boolean
        - name: updated_by
          type: varchar(max)
        - name: meta
          type: varchar(max)
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.seller.public.task/
      schema: lake_seller
      sortkey: []
      table: task
    source:
      database: seller
      table: task
  - flow_id: seller.user_seller_mapping.v1
    topic_name: postgres.seller.public.user_seller_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: user_id
          type: int8
        - name: seller_id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.seller.public.user_seller_mapping/
      schema: lake_seller
      sortkey: []
      table: user_seller_mapping
    source:
      database: seller
      table: user_seller_mapping
  - flow_id: seller.workflow.v1
    topic_name: postgres.seller.public.workflow
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: name
          type: varchar(max)
        - name: type
          type: varchar(max)
        - name: status
          type: varchar(max)
        - name: seller_id
          type: int8
        - name: last_task_type
          type: varchar(max)
        - name: action
          type: varchar(max)
        - name: meta
          type: varchar(max)
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.seller.public.workflow/
      schema: lake_seller
      sortkey: []
      table: workflow
    source:
      database: seller
      table: workflow
  - flow_id: seller.recall_request_batch_pro_detail.v1
    topic_name: postgres.seller.public.recall_request_batch_pro_detail
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: recall_request_batch_id
          type: int8
        - name: pro_id
          type: varchar(max)
        - name: status
          type: varchar(max)
        - name: meta
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: created_at
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.seller.public.recall_request_batch_pro_detail/
      schema: lake_seller
      sortkey: []
      table: recall_request_batch_pro_detail
    source:
      database: seller
      table: recall_request_batch_pro_detail
  - flow_id: seller.recall_request_batch_log.v1
    topic_name: postgres.seller.public.recall_request_batch_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: varchar(32)
        - name: recall_request_batch_id
          type: int8
        - name: event_type
          type: varchar(max)
        - name: changed_data
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: created_at
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.seller.public.recall_request_batch_log/
      schema: lake_seller
      sortkey: []
      table: recall_request_batch_log
    source:
      database: seller
      table: recall_request_batch_log
  - flow_id: seller.recall_request_batch_item.v1
    topic_name: postgres.seller.public.recall_request_batch_item
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: recall_request_batch_id
          type: int8
        - name: item_id
          type: int8
        - name: requested_quantity
          type: int4
        - name: billed_quantity
          type: int4
        - name: dispatched_quantity
          type: int4
        - name: mrp
          type: float8
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: created_at
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.seller.public.recall_request_batch_item/
      schema: lake_seller
      sortkey: []
      table: recall_request_batch_item
    source:
      database: seller
      table: recall_request_batch_item
  - flow_id: seller.recall_request_batch.v1
    topic_name: postgres.seller.public.recall_request_batch
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: recall_request_id
          type: int8
        - name: type
          type: varchar(max)
        - name: status
          type: varchar(max)
        - name: tentative_completion_date
          type: timestamp
        - name: courier_date
          type: timestamp
        - name: meta
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: created_by
          type: varchar(max)
        - name: updated_by
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: created_at
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.seller.public.recall_request_batch/
      schema: lake_seller
      sortkey: []
      table: recall_request_batch
    source:
      database: seller
      table: recall_request_batch
  # - flow_id: seller.item_outlet_inventory.v1
  #   topic_name: postgres.seller.public.item_outlet_inventory
  #   redshift_replication: false
  #   sink:
  #     column_dtypes:
  #       - name: id
  #         type: varchar(32)
  #       - name: seller_id
  #         type: int8
  #       - name: item_id
  #         type: int8
  #       - name: outlet_id
  #         type: int4
  #       - name: parent_outlet_id
  #         type: int4
  #       - name: good_quantity
  #         type: int4
  #       - name: created_at
  #         type: timestamp
  #       - name: updated_at
  #         type: timestamp
  #     copy_params:
  #       - FORMAT AS PARQUET
  #     incremental_key: seller_id
  #     load_type: upsert
  #     primary_key: [id]
  #     s3_url: s3://prod-dse-nessie-output/postgres.seller.public.item_outlet_inventory/
  #     schema: lake_seller
  #     sortkey: []
  #     table: item_outlet_inventory
  #   source:
  #     database: seller
  #     table: item_outlet_inventory
  - flow_id: seller.recall_request.v1
    topic_name: postgres.seller.public.recall_request
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: seller_id
          type: int8
        - name: item_id
          type: int8
        - name: outlet_id
          type: int4
        - name: status
          type: varchar(max)
        - name: type
          type: varchar(max)
        - name: process_start_date
          type: timestamp
        - name: meta
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: created_by
          type: varchar(max)
        - name: updated_by
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: created_at
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.seller.public.recall_request/
      schema: lake_seller
      sortkey: []
      table: recall_request
    source:
      database: seller
      table: recall_request
tags:
  - de
  - replicate
  - seller_1
template_name: nessie
version: 1
