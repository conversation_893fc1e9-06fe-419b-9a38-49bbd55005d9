dag_name: merchandising_catalogue_1
dag_type: etl
escalation_priority: low
alert_configs:
  slack:
    - channel: bl-data-alerts-p1
  error_channel_mapping:
    terminated_with_errors: bl-data-spot-interruptions
    user_request: bl-data-spot-interruptions
    spot: bl-data-spot-interruptions
executor:
  config:
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    cpu:
      limit: 1
      request: 0.3
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    memory:
      limit: 500M
      request: 250M
    node_selectors:
      nodetype: airflow-srp-spot
    tolerations:
      - effect: NoSchedule
        key: service
        operator: Equal
        value: airflow-srp
    volume_mounts:
      - mountPath: /usr/local/airflow/plugins
        name: airflow-dags
        subPath: airflow-de/plugins
  type: celery
namespace: de
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ
path: de/replicate/etl/merchandising_catalogue_1
paused: false
project_name: replicate
schedule:
  end_date: "2026-08-27T00:00:00"
  interval: 0 */1 * * *
  start_date: "2025-09-01T00:00:00"
schedule_type: fixed
sla: 60 minutes
task_concurrency: 8
poke_interval: 181
redshift_sla_seconds: 3600
emr_sensor: {}
spark:
  aws_conn_id: aws_default
  mode: emr
  nessie_jar_version: 2.1.2
  cluster_reuse: true
  emr_version: 6.11.1
support_files: []
tables:
  - flow_id: merchandising_catalogue.asset_instances.v1
    topic_name: postgres.merchandising_catalogue.public.asset_instances
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: asset_type_id
          type: int4
        - name: catalogue_asset_type_mapping_id
          type: int4
        - name: data
          type: varchar(max)
        - name: enabled_flag
          type: boolean
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: created_by
          type: varchar(max)
        - name: updated_by
          type: varchar(max)
        - name: value
          type: varchar(max)
        - name: locality_id
          type: int8
        - name: locality_type
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.merchandising_catalogue.public.asset_instances/
      schema: lake_merchandising_catalogue
      sortkey: []
      table: asset_instances
    source:
      database: merchandising_catalogue
      table: asset_instances
  - flow_id: merchandising_catalogue.asset_types.v1
    topic_name: postgres.merchandising_catalogue.public.asset_types
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: type
          type: varchar(max)
        - name: enabled_flag
          type: boolean
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: created_by
          type: varchar(max)
        - name: updated_by
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.merchandising_catalogue.public.asset_types/
      schema: lake_merchandising_catalogue
      sortkey: []
      table: asset_types
    source:
      database: merchandising_catalogue
      table: asset_types
  - flow_id: merchandising_catalogue.attribute_instances.v1
    topic_name: postgres.merchandising_catalogue.public.attribute_instances
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: attribute_type_id
          type: int4
        - name: value
          type: varchar(max)
        - name: enabled_flag
          type: boolean
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: created_by
          type: varchar(max)
        - name: updated_by
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.merchandising_catalogue.public.attribute_instances/
      schema: lake_merchandising_catalogue
      sortkey: []
      table: attribute_instances
    source:
      database: merchandising_catalogue
      table: attribute_instances
  - flow_id: merchandising_catalogue.attribute_types.v1
    topic_name: postgres.merchandising_catalogue.public.attribute_types
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: attribute_key
          type: varchar(max)
        - name: value_type
          type: varchar(max)
        - name: enabled_flag
          type: boolean
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: created_by
          type: varchar(max)
        - name: updated_by
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.merchandising_catalogue.public.attribute_types/
      schema: lake_merchandising_catalogue
      sortkey: []
      table: attribute_types
    source:
      database: merchandising_catalogue
      table: attribute_types
  - flow_id: merchandising_catalogue.catalogue_asset_type_mappings.v1
    topic_name: postgres.merchandising_catalogue.public.catalogue_asset_type_mappings
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: catalogue_type_id
          type: int4
        - name: asset_type_id
          type: int4
        - name: mapping_type
          type: varchar(max)
        - name: enabled_flag
          type: boolean
        - name: updated_by
          type: varchar(max)
        - name: created_by
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.merchandising_catalogue.public.catalogue_asset_type_mappings/
      schema: lake_merchandising_catalogue
      sortkey: []
      table: catalogue_asset_type_mappings
    source:
      database: merchandising_catalogue
      table: catalogue_asset_type_mappings
  - flow_id: merchandising_catalogue.catalogue_attribute_type_mappings.v1
    topic_name: postgres.merchandising_catalogue.public.catalogue_attribute_type_mappings
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: catalogue_type_id
          type: int4
        - name: attribute_type_id
          type: int4
        - name: enabled_flag
          type: boolean
        - name: updated_by
          type: varchar(max)
        - name: created_by
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.merchandising_catalogue.public.catalogue_attribute_type_mappings/
      schema: lake_merchandising_catalogue
      sortkey: []
      table: catalogue_attribute_type_mappings
    source:
      database: merchandising_catalogue
      table: catalogue_attribute_type_mappings
  - flow_id: merchandising_catalogue.catalogue_instance_asset_mappings.v1
    topic_name: postgres.merchandising_catalogue.public.catalogue_instance_asset_mappings
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: asset_id
          type: int4
        - name: catalogue_instance_id
          type: int4
        - name: enabled_flag
          type: boolean
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: created_by
          type: varchar(max)
        - name: updated_by
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.merchandising_catalogue.public.catalogue_instance_asset_mappings/
      schema: lake_merchandising_catalogue
      sortkey: []
      table: catalogue_instance_asset_mappings
    source:
      database: merchandising_catalogue
      table: catalogue_instance_asset_mappings
  - flow_id: merchandising_catalogue.catalogue_instance_attribute_mappings.v1
    topic_name: postgres.merchandising_catalogue.public.catalogue_instance_attribute_mappings
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: attribute_id
          type: int4
        - name: catalogue_instance_id
          type: int4
        - name: enabled_flag
          type: boolean
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: created_by
          type: varchar(max)
        - name: updated_by
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.merchandising_catalogue.public.catalogue_instance_attribute_mappings/
      schema: lake_merchandising_catalogue
      sortkey: []
      table: catalogue_instance_attribute_mappings
    source:
      database: merchandising_catalogue
      table: catalogue_instance_attribute_mappings
  - flow_id: merchandising_catalogue.catalogue_instances.v1
    topic_name: postgres.merchandising_catalogue.public.catalogue_instances
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: name
          type: varchar(max)
        - name: catalogue_type_id
          type: int4
        - name: tenant_id
          type: int4
        - name: share_link
          type: varchar(max)
        - name: enabled_flag
          type: boolean
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: created_by
          type: varchar(max)
        - name: updated_by
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.merchandising_catalogue.public.catalogue_instances/
      schema: lake_merchandising_catalogue
      sortkey: []
      table: catalogue_instances
    source:
      database: merchandising_catalogue
      table: catalogue_instances
  - flow_id: merchandising_catalogue.catalogue_types.v1
    topic_name: postgres.merchandising_catalogue.public.catalogue_types
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: name
          type: varchar(max)
        - name: version
          type: int4
        - name: tenant_id
          type: int4
        - name: enabled_flag
          type: boolean
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: created_by
          type: varchar(max)
        - name: updated_by
          type: varchar(max)
        - name: details
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.merchandising_catalogue.public.catalogue_types/
      schema: lake_merchandising_catalogue
      sortkey: []
      table: catalogue_types
    source:
      database: merchandising_catalogue
      table: catalogue_types
tags:
  - de
  - replicate
  - merchandising_catalogue_1
template_name: nessie
version: 1
