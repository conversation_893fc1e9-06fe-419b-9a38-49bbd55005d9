dag_name: warehouse_metrics_1
dag_type: etl
escalation_priority: low
alert_configs:
  slack:
    - channel: bl-data-alerts-p1
  error_channel_mapping:
    terminated_with_errors: bl-data-spot-interruptions
    spot: bl-data-spot-interruptions
executor:
  config:
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    cpu:
      limit: 1
      request: 0.3
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    memory:
      limit: 500M
      request: 250M
    node_selectors:
      nodetype: airflow-srp-spot
    tolerations:
      - effect: NoSchedule
        key: service
        operator: Equal
        value: airflow-srp
    volume_mounts:
      - mountPath: /usr/local/airflow/plugins
        name: airflow-dags
        subPath: airflow-de/plugins
  type: celery
namespace: de
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ
path: de/replicate/etl/warehouse_metrics_1
paused: false
project_name: replicate
schedule:
  end_date: "2026-02-19T00:00:00"
  interval: 0 */3 * * *
  start_date: "2025-02-24T00:00:00"
schedule_type: fixed
sla: 180 minutes
task_concurrency: 3
poke_interval: 180
redshift_sla_seconds: 10800
emr_sensor: {}
spark:
  aws_conn_id: aws_default
  mode: emr
  nessie_jar_version: 2.1.2
  cluster_reuse: true
  emr_version: 6.11.1
support_files: []
tables:
  - flow_id: warehouse_metrics.employee_activity_tracker.v1
    topic_name: postgres.warehouse_metrics.public.employee_activity_tracker
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: employee_id
          type: varchar(50)
        - name: outlet_id
          type: int4
        - name: activity_date
          type: date
        - name: activity_time_slot
          type: timestamp
        - name: pick_list_completed_count
          type: int4
        - name: pick_list_picked_item_count
          type: int4
        - name: pick_list_picked_item_quantity
          type: int4
        - name: pick_list_item_not_found_count
          type: int4
        - name: pick_list_item_not_found_quantity
          type: int4
        - name: pick_list_raise_audit_count
          type: int4
        - name: pick_list_item_excess_marked_count
          type: int4
        - name: pick_list_item_short_marked_count
          type: int4
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: packaging_completed_item_count
          type: int4
        - name: packaging_completed_item_quantity
          type: int4
        - name: packaging_completed_container_count
          type: int4
        - name: packaging_excess_marked_quantity
          type: int4
        - name: packaging_short_marked_quantity
          type: int4
        - name: packaging_meta
          type: varchar(max)
        - name: meta
          type: varchar(max)
        - name: put_list_completed_count
          type: int4
        - name: put_list_put_awayed_item_quantity
          type: int4
        - name: put_list_put_awayed_item_count
          type: int4
        - name: put_list_escalated_count
          type: int4
        - name: put_list_escalated_item_quantity
          type: int4
        - name: put_list_escalated_item_count
          type: int4
        - name: putaway_meta
          type: varchar(max)
        - name: unloaded_item_count
          type: int4
        - name: unloaded_item_quantity
          type: int4
        - name: unloading_meta
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.warehouse_metrics.public.employee_activity_tracker/
      schema: lake_warehouse_metrics
      sortkey: []
      table: employee_activity_tracker
    source:
      database: warehouse_metrics
      table: employee_activity_tracker
  - flow_id: warehouse_metrics.outbound_demand.v1
    topic_name: postgres.warehouse_metrics.public.outbound_demand
    redshift_replication: false
    sink:
      column_dtypes:
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: demand_id
          type: varchar(255)
        - name: dispatch_time
          type: timestamp
        - name: dispatch_date
          type: date
        - name: total_container_count
          type: int4
        - name: sorted_container_count
          type: int4
        - name: dispatched_container_count
          type: int4
        - name: billed_container_count
          type: int4
        - name: total_item_quantity
          type: int4
        - name: picked_item_quantity
          type: int4
        - name: outlet_id
          type: int4
        - name: outlet_name
          type: varchar(255)
        - name: destination_outlet_id
          type: int4
        - name: destination_outlet_name
          type: varchar(255)
        - name: item_meta
          type: varchar(max)
        - name: consignment_meta
          type: varchar(max)
        - name: cancelled_item_quantity
          type: int4
        - name: item_not_found_quantity
          type: int4
        - name: created_pick_list_count
          type: int4
        - name: assigned_pick_list_count
          type: int4
        - name: completed_pick_list_count
          type: int4
        - name: cancelled_pick_list_count
          type: int4
        - name: picked_container_count
          type: int4
        - name: packed_container_count
          type: int4
        - name: cancelled_pick_list_details
          type: varchar(max)
        - name: missing_picked_container_count
          type: int4
        - name: missing_packed_container_count
          type: int4
        - name: shift_end_time
          type: timestamp
        - name: total_order_item_quantity
          type: int4
        - name: created_line_items
          type: int4
        - name: fully_picked_line_items
          type: int4
        - name: packed_item_quantity
          type: int4
        - name: missing_picked_item_quantity
          type: int4
        - name: missing_packed_item_quantity
          type: int4
        - name: pick_items_suggested_location_count
          type: int4
        - name: pick_items_not_found_location_count
          type: int4
        - name: outbound_item_quantity
          type: int4
        - name: outbound_sorted_item_quantity
          type: int4
        - name: created_order_line_items
          type: int4
        - name: meta
          type: varchar(max)
        - name: outbound_dispatched_item_quantity
          type: int4
        - name: cancelled_order_item_quantity
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: demand_id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [demand_id]
      s3_url: s3://prod-dse-nessie-output/postgres.warehouse_metrics.public.outbound_demand/
      schema: lake_warehouse_metrics
      sortkey: []
      table: outbound_demand
    source:
      database: warehouse_metrics
      table: outbound_demand
tags:
  - de
  - replicate
  - warehouse_metrics_1
template_name: nessie
version: 1
