dag_name: promo_service_1
dag_type: etl
escalation_priority: low

alert_configs:
  slack:
    - channel: bl-data-alerts-p1
  error_channel_mapping:
    terminated_with_errors: bl-data-spot-interruptions
    spot: bl-data-spot-interruptions

executor:
  config:
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    cpu:
      limit: 1
      request: 0.3
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    memory:
      limit: 500M
      request: 250M
    node_selectors:
      nodetype: airflow-srp-spot
    tolerations:
      - effect: NoSchedule
        key: service
        operator: Equal
        value: airflow-srp
    volume_mounts:
      - mountPath: /usr/local/airflow/plugins
        name: airflow-dags
        subPath: airflow-de/plugins
  type: celery
namespace: de
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ

path: de/replicate/etl/promo_service_1
paused: false
project_name: replicate
schedule:
  interval: 29,59 0-19,22 * * *
  start_date: "2023-01-16T00:00:00"
  end_date: '2026-08-15T00:00:00'
schedule_type: fixed
sla: 180 minutes
task_concurrency: 9
poke_interval: 180
redshift_sla_seconds: 10800
emr_sensor: {}
nessie_dag_split: true
spark:
  aws_conn_id: aws_application_dse_airflow
  mode: emr
  nessie_jar_version: 2.1.2
  cluster_reuse: true
  emr_version: 6.11.1
support_files: []
tables:
  - flow_id: promo_service.campaign_conditions.v1
    topic_name: mysql.promo_service.promo_service.campaign_conditions
    redshift_replication: false
    sink:
      column_dtypes:
        - name: condition_id
          type: int8
        - name: name
          type: varchar(45)
        - name: start_datetime
          type: timestamp
        - name: end_datetime
          type: timestamp
        - name: is_active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: campaign_id
          type: int8
        - name: priority
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: condition_id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [condition_id]
      s3_url: s3://prod-dse-nessie-output/mysql.promo_service.promo_service.campaign_conditions/
      schema: lake_promo_service
      sortkey: []
      table: campaign_conditions
    source:
      database: promo_service
      table: campaign_conditions
  - flow_id: promo_service.campaign_expressions.v1
    topic_name: mysql.promo_service.promo_service.campaign_expressions
    redshift_replication: false
    sink:
      column_dtypes:
        - name: expression_id
          type: int8
        - name: expression_operator
          type: varchar
        - name: expression_value
          type: varchar(500)
        - name: expression_param_values
          type: varchar(500)
        - name: expression_class_name
          type: varchar(100)
        - name: is_active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: condition_id
          type: int8
        - name: expression_class_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: expression_id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [expression_id]
      s3_url: s3://prod-dse-nessie-output/mysql.promo_service.promo_service.campaign_expressions/
      schema: lake_promo_service
      sortkey: []
      table: campaign_expressions
    source:
      database: promo_service
      table: campaign_expressions
  - flow_id: promo_service.campaign_history.v1
    topic_name: mysql.promo_service.promo_service.campaign_history
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: campaign_id
          type: int8
        - name: updated_table_name
          type: varchar(30)
        - name: old_data_json
          type: varchar(max)
        - name: new_data_json
          type: varchar(max)
        - name: updated_by
          type: int8
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.promo_service.promo_service.campaign_history/
      schema: lake_promo_service
      sortkey: []
      table: campaign_history
    source:
      database: promo_service
      table: campaign_history
  - flow_id: promo_service.campaigns.v1
    topic_name: mysql.promo_service.promo_service.campaigns
    redshift_replication: false
    sink:
      column_dtypes:
        - name: campaign_id
          type: int8
        - name: name
          type: varchar(45)
        - name: start_datetime
          type: timestamp
        - name: end_datetime
          type: timestamp
        - name: is_active
          type: int4
        - name: is_logged_out_user_campaign
          type: int4
        - name: priority
          type: int8
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: campaign_type
          type: varchar
        - name: campaign_type_id
          type: int8
        - name: promo_condition_id
          type: int8
        - name: description
          type: varchar(500)
        - name: bucket_type
          type: varchar
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: campaign_id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [campaign_id]
      s3_url: s3://prod-dse-nessie-output/mysql.promo_service.promo_service.campaigns/
      schema: lake_promo_service
      sortkey: []
      table: campaigns
    source:
      database: promo_service
      table: campaigns
  - flow_id: promo_service.conditions.v1
    topic_name: mysql.promo_service.promo_service.conditions
    redshift_replication: false
    sink:
      column_dtypes:
        - name: condition_id
          type: int8
        - name: name
          type: varchar(500)
        - name: is_active
          type: int4
        - name: priority
          type: int4
        - name: promo_id
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: condition_id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [condition_id]
      s3_url: s3://prod-dse-nessie-output/mysql.promo_service.promo_service.conditions/
      schema: lake_promo_service
      sortkey: []
      table: conditions
    source:
      database: promo_service
      table: conditions
  - flow_id: promo_service.expressions.v1
    topic_name: mysql.promo_service.promo_service.expressions
    redshift_replication: false
    sink:
      column_dtypes:
        - name: expression_id
          type: int8
        - name: expression_operator
          type: varchar
        - name: expression_value
          type: varchar(500)
        - name: expression_param_values
          type: varchar(max)
        - name: failure_message
          type: varchar(500)
        - name: condition_id
          type: int8
        - name: expression_class_id
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: failure_title
          type: varchar(100)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: expression_id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [expression_id]
      s3_url: s3://prod-dse-nessie-output/mysql.promo_service.promo_service.expressions/
      schema: lake_promo_service
      sortkey: []
      table: expressions
    source:
      database: promo_service
      table: expressions
  - flow_id: promo_service.offer_budget.v1
    topic_name: mysql.promo_service.promo_service.offer_budget
    redshift_replication: false
    sink:
      column_dtypes:
        - name: budget_id
          type: int8
        - name: offer_id
          type: int8
        - name: offer_type
          type: varchar
        - name: entity_id
          type: int8
        - name: entity_type
          type: varchar
        - name: share
          type: decimal(5, 2)
        - name: budget
          type: decimal(15, 2)
        - name: consumption
          type: decimal(15, 2)
        - name: pids
          type: varchar(500)
        - name: is_active
          type: int4
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: budget_id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [budget_id]
      s3_url: s3://prod-dse-nessie-output/mysql.promo_service.promo_service.offer_budget/
      schema: lake_promo_service
      sortkey: []
      table: offer_budget
    source:
      database: promo_service
      table: offer_budget
  - flow_id: promo_service.offers.v1
    topic_name: mysql.promo_service.promo_service.offers
    redshift_replication: false
    sink:
      column_dtypes:
        - name: offer_id
          type: int8
        - name: offer_type
          type: varchar
        - name: value
          type: decimal(14, 3)
        - name: days_to_expire
          type: int4
        - name: max_offer
          type: decimal(14, 3)
        - name: condition_id
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: is_active
          type: int4
        - name: offer_group_id
          type: int8
        - name: applicable_on
          type: varchar
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: offer_id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [offer_id]
      s3_url: s3://prod-dse-nessie-output/mysql.promo_service.promo_service.offers/
      schema: lake_promo_service
      sortkey: []
      table: offers
    source:
      database: promo_service
      table: offers
  - flow_id: promo_service.promo_codes.v1
    topic_name: mysql.promo_service.promo_service.promo_codes
    redshift_replication: false
    sink:
      column_dtypes:
        - name: code_id
          type: int8
        - name: code
          type: varchar(40)
        - name: promo_id
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: is_active
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: code_id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [code_id]
      s3_url: s3://prod-dse-nessie-output/mysql.promo_service.promo_service.promo_codes/
      schema: lake_promo_service
      sortkey: []
      table: promo_codes
    source:
      database: promo_service
      table: promo_codes
  - flow_id: promo_service.promos.v1
    topic_name: mysql.promo_service.promo_service.promos
    redshift_replication: false
    sink:
      column_dtypes:
        - name: promo_id
          type: int8
        - name: name
          type: varchar(32)
        - name: start_datetime
          type: timestamp
        - name: end_datetime
          type: timestamp
        - name: is_active
          type: int4
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: service
          type: varchar
        - name: rate_card
          type: varchar
        - name: auto_apply
          type: int4
        - name: is_silent
          type: int4
        - name: priority
          type: int4
        - name: created_by
          type: int8
        - name: description
          type: varchar(500)
        - name: group_id
          type: int8
        - name: is_promoted
          type: int4
        - name: promo_type
          type: varchar
        - name: approval_status
          type: varchar
        - name: approved_by
          type: int8
        - name: approved_at
          type: timestamp
        - name: rate_card_group
          type: varchar(32)
        - name: rate_card_category
          type: varchar(32)
        - name: business_name
          type: varchar
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: promo_id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [promo_id]
      s3_url: s3://prod-dse-nessie-output/mysql.promo_service.promo_service.promos/
      schema: lake_promo_service
      sortkey: []
      table: promos
    source:
      database: promo_service
      table: promos
  - flow_id: promo_service.salts.v1
    topic_name: mysql.promo_service.promo_service.salts
    redshift_replication: false
    sink:
      column_dtypes:
        - name: salt_id
          type: int8
        - name: service
          type: varchar
        - name: salt_hash_key
          type: varchar(32)
        - name: source
          type: varchar(32)
        - name: start_datetime
          type: timestamp
        - name: end_datetime
          type: timestamp
        - name: status
          type: varchar
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_by
          type: int8
        - name: rate_card
          type: varchar
        - name: discount_type
          type: varchar
        - name: salt_type
          type: varchar
        - name: business_name
          type: varchar
        - name: name
          type: varchar(60)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: salt_id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [salt_id]
      s3_url: s3://prod-dse-nessie-output/mysql.promo_service.promo_service.salts/
      schema: lake_promo_service
      sortkey: []
      table: salts
    source:
      database: promo_service
      table: salts
  - flow_id: promo_service.user.v1
    topic_name: mysql.promo_service.promo_service.user
    redshift_replication: false
    sink:
      column_dtypes:
        - name: user_id
          type: int8
        - name: email
          type: varchar(255)
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: is_active
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: user_id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [user_id]
      s3_url: s3://prod-dse-nessie-output/mysql.promo_service.promo_service.user/
      schema: lake_promo_service
      sortkey: []
      table: user
    source:
      database: promo_service
      table: user
tags:
  - de
  - replicate
  - promo_service_1
template_name: nessie
version: 1
