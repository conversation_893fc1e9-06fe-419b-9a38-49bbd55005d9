dag_name: wms_3
dag_type: etl
escalation_priority: low

alert_configs:
  slack:
    - channel: bl-data-alerts-p1
  error_channel_mapping:
    terminated_with_errors: bl-data-spot-interruptions
    spot: bl-data-spot-interruptions

executor:
  config:
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    cpu:
      limit: 1
      request: 0.3
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    memory:
      limit: 500M
      request: 250M
    node_selectors:
      nodetype: airflow-srp-spot
    tolerations:
      - effect: NoSchedule
        key: service
        operator: Equal
        value: airflow-srp
    volume_mounts:
      - mountPath: /usr/local/airflow/plugins
        name: airflow-dags
        subPath: airflow-de/plugins
  type: celery
namespace: de
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ

path: de/replicate/etl/wms_3
paused: false
project_name: replicate
schedule:
  interval: 12,27,42,57 * * * *
  start_date: "2024-04-15T00:00:00"
  end_date: '2026-08-15T00:00:00'
schedule_type: fixed
sla: 180 minutes
task_concurrency: 15
poke_interval: 180
redshift_sla_seconds: 10800
emr_sensor: {}
nessie_dag_split: true
spark:
  aws_conn_id: aws_default
  mode: emr
  nessie_jar_version: 2.1.2
  cluster_reuse: true
  emr_version: 6.11.1
support_files: []
tables:
  - flow_id: wms.app_layout.v1
    topic_name: mysql.wms.wms.app_layout
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: layout_name
          type: varchar(50)
        - name: layout_display_name
          type: varchar(50)
        - name: layout_description
          type: varchar(200)
        - name: client_type
          type: varchar(50)
        - name: access_groups
          type: varchar(max)
        - name: parent_layout_id
          type: int8
        - name: app_min_version
          type: int8
        - name: app_max_version
          type: int8
        - name: rank
          type: int8
        - name: meta
          type: varchar(max)
        - name: created_by
          type: varchar(50)
        - name: updated_by
          type: varchar(50)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: type
          type: varchar(50)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.wms.wms.app_layout/
      schema: lake_wms
      sortkey: []
      table: app_layout
    source:
      database: wms
      table: app_layout
  - flow_id: wms.bad_stock_container.v1
    topic_name: mysql.wms.wms.bad_stock_container
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: outlet_id
          type: int8
        - name: container_id
          type: varchar(255)
        - name: source_flow
          type: varchar(50)
        - name: state
          type: varchar(50)
        - name: inventory_bucket
          type: varchar(50)
        - name: is_rtv_eligible
          type: int4
        - name: current_zone
          type: varchar(255)
        - name: meta
          type: varchar(max)
        - name: created_by
          type: varchar(50)
        - name: updated_by
          type: varchar(50)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.wms.wms.bad_stock_container/
      schema: lake_wms
      sortkey: []
      table: bad_stock_container
    source:
      database: wms
      table: bad_stock_container
  - flow_id: wms.bad_stock_item.v1
    topic_name: mysql.wms.wms.bad_stock_item
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: outlet_id
          type: int8
        - name: variant_id
          type: varchar(36)
        - name: entity_vendor_id
          type: int8
        - name: source_flow
          type: varchar(50)
        - name: bad_reason
          type: varchar(50)
        - name: quantity
          type: int8
        - name: handover_recieved_quantity
          type: int8
        - name: inventory_bucket
          type: varchar(50)
        - name: is_rtv_eligible
          type: int4
        - name: bad_stock_container_id
          type: int8
        - name: item_details
          type: varchar(max)
        - name: meta
          type: varchar(max)
        - name: created_by
          type: varchar(50)
        - name: updated_by
          type: varchar(50)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.wms.wms.bad_stock_item/
      schema: lake_wms
      sortkey: []
      table: bad_stock_item
    source:
      database: wms
      table: bad_stock_item
  - flow_id: wms.dispatch_consignment_state_log.v1
    topic_name: mysql.wms.wms.dispatch_consignment_state_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: dispatch_consignment_id
          type: int8
        - name: state
          type: varchar(50)
        - name: created_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.wms.wms.dispatch_consignment_state_log/
      schema: lake_wms
      sortkey: []
      table: dispatch_consignment_state_log
    source:
      database: wms
      table: dispatch_consignment_state_log
  - flow_id: wms.dispatch_trip_activity.v1
    topic_name: mysql.wms.wms.dispatch_trip_activity
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: dispatch_trip_id
          type: int8
        - name: activity_type
          type: varchar(50)
        - name: state
          type: varchar(50)
        - name: assigned_to
          type: varchar(50)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.wms.wms.dispatch_trip_activity/
      schema: lake_wms
      sortkey: []
      table: dispatch_trip_activity
    source:
      database: wms
      table: dispatch_trip_activity
  - flow_id: wms.dispatch_trip_state_log.v1
    topic_name: mysql.wms.wms.dispatch_trip_state_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: dispatch_trip_id
          type: int8
        - name: state
          type: varchar(50)
        - name: created_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.wms.wms.dispatch_trip_state_log/
      schema: lake_wms
      sortkey: []
      table: dispatch_trip_state_log
    source:
      database: wms
      table: dispatch_trip_state_log
  - flow_id: wms.entity_config_mappings.v1
    topic_name: mysql.wms.wms.entity_config_mappings
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: entity_type
          type: varchar(50)
        - name: entity_id
          type: varchar(50)
        - name: config_id
          type: int8
        - name: value
          type: varchar(max)
        - name: created_by
          type: varchar(50)
        - name: updated_by
          type: varchar(50)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.wms.wms.entity_config_mappings/
      schema: lake_wms
      sortkey: []
      table: entity_config_mappings
    source:
      database: wms
      table: entity_config_mappings
  - flow_id: wms.external_order_activity_invoice_tracker.v1
    topic_name: mysql.wms.wms.external_order_activity_invoice_tracker
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: outlet_id
          type: int8
        - name: activity_id
          type: int8
        - name: order_type
          type: varchar(50)
        - name: order_id
          type: varchar(50)
        - name: transaction_id
          type: varchar(50)
        - name: invoice_details
          type: varchar(max)
        - name: state
          type: varchar(50)
        - name: meta
          type: varchar(max)
        - name: created_by
          type: varchar(50)
        - name: updated_by
          type: varchar(50)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.wms.wms.external_order_activity_invoice_tracker/
      schema: lake_wms
      sortkey: []
      table: external_order_activity_invoice_tracker
    source:
      database: wms
      table: external_order_activity_invoice_tracker
  - flow_id: wms.handover_qc_activity.v1
    topic_name: mysql.wms.wms.handover_qc_activity
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: outlet_id
          type: int8
        - name: activity_type
          type: varchar(50)
        - name: assigned_to
          type: varchar(50)
        - name: state
          type: varchar(50)
        - name: meta
          type: varchar(max)
        - name: created_by
          type: varchar(50)
        - name: updated_by
          type: varchar(50)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.wms.wms.handover_qc_activity/
      schema: lake_wms
      sortkey: []
      table: handover_qc_activity
    source:
      database: wms
      table: handover_qc_activity
  - flow_id: wms.handover_qc_activity_container.v1
    topic_name: mysql.wms.wms.handover_qc_activity_container
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: qc_activity_id
          type: int8
        - name: container_id
          type: varchar(255)
        - name: type
          type: varchar(50)
        - name: output_category
          type: varchar(50)
        - name: current_zone
          type: varchar(50)
        - name: meta
          type: varchar(max)
        - name: created_by
          type: varchar(50)
        - name: updated_by
          type: varchar(50)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: state
          type: varchar(50)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.wms.wms.handover_qc_activity_container/
      schema: lake_wms
      sortkey: []
      table: handover_qc_activity_container
    source:
      database: wms
      table: handover_qc_activity_container
  - flow_id: wms.handover_qc_activity_item.v1
    topic_name: mysql.wms.wms.handover_qc_activity_item
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: qc_activity_id
          type: int8
        - name: variant_id
          type: varchar(36)
        - name: entity_id
          type: varchar(255)
        - name: entity_type
          type: varchar(50)
        - name: quantity
          type: int8
        - name: attributed_quantity
          type: int8
        - name: item_processing_type
          type: varchar(50)
        - name: item_processing_label
          type: varchar(50)
        - name: inventory_bucket
          type: varchar(50)
        - name: activity_container_id
          type: int8
        - name: item_details
          type: varchar(max)
        - name: meta
          type: varchar(max)
        - name: created_by
          type: varchar(50)
        - name: updated_by
          type: varchar(50)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.wms.wms.handover_qc_activity_item/
      schema: lake_wms
      sortkey: []
      table: handover_qc_activity_item
    source:
      database: wms
      table: handover_qc_activity_item
  - flow_id: wms.inbound_job_request.v1
    topic_name: mysql.wms.wms.inbound_job_request
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: outlet_id
          type: int8
        - name: job_id
          type: varchar(50)
        - name: type
          type: varchar(50)
        - name: sub_type
          type: varchar(50)
        - name: state
          type: varchar(50)
        - name: data
          type: varchar(max)
        - name: created_by
          type: varchar(50)
        - name: updated_by
          type: varchar(50)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.wms.wms.inbound_job_request/
      schema: lake_wms
      sortkey: []
      table: inbound_job_request
    source:
      database: wms
      table: inbound_job_request
  - flow_id: wms.internal_movement_container.v1
    topic_name: mysql.wms.wms.internal_movement_container
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: outlet_id
          type: int8
        - name: container_id
          type: varchar(50)
        - name: state
          type: varchar(50)
        - name: type
          type: varchar(50)
        - name: drop_zone_name
          type: varchar(50)
        - name: assigned_to
          type: varchar(50)
        - name: meta
          type: varchar(max)
        - name: created_by
          type: varchar(50)
        - name: updated_by
          type: varchar(50)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.wms.wms.internal_movement_container/
      schema: lake_wms
      sortkey: []
      table: internal_movement_container
    source:
      database: wms
      table: internal_movement_container
  - flow_id: wms.internal_movement_container_item.v1
    topic_name: mysql.wms.wms.internal_movement_container_item
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: internal_movement_container_id
          type: int8
        - name: variant_id
          type: varchar(50)
        - name: item_processing_type
          type: varchar(50)
        - name: entity_vendor_id
          type: int8
        - name: batch
          type: date
        - name: quantity
          type: int8
        - name: meta
          type: varchar(max)
        - name: created_by
          type: varchar(50)
        - name: updated_by
          type: varchar(50)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.wms.wms.internal_movement_container_item/
      schema: lake_wms
      sortkey: []
      table: internal_movement_container_item
    source:
      database: wms
      table: internal_movement_container_item
  - flow_id: wms.ob_pkg_external_order_activity.v1
    topic_name: mysql.wms.wms.ob_pkg_external_order_activity
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: entity_id
          type: int8
        - name: outlet_id
          type: int8
        - name: merchant_id
          type: int8
        - name: order_type
          type: varchar(50)
        - name: order_count
          type: int8
        - name: completed_order_count
          type: int8
        - name: state
          type: varchar(50)
        - name: type
          type: varchar(50)
        - name: processed_at
          type: timestamp
        - name: enqueued
          type: int4
        - name: meta
          type: varchar(max)
        - name: created_by
          type: varchar(50)
        - name: updated_by
          type: varchar(50)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: billing_type
          type: varchar(50)
        - name: transaction_id
          type: varchar(50)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.wms.wms.ob_pkg_external_order_activity/
      schema: lake_wms
      sortkey: []
      table: ob_pkg_external_order_activity
    source:
      database: wms
      table: ob_pkg_external_order_activity
  - flow_id: wms.outbound_config.v1
    topic_name: mysql.wms.wms.outbound_config
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: source_outlet_id
          type: int8
        - name: destination_outlet_id
          type: int8
        - name: config_type
          type: varchar(48)
        - name: config
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: varchar(50)
        - name: active
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.wms.wms.outbound_config/
      schema: lake_wms
      sortkey: []
      table: outbound_config
    source:
      database: wms
      table: outbound_config
  - flow_id: wms.outbound_config_log.v1
    topic_name: mysql.wms.wms.outbound_config_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: outbound_config_id
          type: int8
        - name: source_outlet_id
          type: int8
        - name: destination_outlet_id
          type: int8
        - name: config_type
          type: varchar(48)
        - name: config
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(50)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.wms.wms.outbound_config_log/
      schema: lake_wms
      sortkey: []
      table: outbound_config_log
    source:
      database: wms
      table: outbound_config_log
  - flow_id: wms.outlet_vehicle_entry.v1
    topic_name: mysql.wms.wms.outlet_vehicle_entry
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: outlet_id
          type: int8
        - name: entry_id
          type: varchar(100)
        - name: truck_number
          type: varchar(32)
        - name: entry_type
          type: varchar(32)
        - name: entity_id
          type: int8
        - name: entry_time
          type: timestamp
        - name: exit_time
          type: timestamp
        - name: created_by
          type: varchar(50)
        - name: updated_by
          type: varchar(50)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.wms.wms.outlet_vehicle_entry/
      schema: lake_wms
      sortkey: []
      table: outlet_vehicle_entry
    source:
      database: wms
      table: outlet_vehicle_entry
  - flow_id: wms.pick_list.v1
    topic_name: mysql.wms.wms.pick_list
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: wave_id
          type: int8
        - name: outlet_id
          type: int8
        - name: pos_pick_list_id
          type: int8
        - name: state
          type: varchar(50)
        - name: order_type
          type: varchar(50)
        - name: type
          type: varchar(50)
        - name: sku_quantity
          type: int8
        - name: sku_count
          type: int8
        - name: assigned_to
          type: varchar(100)
        - name: aisle
          type: varchar(50)
        - name: zone_identifier
          type: varchar(50)
        - name: picking_zone_identifier
          type: varchar(50)
        - name: scheduled_time
          type: timestamp
        - name: assigned_at
          type: timestamp
        - name: started_at
          type: timestamp
        - name: completed_at
          type: timestamp
        - name: billed_at
          type: timestamp
        - name: dispatched_at
          type: timestamp
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: varchar(100)
        - name: meta
          type: varchar(max)
        - name: merchant_id
          type: int8
        - name: batch_id
          type: varchar(255)
        - name: demand_id
          type: varchar(255)
        - name: demand_type
          type: varchar(50)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.wms.wms.pick_list/
      schema: lake_wms
      sortkey: []
      table: pick_list
    source:
      database: wms
      table: pick_list
  - flow_id: wms.pick_list_item.v1
    topic_name: mysql.wms.wms.pick_list_item
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: pos_pick_list_item_id
          type: int8
        - name: pick_list_id
          type: int8
        - name: item_id
          type: int8
        - name: state
          type: varchar(50)
        - name: required_quantity
          type: int8
        - name: picked_quantity
          type: int8
        - name: entity_vendor_id
          type: int8
        - name: created_by
          type: varchar(100)
        - name: updated_by
          type: varchar(100)
        - name: meta
          type: varchar(max)
        - name: item_processing_type
          type: varchar(32)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: initial_quantity
          type: int8
        - name: cancelled_quantity
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.wms.wms.pick_list_item/
      schema: lake_wms
      sortkey: []
      table: pick_list_item
    source:
      database: wms
      table: pick_list_item
  - flow_id: wms.pick_list_item_order_mapping.v1
    topic_name: mysql.wms.wms.pick_list_item_order_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: pos_pick_list_item_order_mapping_id
          type: varchar(50)
        - name: pick_list_item_id
          type: int8
        - name: order_id
          type: varchar(50)
        - name: order_type
          type: varchar(50)
        - name: required_quantity
          type: int8
        - name: billed_quantity
          type: int8
        - name: state
          type: varchar(50)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: picked_quantity
          type: int8
        - name: post_processing_state
          type: varchar(50)
        - name: meta
          type: varchar(max)
        - name: demand_cut_off_time
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.wms.wms.pick_list_item_order_mapping/
      schema: lake_wms
      sortkey: []
      table: pick_list_item_order_mapping
    source:
      database: wms
      table: pick_list_item_order_mapping
  - flow_id: wms.pick_list_log.v1
    topic_name: mysql.wms.wms.pick_list_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: pick_list_id
          type: int8
        - name: outlet_id
          type: int8
        - name: state
          type: varchar(50)
        - name: assigned_to
          type: varchar(100)
        - name: created_at
          type: timestamp
        - name: meta
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.wms.wms.pick_list_log/
      schema: lake_wms
      sortkey: []
      table: pick_list_log
    source:
      database: wms
      table: pick_list_log
  - flow_id: wms.picking_slot_demand_details.v1
    topic_name: mysql.wms.wms.picking_slot_demand_details
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: slot_id
          type: int8
        - name: outlet_id
          type: int8
        - name: zone_id
          type: varchar(50)
        - name: zone_type
          type: int8
        - name: picking_type
          type: varchar(50)
        - name: zone_restriction_type
          type: varchar(100)
        - name: picklist_count
          type: int8
        - name: total_sku
          type: int8
        - name: total_qty
          type: int8
        - name: pending_picklist_count
          type: int8
        - name: pending_sku
          type: int8
        - name: pending_qty
          type: int8
        - name: system_iph
          type: int8
        - name: system_lph
          type: int8
        - name: current_iph
          type: int8
        - name: current_lph
          type: int8
        - name: picker_required
          type: int8
        - name: current_picker_count
          type: int8
        - name: state
          type: varchar(50)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: meta
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.wms.wms.picking_slot_demand_details/
      schema: lake_wms
      sortkey: []
      table: picking_slot_demand_details
    source:
      database: wms
      table: picking_slot_demand_details
  - flow_id: wms.picking_slot_demand_details_log.v1
    topic_name: mysql.wms.wms.picking_slot_demand_details_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: slot_demand_details_id
          type: int8
        - name: slot_id
          type: int8
        - name: current_iph
          type: int8
        - name: current_lph
          type: int8
        - name: pickers_required
          type: int8
        - name: current_picker_count
          type: int8
        - name: state
          type: varchar(50)
        - name: created_at
          type: timestamp
        - name: meta
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.wms.wms.picking_slot_demand_details_log/
      schema: lake_wms
      sortkey: []
      table: picking_slot_demand_details_log
    source:
      database: wms
      table: picking_slot_demand_details_log
  - flow_id: wms.picking_slot_info.v1
    topic_name: mysql.wms.wms.picking_slot_info
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: start_time
          type: timestamp
        - name: expected_end_time
          type: timestamp
        - name: actual_end_time
          type: timestamp
        - name: outlet_id
          type: int8
        - name: total_sku
          type: int8
        - name: total_qty
          type: int8
        - name: total_picklist_count
          type: int8
        - name: state
          type: varchar(50)
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: meta
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.wms.wms.picking_slot_info/
      schema: lake_wms
      sortkey: []
      table: picking_slot_info
    source:
      database: wms
      table: picking_slot_info
  - flow_id: wms.unloading_proof_of_delivery.v1
    topic_name: mysql.wms.wms.unloading_proof_of_delivery
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: pod_id
          type: varchar(50)
        - name: outlet_id
          type: int8
        - name: vendor_id
          type: int8
        - name: unloading_timestamp
          type: timestamp
        - name: total_box_count
          type: int8
        - name: created_by
          type: varchar(50)
        - name: updated_by
          type: varchar(50)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: meta
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.wms.wms.unloading_proof_of_delivery/
      schema: lake_wms
      sortkey: []
      table: unloading_proof_of_delivery
    source:
      database: wms
      table: unloading_proof_of_delivery
  - flow_id: wms.unloading_task_po_invoice_mapping.v1
    topic_name: mysql.wms.wms.unloading_task_po_invoice_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: unloading_task_id
          type: int8
        - name: po_number
          type: varchar(30)
        - name: invoice_id
          type: varchar(255)
        - name: entity_vendor_id
          type: int8
        - name: created_by
          type: varchar(50)
        - name: created_at
          type: timestamp
        - name: meta
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.wms.wms.unloading_task_po_invoice_mapping/
      schema: lake_wms
      sortkey: []
      table: unloading_task_po_invoice_mapping
    source:
      database: wms
      table: unloading_task_po_invoice_mapping
  - flow_id: wms.user_attendance.v2
    topic_name: mysql.wms.wms.user_attendance
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: employee_id
          type: varchar(50)
        - name: outlet_id
          type: int8
        - name: department_key
          type: varchar(50)
        - name: roster_key
          type: varchar(100)
        - name: expected_in
          type: timestamp
        - name: expected_out
          type: timestamp
        - name: first_in
          type: timestamp
        - name: last_out
          type: timestamp
        - name: attendance_status
          type: varchar(30)
        - name: roster_adherence
          type: varchar(30)
        - name: meta
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: created_by
          type: varchar(50)
        - name: updated_by
          type: varchar(50)
        - name: is_active
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.wms.wms.user_attendance/
      schema: lake_wms
      sortkey: []
      table: user_attendance
    source:
      database: wms
      table: user_attendance
  - flow_id: wms.vehicle_registry.v1
    topic_name: mysql.wms.wms.vehicle_registry
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: outlet_id
          type: int8
        - name: vehicle_number
          type: varchar(32)
        - name: processing_type
          type: varchar(32)
        - name: vehicle_type
          type: varchar(32)
        - name: state
          type: varchar(32)
        - name: entry_time
          type: timestamp
        - name: exit_time
          type: timestamp
        - name: meta
          type: varchar(max)
        - name: created_by
          type: varchar(50)
        - name: updated_by
          type: varchar(50)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.wms.wms.vehicle_registry/
      schema: lake_wms
      sortkey: []
      table: vehicle_registry
    source:
      database: wms
      table: vehicle_registry
  - flow_id: wms.vehicle_registry_log.v1
    topic_name: mysql.wms.wms.vehicle_registry_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: vehicle_registry_id
          type: int8
        - name: state
          type: varchar(32)
        - name: meta
          type: varchar(max)
        - name: created_by
          type: varchar(50)
        - name: created_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.wms.wms.vehicle_registry_log/
      schema: lake_wms
      sortkey: []
      table: vehicle_registry_log
    source:
      database: wms
      table: vehicle_registry_log
  - flow_id: wms.warehouse_dock.v1
    topic_name: mysql.wms.wms.warehouse_dock
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: name
          type: varchar(50)
        - name: outlet_id
          type: int8
        - name: operation_type
          type: varchar(50)
        - name: load_type
          type: varchar(50)
        - name: state
          type: varchar(50)
        - name: capacity
          type: int8
        - name: occupied_capacity
          type: int8
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: meta
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.wms.wms.warehouse_dock/
      schema: lake_wms
      sortkey: []
      table: warehouse_dock
    source:
      database: wms
      table: warehouse_dock
  - flow_id: wms.warehouse_dock_activity.v1
    topic_name: mysql.wms.wms.warehouse_dock_activity
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: dock_id
          type: int8
        - name: vehicle_number
          type: varchar(50)
        - name: activity_type
          type: varchar(50)
        - name: state
          type: varchar(50)
        - name: assigned_at
          type: timestamp
        - name: started_at
          type: timestamp
        - name: completed_at
          type: timestamp
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: meta
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.wms.wms.warehouse_dock_activity/
      schema: lake_wms
      sortkey: []
      table: warehouse_dock_activity
    source:
      database: wms
      table: warehouse_dock_activity
  - flow_id: wms.wave.v1
    topic_name: mysql.wms.wms.wave
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: outlet_id
          type: int8
        - name: pos_wave_id
          type: int8
        - name: state
          type: varchar(50)
        - name: type
          type: varchar(50)
        - name: pick_list_details
          type: varchar(max)
        - name: processed_batch_count
          type: int8
        - name: total_batch_count
          type: int8
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(100)
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: varchar(100)
        - name: meta
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.wms.wms.wave/
      schema: lake_wms
      sortkey: []
      table: wave
    source:
      database: wms
      table: wave
  - flow_id: wms.entity_group.v1
    topic_name: mysql.wms.wms.entity_group
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: outlet_id
          type: int4
        - name: name
          type: varchar(100)
        - name: display_name
          type: varchar(100)
        - name: type
          type: varchar(100)
        - name: group_members
          type: varchar(max)
        - name: applicable_entities
          type: varchar(max)
        - name: config
          type: varchar(max)
        - name: active
          type: int4
        - name: created_by
          type: varchar(50)
        - name: updated_by
          type: varchar(50)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.wms.wms.entity_group/
      schema: lake_wms
      sortkey: []
      table: entity_group
    source:
      database: wms
      table: entity_group
  - flow_id: wms.outbound_dispatch_config.v1
    topic_name: mysql.wms.wms.outbound_dispatch_config
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: source_outlet_id
          type: int8
        - name: destination_outlet_id
          type: int8
        - name: dispatch_time
          type: varchar(50)
        - name: created_by
          type: varchar(50)
        - name: updated_by
          type: varchar(50)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: meta
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.wms.wms.outbound_dispatch_config/
      schema: lake_wms
      sortkey: []
      table: outbound_dispatch_config
    source:
      database: wms
      table: outbound_dispatch_config
tags:
  - de
  - replicate
  - wms_3
template_name: nessie
version: 1

