dag_name: transit_server_1
dag_type: etl
escalation_priority: low
alert_configs:
  slack:
    - channel: bl-data-alerts-p1
  error_channel_mapping:
    terminated_with_errors: bl-data-spot-interruptions
    user_request: bl-data-spot-interruptions
    spot: bl-data-spot-interruptions
executor:
  config:
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    cpu:
      limit: 1
      request: 0.3
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    memory:
      limit: 500M
      request: 250M
    node_selectors:
      nodetype: airflow-srp-spot
    tolerations:
      - effect: NoSchedule
        key: service
        operator: Equal
        value: airflow-srp
    volume_mounts:
      - mountPath: /usr/local/airflow/plugins
        name: airflow-dags
        subPath: airflow-de/plugins
  type: celery
namespace: de
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ

path: de/replicate/etl/transit_server_1
paused: false
project_name: replicate
schedule:
  interval: 27,57 0-19,22 * * *
  start_date: "2022-10-26T00:00:00"
  end_date: '2026-08-15T00:00:00'
schedule_type: fixed
sla: 180 minutes
task_concurrency: 15
poke_interval: 180
spark:
  aws_conn_id: aws_default
  mode: emr
  nessie_jar_version: 2.1.2
  cluster_reuse: true
  emr_version: 6.11.1
support_files: []
redshift_sla_seconds: 7200
emr_sensor: {}
nessie_dag_split: true
tables:
  - flow_id: transit_server.shipments_shipmentallocationstatelog.v1
    topic_name: postgres.transit_server.public.shipments_shipmentallocationstatelog
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: from_state
          type: varchar(max)
        - name: to_state
          type: varchar(max)
        - name: shipment_allocation_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.shipments_shipmentallocationstatelog/
      schema: lake_transit_server
      sortkey: []
      table: shipments_shipmentallocationstatelog
    source:
      database: transit_server
      table: shipments_shipmentallocationstatelog
  - flow_id: transit_server.shipments_shipmentstatelog.v1
    topic_name: postgres.transit_server.public.shipments_shipmentstatelog
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: from_state
          type: varchar(max)
        - name: to_state
          type: varchar(max)
        - name: metadata
          type: varchar(max)
        - name: shipment_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.shipments_shipmentstatelog/
      schema: lake_transit_server
      sortkey: []
      table: shipments_shipmentstatelog
    source:
      database: transit_server
      table: shipments_shipmentstatelog
  - flow_id: transit_server.task_management_taskstatelog.v1
    topic_name: postgres.transit_server.public.task_management_taskstatelog
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: task_type
          type: varchar(max)
        - name: from_state
          type: varchar(max)
        - name: to_state
          type: varchar(max)
        - name: metadata
          type: varchar(max)
        - name: task_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.task_management_taskstatelog/
      schema: lake_transit_server
      sortkey: []
      table: task_management_taskstatelog
    source:
      database: transit_server
      table: task_management_taskstatelog
  - flow_id: transit_server.transit_address.v1
    topic_name: postgres.transit_server.public.transit_address
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: latitude
          type: float8
        - name: longitude
          type: float8
        - name: accuracy
          type: float8
        - name: formatted_address
          type: varchar(max)
        - name: address_components
          type: varchar(max)
        - name: landmark
          type: varchar(max)
        - name: pincode
          type: varchar(max)
        - name: client_id
          type: varchar(max)
        - name: external_id
          type: varchar(max)
        - name: customer_name
          type: varchar(max)
        - name: customer_phone
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_address/
      schema: lake_transit_server
      sortkey: []
      table: transit_address
    source:
      database: transit_server
      table: transit_address
  - flow_id: transit_server.transit_allocation_batch.v1
    topic_name: postgres.transit_server.public.transit_allocation_batch
    redshift_replication: false
    sink:
      column_dtypes:
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: id
          type: varchar(max)
        - name: assigned_entities
          type: varchar(max)
        - name: state
          type: varchar(50)
        - name: type
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_allocation_batch/
      schema: lake_transit_server
      sortkey: []
      table: transit_allocation_batch
    source:
      database: transit_server
      table: transit_allocation_batch
  - flow_id: transit_server.transit_allocation_request.v1
    topic_name: postgres.transit_server.public.transit_allocation_request
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: created_by_id
          type: int4
        - name: updated_by_id
          type: int4
        - name: type
          type: varchar(max)
        - name: label_type
          type: varchar(max)
        - name: label_value
          type: varchar(max)
        - name: payload
          type: varchar(max)
        - name: state
          type: varchar(50)
        - name: eta
          type: timestamp
        - name: expiry
          type: timestamp
        - name: metadata
          type: varchar(max)
        - name: allocation_batch_id
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_allocation_request/
      schema: lake_transit_server
      sortkey: []
      table: transit_allocation_request
    source:
      database: transit_server
      table: transit_allocation_request
  - flow_id: transit_server.transit_allocation_vehicle.v1
    topic_name: postgres.transit_server.public.transit_allocation_vehicle
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: created_by
          type: varchar(100)
        - name: updated_by
          type: varchar(100)
        - name: vehicle_type
          type: varchar(50)
        - name: vehicle_number
          type: varchar(50)
        - name: node_id
          type: varchar(50)
        - name: state
          type: varchar(50)
        - name: activity_type
          type: varchar(50)
        - name: trip_id
          type: varchar(50)
        - name: scheduled_start
          type: timestamp
        - name: scheduled_end
          type: timestamp
        - name: metadata
          type: varchar(max)
        - name: completion_time
          type: timestamp
        - name: approved_by
          type: varchar(100)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_allocation_vehicle/
      schema: lake_transit_server
      sortkey: []
      table: transit_allocation_vehicle
    source:
      database: transit_server
      table: transit_allocation_vehicle
  - flow_id: transit_server.transit_allocation_vehicle_label.v1
    topic_name: postgres.transit_server.public.transit_allocation_vehicle_label
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: created_by
          type: varchar(100)
        - name: updated_by
          type: varchar(100)
        - name: label_type
          type: varchar(50)
        - name: label_value
          type: varchar(255)
        - name: metadata
          type: varchar(max)
        - name: allocation_vehicle_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_allocation_vehicle_label/
      schema: lake_transit_server
      sortkey: []
      table: transit_allocation_vehicle_label
    source:
      database: transit_server
      table: transit_allocation_vehicle_label
  - flow_id: transit_server.transit_consignment_container.v2
    topic_name: postgres.transit_server.public.transit_consignment_container
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: state
          type: varchar(30)
        - name: external_id
          type: varchar(50)
        - name: type
          type: varchar(30)
        - name: metadata
          type: varchar(max)
        - name: consignment_id
          type: int4
        - name: invoice_id_id
          type: int4
        - name: billed_destination
          type: varchar(10)
        - name: billed_source
          type: varchar(10)
        - name: billed_destination_node_type
          type: varchar(30)
        - name: billed_source_node_type
          type: varchar(30)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_consignment_container/
      schema: lake_transit_server
      sortkey: []
      table: transit_consignment_container
    source:
      database: transit_server
      table: transit_consignment_container
  - flow_id: transit_server.transit_consignment_item.v1
    topic_name: postgres.transit_server.public.transit_consignment_item
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: upc_id
          type: varchar(255)
        - name: variant_id
          type: varchar(255)
        - name: item_id
          type: varchar(255)
        - name: added_quantity
          type: int4
        - name: grn_quantity
          type: int4
        - name: grn_state
          type: varchar(255)
        - name: metadata
          type: varchar(max)
        - name: consignment_id
          type: int4
        - name: container_id
          type: varchar(250)
        - name: inventory_state
          type: varchar(250)
        - name: billing_entity_id
          type: varchar(250)
        - name: billing_entity_type
          type: varchar(250)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_consignment_item/
      schema: lake_transit_server
      sortkey: []
      table: transit_consignment_item
    source:
      database: transit_server
      table: transit_consignment_item
  - flow_id: transit_server.transit_discrepancy.v1
    topic_name: postgres.transit_server.public.transit_discrepancy
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: entity_type
          type: varchar(30)
        - name: entity_id
          type: varchar(50)
        - name: reference_type
          type: varchar(30)
        - name: reference_id
          type: varchar(50)
        - name: discrepancy_type
          type: varchar(30)
        - name: status
          type: varchar(30)
        - name: metadata
          type: varchar(max)
        - name: created_by
          type: varchar(50)
        - name: updated_by
          type: varchar(50)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_discrepancy/
      schema: lake_transit_server
      sortkey: []
      table: transit_discrepancy
    source:
      database: transit_server
      table: transit_discrepancy
  - flow_id: transit_server.transit_discrepancy_rca.v1
    topic_name: postgres.transit_server.public.transit_discrepancy_rca
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: discrepancy_id
          type: int4
        - name: reason
          type: varchar(50)
        - name: resolution_action
          type: varchar(50)
        - name: rca_type
          type: varchar(30)
        - name: remarks
          type: varchar(max)
        - name: created_by
          type: varchar(50)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_discrepancy_rca/
      schema: lake_transit_server
      sortkey: []
      table: transit_discrepancy_rca
    source:
      database: transit_server
      table: transit_discrepancy_rca
  - flow_id: transit_server.transit_discrepancy_reason_type.v1
    topic_name: postgres.transit_server.public.transit_discrepancy_reason_type
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: display_name
          type: varchar(100)
        - name: reason_code
          type: varchar(50)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_discrepancy_reason_type/
      schema: lake_transit_server
      sortkey: []
      table: transit_discrepancy_reason_type
    source:
      database: transit_server
      table: transit_discrepancy_reason_type
  - flow_id: transit_server.transit_discrepancy_resolution_action_type.v1
    topic_name: postgres.transit_server.public.transit_discrepancy_resolution_action_type
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: display_name
          type: varchar(100)
        - name: action_code
          type: varchar(50)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_discrepancy_resolution_action_type/
      schema: lake_transit_server
      sortkey: []
      table: transit_discrepancy_resolution_action_type
    source:
      database: transit_server
      table: transit_discrepancy_resolution_action_type
  - flow_id: transit_server.transit_express_allocation_field_executive_queue.v1
    topic_name: postgres.transit_server.public.transit_express_allocation_field_executive_queue
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: user_profile_id
          type: varchar(max)
        - name: user_type
          type: varchar(max)
        - name: dark_store_id
          type: varchar(max)
        - name: entry_ts
          type: timestamp
        - name: exit_ts
          type: timestamp
        - name: active
          type: boolean
        - name: meta
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_express_allocation_field_executive_queue/
      schema: lake_transit_server
      sortkey: []
      table: transit_express_allocation_field_executive_queue
    source:
      database: transit_server
      table: transit_express_allocation_field_executive_queue
  - flow_id: transit_server.transit_file_storage.v1
    topic_name: postgres.transit_server.public.transit_file_storage
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: picture_url
          type: varchar(100)
        - name: metadata
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_file_storage/
      schema: lake_transit_server
      sortkey: []
      table: transit_file_storage
    source:
      database: transit_server
      table: transit_file_storage
  - flow_id: transit_server.transit_fleet_plan.v1
    topic_name: postgres.transit_server.public.transit_fleet_plan
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: source_node_id
          type: varchar(50)
        - name: destination_node_id
          type: varchar(50)
        - name: tenure
          type: varchar(max)
        - name: meta
          type: varchar(max)
        - name: is_active
          type: boolean
        - name: created_by
          type: varchar(100)
        - name: updated_by
          type: varchar(100)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_fleet_plan/
      schema: lake_transit_server
      sortkey: []
      table: transit_fleet_plan
    source:
      database: transit_server
      table: transit_fleet_plan
  - flow_id: transit_server.transit_fleet_plan_dispatch_slot.v1
    topic_name: postgres.transit_server.public.transit_fleet_plan_dispatch_slot
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: created_by
          type: varchar(100)
        - name: updated_by
          type: varchar(100)
        - name: slot_time
          type: time
        - name: fleet_plan_id
          type: int4
      copy_params:
        - ESCAPE
        - REMOVEQUOTES
        - DELIMITER ','
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_fleet_plan_dispatch_slot/
      schema: lake_transit_server
      sortkey: []
      table: transit_fleet_plan_dispatch_slot
    source:
      database: transit_server
      table: transit_fleet_plan_dispatch_slot
  - flow_id: transit_server.transit_fleet_plan_slot_assigned_vehicle.v1
    topic_name: postgres.transit_server.public.transit_fleet_plan_slot_assigned_vehicle
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: vehicle_number
          type: varchar(30)
        - name: billing_type
          type: varchar(30)
        - name: handling_type
          type: varchar(30)
        - name: meta
          type: varchar(max)
        - name: dispatch_slot_id
          type: int4
        - name: destination_arrival_time
          type: time
      copy_params:
        - ESCAPE
        - REMOVEQUOTES
        - DELIMITER ','
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_fleet_plan_slot_assigned_vehicle/
      schema: lake_transit_server
      sortkey: []
      table: transit_fleet_plan_slot_assigned_vehicle
    source:
      database: transit_server
      table: transit_fleet_plan_slot_assigned_vehicle
  - flow_id: transit_server.transit_location.v1
    topic_name: postgres.transit_server.public.transit_location
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: transit_trip_id
          type: varchar(255)
        - name: external_reference
          type: varchar(255)
        - name: location_tracker_trip_id
          type: varchar(255)
        - name: current_status
          type: varchar(30)
        - name: location_ping_frequency
          type: int4
        - name: metadata
          type: varchar(max)
        - name: trip_metric
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_location/
      schema: lake_transit_server
      sortkey: []
      table: transit_location
    source:
      database: transit_server
      table: transit_location
  - flow_id: transit_server.transit_location_rule_trigger.v1
    topic_name: postgres.transit_server.public.transit_location_rule_trigger
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: trigger_type
          type: varchar(255)
        - name: trigger_payload
          type: varchar(max)
        - name: recurring
          type: boolean
        - name: meta
          type: varchar(max)
        - name: rule_id
          type: int4
        - name: trigger_order
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_location_rule_trigger/
      schema: lake_transit_server
      sortkey: []
      table: transit_location_rule_trigger
    source:
      database: transit_server
      table: transit_location_rule_trigger
  - flow_id: transit_server.transit_location_rule_trigger_log.v1
    topic_name: postgres.transit_server.public.transit_location_rule_trigger_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: trip_id
          type: varchar(255)
        - name: trigger_status
          type: varchar(255)
        - name: rule_trigger_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_location_rule_trigger_log/
      schema: lake_transit_server
      sortkey: []
      table: transit_location_rule_trigger_log
    source:
      database: transit_server
      table: transit_location_rule_trigger_log
  - flow_id: transit_server.transit_node.v1
    topic_name: postgres.transit_server.public.transit_node
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: created_by_id
          type: int4
        - name: updated_by_id
          type: int4
        - name: type
          type: varchar(max)
        - name: external_id
          type: varchar(max)
        - name: external_name
          type: varchar(max)
        - name: display_name
          type: varchar(max)
        - name: metadata
          type: varchar(max)
        - name: service_boundary_id
          type: varchar(max)
        - name: store_type
          type: varchar(255)
        - name: active
          type: boolean
        - name: node_address_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_node/
      schema: lake_transit_server
      sortkey: []
      table: transit_node
    source:
      database: transit_server
      table: transit_node
  - flow_id: transit_server.transit_node_address.v1
    topic_name: postgres.transit_server.public.transit_node_address
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: latitude
          type: float8
        - name: longitude
          type: float8
        - name: accuracy
          type: float8
        - name: formatted_address
          type: varchar(max)
        - name: address_components
          type: varchar(max)
        - name: landmark
          type: varchar(max)
        - name: pincode
          type: varchar(max)
        - name: city
          type: varchar(255)
        - name: store_area
          type: int4
        - name: ownership
          type: varchar(255)
        - name: client_id
          type: varchar(max)
        - name: client_email_id
          type: varchar(254)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_node_address/
      schema: lake_transit_server
      sortkey: []
      table: transit_node_address
    source:
      database: transit_server
      table: transit_node_address
  - flow_id: transit_server.transit_projection_consignment.v1
    topic_name: postgres.transit_server.public.transit_projection_consignment
    redshift_replication: false
    sink:
      column_dtypes:
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: consignment_id
          type: int4
        - name: trip_id
          type: varchar(250)
        - name: consignment_type
          type: varchar(30)
        - name: source_id
          type: varchar(250)
        - name: consignment_created_at
          type: timestamp
        - name: source_meta
          type: varchar(max)
        - name: destination_id
          type: varchar(250)
        - name: destination_meta
          type: varchar(max)
        - name: documents
          type: varchar(max)
        - name: containers
          type: varchar(max)
        - name: container_counts
          type: varchar(max)
        - name: state
          type: varchar(20)
        - name: state_log
          type: varchar(max)
        - name: grn_log
          type: varchar(max)
        - name: grn_state
          type: varchar(250)
        - name: grn_meta
          type: varchar(max)
        - name: rca_meta
          type: varchar(max)
        - name: rca_status
          type: varchar(30)
        - name: metrics
          type: varchar(max)
        - name: meta
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: consignment_id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [consignment_id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_projection_consignment/
      schema: lake_transit_server
      sortkey: []
      table: transit_projection_consignment
    source:
      database: transit_server
      table: transit_projection_consignment
  - flow_id: transit_server.transit_projection_trip.v1
    topic_name: postgres.transit_server.public.transit_projection_trip
    redshift_replication: false
    sink:
      column_dtypes:
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: trip_id
          type: varchar(50)
        - name: trip_meta
          type: varchar(max)
        - name: truck_number
          type: varchar(50)
        - name: truck_meta
          type: varchar(max)
        - name: state
          type: varchar(50)
        - name: user_profile_id
          type: int4
        - name: user_profile_meta
          type: varchar(max)
        - name: state_log
          type: varchar(max)
        - name: handshake_proofs
          type: varchar(max)
        - name: discrepancies
          type: varchar(max)
        - name: event_timeline_meta
          type: varchar(max)
        - name: source_node_id
          type: varchar(max)
        - name: source_node_meta
          type: varchar(max)
        - name: vehicle_handling_type
          type: varchar(50)
        - name: delayed
          type: boolean
        - name: driver_phone_number
          type: varchar(10)
        - name: trip_tags
          type: varchar(100)
        - name: location_ping_source
          type: varchar(20)
        - name: is_location_trackable
          type: boolean
        - name: tenant
          type: varchar(20)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: trip_id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [trip_id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_projection_trip/
      schema: lake_transit_server
      sortkey: []
      table: transit_projection_trip
    source:
      database: transit_server
      table: transit_projection_trip
  - flow_id: transit_server.transit_projection_trip_event_timeline.v1
    topic_name: postgres.transit_server.public.transit_projection_trip_event_timeline
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: trip_id
          type: varchar(250)
        - name: event_type
          type: varchar(30)
        - name: event_meta
          type: varchar(max)
        - name: scheduled_ts
          type: timestamp
        - name: expected_ts
          type: timestamp
        - name: actual_ts
          type: timestamp
        - name: delayed
          type: boolean
        - name: timing_label
          type: varchar(30)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_projection_trip_event_timeline/
      schema: lake_transit_server
      sortkey: []
      table: transit_projection_trip_event_timeline
    source:
      database: transit_server
      table: transit_projection_trip_event_timeline
  - flow_id: transit_server.transit_shipment.v1
    topic_name: postgres.transit_server.public.transit_shipment
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: tracking_id
          type: varchar(max)
        - name: type
          type: varchar(max)
        - name: status
          type: varchar(50)
        - name: collectible_amount
          type: int4
        - name: initial_value
          type: int4
        - name: metadata
          type: varchar(max)
        - name: tags
          type: varchar(max)
        - name: external_id
          type: varchar(max)
        - name: client_id
          type: varchar(max)
        - name: weight
          type: float8
        - name: current_value
          type: float8
        - name: expiry_time
          type: timestamp
        - name: delivery_start
          type: timestamp
        - name: delivery_end
          type: timestamp
        - name: pickup_start
          type: timestamp
        - name: pickup_end
          type: timestamp
        - name: payment_mode
          type: varchar(max)
        - name: draft
          type: boolean
        - name: workflow_id
          type: varchar(max)
        - name: delivery_address_id
          type: int4
        - name: pickup_address_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_shipment/
      schema: lake_transit_server
      sortkey: []
      table: transit_shipment
    source:
      database: transit_server
      table: transit_shipment
  - flow_id: transit_server.transit_shipment_allocation.v1
    topic_name: postgres.transit_server.public.transit_shipment_allocation
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: workflow_id
          type: varchar(max)
        - name: workflow_type
          type: varchar(max)
        - name: state
          type: varchar(50)
        - name: shipment_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_shipment_allocation/
      schema: lake_transit_server
      sortkey: []
      table: transit_shipment_allocation
    source:
      database: transit_server
      table: transit_shipment_allocation
  - flow_id: transit_server.transit_shipment_label.v1
    topic_name: postgres.transit_server.public.transit_shipment_label
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: label_type
          type: varchar(max)
        - name: label_value
          type: varchar(max)
        - name: shipment_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_shipment_label/
      schema: lake_transit_server
      sortkey: []
      table: transit_shipment_label
    source:
      database: transit_server
      table: transit_shipment_label
  - flow_id: transit_server.transit_task.v1
    topic_name: postgres.transit_server.public.transit_task
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: type
          type: varchar(max)
        - name: payload
          type: varchar(max)
        - name: form_data
          type: varchar(max)
        - name: state
          type: varchar(50)
        - name: user_profile_id
          type: int4
        - name: shipment_label_type
          type: varchar(max)
        - name: shipment_label_value
          type: varchar(max)
        - name: workflow_id
          type: varchar(max)
        - name: offline_synced
          type: boolean
        - name: parent_task_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_task/
      schema: lake_transit_server
      sortkey: []
      table: transit_task
    source:
      database: transit_server
      table: transit_task
  - flow_id: transit_server.transit_trip_error_log.v1
    topic_name: postgres.transit_server.public.transit_trip_error_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: trip_id
          type: varchar(30)
        - name: error_type
          type: varchar(250)
        - name: error_payload
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_trip_error_log/
      schema: lake_transit_server
      sortkey: []
      table: transit_trip_error_log
    source:
      database: transit_server
      table: transit_trip_error_log
  - flow_id: transit_server.transit_trip_metadata.v1
    topic_name: postgres.transit_server.public.transit_trip_metadata
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: trip_id
          type: varchar(max)
        - name: filter_type
          type: varchar(30)
        - name: filter_value
          type: varchar(30)
        - name: label_type
          type: varchar(30)
        - name: label_value
          type: varchar(1000)
        - name: metadata
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_trip_metadata/
      schema: lake_transit_server
      sortkey: []
      table: transit_trip_metadata
    source:
      database: transit_server
      table: transit_trip_metadata
  - flow_id: transit_server.transit_user.v1
    topic_name: postgres.transit_server.public.transit_user
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: created_by_id
          type: int4
        - name: updated_by_id
          type: int4
        - name: name
          type: varchar(max)
        - name: phone
          type: varchar(max)
        - name: email
          type: varchar(max)
        - name: created_by
          type: varchar(100)
        - name: updated_by
          type: varchar(100)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_user/
      schema: lake_transit_server
      sortkey: []
      table: transit_user
    source:
      database: transit_server
      table: transit_user
  - flow_id: transit_server.transit_user_profile.v1
    topic_name: postgres.transit_server.public.transit_user_profile
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: created_by_id
          type: int4
        - name: updated_by_id
          type: int4
        - name: type
          type: varchar(max)
        - name: employee_id
          type: varchar(max)
        - name: display_id
          type: varchar(max)
        - name: meta
          type: varchar(max)
        - name: active
          type: boolean
        - name: user_id
          type: int4
        - name: created_by
          type: varchar(100)
        - name: updated_by
          type: varchar(100)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_user_profile/
      schema: lake_transit_server
      sortkey: []
      table: transit_user_profile
    source:
      database: transit_server
      table: transit_user_profile
  - flow_id: transit_server.transit_workflow.v1
    topic_name: postgres.transit_server.public.transit_workflow
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: shipments
          type: varchar(max)
        - name: tasks
          type: varchar(max)
        - name: shipment_label_type
          type: varchar(max)
        - name: shipment_label_value
          type: varchar(max)
        - name: network_path
          type: varchar(max)
        - name: stage
          type: varchar(max)
        - name: workflow_type_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_workflow/
      schema: lake_transit_server
      sortkey: []
      table: transit_workflow
    source:
      database: transit_server
      table: transit_workflow
  - flow_id: transit_server.transit_workflow_step.v1
    topic_name: postgres.transit_server.public.transit_workflow_step
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: state
          type: varchar(50)
        - name: shipment_label_type
          type: varchar(max)
        - name: shipment_label_value
          type: varchar(max)
        - name: step_type
          type: varchar(max)
        - name: task_id
          type: varchar(max)
        - name: parent_id
          type: int4
        - name: workflow_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_workflow_step/
      schema: lake_transit_server
      sortkey: []
      table: transit_workflow_step
    source:
      database: transit_server
      table: transit_workflow_step
  - flow_id: transit_server.transit_workflow_type.v1
    topic_name: postgres.transit_server.public.transit_workflow_type
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: type
          type: varchar(50)
        - name: version
          type: varchar(max)
        - name: props
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_workflow_type/
      schema: lake_transit_server
      sortkey: []
      table: transit_workflow_type
    source:
      database: transit_server
      table: transit_workflow_type
tags:
  - de
  - replicate
  - transit_server_1
template_name: nessie
version: 1
