dag_name: irs_1
dag_type: etl
escalation_priority: low
alert_configs:
  slack:
    - channel: bl-data-alerts-p1
  error_channel_mapping:
    terminated_with_errors: bl-data-spot-interruptions
    user_request: bl-data-spot-interruptions
    spot: bl-data-spot-interruptions
executor:
  config:
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    cpu:
      limit: 1
      request: 0.3
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    memory:
      limit: 500M
      request: 250M
    node_selectors:
      nodetype: airflow-srp-spot
    tolerations:
      - effect: NoSchedule
        key: service
        operator: Equal
        value: airflow-srp
    volume_mounts:
      - mountPath: /usr/local/airflow/plugins
        name: airflow-dags
        subPath: airflow-de/plugins
  type: celery
namespace: de
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ

path: de/replicate/etl/irs_1
paused: false
project_name: replicate
schedule:
  interval: 0 */3 * * *
  start_date: "2024-05-20T00:00:00"
  end_date: "2026-01-21T00:00:00"
schedule_type: fixed
sla: 180 minutes
task_concurrency: 4
poke_interval: 180
redshift_sla_seconds: 10800
emr_sensor: {}
spark:
  aws_conn_id: aws_default
  mode: emr
  nessie_jar_version: 2.1.2
  cluster_reuse: true
  emr_version: 6.11.1
support_files: []
tables:
  - flow_id: irs.ewaybill.v1
    topic_name: postgres.irs.public.ewaybill
    redshift_replication: false
    sink:
      column_dtypes:
        - name: document_no
          type: varchar(100)
        - name: source_gstin
          type: varchar(50)
        - name: vehicle_number
          type: varchar(20)
        - name: hashed_request_payload
          type: varchar(255)
        - name: ewb_no
          type: varchar(150)
        - name: ewb_creation_timestamp
          type: timestamp
        - name: ewb_valid_till
          type: timestamp
        - name: meta
          type: varchar(max)
        - name: error
          type: varchar(max)
        - name: error_code
          type: varchar(max)
        - name: id
          type: int8
        - name: request_payload_file_path
          type: varchar(255)
        - name: tenant_id
          type: int4
        - name: retry_count
          type: int4
        - name: vendor
          type: varchar(50)
        - name: external_state
          type: varchar(50)
        - name: status
          type: varchar(50)
        - name: pdf_file_path
          type: varchar(255)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.irs.public.ewaybill/
      schema: lake_irs
      sortkey: []
      table: ewaybill
    source:
      database: irs
      table: ewaybill
  - flow_id: irs.ewaybill_logs.v1
    topic_name: postgres.irs.public.ewaybill_logs
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: ewaybill_id
          type: int8
        - name: status
          type: varchar(50)
        - name: vendor
          type: varchar(50)
        - name: error
          type: varchar(max)
        - name: error_code
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.irs.public.ewaybill_logs/
      schema: lake_irs
      sortkey: []
      table: ewaybill_logs
    source:
      database: irs
      table: ewaybill_logs
  - flow_id: irs.irn.v1
    topic_name: postgres.irs.public.irn
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: invoice_id
          type: varchar(50)
        - name: invoice_type
          type: varchar(50)
        - name: source_gstin
          type: varchar(50)
        - name: financial_year
          type: int4
        - name: vendor
          type: varchar(50)
        - name: irn
          type: varchar(255)
        - name: qr_code
          type: varchar(max)
        - name: meta
          type: varchar(max)
        - name: error
          type: varchar(max)
        - name: error_code
          type: varchar(max)
        - name: hashed_request_payload
          type: varchar(255)
        - name: request_payload_file_path
          type: varchar(255)
        - name: tenant_id
          type: int4
        - name: retry_count
          type: int4
        - name: status
          type: varchar(50)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: external_state
          type: varchar(50)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.irs.public.irn/
      schema: lake_irs
      sortkey: []
      table: irn
    source:
      database: irs
      table: irn
  - flow_id: irs.irn_logs.v1
    topic_name: postgres.irs.public.irn_logs
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: irn_id
          type: int8
        - name: status
          type: varchar(50)
        - name: vendor
          type: varchar(50)
        - name: error_code
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: error
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.irs.public.irn_logs/
      schema: lake_irs
      sortkey: []
      table: irn_logs
    source:
      database: irs
      table: irn_logs
  - flow_id: irs.legal_entity.v1
    topic_name: postgres.irs.public.legal_entity
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: entity_identifier
          type: varchar(50)
        - name: entity_identifier_type
          type: varchar(50)
        - name: name
          type: varchar(255)
        - name: is_active
          type: boolean
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.irs.public.legal_entity/
      schema: lake_irs
      sortkey: []
      table: legal_entity
    source:
      database: irs
      table: legal_entity
  - flow_id: irs.legal_identifier.v1
    topic_name: postgres.irs.public.legal_identifier
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: legal_identifier_id
          type: varchar(64)
        - name: legal_identifier_type
          type: varchar(50)
        - name: legal_entity_id
          type: int4
        - name: is_active
          type: boolean
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.irs.public.legal_identifier/
      schema: lake_irs
      sortkey: []
      table: legal_identifier
    source:
      database: irs
      table: legal_identifier
  - flow_id: irs.vendor_functionality_whitelisting.v1
    topic_name: postgres.irs.public.vendor_functionality_whitelisting
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: vendor
          type: varchar(50)
        - name: functionality
          type: varchar(50)
        - name: enabled
          type: boolean
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: legal_identifier_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.irs.public.vendor_functionality_whitelisting/
      schema: lake_irs
      sortkey: []
      table: vendor_functionality_whitelisting
    source:
      database: irs
      table: vendor_functionality_whitelisting
tags:
  - de
  - replicate
  - irs_1
template_name: nessie
version: 1
