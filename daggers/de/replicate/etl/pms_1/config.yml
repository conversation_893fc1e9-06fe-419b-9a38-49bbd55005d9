dag_name: pms_1
dag_type: etl
escalation_priority: low

alert_configs:
  slack:
    - channel: bl-data-alerts-p1
  error_channel_mapping:
    terminated_with_errors: bl-data-spot-interruptions
    spot: bl-data-spot-interruptions

executor:
  config:
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    cpu:
      limit: 1
      request: 0.3
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    memory:
      limit: 500M
      request: 250M
    node_selectors:
      nodetype: airflow-srp-spot
    tolerations:
      - effect: NoSchedule
        key: service
        operator: Equal
        value: airflow-srp
    volume_mounts:
      - mountPath: /usr/local/airflow/plugins
        name: airflow-dags
        subPath: airflow-de/plugins
  type: celery
namespace: de
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ

path: de/replicate/etl/pms_1
paused: false
project_name: replicate
schedule:
  interval: 0 */3 * * *
  start_date: "2024-09-24T00:00:00"
  end_date: '2025-12-16T00:00:00'
schedule_type: fixed
sla: 180 minutes
task_concurrency: 4
poke_interval: 180
redshift_sla_seconds: 10800
emr_sensor: {}
spark:
  aws_conn_id: aws_default
  mode: emr
  nessie_jar_version: 2.1.2
  cluster_reuse: true
  emr_version: 6.11.1
support_files: []
tables:
  - flow_id: pms.paas_ticketing_system.v1
    topic_name: postgres.pms.public.paas_ticketing_system
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: ticket_id
          type: varchar(max)
        - name: status
          type: varchar(max)
        - name: ticket_meta
          type: varchar(max)
        - name: ticket_type
          type: varchar(max)
        - name: ticket_subtype
          type: varchar(max)
        - name: printer_uuid
          type: varchar(max)
        - name: merchant_id
          type: varchar(max)
        - name: created_at
          type: int8
        - name: is_resource_disabled
          type: boolean
        - name: priority
          type: int4
        - name: updated_at
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pms.public.paas_ticketing_system/
      schema: lake_pms
      sortkey: []
      table: paas_ticketing_system
    source:
      database: pms
      table: paas_ticketing_system
  - flow_id: pms.printing_events.v1
    topic_name: postgres.pms.public.printing_events
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: aggr_id
          type: varchar(255)
        - name: event_type
          type: int4
        - name: event_name
          type: varchar(255)
        - name: event_meta
          type: varchar(max)
        - name: created_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pms.public.printing_events/
      schema: lake_pms
      sortkey: []
      table: printing_events
    source:
      database: pms
      table: printing_events
  - flow_id: pms.printing_serviceability.v1
    topic_name: postgres.pms.public.printing_serviceability
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: merchant_id
          type: int4
        - name: print_type
          type: int4
        - name: option
          type: varchar(max)
        - name: is_serviceable
          type: boolean
        - name: state
          type: int4
        - name: unserviceable_reasons
          type: varchar(max)
        - name: last_serviceability_ts
          type: int8
        - name: initial_delay
          type: int4
        - name: per_item_duration
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pms.public.printing_serviceability/
      schema: lake_pms
      sortkey: []
      table: printing_serviceability
    source:
      database: pms
      table: printing_serviceability
  - flow_id: pms.printings.v1
    topic_name: postgres.pms.public.printings
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: user_id
          type: int4
        - name: file_url
          type: varchar(max)
        - name: status
          type: varchar(20)
        - name: copy_count
          type: int4
        - name: product_id
          type: int4
        - name: merchant_id
          type: int4
        - name: updated_at
          type: timestamp
        - name: cart_id
          type: int4
        - name: order_id
          type: int4
        - name: job_id
          type: varchar(255)
        - name: page_count
          type: int4
        - name: created_at
          type: timestamp
        - name: secret_key
          type: varchar(255)
        - name: file_name
          type: varchar(255)
        - name: suborder_id
          type: int4
        - name: encryption_status
          type: int4
        - name: metadata
          type: varchar(max)
        - name: reprint_count
          type: int4
        - name: checkout_at
          type: timestamp
        - name: print_type
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.pms.public.printings/
      schema: lake_pms
      sortkey: []
      table: printings
    source:
      database: pms
      table: printings
tags:
  - de
  - replicate
  - pms_1
template_name: nessie
version: 1
