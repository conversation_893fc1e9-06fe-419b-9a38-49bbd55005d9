dag_name: dse_db_1
dag_type: etl
escalation_priority: low
alert_configs:
  slack:
    - channel: bl-data-alerts-p1
  error_channel_mapping:
    terminated_with_errors: bl-data-spot-interruptions
    spot: bl-data-spot-interruptions
executor:
  config:
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    cpu:
      limit: 1
      request: 0.3
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    memory:
      limit: 500M
      request: 250M
    node_selectors:
      nodetype: airflow-srp-spot
    tolerations:
      - effect: NoSchedule
        key: service
        operator: Equal
        value: airflow-srp
    volume_mounts:
      - mountPath: /usr/local/airflow/plugins
        name: airflow-dags
        subPath: airflow-de/plugins
  type: celery
namespace: de
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ
path: de/replicate/etl/dse_db_1
paused: false
project_name: replicate
schedule:
  end_date: "2026-06-08T00:00:00"
  interval: 0 */3 * * *
  start_date: "2024-09-04T00:00:00"
schedule_type: fixed
sla: 180 minutes
task_concurrency: 6
poke_interval: 180
redshift_sla_seconds: 10800
emr_sensor: {}
spark:
  aws_conn_id: aws_default
  mode: emr
  nessie_jar_version: 2.1.2
  cluster_reuse: true
  emr_version: 6.11.1
support_files: []
tables:
  - flow_id: dse_db.because_you_bought_usecase_v2.v1
    topic_name: postgres.dse_db.public.because_you_bought_usecase_v2
    redshift_replication: false
    sink:
      column_dtypes:
        - name: is_enabled
          type: int4
        - name: usecase
          type: varchar(255)
        - name: relation
          type: varchar(255)
        - name: start_time
          type: time
        - name: end_time
          type: time
        - name: type
          type: varchar(255)
        - name: scope
          type: varchar(255)
        - name: collection_uuid
          type: varchar(255)
      copy_params:
        - ESCAPE
        - REMOVEQUOTES
        - DELIMITER ','
      incremental_key: collection_uuid
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [collection_uuid]
      s3_url: s3://prod-dse-nessie-output/postgres.dse_db.public.because_you_bought_usecase_v2/
      schema: lake_dse_db
      sortkey: []
      table: because_you_bought_usecase_v2
    source:
      database: dse_db
      table: because_you_bought_usecase_v2
  - flow_id: dse_db.complementary_products.v1
    topic_name: postgres.dse_db.public.complementary_products
    redshift_replication: false
    sink:
      column_dtypes:
        - name: antecedent_pid
          type: int4
        - name: consequent_ptype
          type: varchar(255)
        - name: antecedent_collection_uuid
          type: varchar(255)
        - name: consequent_collection_uuid
          type: varchar(255)
        - name: antecedent_ptype
          type: varchar(255)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: antecedent_pid,consequent_ptype
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [antecedent_pid,consequent_ptype]
      s3_url: s3://prod-dse-nessie-output/postgres.dse_db.public.complementary_products/
      schema: lake_dse_db
      sortkey: []
      table: complementary_products
    source:
      database: dse_db
      table: complementary_products
  - flow_id: dse_db.frequently_bought_groups_meta.v1
    topic_name: postgres.dse_db.public.frequently_bought_groups_meta
    redshift_replication: false
    sink:
      column_dtypes:
        - name: group_id
          type: int4
        - name: is_enabled
          type: int4
        - name: group_display_name
          type: varchar(255)
        - name: see_all_deeplink
          type: varchar(255)
        - name: created_at
          type: varchar(255)
        - name: updated_at
          type: varchar(255)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: group_id,is_enabled
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [group_id,is_enabled]
      s3_url: s3://prod-dse-nessie-output/postgres.dse_db.public.frequently_bought_groups_meta/
      schema: lake_dse_db
      sortkey: []
      table: frequently_bought_groups_meta
    source:
      database: dse_db
      table: frequently_bought_groups_meta
  - flow_id: dse_db.master_reranking_data_v2.v1
    topic_name: postgres.dse_db.public.master_reranking_data_v2
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: usecase
          type: varchar(255)
        - name: metadata_usecase
          type: varchar(255)
        - name: antecedent_id
          type: int4
        - name: antecedent_id_type
          type: varchar(255)
        - name: rerank_ids
          type: varchar(max)
        - name: rerank_id_type
          type: varchar(255)
        - name: rerank_type
          type: varchar(255)
        - name: location_granularity_id
          type: int4
        - name: location_granularity_type
          type: varchar(255)
        - name: usecase_priority
          type: int4
        - name: widget
          type: varchar(255)
        - name: config_variation_name
          type: varchar(255)
        - name: start_time
          type: int8
        - name: end_time
          type: int8
        - name: cohort_in_filter
          type: varchar(max)
        - name: cohort_not_in_filter
          type: varchar(max)
        - name: widget_title
          type: varchar(255)
        - name: widget_position
          type: int4
        - name: created_at
          type: int8
        - name: updated_at
          type: int8
        - name: is_enabled
          type: boolean
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.dse_db.public.master_reranking_data_v2/
      schema: lake_dse_db
      sortkey: []
      table: master_reranking_data_v2
    source:
      database: dse_db
      table: master_reranking_data_v2
  - flow_id: dse_db.personalisation_story_metadata.v1
    topic_name: postgres.dse_db.public.personalisation_story_metadata
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: hash_key
          type: varchar(16)
        - name: name
          type: varchar(30)
        - name: status
          type: varchar(max)
        - name: story_type
          type: varchar(20)
        - name: collection_attributes
          type: varchar(max)
        - name: candidate_list
          type: varchar(max)
        - name: image_id
          type: int8
        - name: created_at
          type: int8
        - name: updated_at
          type: int8
        - name: rule_set
          type: varchar(max)
        - name: story_uuid
          type: varchar(10)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.dse_db.public.personalisation_story_metadata/
      schema: lake_dse_db
      sortkey: []
      table: personalisation_story_metadata
    source:
      database: dse_db
      table: personalisation_story_metadata
  - flow_id: dse_db.story_information.v1
    topic_name: postgres.dse_db.public.story_information
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: hash_key
          type: varchar(16)
        - name: collection_uuid
          type: varchar(50)
        - name: story_id
          type: int8
        - name: status
          type: varchar(50)
        - name: is_enabled
          type: boolean
        - name: created_at
          type: int8
        - name: updated_at
          type: int8
        - name: story_uuid
          type: varchar(10)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.dse_db.public.story_information/
      schema: lake_dse_db
      sortkey: []
      table: story_information
    source:
      database: dse_db
      table: story_information
  - flow_id: dse_db.trending_entities.v1
    topic_name: postgres.dse_db.public.trending_entities
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: city_id
          type: int4
        - name: entity_id
          type: int4
        - name: entity_type
          type: varchar(max)
        - name: entity_name
          type: varchar(255)
        - name: product_list
          type: varchar(max)
        - name: entity_product_cutoff
          type: int4
        - name: score
          type: float8
        - name: start_at
          type: int8
        - name: end_at
          type: int8
        - name: is_enabled
          type: boolean
        - name: created_at
          type: int8
        - name: updated_at
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.dse_db.public.trending_entities/
      schema: lake_dse_db
      sortkey: []
      table: trending_entities
    source:
      database: dse_db
      table: trending_entities
tags:
  - de
  - replicate
  - dse_db_1
template_name: nessie
version: 1
