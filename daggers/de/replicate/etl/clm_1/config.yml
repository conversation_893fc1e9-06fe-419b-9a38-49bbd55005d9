dag_name: clm_1
dag_type: etl
escalation_priority: low
alert_configs:
  slack:
    - channel: bl-data-alerts-p1
  error_channel_mapping:
    terminated_with_errors: bl-data-spot-interruptions
    spot: bl-data-spot-interruptions
executor:
  config:
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    cpu:
      limit: 1
      request: 0.3
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    memory:
      limit: 500M
      request: 250M
    node_selectors:
      nodetype: airflow-srp-spot
    tolerations:
      - effect: NoSchedule
        key: service
        operator: Equal
        value: airflow-srp
    volume_mounts:
      - mountPath: /usr/local/airflow/plugins
        name: airflow-dags
        subPath: airflow-de/plugins
  type: celery
namespace: de
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ
path: de/replicate/etl/clm_1
paused: false
project_name: replicate
schedule:
  end_date: "2026-09-10T00:00:00"
  interval: 0 */3 * * *
  start_date: "2025-09-15T00:00:00"
schedule_type: fixed
sla: 180 minutes
task_concurrency: 8
poke_interval: 180
redshift_sla_seconds: 10800
emr_sensor: {}
spark:
  aws_conn_id: aws_default
  mode: emr
  nessie_jar_version: 2.1.2
  cluster_reuse: true
  emr_version: 6.11.1
support_files: []
tables:
  - flow_id: clm.clm_artifact.v1
    topic_name: postgres.clm.public.clm_artifact
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: document_type
          type: varchar(32)
        - name: document_url
          type: varchar(max)
        - name: is_deleted
          type: int4
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: created_by
          type: varchar(55)
        - name: updated_by
          type: varchar(55)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.clm.public.clm_artifact/
      schema: lake_clm
      sortkey: []
      table: clm_artifact
    source:
      database: clm
      table: clm_artifact
  - flow_id: clm.clm_artifact_mapping.v1
    topic_name: postgres.clm.public.clm_artifact_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: artifact_id
          type: int8
        - name: mapped_entity_type
          type: varchar(32)
        - name: mapped_entity_id
          type: int8
        - name: is_deleted
          type: int4
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: created_by
          type: varchar(55)
        - name: updated_by
          type: varchar(55)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.clm.public.clm_artifact_mapping/
      schema: lake_clm
      sortkey: []
      table: clm_artifact_mapping
    source:
      database: clm
      table: clm_artifact_mapping
  - flow_id: clm.clm_contract.v1
    topic_name: postgres.clm.public.clm_contract
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: contract_reference_id
          type: varchar(50)
        - name: source_company_id
          type: int8
        - name: source_company_name
          type: varchar(255)
        - name: destination_company_id
          type: int8
        - name: destination_company_name
          type: varchar(255)
        - name: start_date
          type: timestamp
        - name: end_date
          type: timestamp
        - name: execution_date
          type: timestamp
        - name: notice_period
          type: int4
        - name: state
          type: varchar(32)
        - name: is_deleted
          type: int4
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: created_by
          type: varchar(55)
        - name: updated_by
          type: varchar(55)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.clm.public.clm_contract/
      schema: lake_clm
      sortkey: []
      table: clm_contract
    source:
      database: clm
      table: clm_contract
  - flow_id: clm.clm_contract_mapping.v1
    topic_name: postgres.clm.public.clm_contract_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: contract_id
          type: int8
        - name: mapping_type
          type: varchar(32)
        - name: mapped_id
          type: int8
        - name: mapped_value
          type: varchar(255)
        - name: is_deleted
          type: int4
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: created_by
          type: varchar(55)
        - name: updated_by
          type: varchar(55)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.clm.public.clm_contract_mapping/
      schema: lake_clm
      sortkey: []
      table: clm_contract_mapping
    source:
      database: clm
      table: clm_contract_mapping
  - flow_id: clm.clm_contract_section.v1
    topic_name: postgres.clm.public.clm_contract_section
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: contract_id
          type: int8
        - name: section_id
          type: int8
        - name: state
          type: varchar(32)
        - name: is_deleted
          type: int4
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: created_by
          type: varchar(55)
        - name: updated_by
          type: varchar(55)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.clm.public.clm_contract_section/
      schema: lake_clm
      sortkey: []
      table: clm_contract_section
    source:
      database: clm
      table: clm_contract_section
  - flow_id: clm.clm_contract_section_hierarchy.v1
    topic_name: postgres.clm.public.clm_contract_section_hierarchy
    redshift_replication: false
    sink:
      column_dtypes:
        - name: contract_section_id
          type: int8
        - name: hierarchy_id
          type: int8
        - name: is_deleted
          type: int4
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: created_by
          type: varchar(55)
        - name: updated_by
          type: varchar(55)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: contract_section_id,hierarchy_id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [contract_section_id,hierarchy_id]
      s3_url: s3://prod-dse-nessie-output/postgres.clm.public.clm_contract_section_hierarchy/
      schema: lake_clm
      sortkey: []
      table: clm_contract_section_hierarchy
    source:
      database: clm
      table: clm_contract_section_hierarchy
  - flow_id: clm.clm_hierarchy.v1
    topic_name: postgres.clm.public.clm_hierarchy
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: name
          type: varchar(32)
        - name: is_deleted
          type: int4
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: created_by
          type: varchar(55)
        - name: updated_by
          type: varchar(55)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.clm.public.clm_hierarchy/
      schema: lake_clm
      sortkey: []
      table: clm_hierarchy
    source:
      database: clm
      table: clm_hierarchy
  - flow_id: clm.clm_section.v1
    topic_name: postgres.clm.public.clm_section
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: name
          type: varchar(255)
        - name: parent_section_id
          type: int8
        - name: is_deleted
          type: int4
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: created_by
          type: varchar(55)
        - name: updated_by
          type: varchar(55)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.clm.public.clm_section/
      schema: lake_clm
      sortkey: []
      table: clm_section
    source:
      database: clm
      table: clm_section
  - flow_id: clm.clm_section_question_answer.v1
    topic_name: postgres.clm.public.clm_section_question_answer
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: contract_section_id
          type: int8
        - name: question_id
          type: varchar(255)
        - name: answer
          type: varchar(max)
        - name: remarks
          type: varchar(max)
        - name: is_deleted
          type: int4
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: created_by
          type: varchar(55)
        - name: updated_by
          type: varchar(55)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.clm.public.clm_section_question_answer/
      schema: lake_clm
      sortkey: []
      table: clm_section_question_answer
    source:
      database: clm
      table: clm_section_question_answer
tags:
  - de
  - replicate
  - clm_1
template_name: nessie
version: 1
