dag_name: document_digitisation_1
dag_type: etl
escalation_priority: low
alert_configs:
  slack:
  - channel: bl-data-alerts-p1
  error_channel_mapping:
    terminated_with_errors: bl-data-spot-interruptions
    spot: bl-data-spot-interruptions
executor:
  config:
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    cpu:
      limit: 1
      request: 0.3
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    memory:
      limit: 500M
      request: 250M
    node_selectors:
      nodetype: airflow-srp-spot
    tolerations:
    - effect: NoSchedule
      key: service
      operator: Equal
      value: airflow-srp
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow-de/plugins
  type: celery
namespace: de
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ
path: de/replicate/etl/document_digitisation_1
paused: false
project_name: replicate
schedule:
  interval: 0 */3 * * *
  start_date: '2023-12-21T00:00:00'
  end_date: '2026-01-09T00:00:00'
schedule_type: fixed
sla: 180 minutes
task_concurrency: 15
poke_interval: 180
redshift_sla_seconds: 10800
emr_sensor: {}
spark:
  aws_conn_id: aws_default
  mode: emr
  nessie_jar_version: 2.1.2
  cluster_reuse: true
  emr_version: 6.11.1
support_files: []
tables:
- flow_id: document_digitisation.auth_user.v1
  topic_name: postgres.document_digitisation.public.auth_user
  redshift_replication: false
  sink:
    column_dtypes:
    - name: id
      type: int4
    - name: password
      type: varchar(128)
    - name: last_login
      type: timestamp
    - name: is_superuser
      type: boolean
    - name: username
      type: varchar(150)
    - name: first_name
      type: varchar(150)
    - name: last_name
      type: varchar(150)
    - name: email
      type: varchar(254)
    - name: is_staff
      type: boolean
    - name: is_active
      type: boolean
    - name: date_joined
      type: timestamp
    copy_params:
    - FORMAT AS PARQUET
    incremental_key: id
    force_upsert_without_increment_check: true
    load_type: upsert
    primary_key:
    - id
    s3_url: s3://prod-dse-nessie-output/postgres.document_digitisation.public.auth_user/
    schema: lake_document_digitisation
    sortkey: []
    table: auth_user
  source:
    database: document_digitisation
    table: auth_user
- flow_id: document_digitisation.digitisation_invoice_mapping_v2.v1
  topic_name: postgres.document_digitisation.public.digitisation_invoice_mapping_v2
  redshift_replication: false
  sink:
    column_dtypes:
    - name: id
      type: int8
    - name: source_invoice_id
      type: varchar(255)
    - name: invoice_id
      type: varchar(100)
    - name: invoice_status
      type: int4
    - name: grn_id
      type: varchar(255)
    - name: po_number
      type: varchar(30)
    - name: outlet_id
      type: varchar(100)
    - name: merchant_id
      type: varchar(100)
    - name: tenant_id
      type: int4
    - name: priority
      type: int4
    - name: retry_count
      type: int4
    - name: rejection_reason
      type: varchar(255)
    - name: is_active
      type: boolean
    - name: created_at
      type: timestamp
    - name: created_by
      type: int4
    - name: updated_at
      type: timestamp
    - name: updated_by
      type: int4
    - name: request_id
      type: int8
    - name: payload
      type: varchar(max)
    - name: rejection_reason_type
      type: int4
    copy_params:
    - FORMAT AS PARQUET
    incremental_key: id
    force_upsert_without_increment_check: true
    load_type: upsert
    primary_key:
    - id
    s3_url: s3://prod-dse-nessie-output/postgres.document_digitisation.public.digitisation_invoice_mapping_v2/
    schema: lake_document_digitisation
    sortkey: []
    table: digitisation_invoice_mapping_v2
  source:
    database: document_digitisation
    table: digitisation_invoice_mapping_v2
- flow_id: document_digitisation.digitisation_request.v1
  topic_name: postgres.document_digitisation.public.digitisation_request
  redshift_replication: false
  sink:
    column_dtypes:
    - name: id
      type: int8
    - name: request_id
      type: varchar(max)
    - name: document_source
      type: int4
    - name: file_path
      type: varchar(max)
    - name: document_type
      type: int4
    - name: source_document_id
      type: varchar(64)
    - name: request_status
      type: int4
    - name: tenant_id
      type: int4
    - name: created_at
      type: timestamp
    - name: created_by
      type: int4
    - name: updated_at
      type: timestamp
    - name: retry_counts
      type: int4
    - name: request_data
      type: varchar(max)
    - name: meta
      type: varchar(max)
    - name: translation_status
      type: int4
    - name: translation_source
      type: int4
    - name: translation_request_id
      type: varchar(64)
    - name: document_id
      type: varchar(100)
    copy_params:
    - FORMAT AS PARQUET
    incremental_key: id
    force_upsert_without_increment_check: true
    load_type: upsert
    primary_key:
    - id
    s3_url: s3://prod-dse-nessie-output/postgres.document_digitisation.public.digitisation_request/
    schema: lake_document_digitisation
    sortkey: []
    table: digitisation_request
  source:
    database: document_digitisation
    table: digitisation_request
- flow_id: document_digitisation.digitisation_request_action.v1
  topic_name: postgres.document_digitisation.public.digitisation_request_action
  redshift_replication: false
  sink:
    column_dtypes:
    - name: id
      type: int8
    - name: action_type
      type: int4
    - name: action_id
      type: varchar(max)
    - name: document_source_action_id
      type: varchar(64)
    - name: payload
      type: varchar(max)
    - name: request_id
      type: int8
    copy_params:
    - FORMAT AS PARQUET
    incremental_key: id
    force_upsert_without_increment_check: true
    load_type: upsert
    primary_key:
    - id
    s3_url: s3://prod-dse-nessie-output/postgres.document_digitisation.public.digitisation_request_action/
    schema: lake_document_digitisation
    sortkey: []
    table: digitisation_request_action
  source:
    database: document_digitisation
    table: digitisation_request_action
- flow_id: document_digitisation.digitised_document.v1
  topic_name: postgres.document_digitisation.public.digitised_document
  redshift_replication: false
  sink:
    column_dtypes:
    - name: id
      type: int8
    - name: source_document_id
      type: varchar(64)
    - name: tenant_id
      type: int4
    - name: document_type
      type: int4
    - name: request_source
      type: int4
    - name: doc_data
      type: varchar(max)
    - name: meta
      type: varchar(max)
    - name: created_at
      type: timestamp
    - name: created_by
      type: int4
    - name: updated_at
      type: timestamp
    - name: updated_by
      type: int4
    - name: request_id
      type: int8
    copy_params:
    - FORMAT AS PARQUET
    incremental_key: id
    force_upsert_without_increment_check: true
    load_type: upsert
    primary_key:
    - id
    s3_url: s3://prod-dse-nessie-output/postgres.document_digitisation.public.digitised_document/
    schema: lake_document_digitisation
    sortkey: []
    table: digitised_document
  source:
    database: document_digitisation
    table: digitised_document
- flow_id: document_digitisation.digitised_document_log.v1
  topic_name: postgres.document_digitisation.public.digitised_document_log
  redshift_replication: false
  sink:
    column_dtypes:
    - name: id
      type: int8
    - name: source_document_id
      type: varchar(64)
    - name: tenant_id
      type: int4
    - name: document_type
      type: int4
    - name: request_source
      type: int4
    - name: doc_data
      type: varchar(max)
    - name: meta
      type: varchar(max)
    - name: created_at
      type: timestamp
    - name: created_by
      type: int4
    - name: request_id
      type: int8
    copy_params:
    - FORMAT AS PARQUET
    incremental_key: id
    force_upsert_without_increment_check: true
    load_type: upsert
    primary_key:
    - id
    s3_url: s3://prod-dse-nessie-output/postgres.document_digitisation.public.digitised_document_log/
    schema: lake_document_digitisation
    sortkey: []
    table: digitised_document_log
  source:
    database: document_digitisation
    table: digitised_document_log
- flow_id: document_digitisation.discrepancy_note_data_dump.v1
  topic_name: postgres.document_digitisation.public.discrepancy_note_data_dump
  redshift_replication: false
  sink:
    column_dtypes:
    - name: id
      type: int4
    - name: request_id
      type: int4
    - name: dn_id
      type: varchar(255)
    - name: item_id
      type: varchar(64)
    - name: item_name
      type: varchar(512)
    - name: upc
      type: varchar(64)
    - name: variant_uom_text
      type: varchar(max)
    - name: landing_price
      type: float8
    - name: quantity
      type: int4
    - name: dn_amount
      type: float8
    - name: reason
      type: varchar(max)
    - name: tax_details
      type: varchar(max)
    - name: updated_at
      type: timestamp
    - name: created_at
      type: timestamp
    copy_params:
    - FORMAT AS PARQUET
    incremental_key: id
    force_upsert_without_increment_check: true
    load_type: upsert
    primary_key:
    - id
    s3_url: s3://prod-dse-nessie-output/postgres.document_digitisation.public.discrepancy_note_data_dump/
    schema: lake_document_digitisation
    sortkey: []
    table: discrepancy_note_data_dump
  source:
    database: document_digitisation
    table: discrepancy_note_data_dump
- flow_id: document_digitisation.seller.v1
  topic_name: postgres.document_digitisation.public.seller
  redshift_replication: false
  sink:
    column_dtypes:
    - name: id
      type: int8
    - name: created_at
      type: timestamp
    - name: created_by
      type: int4
    - name: updated_at
      type: timestamp
    - name: updated_by
      type: int4
    - name: active
      type: boolean
    - name: name
      type: varchar(255)
    - name: cin
      type: varchar(64)
    copy_params:
    - FORMAT AS PARQUET
    incremental_key: id
    force_upsert_without_increment_check: true
    load_type: upsert
    primary_key:
    - id
    s3_url: s3://prod-dse-nessie-output/postgres.document_digitisation.public.seller/
    schema: lake_document_digitisation
    sortkey: []
    table: seller
  source:
    database: document_digitisation
    table: seller
- flow_id: document_digitisation.tenant.v1
  topic_name: postgres.document_digitisation.public.tenant
  redshift_replication: false
  sink:
    column_dtypes:
    - name: id
      type: int8
    - name: created_at
      type: timestamp
    - name: created_by
      type: int4
    - name: updated_at
      type: timestamp
    - name: updated_by
      type: int4
    - name: active
      type: boolean
    - name: name
      type: varchar(255)
    copy_params:
    - FORMAT AS PARQUET
    incremental_key: id
    force_upsert_without_increment_check: true
    load_type: upsert
    primary_key:
    - id
    s3_url: s3://prod-dse-nessie-output/postgres.document_digitisation.public.tenant/
    schema: lake_document_digitisation
    sortkey: []
    table: tenant
  source:
    database: document_digitisation
    table: tenant
- flow_id: document_digitisation.user_management_user_group.v1
  topic_name: postgres.document_digitisation.public.user_management_user_group
  redshift_replication: false
  sink:
    column_dtypes:
    - name: id
      type: int8
    - name: group_id
      type: int4
    - name: active
      type: boolean
    - name: created_at
      type: timestamp
    - name: updated_at
      type: timestamp
    - name: updated_by_email
      type: varchar(128)
    - name: user_id
      type: int4
    copy_params:
    - FORMAT AS PARQUET
    incremental_key: id
    force_upsert_without_increment_check: true
    load_type: upsert
    primary_key:
    - id
    s3_url: s3://prod-dse-nessie-output/postgres.document_digitisation.public.user_management_user_group/
    schema: lake_document_digitisation
    sortkey: []
    table: user_management_user_group
  source:
    database: document_digitisation
    table: user_management_user_group
- flow_id: document_digitisation.user_seller_map.v1
  topic_name: postgres.document_digitisation.public.user_seller_map
  redshift_replication: false
  sink:
    column_dtypes:
    - name: id
      type: int8
    - name: created_at
      type: timestamp
    - name: created_by
      type: int4
    - name: updated_at
      type: timestamp
    - name: updated_by
      type: int4
    - name: active
      type: boolean
    - name: seller_id
      type: int8
    - name: user_id
      type: int4
    copy_params:
    - FORMAT AS PARQUET
    incremental_key: id
    force_upsert_without_increment_check: true
    load_type: upsert
    primary_key:
    - id
    s3_url: s3://prod-dse-nessie-output/postgres.document_digitisation.public.user_seller_map/
    schema: lake_document_digitisation
    sortkey: []
    table: user_seller_map
  source:
    database: document_digitisation
    table: user_seller_map
- flow_id: document_digitisation.user_tenant_map.v1
  topic_name: postgres.document_digitisation.public.user_tenant_map
  redshift_replication: false
  sink:
    column_dtypes:
    - name: id
      type: int8
    - name: created_at
      type: timestamp
    - name: created_by
      type: int4
    - name: updated_at
      type: timestamp
    - name: updated_by
      type: int4
    - name: active
      type: boolean
    - name: tenant_id
      type: int8
    - name: user_id
      type: int4
    copy_params:
    - FORMAT AS PARQUET
    incremental_key: id
    force_upsert_without_increment_check: true
    load_type: upsert
    primary_key:
    - id
    s3_url: s3://prod-dse-nessie-output/postgres.document_digitisation.public.user_tenant_map/
    schema: lake_document_digitisation
    sortkey: []
    table: user_tenant_map
  source:
    database: document_digitisation
    table: user_tenant_map
- flow_id: document_digitisation.verification_request_step.v1
  topic_name: postgres.document_digitisation.public.verification_request_step
  redshift_replication: false
  sink:
    column_dtypes:
    - name: id
      type: int8
    - name: created_at
      type: timestamp
    - name: created_by
      type: int4
    - name: updated_at
      type: timestamp
    - name: updated_by
      type: int4
    - name: active
      type: boolean
    - name: status
      type: int4
    - name: remarks
      type: varchar(128)
    - name: action_taken_by_id
      type: int4
    - name: request_id
      type: int8
    - name: step_id
      type: int8
    copy_params:
    - FORMAT AS PARQUET
    incremental_key: id
    force_upsert_without_increment_check: true
    load_type: upsert
    primary_key:
    - id
    s3_url: s3://prod-dse-nessie-output/postgres.document_digitisation.public.verification_request_step/
    schema: lake_document_digitisation
    sortkey: []
    table: verification_request_step
  source:
    database: document_digitisation
    table: verification_request_step
- flow_id: document_digitisation.verified_invoice_data_log.v1
  topic_name: postgres.document_digitisation.public.verified_invoice_data_log
  redshift_replication: false
  sink:
    column_dtypes:
    - name: id
      type: int8
    - name: verified_invoice_data_id
      type: int8
    - name: tenant_id
      type: int4
    - name: seller_id
      type: int4
    - name: source_document_id
      type: varchar(128)
    - name: is_stamped
      type: boolean
    - name: document_type
      type: int4
    - name: request_source
      type: int4
    - name: amount
      type: float8
    - name: price_variance_agg
      type: float8
    - name: invoice_meta_data
      type: varchar(max)
    - name: tax_agg
      type: varchar(max)
    - name: invoice_id
      type: varchar(64)
    - name: billed_to_gstin
      type: varchar(64)
    - name: shipped_to_gstin
      type: varchar(64)
    - name: billed_to_name
      type: varchar(255)
    - name: shipped_to_name
      type: varchar(255)
    - name: vendor_gstin
      type: varchar(64)
    - name: vendor_name
      type: varchar(255)
    - name: created_at
      type: timestamp
    - name: is_complete_document
      type: boolean
    - name: is_document_valid
      type: boolean
    - name: is_invoice_date_valid
      type: boolean
    - name: is_invoice_id_valid
      type: boolean
    - name: is_scan_clear
      type: boolean
    - name: additional_charges
      type: float8
    - name: net_payable
      type: float8
    copy_params:
    - FORMAT AS PARQUET
    incremental_key: id
    force_upsert_without_increment_check: true
    load_type: upsert
    primary_key:
    - id
    s3_url: s3://prod-dse-nessie-output/postgres.document_digitisation.public.verified_invoice_data_log/
    schema: lake_document_digitisation
    sortkey: []
    table: verified_invoice_data_log
  source:
    database: document_digitisation
    table: verified_invoice_data_log
- flow_id: document_digitisation.verified_invoice_items_data_log.v1
  topic_name: postgres.document_digitisation.public.verified_invoice_items_data_log
  redshift_replication: false
  sink:
    column_dtypes:
    - name: id
      type: int8
    - name: item_id
      type: varchar(64)
    - name: item_name
      type: varchar(512)
    - name: total_conflicts
      type: int4
    - name: verified_quantity
      type: int4
    - name: verified_tax_rate
      type: float8
    - name: verified_cess_rate
      type: float8
    - name: verified_additional_cess_rate
      type: float8
    - name: verified_taxable_value
      type: float8
    - name: created_at
      type: timestamp
    - name: verified_invoice_item_id
      type: int8
    copy_params:
    - FORMAT AS PARQUET
    incremental_key: id
    force_upsert_without_increment_check: true
    load_type: upsert
    primary_key:
    - id
    s3_url: s3://prod-dse-nessie-output/postgres.document_digitisation.public.verified_invoice_items_data_log/
    schema: lake_document_digitisation
    sortkey: []
    table: verified_invoice_items_data_log
  source:
    database: document_digitisation
    table: verified_invoice_items_data_log
- flow_id: document_digitisation.digitise_bulk_invoice_request.v1
  topic_name: postgres.document_digitisation.public.digitise_bulk_invoice_request
  redshift_replication: false
  sink:
    column_dtypes:
      - name: id
        type: int8
      - name: meta
        type: varchar(max)
      - name: created_at
        type: timestamp
      - name: uploaded_by
        type: varchar(255)
    copy_params:
      - FORMAT AS PARQUET
    incremental_key: id
    force_upsert_without_increment_check: True
    load_type: upsert
    primary_key: [id]
    s3_url: s3://prod-dse-nessie-output/postgres.document_digitisation.public.digitise_bulk_invoice_request/
    schema: lake_document_digitisation
    sortkey: []
    table: digitise_bulk_invoice_request
  source:
    database: document_digitisation
    table: digitise_bulk_invoice_request
- flow_id: document_digitisation.digitise_invoice_request.v1
  topic_name: postgres.document_digitisation.public.digitise_invoice_request
  redshift_replication: false
  sink:
    column_dtypes:
      - name: id
        type: int8
      - name: s3_url
        type: varchar(200)
      - name: created_at
        type: timestamp
      - name: request_id
        type: int8
    copy_params:
      - FORMAT AS PARQUET
    incremental_key: id
    force_upsert_without_increment_check: True
    load_type: upsert
    primary_key: [id]
    s3_url: s3://prod-dse-nessie-output/postgres.document_digitisation.public.digitise_invoice_request/
    schema: lake_document_digitisation
    sortkey: []
    table: digitise_invoice_request
  source:
    database: document_digitisation
    table: digitise_invoice_request
tags:
- de
- replicate
- document_digitisation_1
template_name: nessie
version: 1
