dag_name: transit_server_3
dag_type: etl
escalation_priority: low
alert_configs:
  slack:
    - channel: bl-data-alerts-p1
  error_channel_mapping:
    terminated_with_errors: bl-data-spot-interruptions
    user_request: bl-data-spot-interruptions
    spot: bl-data-spot-interruptions
executor:
  config:
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    cpu:
      limit: 1
      request: 0.3
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    memory:
      limit: 500M
      request: 250M
    node_selectors:
      nodetype: airflow-srp-spot
    tolerations:
      - effect: NoSchedule
        key: service
        operator: Equal
        value: airflow-srp
    volume_mounts:
      - mountPath: /usr/local/airflow/plugins
        name: airflow-dags
        subPath: airflow-de/plugins
  type: celery
namespace: de
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ

path: de/replicate/etl/transit_server_3
paused: false
project_name: replicate
schedule:
  interval: 15,45 * * * *
  start_date: "2024-08-20T00:00:00"
  end_date: "2025-11-15T00:00:00"
schedule_type: fixed
sla: 180 minutes
task_concurrency: 14
poke_interval: 180
redshift_sla_seconds: 10800
emr_sensor: {}
nessie_dag_split: true
spark:
  aws_conn_id: aws_default
  mode: emr
  nessie_jar_version: 2.1.2
  cluster_reuse: true
  emr_version: 6.11.1
support_files: []
tables:
  - flow_id: transit_server.transit_allocation_fulfillment_queue.v1
    topic_name: postgres.transit_server.public.transit_allocation_fulfillment_queue
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: source_node_id
          type: varchar(50)
        - name: source_node_type
          type: varchar(50)
        - name: allocation_type
          type: varchar(50)
        - name: reference_id
          type: varchar(100)
        - name: metadata
          type: varchar(max)
        - name: is_active
          type: boolean
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_allocation_fulfillment_queue/
      schema: lake_transit_server
      sortkey: []
      table: transit_allocation_fulfillment_queue
    source:
      database: transit_server
      table: transit_allocation_fulfillment_queue
  - flow_id: transit_server.transit_courier_order.v1
    topic_name: postgres.transit_server.public.transit_courier_order
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: courier_partner_code
          type: varchar(50)
        - name: courier_tracking_id
          type: varchar(100)
        - name: courier_tracking_type
          type: varchar(50)
        - name: reference_id
          type: varchar(100)
        - name: reference_type
          type: varchar(50)
        - name: tenant
          type: varchar(50)
        - name: status
          type: varchar(50)
        - name: remarks
          type: varchar(max)
        - name: pickup_details
          type: varchar(max)
        - name: delivery_details
          type: varchar(max)
        - name: metadata
          type: varchar(max)
        - name: is_active
          type: boolean
        - name: refreshed_at
          type: timestamp
        - name: destination_node_id
          type: varchar(100)
        - name: destination_node_type
          type: varchar(30)
        - name: source_node_id
          type: varchar(100)
        - name: source_node_type
          type: varchar(30)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_courier_order/
      schema: lake_transit_server
      sortkey: []
      table: transit_courier_order
    source:
      database: transit_server
      table: transit_courier_order
  - flow_id: transit_server.transit_courier_order_details.v1
    topic_name: postgres.transit_server.public.transit_courier_order_details
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: cost_details
          type: varchar(max)
        - name: transporter_document_details
          type: varchar(max)
        - name: order_document_details
          type: varchar(max)
        - name: courier_order_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_courier_order_details/
      schema: lake_transit_server
      sortkey: []
      table: transit_courier_order_details
    source:
      database: transit_server
      table: transit_courier_order_details
  - flow_id: transit_server.transit_courier_order_log.v1
    topic_name: postgres.transit_server.public.transit_courier_order_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: status
          type: varchar(50)
        - name: remarks
          type: varchar(max)
        - name: metadata
          type: varchar(max)
        - name: event_time
          type: timestamp
        - name: courier_order_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_courier_order_log/
      schema: lake_transit_server
      sortkey: []
      table: transit_courier_order_log
    source:
      database: transit_server
      table: transit_courier_order_log
  - flow_id: transit_server.transit_courier_pickup.v1
    topic_name: postgres.transit_server.public.transit_courier_pickup
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: created_by
          type: varchar(100)
        - name: updated_by
          type: varchar(100)
        - name: source_node_id
          type: varchar(100)
        - name: source_node_type
          type: varchar(30)
        - name: pickup_date
          type: date
        - name: scheduled_pickup_time
          type: time
        - name: status
          type: varchar(50)
        - name: courier_partner_code
          type: varchar(50)
        - name: courier_partner_request_id
          type: varchar(100)
        - name: courier_partner_request_type
          type: varchar(50)
        - name: pickup_details
          type: varchar(max)
        - name: metadata
          type: varchar(max)
      copy_params:
        - ESCAPE
        - REMOVEQUOTES
        - DELIMITER ','
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_courier_pickup/
      schema: lake_transit_server
      sortkey: []
      table: transit_courier_pickup
    source:
      database: transit_server
      table: transit_courier_pickup
  - flow_id: transit_server.transit_delivery_challan.v1
    topic_name: postgres.transit_server.public.transit_delivery_challan
    redshift_replication: false
    sink:
      column_dtypes:
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: id
          type: varchar(30)
        - name: state
          type: varchar(15)
        - name: reference_invoice_id
          type: varchar(30)
        - name: reference_invoice_type
          type: varchar(20)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_delivery_challan/
      schema: lake_transit_server
      sortkey: []
      table: transit_delivery_challan
    source:
      database: transit_server
      table: transit_delivery_challan
  - flow_id: transit_server.transit_delivery_challan_item.v1
    topic_name: postgres.transit_server.public.transit_delivery_challan_item
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: delivery_challan_id
          type: varchar(30)
        - name: variant_id
          type: varchar(250)
        - name: quantity
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_delivery_challan_item/
      schema: lake_transit_server
      sortkey: []
      table: transit_delivery_challan_item
    source:
      database: transit_server
      table: transit_delivery_challan_item
  - flow_id: transit_server.transit_discrepancy_notification_mapping.v1
    topic_name: postgres.transit_server.public.transit_discrepancy_notification_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: discrepancy_id
          type: int8
        - name: notification_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_discrepancy_notification_mapping/
      schema: lake_transit_server
      sortkey: []
      table: transit_discrepancy_notification_mapping
    source:
      database: transit_server
      table: transit_discrepancy_notification_mapping
  - flow_id: transit_server.transit_ewaybill.v1
    topic_name: postgres.transit_server.public.transit_ewaybill
    redshift_replication: false
    sink:
      column_dtypes:
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: invoice_id
          type: varchar(16)
        - name: source_gstin
          type: varchar(50)
        - name: vehicle_number
          type: varchar(20)
        - name: status
          type: varchar(50)
        - name: source_store_id_old
          type: varchar(50)
        - name: destination_store_id_old
          type: varchar(50)
        - name: ewb_no
          type: varchar(50)
        - name: ewb_creation_timestamp
          type: timestamp
        - name: ewb_valid_till
          type: timestamp
        - name: flow_type
          type: varchar(15)
        - name: error_msg
          type: varchar(max)
        - name: meta
          type: varchar(max)
        - name: invoice_type
          type: varchar(50)
        - name: is_ewaybill_eligible
          type: boolean
        - name: transporter_id
          type: varchar(50)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: invoice_id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [invoice_id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_ewaybill/
      schema: lake_transit_server
      sortkey: []
      table: transit_ewaybill
    source:
      database: transit_server
      table: transit_ewaybill
  - flow_id: transit_server.transit_metrics.v1
    topic_name: postgres.transit_server.public.transit_metrics
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: metrics_group_id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: trips_planned
          type: int4
        - name: trips_dispatched_on_time
          type: int4
        - name: trips_dispatched_with_delay
          type: int4
        - name: trips_loading_in_progress
          type: int4
        - name: trips_source_checked_in
          type: int4
        - name: trips_with_delay_risk
          type: int4
        - name: trips_pending_from_two_hours
          type: int4
        - name: floor_pendency
          type: int4
        - name: dispatched_crates
          type: int4
        - name: expected_spillover_crates
          type: int4
        - name: on_time_source_arrival
          type: int4
        - name: trips_destination_checked_in_on_time
          type: int4
        - name: trips_destination_checked_in_with_delay
          type: int4
        - name: trips_loading_in_progress_on_time
          type: int4
        - name: trips_loading_in_progress_with_delay
          type: int4
        - name: trips_source_checked_in_on_time
          type: int4
        - name: trips_source_checked_in_with_delay
          type: int4
        - name: perfect_trips
          type: int4
        - name: snapshot_meta
          type: varchar(max)
        - name: loading_time
          type: int4
        - name: idle_time
          type: int4
        - name: trips_enroute_on_time
          type: int4
        - name: trips_enroute_with_delay
          type: int4
        - name: trips_awaiting_unloading_at_store
          type: int4
        - name: vehicle_utilization_percentage
          type: int4
        - name: upcoming_trips_delay_risk
          type: int4
        - name: aligned_vehicle_capacity_kg
          type: int4
        - name: dispatch_demand_count
          type: int4
        - name: consignment_destination_checked_in_on_time
          type: int4
        - name: consignment_destination_checked_in_with_delay
          type: int4
        - name: vehicles_returning_to_wh
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_metrics/
      schema: lake_transit_server
      sortkey: []
      table: transit_metrics
    source:
      database: transit_server
      table: transit_metrics
  - flow_id: transit_server.transit_metrics_group.v1
    topic_name: postgres.transit_server.public.transit_metrics_group
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: source_node_id
          type: varchar(7)
        - name: destination_node_id
          type: varchar(7)
        - name: metric_date
          type: date
        - name: dispatch_slot
          type: time
      copy_params:
        - ESCAPE
        - REMOVEQUOTES
        - DELIMITER ','
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_metrics_group/
      schema: lake_transit_server
      sortkey: []
      table: transit_metrics_group
    source:
      database: transit_server
      table: transit_metrics_group
  - flow_id: transit_server.transit_metrics_group_v2.v1
    topic_name: postgres.transit_server.public.transit_metrics_group_v2
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: view_type
          type: varchar(50)
        - name: metric_date
          type: date
        - name: source_node_id
          type: varchar(7)
        - name: dispatch_slot
          type: time
        - name: timing_label
          type: varchar(255)
        - name: aggregation_label
          type: varchar(255)
      copy_params:
        - ESCAPE
        - REMOVEQUOTES
        - DELIMITER ','
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_metrics_group_v2/
      schema: lake_transit_server
      sortkey: []
      table: transit_metrics_group_v2
    source:
      database: transit_server
      table: transit_metrics_group_v2
  - flow_id: transit_server.transit_metrics_v2.v1
    topic_name: postgres.transit_server.public.transit_metrics_v2
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: loading_time
          type: int4
        - name: idle_time
          type: int4
        - name: trips_planned
          type: int4
        - name: trips_source_checked_in
          type: int4
        - name: trips_loading_in_progress
          type: int4
        - name: trips_dispatched
          type: int4
        - name: trips_enroute
          type: int4
        - name: trips_destination_checked_in
          type: int4
        - name: trips_awaiting_unloading_at_store
          type: int4
        - name: trips_pending_from_two_hours
          type: int4
        - name: floor_pendency
          type: int4
        - name: dispatched_crates
          type: int4
        - name: expected_spillover_crates
          type: int4
        - name: perfect_trips
          type: int4
        - name: vehicle_utilization_percentage
          type: int4
        - name: aligned_vehicle_capacity_kg
          type: int4
        - name: metrics_group_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_metrics_v2/
      schema: lake_transit_server
      sortkey: []
      table: transit_metrics_v2
    source:
      database: transit_server
      table: transit_metrics_v2
  - flow_id: transit_server.transit_node_mapping.v1
    topic_name: postgres.transit_server.public.transit_node_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: created_by
          type: varchar(100)
        - name: updated_by
          type: varchar(100)
        - name: mapping_type
          type: varchar(50)
        - name: parent_node_id
          type: varchar(30)
        - name: child_node_id
          type: varchar(30)
        - name: metadata
          type: varchar(max)
        - name: is_active
          type: boolean
        - name: expected_distance
          type: int4
        - name: distance_update_ts
          type: timestamp
        - name: expected_duration
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_node_mapping/
      schema: lake_transit_server
      sortkey: []
      table: transit_node_mapping
    source:
      database: transit_server
      table: transit_node_mapping
  - flow_id: transit_server.transit_notification.v1
    topic_name: postgres.transit_server.public.transit_notification
    redshift_replication: false
    sink:
      column_dtypes:
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: id
          type: int8
        - name: notification_medium
          type: varchar(50)
        - name: notification_type
          type: varchar(50)
        - name: notification_payload
          type: varchar(max)
        - name: sender_identifier
          type: varchar(250)
        - name: meta
          type: varchar(max)
        - name: sender_type
          type: varchar(50)
        - name: push_notification
          type: boolean
        - name: entity_id
          type: varchar(50)
        - name: entity_type
          type: varchar(50)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_notification/
      schema: lake_transit_server
      sortkey: []
      table: transit_notification
    source:
      database: transit_server
      table: transit_notification
  - flow_id: transit_server.transit_notification_user_mapping.v1
    topic_name: postgres.transit_server.public.transit_notification_user_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: recipient_id
          type: varchar(50)
        - name: recipient_type
          type: varchar(50)
        - name: is_read
          type: boolean
        - name: meta
          type: varchar(max)
        - name: is_active
          type: boolean
        - name: notification_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_notification_user_mapping/
      schema: lake_transit_server
      sortkey: []
      table: transit_notification_user_mapping
    source:
      database: transit_server
      table: transit_notification_user_mapping
  - flow_id: transit_server.transit_projection_dispatch_demand.v1
    topic_name: postgres.transit_server.public.transit_projection_dispatch_demand
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: source_node_id
          type: varchar(30)
        - name: source_meta
          type: varchar(max)
        - name: destination_node_id
          type: varchar(30)
        - name: dispatch_time
          type: timestamp
        - name: destination_meta
          type: varchar(max)
        - name: is_active
          type: boolean
        - name: dispatch_ready_container_count
          type: int4
        - name: dispatched_container_count
          type: int4
        - name: planned_container_count
          type: int4
        - name: created_by
          type: varchar(100)
        - name: updated_by
          type: varchar(100)
        - name: meta
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_projection_dispatch_demand/
      schema: lake_transit_server
      sortkey: []
      table: transit_projection_dispatch_demand
    source:
      database: transit_server
      table: transit_projection_dispatch_demand
  - flow_id: transit_server.transit_projection_plan_vehicle.v1
    topic_name: postgres.transit_server.public.transit_projection_plan_vehicle
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: allocation_vehicle_id
          type: int8
        - name: vehicle_number
          type: varchar(30)
        - name: vehicle_type
          type: varchar(30)
        - name: vehicle_capacity_kg
          type: float8
        - name: dispatch_time
          type: timestamp
        - name: activity_type
          type: varchar(30)
        - name: trip_id
          type: varchar(30)
        - name: delay_duration_minutes
          type: float8
        - name: source_node_id
          type: varchar(30)
        - name: source_meta
          type: varchar(max)
        - name: state
          type: varchar(30)
        - name: billable_vehicle_type
          type: varchar(50)
        - name: dock_name
          type: varchar(30)
        - name: handling_type
          type: varchar(30)
        - name: vehicle_billing_type
          type: varchar(30)
        - name: vehicle_vendor_id
          type: int4
        - name: vehicle_vendor_meta
          type: varchar(max)
        - name: fleet_plan_dispatch_time
          type: timestamp
        - name: approved_by
          type: varchar(100)
        - name: approver_meta
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_projection_plan_vehicle/
      schema: lake_transit_server
      sortkey: []
      table: transit_projection_plan_vehicle
    source:
      database: transit_server
      table: transit_projection_plan_vehicle
  - flow_id: transit_server.transit_projection_plan_vehicle_destination.v1
    topic_name: postgres.transit_server.public.transit_projection_plan_vehicle_destination
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: allocation_vehicle_id
          type: int8
        - name: destination_node_id
          type: varchar(30)
        - name: destination_meta
          type: varchar(max)
        - name: is_active
          type: boolean
        - name: targeted_dispatch_time
          type: timestamp
        - name: load_destinations
          type: varchar(max)
        - name: meta
          type: varchar(max)
        - name: trip_id
          type: varchar(30)
        - name: targeted_arrival_time
          type: timestamp
        - name: created_by
          type: varchar(100)
        - name: updated_by
          type: varchar(100)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_projection_plan_vehicle_destination/
      schema: lake_transit_server
      sortkey: []
      table: transit_projection_plan_vehicle_destination
    source:
      database: transit_server
      table: transit_projection_plan_vehicle_destination
  - flow_id: transit_server.transit_projection_trip_duration.v1
    topic_name: postgres.transit_server.public.transit_projection_trip_duration
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: trip_id
          type: varchar(20)
        - name: source_node_id
          type: varchar(20)
        - name: destination_node_id
          type: varchar(20)
        - name: start_ts
          type: timestamp
        - name: end_ts
          type: timestamp
        - name: expected_distance
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_projection_trip_duration/
      schema: lake_transit_server
      sortkey: []
      table: transit_projection_trip_duration
    source:
      database: transit_server
      table: transit_projection_trip_duration
  - flow_id: transit_server.transit_task_node_detail.v1
    topic_name: postgres.transit_server.public.transit_task_node_detail
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: event
          type: varchar(50)
        - name: node_id
          type: varchar(10)
        - name: meta
          type: varchar(max)
        - name: trip_travel_task_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_task_node_detail/
      schema: lake_transit_server
      sortkey: []
      table: transit_task_node_detail
    source:
      database: transit_server
      table: transit_task_node_detail
  - flow_id: transit_server.transit_travel_segment.v1
    topic_name: postgres.transit_server.public.transit_travel_segment
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: trip_id
          type: varchar(50)
        - name: travelled_polyline
          type: varchar(max)
        - name: start_node_id
          type: varchar(20)
        - name: end_node_id
          type: varchar(20)
        - name: travelled_distance_metres
          type: numeric(10, 3)
        - name: travelled_duration_secs
          type: numeric(10, 3)
        - name: metadata
          type: varchar(max)
        - name: last_location_ts
          type: timestamp
        - name: segment_type
          type: varchar(30)
        - name: halts
          type: varchar(max)
        - name: markers
          type: varchar(max)
        - name: delays
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_travel_segment/
      schema: lake_transit_server
      sortkey: []
      table: transit_travel_segment
    source:
      database: transit_server
      table: transit_travel_segment
  - flow_id: transit_server.transit_user_group.v1
    topic_name: postgres.transit_server.public.transit_user_group
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: name
          type: varchar(max)
        - name: metadata
          type: varchar(max)
        - name: mapping_id
          type: varchar(max)
        - name: parent_group_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_user_group/
      schema: lake_transit_server
      sortkey: []
      table: transit_user_group
    source:
      database: transit_server
      table: transit_user_group
  - flow_id: transit_server.transit_user_group_view_mapping.v1
    topic_name: postgres.transit_server.public.transit_user_group_view_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: view
          type: varchar(max)
        - name: metdata
          type: varchar(max)
        - name: user_group_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_user_group_view_mapping/
      schema: lake_transit_server
      sortkey: []
      table: transit_user_group_view_mapping
    source:
      database: transit_server
      table: transit_user_group_view_mapping
  - flow_id: transit_server.transit_user_profile_group.v1
    topic_name: postgres.transit_server.public.transit_user_profile_group
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: user_group_id
          type: int4
        - name: user_profile_id
          type: int4
        - name: is_active
          type: boolean
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_user_profile_group/
      schema: lake_transit_server
      sortkey: []
      table: transit_user_profile_group
    source:
      database: transit_server
      table: transit_user_profile_group
  - flow_id: transit_server.transit_user_session.v1
    topic_name: postgres.transit_server.public.transit_user_session
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: device_id
          type: varchar(50)
        - name: app_name
          type: varchar(30)
        - name: app_version
          type: varchar(20)
        - name: meta
          type: varchar(max)
        - name: is_active
          type: boolean
        - name: user_profile_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.transit_server.public.transit_user_session/
      schema: lake_transit_server
      sortkey: []
      table: transit_user_session
    source:
      database: transit_server
      table: transit_user_session
tags:
  - de
  - replicate
  - transit_server_3
template_name: nessie
version: 1
