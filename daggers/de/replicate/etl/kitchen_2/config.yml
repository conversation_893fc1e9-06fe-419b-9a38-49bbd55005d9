dag_name: kitchen_2
dag_type: etl
escalation_priority: low
alert_configs:
  slack:
    - channel: bl-data-alerts-p1
  error_channel_mapping:
    terminated_with_errors: bl-data-spot-interruptions
    user_request: bl-data-spot-interruptions
    spot: bl-data-spot-interruptions
executor:
  config:
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    cpu:
      limit: 1
      request: 0.3
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    memory:
      limit: 500M
      request: 250M
    node_selectors:
      nodetype: airflow-srp-spot
    tolerations:
      - effect: NoSchedule
        key: service
        operator: Equal
        value: airflow-srp
    volume_mounts:
      - mountPath: /usr/local/airflow/plugins
        name: airflow-dags
        subPath: airflow-de/plugins
  type: celery
namespace: de
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ
path: de/replicate/etl/kitchen_2
paused: false
project_name: replicate
schedule:
  interval: 13,43 * * * *
  start_date: "2024-11-25T00:00:00"
  end_date: "2025-11-20T00:00:00"
schedule_type: fixed
sla: 180 minutes
task_concurrency: 3
poke_interval: 180
redshift_sla_seconds: 10800
emr_sensor: {}
nessie_dag_split: true
spark:
  aws_conn_id: aws_default
  mode: emr
  nessie_jar_version: 2.1.2
  cluster_reuse: true
  emr_version: 6.11.1
support_files: []
tables:
  - flow_id: kitchen.orders.v1
    topic_name: postgres.kitchen.public.orders
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: external_order_id
          type: int8
        - name: store_id
          type: int4
        - name: status
          type: varchar(64)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: dropzone_id
          type: varchar(64)
        - name: partner_name
          type: varchar(255)
        - name: partner_phone_number
          type: varchar(64)
        - name: serial_number
          type: int4
        - name: picker_employee_id
          type: varchar(255)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.kitchen.public.orders/
      schema: lake_kitchen
      sortkey: []
      table: orders
    source:
      database: kitchen
      table: orders
  - flow_id: kitchen.sub_order.v1
    topic_name: postgres.kitchen.public.sub_order
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: kitchen_order_id
          type: int8
        - name: product_id
          type: int8
        - name: item_id
          type: int8
        - name: name
          type: varchar(255)
        - name: status
          type: varchar(64)
        - name: prep_start_time
          type: timestamp
        - name: prep_end_time
          type: timestamp
        - name: cutlery_id
          type: int8
        - name: packaging_id
          type: int8
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: oms_base_item_id
          type: int8
        - name: oms_item_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.kitchen.public.sub_order/
      schema: lake_kitchen
      sortkey: []
      table: sub_order
    source:
      database: kitchen
      table: sub_order
  - flow_id: kitchen.sub_order_item.v1
    topic_name: postgres.kitchen.public.sub_order_item
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: sub_order_id
          type: int8
        - name: product_id
          type: int8
        - name: item_id
          type: int8
        - name: name
          type: varchar(255)
        - name: quantity
          type: int4
        - name: status
          type: varchar(64)
        - name: station_type
          type: varchar(64)
        - name: kpt
          type: int8
        - name: prep_start_time
          type: timestamp
        - name: prep_end_time
          type: timestamp
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: wait_time
          type: int8
        - name: defer_start_time
          type: timestamp
        - name: predicted_defer_time
          type: int8
        - name: defer_end_time
          type: timestamp
        - name: is_preprepared
          type: int4
        - name: station_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.kitchen.public.sub_order_item/
      schema: lake_kitchen
      sortkey: []
      table: sub_order_item
    source:
      database: kitchen
      table: sub_order_item
  - flow_id: kitchen.complaints_rca.v1
    topic_name: postgres.kitchen.public.complaints_rca
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: complaint_id
          type: int8
        - name: reference_id
          type: int8
        - name: reference_type
          type: varchar(255)
        - name: status
          type: varchar(255)
        - name: at_fault_role
          type: varchar(255)
        - name: reason_code
          type: varchar(255)
        - name: description
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.kitchen.public.complaints_rca/
      schema: lake_kitchen
      sortkey: []
      table: complaints_rca
    source:
      database: kitchen
      table: complaints_rca
tags:
  - de
  - replicate
  - kitchen_2
template_name: nessie
version: 1
