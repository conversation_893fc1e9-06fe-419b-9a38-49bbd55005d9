dag_name: user_management_1
dag_type: etl
escalation_priority: low

alert_configs:
  slack:
    - channel: bl-data-alerts-p1
  error_channel_mapping:
    terminated_with_errors: bl-data-spot-interruptions
    spot: bl-data-spot-interruptions

executor:
  config:
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    cpu:
      limit: 1
      request: 0.3
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    memory:
      limit: 500M
      request: 250M
    node_selectors:
      nodetype: airflow-srp-spot
    tolerations:
      - effect: NoSchedule
        key: service
        operator: Equal
        value: airflow-srp
    volume_mounts:
      - mountPath: /usr/local/airflow/plugins
        name: airflow-dags
        subPath: airflow-de/plugins
  type: celery
namespace: de
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ

path: de/replicate/etl/user_management_1
paused: false
project_name: replicate
schedule:
  end_date: "2026-05-08T00:00:00"
  interval: 0 */3 * * *
  start_date: "2025-05-13T00:00:00"
schedule_type: fixed
sla: 180 minutes
task_concurrency: 8
poke_interval: 180
redshift_sla_seconds: 10800
emr_sensor: {}
spark:
  aws_conn_id: aws_default
  mode: emr
  nessie_jar_version: 2.1.2
  cluster_reuse: true
  emr_version: 6.11.1
support_files: []
tables:
  - flow_id: user_management.access_groups.v1
    topic_name: postgres.user_management.public.access_groups
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: name
          type: varchar(50)
        - name: tenant_id
          type: varchar(36)
        - name: enabled
          type: boolean
        - name: restricted
          type: boolean
        - name: created_by
          type: varchar(50)
        - name: updated_by
          type: varchar(50)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.user_management.public.access_groups/
      schema: lake_user_management
      sortkey: []
      table: access_groups
    source:
      database: user_management
      table: access_groups
  - flow_id: user_management.departments.v1
    topic_name: postgres.user_management.public.departments
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: name
          type: varchar(50)
        - name: display_name
          type: varchar(100)
        - name: description
          type: varchar(200)
        - name: tenant_id
          type: varchar(36)
        - name: enabled
          type: boolean
        - name: created_by
          type: varchar(50)
        - name: updated_by
          type: varchar(50)
        - name: parent_department_id
          type: int4
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.user_management.public.departments/
      schema: lake_user_management
      sortkey: []
      table: departments
    source:
      database: user_management
      table: departments
  - flow_id: user_management.designations.v1
    topic_name: postgres.user_management.public.designations
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: name
          type: varchar(50)
        - name: display_name
          type: varchar(100)
        - name: description
          type: varchar(200)
        - name: tenant_id
          type: varchar(36)
        - name: enabled
          type: boolean
        - name: created_by
          type: varchar(50)
        - name: updated_by
          type: varchar(50)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.user_management.public.designations/
      schema: lake_user_management
      sortkey: []
      table: designations
    source:
      database: user_management
      table: designations
  - flow_id: user_management.enrollments.v1
    topic_name: postgres.user_management.public.enrollments
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: tenant_id
          type: varchar(255)
        - name: employee_id
          type: varchar(50)
        - name: enrollment_type
          type: varchar(50)
        - name: state
          type: varchar(50)
        - name: valid_till
          type: timestamp
        - name: enrolled_at
          type: timestamp
        - name: meta
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: created_by
          type: varchar(50)
        - name: updated_by
          type: varchar(50)
        - name: is_active
          type: boolean
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.user_management.public.enrollments/
      schema: lake_user_management
      sortkey: []
      table: enrollments
    source:
      database: user_management
      table: enrollments
  - flow_id: user_management.rosters.v1
    topic_name: postgres.user_management.public.rosters
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: site_id
          type: varchar(50)
        - name: tenant_id
          type: varchar(36)
        - name: start_time
          type: timestamp
        - name: end_time
          type: timestamp
        - name: active
          type: boolean
        - name: created_by
          type: varchar(50)
        - name: updated_by
          type: varchar(50)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: name
          type: varchar(100)
        - name: display_name
          type: varchar(100)
        - name: type
          type: varchar(50)
        - name: capacity
          type: int4
        - name: identifier_key
          type: varchar(50)
        - name: identifier_value
          type: varchar(50)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.user_management.public.rosters/
      schema: lake_user_management
      sortkey: []
      table: rosters
    source:
      database: user_management
      table: rosters
  - flow_id: user_management.user_access_group_mappings.v1
    topic_name: postgres.user_management.public.user_access_group_mappings
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: user_id
          type: int8
        - name: access_group_id
          type: int8
        - name: enabled
          type: boolean
        - name: created_by
          type: varchar(50)
        - name: updated_by
          type: varchar(50)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.user_management.public.user_access_group_mappings/
      schema: lake_user_management
      sortkey: []
      table: user_access_group_mappings
    source:
      database: user_management
      table: user_access_group_mappings
  - flow_id: user_management.user_documents.v1
    topic_name: postgres.user_management.public.user_documents
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: user_id
          type: int8
        - name: document_id
          type: varchar(max)
        - name: document_type
          type: varchar(255)
        - name: document_url
          type: varchar(max)
        - name: additional_info
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: active
          type: boolean
        - name: tenant_id
          type: varchar(36)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.user_management.public.user_documents/
      schema: lake_user_management
      sortkey: []
      table: user_documents
    source:
      database: user_management
      table: user_documents
  - flow_id: user_management.user_properties.v1
    topic_name: postgres.user_management.public.user_properties
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: user_id
          type: int8
        - name: entity_name
          type: varchar(50)
        - name: entity_id
          type: varchar(50)
        - name: department_id
          type: int4
        - name: designation_id
          type: int4
        - name: additional_properties
          type: varchar(max)
        - name: created_by
          type: varchar(100)
        - name: updated_by
          type: varchar(100)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.user_management.public.user_properties/
      schema: lake_user_management
      sortkey: []
      table: user_properties
    source:
      database: user_management
      table: user_properties
  - flow_id: user_management.user_rosters.v1
    topic_name: postgres.user_management.public.user_rosters
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: user_id
          type: int8
        - name: roster_id
          type: int8
        - name: type_of_leave
          type: varchar(50)
        - name: active
          type: boolean
        - name: created_by
          type: varchar(50)
        - name: updated_by
          type: varchar(50)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: employee_id
          type: varchar(50)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.user_management.public.user_rosters/
      schema: lake_user_management
      sortkey: []
      table: user_rosters
    source:
      database: user_management
      table: user_rosters
  - flow_id: user_management.users.v1
    topic_name: postgres.user_management.public.users
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: employee_id
          type: varchar(50)
        - name: tenant_id
          type: varchar(36)
        - name: primary_site_id
          type: varchar(50)
        - name: first_name
          type: varchar(50)
        - name: last_name
          type: varchar(50)
        - name: email_id
          type: varchar(100)
        - name: department_id
          type: int4
        - name: designation_id
          type: int4
        - name: phone
          type: varchar(20)
        - name: status
          type: varchar(50)
        - name: date_of_joining
          type: date
        - name: date_of_exit
          type: date
        - name: meta
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.user_management.public.users/
      schema: lake_user_management
      sortkey: []
      table: users
    source:
      database: user_management
      table: users
tags:
  - de
  - replicate
  - user_management_1
template_name: nessie
version: 1
