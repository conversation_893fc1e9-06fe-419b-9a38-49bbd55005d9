dag_name: locus_v2_1
dag_type: etl
escalation_priority: low

alert_configs:
  slack:
    - channel: bl-data-alerts-p1
  error_channel_mapping:
    terminated_with_errors: bl-data-spot-interruptions
    spot: bl-data-spot-interruptions

executor:
  config:
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    cpu:
      limit: 1
      request: 0.3
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    memory:
      limit: 500M
      request: 250M
    node_selectors:
      nodetype: airflow-srp-spot
    tolerations:
      - effect: NoSchedule
        key: service
        operator: Equal
        value: airflow-srp
    volume_mounts:
      - mountPath: /usr/local/airflow/plugins
        name: airflow-dags
        subPath: airflow-de/plugins
  type: celery
namespace: de
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ

path: de/replicate/etl/locus_v2_1
paused: false
project_name: replicate
schedule:
  end_date: "2026-01-07T00:00:00"
  interval: 0 */1 * * *
  start_date: "2024-08-20T00:00:00"
schedule_type: fixed
sla: 180 minutes
task_concurrency: 9
poke_interval: 180
redshift_sla_seconds: 10800
emr_sensor: {}
spark:
  aws_conn_id: aws_default
  mode: emr
  nessie_jar_version: 2.1.2
  cluster_reuse: true
  emr_version: 6.11.1
support_files: []
tables:
  - flow_id: locus_v2.document_classifications.v1
    topic_name: postgres.locus_v2.public.document_classifications
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: type
          type: varchar(max)
        - name: sub_type
          type: varchar(max)
        - name: is_active
          type: boolean
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: category
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.locus_v2.public.document_classifications/
      schema: lake_locus_v2
      sortkey: []
      table: document_classifications
    source:
      database: locus_v2
      table: document_classifications
  - flow_id: locus_v2.document_form_mappings.v1
    topic_name: postgres.locus_v2.public.document_form_mappings
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: form_id
          type: int8
        - name: document_classification_id
          type: int8
        - name: team_id
          type: int8
        - name: document_id
          type: int8
        - name: is_active
          type: boolean
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: sub_form_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.locus_v2.public.document_form_mappings/
      schema: lake_locus_v2
      sortkey: []
      table: document_form_mappings
    source:
      database: locus_v2
      table: document_form_mappings
  - flow_id: locus_v2.document_records.v1
    topic_name: postgres.locus_v2.public.document_records
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: name
          type: varchar(max)
        - name: path
          type: varchar(max)
        - name: thumbnail_path
          type: varchar(max)
        - name: is_archived
          type: boolean
        - name: parent_id
          type: int8
        - name: created_by
          type: int8
        - name: is_active
          type: boolean
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: remarks
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.locus_v2.public.document_records/
      schema: lake_locus_v2
      sortkey: []
      table: document_records
    source:
      database: locus_v2
      table: document_records
  - flow_id: locus_v2.form.v1
    topic_name: postgres.locus_v2.public.form
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: user_id
          type: int8
        - name: project_id
          type: int8
        - name: zone_id
          type: int8
        - name: form_type
          type: varchar(max)
        - name: form_version
          type: varchar(max)
        - name: form_data
          type: varchar(max)
        - name: is_active
          type: boolean
        - name: updated_by
          type: int8
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.locus_v2.public.form/
      schema: lake_locus_v2
      sortkey: []
      table: form
    source:
      database: locus_v2
      table: form
  - flow_id: locus_v2.form_final_status.v1
    topic_name: postgres.locus_v2.public.form_final_status
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: form_id
          type: int8
        - name: final_status_id
          type: int8
        - name: is_active
          type: boolean
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.locus_v2.public.form_final_status/
      schema: lake_locus_v2
      sortkey: []
      table: form_final_status
    source:
      database: locus_v2
      table: form_final_status
  - flow_id: locus_v2.form_final_status_lookup.v1
    topic_name: postgres.locus_v2.public.form_final_status_lookup
    redshift_replication: false
    sink:
      column_dtypes:
        - name: form_id
          type: int8
        - name: team_id
          type: int8
        - name: status_id
          type: int8
        - name: branch
          type: int8
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: form_id,team_id,status_id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [form_id,team_id,status_id]
      s3_url: s3://prod-dse-nessie-output/postgres.locus_v2.public.form_final_status_lookup/
      schema: lake_locus_v2
      sortkey: []
      table: form_final_status_lookup
    source:
      database: locus_v2
      table: form_final_status_lookup
  - flow_id: locus_v2.form_team_status_mapping.v1
    topic_name: postgres.locus_v2.public.form_team_status_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: form_id
          type: int8
        - name: team_id
          type: int8
        - name: status_id
          type: int8
        - name: updated_by
          type: int8
        - name: reason
          type: varchar(max)
        - name: meta
          type: varchar(max)
        - name: is_active
          type: boolean
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.locus_v2.public.form_team_status_mapping/
      schema: lake_locus_v2
      sortkey: []
      table: form_team_status_mapping
    source:
      database: locus_v2
      table: form_team_status_mapping
  - flow_id: locus_v2.network_team.v1
    topic_name: postgres.locus_v2.public.network_team
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: name
          type: varchar(max)
        - name: is_active
          type: boolean
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.locus_v2.public.network_team/
      schema: lake_locus_v2
      sortkey: []
      table: network_team
    source:
      database: locus_v2
      table: network_team
  - flow_id: locus_v2.project.v1
    topic_name: postgres.locus_v2.public.project
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: project_id
          type: varchar(max)
        - name: zone_id
          type: int8
        - name: system_zone_id
          type: varchar(max)
        - name: zone
          type: varchar(max)
        - name: network_action
          type: varchar(max)
        - name: nsa_addition
          type: varchar(max)
        - name: target_area
          type: varchar(max)
        - name: latitude
          type: numeric(17, 15)
        - name: longitude
          type: numeric(17, 15)
        - name: comments
          type: varchar(max)
        - name: priority_order
          type: varchar(max)
        - name: final_rank
          type: int8
        - name: status
          type: varchar(max)
        - name: quarter_focus
          type: varchar(max)
        - name: is_active
          type: boolean
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: created_by
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.locus_v2.public.project/
      schema: lake_locus_v2
      sortkey: []
      table: project
    source:
      database: locus_v2
      table: project
#  - flow_id: locus_v2.user_data.v1
#    topic_name: postgres.locus_v2.public.user_data
#    redshift_replication: false
#    sink:
#      column_dtypes:
#        - name: id
#          type: int8
#        - name: first_name
#          type: varchar(max)
#        - name: last_name
#          type: varchar(max)
#        - name: phone
#          type: varchar(max)
#        - name: is_active
#          type: boolean
#        - name: email
#          type: varchar(max)
#        - name: device_token
#          type: varchar(max)
#        - name: created_at
#          type: timestamp
#        - name: updated_at
#          type: timestamp
#      copy_params:
#        - FORMAT AS PARQUET
#      incremental_key: id
#      force_upsert_without_increment_check: True
#      load_type: upsert
#      primary_key: [id]
#      s3_url: s3://prod-dse-nessie-output/postgres.locus_v2.public.user_data/
#      schema: lake_locus_v2
#      sortkey: []
#      table: user_data
#    source:
#      database: locus_v2
#      table: user_data
  - flow_id: locus_v2.project_final_status_lookup.v1
    topic_name: postgres.locus_v2.public.project_final_status_lookup
    redshift_replication: false
    sink:
      column_dtypes:
        - name: project_id
          type: int8
        - name: form_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: form_id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [form_id]
      s3_url: s3://prod-dse-nessie-output/postgres.locus_v2.public.project_final_status_lookup/
      schema: lake_locus_v2
      sortkey: []
      table: project_final_status_lookup
    source:
      database: locus_v2
      table: project_final_status_lookup
  - flow_id: locus_v2.status_type.v1
    topic_name: postgres.locus_v2.public.status_type
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: name
          type: varchar(max)
        - name: is_active
          type: boolean
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.locus_v2.public.status_type/
      schema: lake_locus_v2
      sortkey: []
      table: status_type
    source:
      database: locus_v2
      table: status_type
  - flow_id: locus_v2.sub_forms.v1
    topic_name: postgres.locus_v2.public.sub_forms
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: team_id
          type: int8
        - name: form_id
          type: int8
        - name: form_data
          type: varchar(max)
        - name: created_by
          type: int8
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: is_active
          type: boolean
        - name: updated_by
          type: int8
        - name: version
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.locus_v2.public.sub_forms/
      schema: lake_locus_v2
      sortkey: []
      table: sub_forms
    source:
      database: locus_v2
      table: sub_forms
  - flow_id: locus_v2.team_final_status.v1
    topic_name: postgres.locus_v2.public.team_final_status
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: form_id
          type: int8
        - name: team_id
          type: int8
        - name: final_status_id
          type: int8
        - name: is_active
          type: boolean
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.locus_v2.public.team_final_status/
      schema: lake_locus_v2
      sortkey: []
      table: team_final_status
    source:
      database: locus_v2
      table: team_final_status
  - flow_id: locus_v2.user_data.v2
    topic_name: postgres.locus_v2.public.user_data
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: first_name
          type: varchar(max)
        - name: last_name
          type: varchar(max)
        - name: phone
          type: varchar(max)
        - name: is_active
          type: boolean
        - name: email
          type: varchar(max)
        - name: device_token
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.locus_v2.public.user_data/
      schema: lake_locus_v2
      sortkey: []
      table: user_data
    source:
      database: locus_v2
      table: user_data
  - flow_id: locus_v2.user_ops_form_mapping.v1
    topic_name: postgres.locus_v2.public.user_ops_form_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: user_id
          type: int8
        - name: form_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.locus_v2.public.user_ops_form_mapping/
      schema: lake_locus_v2
      sortkey: []
      table: user_ops_form_mapping
    source:
      database: locus_v2
      table: user_ops_form_mapping
  - flow_id: locus_v2.user_re_project_mapping.v1
    topic_name: postgres.locus_v2.public.user_re_project_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: project_id
          type: int8
        - name: user_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.locus_v2.public.user_re_project_mapping/
      schema: lake_locus_v2
      sortkey: []
      table: user_re_project_mapping
    source:
      database: locus_v2
      table: user_re_project_mapping
  - flow_id: locus_v2.user_role_mapping.v1
    topic_name: postgres.locus_v2.public.user_role_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: user_id
          type: int8
        - name: role_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: user_id,role_id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [user_id,role_id]
      s3_url: s3://prod-dse-nessie-output/postgres.locus_v2.public.user_role_mapping/
      schema: lake_locus_v2
      sortkey: []
      table: user_role_mapping
    source:
      database: locus_v2
      table: user_role_mapping
  - flow_id: locus_v2.user_roles.v1
    topic_name: postgres.locus_v2.public.user_roles
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: name
          type: varchar(max)
        - name: is_active
          type: boolean
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: None
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.locus_v2.public.user_roles/
      schema: lake_locus_v2
      sortkey: []
      table: user_roles
    source:
      database: locus_v2
      table: user_roles
  - flow_id: locus_v2.user_zone_mapping.v1
    topic_name: postgres.locus_v2.public.user_zone_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: zone_id
          type: int8
        - name: user_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: user_id,zone_id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [user_id,zone_id]
      s3_url: s3://prod-dse-nessie-output/postgres.locus_v2.public.user_zone_mapping/
      schema: lake_locus_v2
      sortkey: []
      table: user_zone_mapping
    source:
      database: locus_v2
      table: user_zone_mapping
  - flow_id: locus_v2.user_zones.v1
    topic_name: postgres.locus_v2.public.user_zones
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: name
          type: varchar(max)
        - name: system_zone_id
          type: varchar(max)
        - name: is_active
          type: boolean
        - name: slack_channel
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.locus_v2.public.user_zones/
      schema: lake_locus_v2
      sortkey: []
      table: user_zones
    source:
      database: locus_v2
      table: user_zones
  - flow_id: locus_v2.project_business_network.v1
    topic_name: postgres.locus_v2.public.project_business_network
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: project_id
          type: int8
        - name: business_type_id
          type: int8
        - name: network_action_id
          type: int8
        - name: is_active
          type: boolean
        - name: replacement_outlet_id
          type: int8
        - name: created_by
          type: int8
        - name: updated_by
          type: int8
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.locus_v2.public.project_business_network/
      schema: lake_locus_v2
      sortkey: []
      table: project_business_network
    source:
      database: locus_v2
      table: project_business_network
tags:
  - de
  - replicate
  - locus_v2_1
template_name: nessie
version: 1
