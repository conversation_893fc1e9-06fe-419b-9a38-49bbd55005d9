dag_name: elite_store_partner_1
dag_type: etl
escalation_priority: low
alert_configs:
  slack:
    - channel: bl-data-alerts-p1
  error_channel_mapping:
    terminated_with_errors: bl-data-spot-interruptions
    user_request: bl-data-spot-interruptions
    spot: bl-data-spot-interruptions
executor:
  config:
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    cpu:
      limit: 1
      request: 0.3
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    memory:
      limit: 500M
      request: 250M
    node_selectors:
      nodetype: airflow-srp-spot
    tolerations:
      - effect: NoSchedule
        key: service
        operator: Equal
        value: airflow-srp
    volume_mounts:
      - mountPath: /usr/local/airflow/plugins
        name: airflow-dags
        subPath: airflow-de/plugins
  type: celery
namespace: de
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ
path: de/replicate/etl/elite_store_partner_1
paused: false
project_name: replicate
schedule:
  end_date: "2026-09-11T00:00:00"
  interval: 0 */3 * * *
  start_date: "2025-09-16T00:00:00"
schedule_type: fixed
sla: 180 minutes
task_concurrency: 8
poke_interval: 180
redshift_sla_seconds: 10800
emr_sensor: {}
spark:
  aws_conn_id: aws_default
  mode: emr
  nessie_jar_version: 2.1.2
  cluster_reuse: true
  emr_version: 6.11.1
support_files: []
tables:
  - flow_id: elite_store_partner.city.v1
    topic_name: postgres.elite_store_partner.public.city
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: name
          type: varchar(max)
        - name: pin_code
          type: varchar(max)
        - name: state
          type: varchar(max)
        - name: is_active
          type: boolean
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: locality
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.elite_store_partner.public.city/
      schema: lake_elite_store_partner
      sortkey: []
      table: city
    source:
      database: elite_store_partner
      table: city
  - flow_id: elite_store_partner.electricity_bill.v1
    topic_name: postgres.elite_store_partner.public.electricity_bill
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: store_id
          type: int8
        - name: bill_number
          type: varchar(max)
        - name: previous_reading
          type: numeric(10, 2)
        - name: current_reading
          type: numeric(10, 2)
        - name: bill_amount
          type: numeric(10, 2)
        - name: bill_start_date
          type: timestamp
        - name: bill_end_date
          type: timestamp
        - name: payout_start_date
          type: timestamp
        - name: payout_end_date
          type: timestamp
        - name: bill_file_name
          type: varchar(max)
        - name: payment_receipt_file_name
          type: varchar(max)
        - name: bill_upload_status
          type: varchar(max)
        - name: is_active
          type: boolean
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: meter_number
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.elite_store_partner.public.electricity_bill/
      schema: lake_elite_store_partner
      sortkey: []
      table: electricity_bill
    source:
      database: elite_store_partner
      table: electricity_bill
  - flow_id: elite_store_partner.electricity_bill_final_status.v1
    topic_name: postgres.elite_store_partner.public.electricity_bill_final_status
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: bill_id
          type: int8
        - name: bill_status_id
          type: int8
        - name: is_active
          type: boolean
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.elite_store_partner.public.electricity_bill_final_status/
      schema: lake_elite_store_partner
      sortkey: []
      table: electricity_bill_final_status
    source:
      database: elite_store_partner
      table: electricity_bill_final_status
  - flow_id: elite_store_partner.electricity_bill_status.v1
    topic_name: postgres.elite_store_partner.public.electricity_bill_status
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: bill_id
          type: int8
        - name: user_id
          type: int8
        - name: status
          type: varchar(max)
        - name: team
          type: varchar(max)
        - name: approved_amount
          type: numeric(10, 2)
        - name: reason
          type: varchar(max)
        - name: remarks
          type: varchar(max)
        - name: is_active
          type: boolean
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.elite_store_partner.public.electricity_bill_status/
      schema: lake_elite_store_partner
      sortkey: []
      table: electricity_bill_status
    source:
      database: elite_store_partner
      table: electricity_bill_status
  - flow_id: elite_store_partner.merchant_lead.v1
    topic_name: postgres.elite_store_partner.public.merchant_lead
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: name
          type: varchar(255)
        - name: email
          type: varchar(255)
        - name: phone
          type: varchar(255)
        - name: occupation
          type: varchar(255)
        - name: city
          type: varchar(255)
        - name: status
          type: varchar(max)
        - name: stage
          type: varchar(max)
        - name: score
          type: numeric(5, 2)
        - name: is_active
          type: boolean
        - name: stage_updated_at
          type: timestamp
        - name: assigned_to
          type: int4
        - name: source
          type: varchar(max)
        - name: referrer_name
          type: varchar(255)
        - name: referrer_phone
          type: varchar(255)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: total_score
          type: numeric(5, 2)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.elite_store_partner.public.merchant_lead/
      schema: lake_elite_store_partner
      sortkey: []
      table: merchant_lead
    source:
      database: elite_store_partner
      table: merchant_lead
  - flow_id: elite_store_partner.merchant_lead_final_status.v1
    topic_name: postgres.elite_store_partner.public.merchant_lead_final_status
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: lead_id
          type: int8
        - name: lead_status_id
          type: int8
        - name: is_active
          type: boolean
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.elite_store_partner.public.merchant_lead_final_status/
      schema: lake_elite_store_partner
      sortkey: []
      table: merchant_lead_final_status
    source:
      database: elite_store_partner
      table: merchant_lead_final_status
  - flow_id: elite_store_partner.merchant_lead_req.v1
    topic_name: postgres.elite_store_partner.public.merchant_lead_req
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: merchant_id
          type: int8
        - name: city_id
          type: int8
        - name: assigned_to
          type: int8
        - name: is_warm_lead
          type: boolean
        - name: source
          type: varchar(max)
        - name: referrer_name
          type: varchar(max)
        - name: referrer_phone
          type: varchar(max)
        - name: is_active
          type: boolean
        - name: warm_lead_created_at
          type: timestamp
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: state
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.elite_store_partner.public.merchant_lead_req/
      schema: lake_elite_store_partner
      sortkey: []
      table: merchant_lead_req
    source:
      database: elite_store_partner
      table: merchant_lead_req
  - flow_id: elite_store_partner.merchant_lead_status.v1
    topic_name: postgres.elite_store_partner.public.merchant_lead_status
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: status
          type: varchar(max)
        - name: stage
          type: int8
        - name: lead_approval_type
          type: varchar(max)
        - name: assessment
          type: varchar(max)
        - name: lead_id
          type: int8
        - name: user_id
          type: int8
        - name: is_active
          type: boolean
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: type
          type: varchar(max)
        - name: value
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.elite_store_partner.public.merchant_lead_status/
      schema: lake_elite_store_partner
      sortkey: []
      table: merchant_lead_status
    source:
      database: elite_store_partner
      table: merchant_lead_status
  - flow_id: elite_store_partner.merchant_lead_user.v1
    topic_name: postgres.elite_store_partner.public.merchant_lead_user
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: name
          type: varchar(max)
        - name: email
          type: varchar(max)
        - name: phone
          type: varchar(max)
        - name: occupation
          type: varchar(max)
        - name: is_active
          type: boolean
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.elite_store_partner.public.merchant_lead_user/
      schema: lake_elite_store_partner
      sortkey: []
      table: merchant_lead_user
    source:
      database: elite_store_partner
      table: merchant_lead_user
  - flow_id: elite_store_partner.store.v1
    topic_name: postgres.elite_store_partner.public.store
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: name
          type: varchar(max)
        - name: cms_frontend_id
          type: int8
        - name: cms_backend_id
          type: int8
        - name: retail_outlet_id
          type: int8
        - name: is_active
          type: boolean
        - name: address
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: gst_number
          type: varchar(max)
        - name: entity_name
          type: varchar(max)
        - name: entity_address
          type: varchar(max)
        - name: sap_vendor_code
          type: varchar(max)
        - name: location_code
          type: varchar(max)
        - name: gst_type
          type: varchar(max)
        - name: blinkit_poc_email
          type: varchar(max)
        - name: blinkit_poc_contact
          type: varchar(max)
        - name: blinkit_gst_number
          type: varchar(max)
        - name: blinkit_state
          type: varchar(max)
        - name: entity_state
          type: varchar(max)
        - name: store_city
          type: varchar(max)
        - name: live_date
          type: timestamp
        - name: meter_number
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.elite_store_partner.public.store/
      schema: lake_elite_store_partner
      sortkey: []
      table: store
    source:
      database: elite_store_partner
      table: store
tags:
  - de
  - replicate
  - elite_store_partner_1
template_name: nessie
version: 1
