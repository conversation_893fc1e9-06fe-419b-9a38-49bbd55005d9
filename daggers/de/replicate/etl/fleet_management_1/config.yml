dag_name: fleet_management_1
dag_type: etl
escalation_priority: low
alert_configs:
  slack:
    - channel: bl-data-alerts-p1
  error_channel_mapping:
    terminated_with_errors: bl-data-spot-interruptions
    spot: bl-data-spot-interruptions
executor:
  config:
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    cpu:
      limit: 1
      request: 0.3
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    memory:
      limit: 500M
      request: 250M
    node_selectors:
      nodetype: airflow-srp-spot
    tolerations:
      - effect: NoSchedule
        key: service
        operator: Equal
        value: airflow-srp
    volume_mounts:
      - mountPath: /usr/local/airflow/plugins
        name: airflow-dags
        subPath: airflow-de/plugins
  type: celery
namespace: de
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ

path: de/replicate/etl/fleet_management_1
paused: false
project_name: replicate
schedule:
  interval: 7,37 * * * *
  start_date: "2024-01-12T00:00:00"
  end_date: '2026-05-23T00:00:00'
schedule_type: fixed
sla: 180 minutes
task_concurrency: 15
poke_interval: 180
redshift_sla_seconds: 10800
emr_sensor: {}
nessie_dag_split: true
spark:
  aws_conn_id: aws_default
  mode: emr
  nessie_jar_version: 2.1.2
  cluster_reuse: true
  emr_version: 6.11.1
support_files: []
tables:
  - flow_id: fleet_management.fleet_management_approval_request.v1
    topic_name: postgres.fleet_management.public.fleet_management_approval_request
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: request_type
          type: varchar(max)
        - name: payload
          type: varchar(max)
        - name: created_by
          type: varchar(255)
        - name: updated_by
          type: varchar(255)
        - name: status
          type: varchar(255)
        - name: reason
          type: varchar(max)
        - name: parent_id
          type: int4
        - name: reference_id
          type: varchar(max)
        - name: reference_type
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.fleet_management.public.fleet_management_approval_request/
      schema: lake_fleet_management
      sortkey: []
      table: fleet_management_approval_request
    source:
      database: fleet_management
      table: fleet_management_approval_request
  - flow_id: fleet_management.fleet_management_device.v1
    topic_name: postgres.fleet_management.public.fleet_management_device
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: external_device_id
          type: varchar(255)
        - name: device_type
          type: varchar(50)
        - name: owner_node_id
          type: varchar(10)
        - name: state
          type: varchar(50)
        - name: reference_id
          type: varchar(50)
        - name: reference_type
          type: varchar(50)
        - name: active
          type: boolean
        - name: metadata
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.fleet_management.public.fleet_management_device/
      schema: lake_fleet_management
      sortkey: []
      table: fleet_management_device
    source:
      database: fleet_management
      table: fleet_management_device
  - flow_id: fleet_management.fleet_management_distance_mapping.v1
    topic_name: postgres.fleet_management.public.fleet_management_distance_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: source_node_id
          type: varchar(255)
        - name: destination_node_id
          type: varchar(255)
        - name: system_distance_in_km
          type: int4
        - name: team_recommended_distance_in_km
          type: int4
        - name: meta
          type: varchar(max)
        - name: tenure
          type: varchar(max)
        - name: is_active
          type: boolean
        - name: status
          type: varchar(100)
        - name: updated_by
          type: varchar(255)
        - name: approved_by
          type: varchar(255)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.fleet_management.public.fleet_management_distance_mapping/
      schema: lake_fleet_management
      sortkey: []
      table: fleet_management_distance_mapping
    source:
      database: fleet_management
      table: fleet_management_distance_mapping
  - flow_id: fleet_management.fleet_management_node_billable_city_mapping.v1
    topic_name: postgres.fleet_management.public.fleet_management_node_billable_city_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: destination_node_id
          type: varchar(255)
        - name: billable_destination_city
          type: varchar(255)
        - name: is_active
          type: boolean
        - name: truck_vendor_mapping_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.fleet_management.public.fleet_management_node_billable_city_mapping/
      schema: lake_fleet_management
      sortkey: []
      table: fleet_management_node_billable_city_mapping
    source:
      database: fleet_management
      table: fleet_management_node_billable_city_mapping
  - flow_id: fleet_management.fleet_management_predicate.v1
    topic_name: postgres.fleet_management.public.fleet_management_predicate
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: attribute_name
          type: varchar(max)
        - name: value
          type: varchar(max)
        - name: operator
          type: varchar(max)
        - name: type
          type: varchar(max)
        - name: rule_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.fleet_management.public.fleet_management_predicate/
      schema: lake_fleet_management
      sortkey: []
      table: fleet_management_predicate
    source:
      database: fleet_management
      table: fleet_management_predicate
  - flow_id: fleet_management.fleet_management_rule.v1
    topic_name: postgres.fleet_management.public.fleet_management_rule
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: rule_name
          type: varchar(max)
        - name: output
          type: varchar(max)
        - name: output_type
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.fleet_management.public.fleet_management_rule/
      schema: lake_fleet_management
      sortkey: []
      table: fleet_management_rule
    source:
      database: fleet_management
      table: fleet_management_rule
  - flow_id: fleet_management.fleet_management_tolls_cost.v1
    topic_name: postgres.fleet_management.public.fleet_management_tolls_cost
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: tolls_cost
          type: numeric(7, 2)
        - name: source_node_id
          type: varchar(255)
        - name: destination_node_id
          type: varchar(255)
        - name: truck_type
          type: varchar(255)
        - name: tenure
          type: varchar(max)
        - name: created_by
          type: varchar(255)
        - name: updated_by
          type: varchar(255)
        - name: is_active
          type: boolean
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.fleet_management.public.fleet_management_tolls_cost/
      schema: lake_fleet_management
      sortkey: []
      table: fleet_management_tolls_cost
    source:
      database: fleet_management
      table: fleet_management_tolls_cost
  - flow_id: fleet_management.fleet_management_transaction_logs.v1
    topic_name: postgres.fleet_management.public.fleet_management_transaction_logs
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: table_name
          type: varchar(100)
        - name: updated_by
          type: varchar(100)
        - name: key_name
          type: varchar(50)
        - name: key_value
          type: varchar(50)
        - name: initial_value
          type: varchar(max)
        - name: final_value
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.fleet_management.public.fleet_management_transaction_logs/
      schema: lake_fleet_management
      sortkey: []
      table: fleet_management_transaction_logs
    source:
      database: fleet_management
      table: fleet_management_transaction_logs
  - flow_id: fleet_management.fleet_management_trip_adhoc_costs_attributes.v1
    topic_name: postgres.fleet_management.public.fleet_management_trip_adhoc_costs_attributes
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: trip_id
          type: varchar(255)
        - name: attribute_type
          type: varchar(255)
        - name: value
          type: varchar(255)
        - name: value_type
          type: varchar(255)
        - name: metadata
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.fleet_management.public.fleet_management_trip_adhoc_costs_attributes/
      schema: lake_fleet_management
      sortkey: []
      table: fleet_management_trip_adhoc_costs_attributes
    source:
      database: fleet_management
      table: fleet_management_trip_adhoc_costs_attributes
  - flow_id: fleet_management.fleet_management_trips_cost.v1
    topic_name: postgres.fleet_management.public.fleet_management_trips_cost
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: trip_id
          type: varchar(50)
        - name: trip_creation_time
          type: timestamp
        - name: source_node_id
          type: varchar(50)
        - name: source_node_meta
          type: varchar(max)
        - name: trip_source_node_id
          type: varchar(50)
        - name: trip_source_node_meta
          type: varchar(max)
        - name: destination_node_ids
          type: varchar(max)
        - name: destination_node_meta
          type: varchar(max)
        - name: fixed_cost
          type: numeric(10, 3)
        - name: tolls_cost
          type: numeric(10, 3)
        - name: misc_charges
          type: numeric(10, 3)
        - name: truck_number
          type: varchar(255)
        - name: truck_type
          type: varchar(255)
        - name: billable_truck_type
          type: varchar(255)
        - name: truck_billing_type
          type: varchar(255)
        - name: truck_node_city_mapping
          type: varchar(max)
        - name: km_slab
          type: int4
        - name: hr_slab
          type: int4
        - name: vendor_details
          type: varchar(max)
        - name: status
          type: varchar(50)
        - name: is_vendor_rate_card_present
          type: boolean
        - name: is_tolls_cost_present
          type: boolean
        - name: route_id
          type: int4
        - name: vendor_id
          type: int4
        - name: trip_dispatch_time
          type: timestamp
        - name: trip_state
          type: varchar(50)
        - name: trip_completion_time
          type: timestamp
        - name: cost_breakdown
          type: varchar(max)
        - name: trip_tags
          type: varchar(255)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.fleet_management.public.fleet_management_trips_cost/
      schema: lake_fleet_management
      sortkey: []
      table: fleet_management_trips_cost
    source:
      database: fleet_management
      table: fleet_management_trips_cost
  - flow_id: fleet_management.fleet_management_truck_management.v1
    topic_name: postgres.fleet_management.public.fleet_management_truck_management
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: truck_number
          type: varchar(50)
        - name: tenure
          type: varchar(max)
        - name: actual_truck_type
          type: varchar(50)
        - name: billable_truck_type
          type: varchar(50)
        - name: metadata
          type: varchar(max)
        - name: is_active
          type: boolean
        - name: updated_by
          type: varchar(255)
        - name: vendor_id
          type: int4
        - name: tenure_range
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.fleet_management.public.fleet_management_truck_management/
      schema: lake_fleet_management
      sortkey: []
      table: fleet_management_truck_management
    source:
      database: fleet_management
      table: fleet_management_truck_management
  - flow_id: fleet_management.fleet_management_truck_vendor_mapping.v1
    topic_name: postgres.fleet_management.public.fleet_management_truck_vendor_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: truck_number
          type: varchar(255)
        - name: source_node_id
          type: int4
        - name: destination_city
          type: varchar(max)
        - name: tenure
          type: varchar(max)
        - name: truck_billing_type
          type: varchar(255)
        - name: truck_type
          type: varchar(255)
        - name: metadata
          type: varchar(max)
        - name: created_by
          type: varchar(255)
        - name: updated_by
          type: varchar(255)
        - name: is_active
          type: boolean
        - name: vendor_id
          type: int4
        - name: expected_trips_per_day
          type: float8
        - name: secondary_source_node_ids
          type: varchar(max)
        - name: hr_slab
          type: int4
        - name: km_slab
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.fleet_management.public.fleet_management_truck_vendor_mapping/
      schema: lake_fleet_management
      sortkey: []
      table: fleet_management_truck_vendor_mapping
    source:
      database: fleet_management
      table: fleet_management_truck_vendor_mapping
  - flow_id: fleet_management.fleet_management_user.v2
    topic_name: postgres.fleet_management.public.fleet_management_user
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: name
          type: varchar(256)
        - name: display_name
          type: varchar(256)
        - name: phone
          type: varchar(50)
        - name: employee_id
          type: varchar(100)
        - name: blood_group
          type: varchar(255)
        - name: date_of_birth
          type: date
        - name: date_of_joining
          type: date
        - name: fathers_name
          type: varchar(255)
        - name: gender
          type: varchar(255)
        - name: marital_status
          type: varchar(255)
        - name: meta
          type: varchar(max)
        - name: profile_image
          type: varchar(100)
        - name: status
          type: varchar(255)
        - name: old_employee_id
          type: varchar(255)
        - name: activated_by
          type: varchar(255)
        - name: ccrv_status
          type: varchar(255)
        - name: created_by
          type: varchar(100)
        - name: updated_by
          type: varchar(100)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.fleet_management.public.fleet_management_user/
      schema: lake_fleet_management
      sortkey: []
      table: fleet_management_user
    source:
      database: fleet_management
      table: fleet_management_user
  - flow_id: fleet_management.fleet_management_user_address.v2
    topic_name: postgres.fleet_management.public.fleet_management_user_address
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: address_line_1
          type: varchar(max)
        - name: address_line_2
          type: varchar(max)
        - name: landmark
          type: varchar(50)
        - name: pincode
          type: varchar(50)
        - name: city
          type: varchar(100)
        - name: state
          type: varchar(100)
        - name: tag
          type: varchar(100)
        - name: active
          type: boolean
        - name: user_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.fleet_management.public.fleet_management_user_address/
      schema: lake_fleet_management
      sortkey: []
      table: fleet_management_user_address
    source:
      database: fleet_management
      table: fleet_management_user_address
  - flow_id: fleet_management.fleet_management_user_detail.v2
    topic_name: postgres.fleet_management.public.fleet_management_user_detail
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: vehicle_type
          type: varchar(20)
        - name: employee_type
          type: varchar(20)
        - name: meta_data
          type: varchar(max)
        - name: user_id
          type: int4
        - name: device_info
          type: varchar(max)
        - name: hiring_source_id
          type: int4
        - name: is_ratecard_accepted
          type: boolean
        - name: is_tnc_accepted
          type: boolean
        - name: onboarding_status
          type: varchar(50)
        - name: onboarding_user_id
          type: varchar(100)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.fleet_management.public.fleet_management_user_detail/
      schema: lake_fleet_management
      sortkey: []
      table: fleet_management_user_detail
    source:
      database: fleet_management
      table: fleet_management_user_detail
  - flow_id: fleet_management.fleet_management_user_detail_store_mapping.v2
    topic_name: postgres.fleet_management.public.fleet_management_user_detail_store_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: store_id
          type: varchar(50)
        - name: active
          type: boolean
        - name: user_detail_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.fleet_management.public.fleet_management_user_detail_store_mapping/
      schema: lake_fleet_management
      sortkey: []
      table: fleet_management_user_detail_store_mapping
    source:
      database: fleet_management
      table: fleet_management_user_detail_store_mapping
  - flow_id: fleet_management.fleet_management_user_document.v2
    topic_name: postgres.fleet_management.public.fleet_management_user_document
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: type
          type: varchar(30)
        - name: front_picture
          type: varchar(100)
        - name: back_picture
          type: varchar(100)
        - name: number
          type: varchar(50)
        - name: user_id
          type: int4
        - name: meta_data
          type: varchar(max)
        - name: status
          type: varchar(50)
        - name: doc_file
          type: varchar(100)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.fleet_management.public.fleet_management_user_document/
      schema: lake_fleet_management
      sortkey: []
      table: fleet_management_user_document
    source:
      database: fleet_management
      table: fleet_management_user_document
  - flow_id: fleet_management.fleet_management_vehicle.v1
    topic_name: postgres.fleet_management.public.fleet_management_vehicle
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: registration_number
          type: varchar(15)
        - name: vehicle_type
          type: varchar(50)
        - name: gps_provider
          type: varchar(20)
        - name: meta
          type: varchar(max)
        - name: status
          type: varchar(15)
        - name: updated_by
          type: varchar(100)
        - name: created_by
          type: varchar(100)
        - name: vendor_id
          type: int4
        - name: gps_provider_id
          type: int4
        - name: vehicle_load_capacity
          type: int4
        - name: onboarding_status
          type: varchar(100)
        - name: tenant
          type: varchar(100)
        - name: vehicle_type_id
          type: int8
        - name: vehicle_type_identifier_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.fleet_management.public.fleet_management_vehicle/
      schema: lake_fleet_management
      sortkey: []
      table: fleet_management_vehicle
    source:
      database: fleet_management
      table: fleet_management_vehicle
  - flow_id: fleet_management.fleet_management_vehicle_detail.v1
    topic_name: postgres.fleet_management.public.fleet_management_vehicle_detail
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: rc_expiry_date
          type: date
        - name: chassis_number
          type: varchar(17)
        - name: fuel_type
          type: varchar(50)
        - name: vehicle_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.fleet_management.public.fleet_management_vehicle_detail/
      schema: lake_fleet_management
      sortkey: []
      table: fleet_management_vehicle_detail
    source:
      database: fleet_management
      table: fleet_management_vehicle_detail
  - flow_id: fleet_management.fleet_management_vendor.v1
    topic_name: postgres.fleet_management.public.fleet_management_vendor
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: name
          type: varchar(255)
        - name: type
          type: varchar(255)
        - name: code
          type: varchar(255)
        - name: active
          type: boolean
        - name: meta
          type: varchar(max)
        - name: user_id
          type: int4
        - name: email
          type: varchar(50)
        - name: lead_id
          type: int8
        - name: phone
          type: varchar(10)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.fleet_management.public.fleet_management_vendor/
      schema: lake_fleet_management
      sortkey: []
      table: fleet_management_vendor
    source:
      database: fleet_management
      table: fleet_management_vendor
  - flow_id: fleet_management.fleet_management_vendor_rate_card_config.v1
    topic_name: postgres.fleet_management.public.fleet_management_vendor_rate_card_config
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: source_node_id
          type: int4
        - name: destination_city
          type: varchar(max)
        - name: tenure
          type: varchar(max)
        - name: truck_billing_type
          type: varchar(255)
        - name: truck_type
          type: varchar(255)
        - name: is_active
          type: boolean
        - name: approval_request_id_id
          type: int4
        - name: vendor_id
          type: int4
        - name: rule_version
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.fleet_management.public.fleet_management_vendor_rate_card_config/
      schema: lake_fleet_management
      sortkey: []
      table: fleet_management_vendor_rate_card_config
    source:
      database: fleet_management
      table: fleet_management_vendor_rate_card_config
  - flow_id: fleet_management.fleet_management_vendor_rate_card_config_rule_mapping.v1
    topic_name: postgres.fleet_management.public.fleet_management_vendor_rate_card_config_rule_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: install_ts
          type: timestamp
        - name: update_ts
          type: timestamp
        - name: is_active
          type: boolean
        - name: rule_id
          type: int4
        - name: vendor_rate_card_config_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.fleet_management.public.fleet_management_vendor_rate_card_config_rule_mapping/
      schema: lake_fleet_management
      sortkey: []
      table: fleet_management_vendor_rate_card_config_rule_mapping
    source:
      database: fleet_management
      table: fleet_management_vendor_rate_card_config_rule_mapping
tags:
  - de
  - replicate
  - fleet_management_1
template_name: nessie
version: 2
