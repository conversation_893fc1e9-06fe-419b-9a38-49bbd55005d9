dag_name: rpc_2
dag_type: etl
escalation_priority: low
alert_configs:
  slack:
    - channel: bl-data-alerts-p1
  error_channel_mapping:
    terminated_with_errors: bl-data-spot-interruptions
    spot: bl-data-spot-interruptions
executor:
  config:
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    cpu:
      limit: 1
      request: 0.3
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    memory:
      limit: 500M
      request: 250M
    node_selectors:
      nodetype: airflow-srp-spot
    tolerations:
      - effect: NoSchedule
        key: service
        operator: Equal
        value: airflow-srp
    volume_mounts:
      - mountPath: /usr/local/airflow/plugins
        name: airflow-dags
        subPath: airflow-de/plugins
  type: celery
namespace: de
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ

path: de/replicate/etl/rpc_2
paused: false
project_name: replicate
schedule:
  interval: 21,51 * * * *
  start_date: "2022-09-01T00:00:00"
  end_date: '2025-12-14T00:00:00'
schedule_type: fixed
sla: 120 minutes
task_concurrency: 15
poke_interval: 120
redshift_sla_seconds: 7200
emr_sensor: {}
nessie_dag_split: true
spark:
  aws_conn_id: aws_default
  mode: emr
  nessie_jar_version: 2.1.2
  cluster_reuse: true
  emr_version: 6.11.1
support_files: []
tables:
  - flow_id: rpc.product_tax_revision.v1
    topic_name: mysql.rpc.rpc.product_tax_revision
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: item_id
          type: int8
        - name: new_regime
          type: int4
        - name: cgst
          type: decimal(10, 3)
        - name: sgst
          type: decimal(10, 3)
        - name: igst
          type: decimal(10, 3)
        - name: cess
          type: decimal(10, 3)
        - name: additional_cess_value
          type: decimal(10, 3)
        - name: revision_date
          type: date
        - name: active
          type: int4
        - name: created_by
          type: int8
        - name: created_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.rpc.rpc.product_tax_revision/
      schema: lake_rpc
      sortkey: []
      table: product_tax_revision
    source:
      database: rpc
      table: product_tax_revision
  - flow_id: rpc.ams_city_cluster_mapping.v1
    topic_name: mysql.rpc.rpc.ams_city_cluster_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: cluster_id
          type: int8
        - name: city_id
          type: int8
        - name: active
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.rpc.rpc.ams_city_cluster_mapping/
      schema: lake_rpc
      sortkey: []
      table: ams_city_cluster_mapping
    source:
      database: rpc
      table: ams_city_cluster_mapping
  - flow_id: rpc.ams_cluster.v1
    topic_name: mysql.rpc.rpc.ams_cluster
    redshift_replication: false
    sink:
      column_dtypes:
        - name: cluster_id
          type: int8
        - name: cluster_name
          type: varchar(256)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: cluster_id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [cluster_id]
      s3_url: s3://prod-dse-nessie-output/mysql.rpc.rpc.ams_cluster/
      schema: lake_rpc
      sortkey: []
      table: ams_cluster
    source:
      database: rpc
      table: ams_cluster
  - flow_id: rpc.attribute_outlet_entity_vendor.v1
    topic_name: mysql.rpc.rpc.attribute_outlet_entity_vendor
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: outlet_id
          type: int8
        - name: attribute_type
          type: varchar(64)
        - name: attribute_id
          type: int8
        - name: entity_vendor_id
          type: int8
        - name: active
          type: int4
        - name: created_by
          type: int8
        - name: created_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.rpc.rpc.attribute_outlet_entity_vendor/
      schema: lake_rpc
      sortkey: []
      table: attribute_outlet_entity_vendor
    source:
      database: rpc
      table: attribute_outlet_entity_vendor
  - flow_id: rpc.dated_tot_margin.v1
    topic_name: mysql.rpc.rpc.dated_tot_margin
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: item_id
          type: int8
        - name: facility_id
          type: int8
        - name: city_id
          type: int8
        - name: vendor_id
          type: int8
        - name: on_invoice_margin_type
          type: int8
        - name: on_invoice_margin_value
          type: decimal(5, 2)
        - name: mrp
          type: decimal(9, 2)
        - name: landing_price
          type: decimal(7, 2)
        - name: early_payment_incentive
          type: decimal(5, 2)
        - name: early_payment_credit_period
          type: decimal(5, 2)
        - name: performance_incentive
          type: decimal(5, 2)
        - name: performance_target
          type: decimal(5, 2)
        - name: info_sales_reporting
          type: decimal(5, 2)
        - name: listing_and_visibility
          type: decimal(5, 2)
        - name: supporting_doc_url
          type: varchar(500)
        - name: start_date
          type: date
        - name: end_date
          type: date
        - name: bu_job_id
          type: int8
        - name: comments
          type: varchar(1000)
        - name: active
          type: int4
        - name: is_approved
          type: int4
        - name: created_by
          type: int8
        - name: created_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.rpc.rpc.dated_tot_margin/
      schema: lake_rpc
      sortkey: []
      table: dated_tot_margin
    source:
      database: rpc
      table: dated_tot_margin
  - flow_id: rpc.facility_adjacency.v1
    topic_name: mysql.rpc.rpc.facility_adjacency
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: overlap_percentage
          type: decimal(5, 2)
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: adjacent_facility_polygon_id
          type: int8
        - name: facility_polygon_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.rpc.rpc.facility_adjacency/
      schema: lake_rpc
      sortkey: []
      table: facility_adjacency
    source:
      database: rpc
      table: facility_adjacency
  - flow_id: rpc.facility_adjacency_log.v1
    topic_name: mysql.rpc.rpc.facility_adjacency_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: adjacent_facility_polygon_id
          type: int8
        - name: facility_polygon_id
          type: int8
        - name: overlap_percentage
          type: decimal(5, 2)
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.rpc.rpc.facility_adjacency_log/
      schema: lake_rpc
      sortkey: []
      table: facility_adjacency_log
    source:
      database: rpc
      table: facility_adjacency_log
  - flow_id: rpc.facility_polygon.v1
    topic_name: mysql.rpc.rpc.facility_polygon
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: facility_id
          type: int8
        - name: polygon_type
          type: varchar(255)
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.rpc.rpc.facility_polygon/
      schema: lake_rpc
      sortkey: []
      table: facility_polygon
    source:
      database: rpc
      table: facility_polygon
  - flow_id: rpc.facility_polygon_log.v1
    topic_name: mysql.rpc.rpc.facility_polygon_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: facility_id
          type: int8
        - name: polygon_type
          type: varchar(255)
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.rpc.rpc.facility_polygon_log/
      schema: lake_rpc
      sortkey: []
      table: facility_polygon_log
    source:
      database: rpc
      table: facility_polygon_log
  - flow_id: rpc.item_tag_type.v1
    topic_name: mysql.rpc.rpc.item_tag_type
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: name
          type: varchar(128)
        - name: description
          type: varchar(max)
        - name: active
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.rpc.rpc.item_tag_type/
      schema: lake_rpc
      sortkey: []
      table: item_tag_type
    source:
      database: rpc
      table: item_tag_type
  - flow_id: rpc.item_tag_type_value_mapping.v1
    topic_name: mysql.rpc.rpc.item_tag_type_value_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: tag_type
          type: varchar(max)
        - name: tag_type_id
          type: int8
        - name: tag_value
          type: varchar(max)
        - name: tag_value_id
          type: varchar(128)
        - name: created_by
          type: int8
        - name: created_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: active
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.rpc.rpc.item_tag_type_value_mapping/
      schema: lake_rpc
      sortkey: []
      table: item_tag_type_value_mapping
    source:
      database: rpc
      table: item_tag_type_value_mapping
  - flow_id: rpc.new_store_assortment_request.v1
    topic_name: mysql.rpc.rpc.new_store_assortment_request
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: active
          type: int4
        - name: created_by
          type: int8
        - name: created_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: new_store_facility_id
          type: int8
        - name: payload
          type: varchar(max)
        - name: status
          type: varchar(50)
        - name: meta
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.rpc.rpc.new_store_assortment_request/
      schema: lake_rpc
      sortkey: []
      table: new_store_assortment_request
    source:
      database: rpc
      table: new_store_assortment_request
  - flow_id: rpc.new_store_assortment_request_log.v1
    topic_name: mysql.rpc.rpc.new_store_assortment_request_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: action_type
          type: varchar(50)
        - name: diff
          type: varchar(max)
        - name: created_by
          type: int8
        - name: created_at
          type: timestamp
        - name: new_store_assortment_request_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.rpc.rpc.new_store_assortment_request_log/
      schema: lake_rpc
      sortkey: []
      table: new_store_assortment_request_log
    source:
      database: rpc
      table: new_store_assortment_request_log
  - flow_id: rpc.off_invoice_rule.v1
    topic_name: mysql.rpc.rpc.off_invoice_rule
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: active
          type: int4
        - name: created_by
          type: int8
        - name: created_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: rule_number
          type: varchar(20)
        - name: start_date
          type: date
        - name: end_date
          type: date
        - name: claim_type
          type: varchar(128)
        - name: computation_type
          type: varchar(128)
        - name: frequency
          type: varchar(128)
        - name: entity_type
          type: varchar(128)
        - name: status
          type: varchar(128)
        - name: email_dl
          type: varchar(max)
        - name: document_urls
          type: varchar(max)
        - name: manufacturer_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.rpc.rpc.off_invoice_rule/
      schema: lake_rpc
      sortkey: []
      table: off_invoice_rule
    source:
      database: rpc
      table: off_invoice_rule
  # - flow_id: rpc.off_invoice_rule_claim.v1
  #   topic_name: mysql.rpc.rpc.off_invoice_rule_claim
  #   redshift_replication: false
  #   sink:
  #     column_dtypes:
  #       - name: id
  #         type: int8
  #       - name: active
  #         type: int4
  #       - name: created_by
  #         type: int8
  #       - name: created_at
  #         type: timestamp
  #       - name: updated_by
  #         type: int8
  #       - name: updated_at
  #         type: timestamp
  #       - name: claim_percentage
  #         type: decimal(5, 3)
  #       - name: rule_id
  #         type: int8
  #     copy_params:
  #       - FORMAT AS PARQUET
  #     incremental_key: id
  #     force_upsert_without_increment_check: True
  #     load_type: upsert
  #     primary_key: [id]
  #     s3_url: s3://prod-dse-nessie-output/mysql.rpc.rpc.off_invoice_rule_claim/
  #     schema: lake_rpc
  #     sortkey: []
  #     table: off_invoice_rule_claim
  #   source:
  #     database: rpc
  #     table: off_invoice_rule_claim
  - flow_id: rpc.off_invoice_rule_instance.v1
    topic_name: mysql.rpc.rpc.off_invoice_rule_instance
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: active
          type: int4
        - name: created_by
          type: int8
        - name: created_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: start_date
          type: date
        - name: end_date
          type: date
        - name: achieved_value
          type: float8
        - name: target_value
          type: float8
        - name: invoice_value
          type: float8
        - name: status
          type: varchar(256)
        - name: rule_entity_claim_data
          type: varchar(max)
        - name: last_execution_date
          type: date
        - name: file_path
          type: varchar(max)
        - name: meta
          type: varchar(max)
        - name: rule_id
          type: int8
        - name: invoice_number
          type: varchar(36)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.rpc.rpc.off_invoice_rule_instance/
      schema: lake_rpc
      sortkey: []
      table: off_invoice_rule_instance
    source:
      database: rpc
      table: off_invoice_rule_instance
  # - flow_id: rpc.off_invoice_rule_target.v1
  #   topic_name: mysql.rpc.rpc.off_invoice_rule_target
  #   redshift_replication: false
  #   sink:
  #     column_dtypes:
  #       - name: id
  #         type: int8
  #       - name: active
  #         type: int4
  #       - name: created_by
  #         type: int8
  #       - name: created_at
  #         type: timestamp
  #       - name: updated_by
  #         type: int8
  #       - name: updated_at
  #         type: timestamp
  #       - name: target_value
  #         type: int8
  #       - name: target_type
  #         type: varchar(128)
  #       - name: rule_id
  #         type: int8
  #     copy_params:
  #       - FORMAT AS PARQUET
  #     incremental_key: id
  #     force_upsert_without_increment_check: True
  #     load_type: upsert
  #     primary_key: [id]
  #     s3_url: s3://prod-dse-nessie-output/mysql.rpc.rpc.off_invoice_rule_target/
  #     schema: lake_rpc
  #     sortkey: []
  #     table: off_invoice_rule_target
  #   source:
  #     database: rpc
  #     table: off_invoice_rule_target
  - flow_id: rpc.off_invoice_rule_update_request.v1
    topic_name: mysql.rpc.rpc.off_invoice_rule_update_request
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: active
          type: int4
        - name: created_by
          type: int8
        - name: created_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: rule_update_request
          type: varchar(max)
        - name: status
          type: varchar(128)
        - name: verified_by
          type: int8
        - name: verified_at
          type: timestamp
        - name: reason
          type: varchar(256)
        - name: comments
          type: varchar(1024)
        - name: rule_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.rpc.rpc.off_invoice_rule_update_request/
      schema: lake_rpc
      sortkey: []
      table: off_invoice_rule_update_request
    source:
      database: rpc
      table: off_invoice_rule_update_request
  # - flow_id: rpc.off_invoice_target_claim_entity.v1
  #   topic_name: mysql.rpc.rpc.off_invoice_target_claim_entity
  #   redshift_replication: false
  #   sink:
  #     column_dtypes:
  #       - name: id
  #         type: int8
  #       - name: active
  #         type: int4
  #       - name: created_by
  #         type: int8
  #       - name: created_at
  #         type: timestamp
  #       - name: updated_by
  #         type: int8
  #       - name: updated_at
  #         type: timestamp
  #       - name: entity_id
  #         type: int8
  #       - name: claim_id
  #         type: int8
  #       - name: target_id
  #         type: int8
  #     copy_params:
  #       - FORMAT AS PARQUET
  #     incremental_key: id
  #     force_upsert_without_increment_check: True
  #     load_type: upsert
  #     primary_key: [id]
  #     s3_url: s3://prod-dse-nessie-output/mysql.rpc.rpc.off_invoice_target_claim_entity/
  #     schema: lake_rpc
  #     sortkey: []
  #     table: off_invoice_target_claim_entity
  #   source:
  #     database: rpc
  #     table: off_invoice_target_claim_entity
  - flow_id: rpc.product_product_log.v1
    topic_name: mysql.rpc.rpc.product_product_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: item_id
          type: int8
        - name: variant_id
          type: varchar(255)
        - name: meta
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.rpc.rpc.product_product_log/
      schema: lake_rpc
      sortkey: []
      table: product_product_log
    source:
      database: rpc
      table: product_product_log
  - flow_id: rpc.return_policy.v1
    topic_name: mysql.rpc.rpc.return_policy
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: scope_type
          type: int4
        - name: reference_id
          type: int8
        - name: return_type
          type: int4
        - name: active
          type: int4
        - name: created_by
          type: int8
        - name: created_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.rpc.rpc.return_policy/
      schema: lake_rpc
      sortkey: []
      table: return_policy
    source:
      database: rpc
      table: return_policy
  - flow_id: rpc.return_policy_bucket_attribute.v1
    topic_name: mysql.rpc.rpc.return_policy_bucket_attribute
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: bucket_name
          type: int4
        - name: attribute_name
          type: int4
        - name: value
          type: varchar(100)
        - name: active
          type: int4
        - name: created_by
          type: int8
        - name: created_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: policy_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.rpc.rpc.return_policy_bucket_attribute/
      schema: lake_rpc
      sortkey: []
      table: return_policy_bucket_attribute
    source:
      database: rpc
      table: return_policy_bucket_attribute
  - flow_id: rpc.substitutable_group.v1
    topic_name: mysql.rpc.rpc.substitutable_group
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: name
          type: varchar(255)
        - name: ars_active
          type: int4
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: l0
          type: varchar(1000)
        - name: l0_id
          type: int8
        - name: l1
          type: varchar(1000)
        - name: l1_id
          type: int8
        - name: l2
          type: varchar(1000)
        - name: l2_id
          type: int8
        - name: product_type
          type: varchar(1000)
        - name: product_type_id
          type: int8
        - name: storage_type
          type: varchar(20)
        - name: type
          type: int8
        - name: version
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.rpc.rpc.substitutable_group/
      schema: lake_rpc
      sortkey: []
      table: substitutable_group
    source:
      database: rpc
      table: substitutable_group
  - flow_id: rpc.substitutable_item_group.v1
    topic_name: mysql.rpc.rpc.substitutable_item_group
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: item_id
          type: int8
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: group_id
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: meta
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.rpc.rpc.substitutable_item_group/
      schema: lake_rpc
      sortkey: []
      table: substitutable_item_group
    source:
      database: rpc
      table: substitutable_item_group
  - flow_id: rpc.supply_event.v1
    topic_name: mysql.rpc.rpc.supply_event
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: name
          type: varchar(32)
        - name: description
          type: varchar(max)
        - name: active
          type: int4
        - name: created_by
          type: int8
        - name: created_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.rpc.rpc.supply_event/
      schema: lake_rpc
      sortkey: []
      table: supply_event
    source:
      database: rpc
      table: supply_event
  - flow_id: rpc.supply_event_info.v1
    topic_name: mysql.rpc.rpc.supply_event_info
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: supply_event_id
          type: int8
        - name: start_date
          type: date
        - name: end_date
          type: date
        - name: active
          type: int4
        - name: created_by
          type: int8
        - name: created_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: exclude_sales
          type: int4
        - name: exclude_sales_end_date
          type: date
        - name: exclude_sales_start_date
          type: date
        - name: festival_live_date
          type: date
        - name: reference_occurrence_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.rpc.rpc.supply_event_info/
      schema: lake_rpc
      sortkey: []
      table: supply_event_info
    source:
      database: rpc
      table: supply_event_info
  - flow_id: rpc.temporary_stock_details.v1
    topic_name: mysql.rpc.rpc.temporary_stock_details
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: outlet_id
          type: int8
        - name: item_id
          type: int8
        - name: variant_id
          type: varchar(256)
        - name: created_by
          type: int8
        - name: created_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.rpc.rpc.temporary_stock_details/
      schema: lake_rpc
      sortkey: []
      table: temporary_stock_details
    source:
      database: rpc
      table: temporary_stock_details
  - flow_id: rpc.temporary_stock_details_log.v1
    topic_name: mysql.rpc.rpc.temporary_stock_details_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: outlet_id
          type: int8
        - name: item_id
          type: int8
        - name: variant_id
          type: varchar(256)
        - name: created_by
          type: int8
        - name: created_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.rpc.rpc.temporary_stock_details_log/
      schema: lake_rpc
      sortkey: []
      table: temporary_stock_details_log
    source:
      database: rpc
      table: temporary_stock_details_log
  - flow_id: rpc.transfer_case_size.v1
    topic_name: mysql.rpc.rpc.transfer_case_size
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: backend_facility_id
          type: int8
        - name: item_id
          type: int8
        - name: case_size
          type: int8
        - name: case_type
          type: varchar(32)
        - name: source
          type: int8
        - name: upper_cap
          type: int8
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_by
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.rpc.rpc.transfer_case_size/
      schema: lake_rpc
      sortkey: []
      table: transfer_case_size
    source:
      database: rpc
      table: transfer_case_size
  - flow_id: rpc.transfer_case_size_log.v1
    topic_name: mysql.rpc.rpc.transfer_case_size_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: backend_facility_id
          type: int8
        - name: item_id
          type: int8
        - name: case_size
          type: int8
        - name: case_type
          type: varchar(100)
        - name: source
          type: int8
        - name: upper_cap
          type: int8
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.rpc.rpc.transfer_case_size_log/
      schema: lake_rpc
      sortkey: []
      table: transfer_case_size_log
    source:
      database: rpc
      table: transfer_case_size_log
  - flow_id: rpc.transfer_tag_rules.v1
    topic_name: mysql.rpc.rpc.transfer_tag_rules
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: category
          type: varchar(256)
        - name: item_id
          type: int8
        - name: frontend_facility_id
          type: int8
        - name: backend_outlet_id
          type: int8
        - name: city_id
          type: int8
        - name: active
          type: int4
        - name: created_by
          type: int8
        - name: created_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: backend_outlet_ids
          type: varchar(max)
        - name: manufacturer_id
          type: int8
        - name: reason_code
          type: int4
        - name: remarks
          type: varchar(1000)
        - name: tenant_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.rpc.rpc.transfer_tag_rules/
      schema: lake_rpc
      sortkey: []
      table: transfer_tag_rules
    source:
      database: rpc
      table: transfer_tag_rules
  - flow_id: rpc.transfer_tag_rules_log.v1
    topic_name: mysql.rpc.rpc.transfer_tag_rules_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: transfer_tag_rules_id
          type: int8
        - name: action_type
          type: varchar(50)
        - name: changes
          type: varchar(max)
        - name: created_by
          type: int8
        - name: created_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.rpc.rpc.transfer_tag_rules_log/
      schema: lake_rpc
      sortkey: []
      table: transfer_tag_rules_log
    source:
      database: rpc
      table: transfer_tag_rules_log
  - flow_id: rpc.warehouse_transition.v1
    topic_name: mysql.rpc.rpc.warehouse_transition
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: parent_id
          type: int8
        - name: parent_type
          type: varchar(32)
        - name: child_id
          type: int8
        - name: child_type
          type: varchar(32)
        - name: attribute_id
          type: int8
        - name: attribute_type
          type: varchar(32)
        - name: for_date
          type: date
        - name: mode
          type: varchar(32)
        - name: active
          type: int4
        - name: created_by
          type: int8
        - name: created_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.rpc.rpc.warehouse_transition/
      schema: lake_rpc
      sortkey: []
      table: warehouse_transition
    source:
      database: rpc
      table: warehouse_transition
tags:
  - de
  - replicate
  - rpc_2
template_name: nessie
version: 1
