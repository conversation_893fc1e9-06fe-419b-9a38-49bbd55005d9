dag_name: location_1
dag_type: etl
escalation_priority: low

alert_configs:
  slack:
    - channel: bl-data-alerts-p1
  error_channel_mapping:
    terminated_with_errors: bl-data-spot-interruptions
    spot: bl-data-spot-interruptions

executor:
  config:
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    cpu:
      limit: 1
      request: 0.3
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    memory:
      limit: 500M
      request: 250M
    node_selectors:
      nodetype: airflow-srp-spot
    tolerations:
      - effect: NoSchedule
        key: service
        operator: Equal
        value: airflow-srp
    volume_mounts:
      - mountPath: /usr/local/airflow/plugins
        name: airflow-dags
        subPath: airflow-de/plugins
  type: celery
namespace: de
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ

path: de/replicate/etl/location_1
paused: false
project_name: replicate
schedule:
  interval: "14,44 0-19,22 * * *"
  start_date: "2024-09-11T00:00:00"
  end_date: "2026-09-11T00:00:00"
schedule_type: fixed
sla: 180 minutes
task_concurrency: 12
poke_interval: 180
redshift_sla_seconds: 10800
emr_sensor: {}
nessie_dag_split: true
spark:
  aws_conn_id: aws_default
  mode: emr
  nessie_jar_version: 2.1.2
  cluster_reuse: true
  emr_version: 6.11.1
support_files: []
tables:
  - flow_id: location.layout_component.v1
    topic_name: postgres.location.public.layout_component
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: tenant_id
          type: varchar(225)
        - name: component_uuid
          type: varchar(255)
        - name: component_type
          type: varchar(255)
        - name: legend_mapping_id
          type: int8
        - name: layout_id
          type: int8
        - name: component_coordinates
          type: varchar(max)
        - name: component_details
          type: varchar(max)
        - name: status
          type: varchar(255)
        - name: version_metadata
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.location.public.layout_component/
      schema: lake_location
      sortkey: []
      table: layout_component
    source:
      database: location
      table: layout_component
  - flow_id: location.legend_mapping_detail.v1
    topic_name: postgres.location.public.legend_mapping_detail
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: tenant_id
          type: varchar(225)
        - name: legend_code
          type: varchar(255)
        - name: rack_type
          type: varchar(255)
        - name: status
          type: varchar(255)
        - name: properties
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.location.public.legend_mapping_detail/
      schema: lake_location
      sortkey: []
      table: legend_mapping_detail
    source:
      database: location
      table: legend_mapping_detail
  - flow_id: location.location.v1
    topic_name: postgres.location.public.location
    redshift_replication: false
    sink:
      column_dtypes:
        - name: location_id
          type: varchar(255)
        - name: outlet_id
          type: varchar(255)
        - name: location_type
          type: varchar(255)
        - name: floor
          type: varchar(255)
        - name: rack
          type: varchar(255)
        - name: aisle
          type: varchar(255)
        - name: status
          type: varchar(255)
        - name: sequence_number
          type: int4
        - name: tenant_id
          type: varchar(255)
        - name: created_by
          type: varchar(255)
        - name: created_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: meta
          type: varchar(max)
        - name: shelf
          type: varchar(225)
        - name: rack_type
          type: varchar(225)
        - name: location_qr
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: location_id,outlet_id,tenant_id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [location_id,outlet_id,tenant_id]
      s3_url: s3://prod-dse-nessie-output/postgres.location.public.location/
      schema: lake_location
      sortkey: []
      table: location
    source:
      database: location
      table: location
  - flow_id: location.location_component_location_type_mapping.v1
    topic_name: postgres.location.public.location_component_location_type_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: tenant_id
          type: varchar(225)
        - name: location_component_rack_type
          type: varchar(255)
        - name: location_component_floor_area_type
          type: varchar(255)
        - name: location_type
          type: varchar(255)
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.location.public.location_component_location_type_mapping/
      schema: lake_location
      sortkey: []
      table: location_component_location_type_mapping
    source:
      database: location
      table: location_component_location_type_mapping
  - flow_id: location.location_components_config.v1
    topic_name: postgres.location.public.location_components_config
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: tenant_id
          type: varchar(255)
        - name: location_component
          type: varchar(255)
        - name: location_component_type
          type: varchar(255)
        - name: title
          type: varchar(255)
        - name: default_properties
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: updated_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.location.public.location_components_config/
      schema: lake_location
      sortkey: []
      table: location_components_config
    source:
      database: location
      table: location_components_config
  - flow_id: location.location_migration_mapping.v2
    topic_name: postgres.location.public.location_migration_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: outlet_id
          type: varchar(255)
        - name: old_location
          type: varchar(255)
        - name: new_location
          type: varchar(255)
        - name: status
          type: varchar(255)
        - name: reason
          type: varchar(255)
        - name: updated_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: created_at
          type: timestamp
        - name: tenant_id
          type: varchar(255)
        - name: migration_error
          type: varchar(512)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.location.public.location_migration_mapping/
      schema: lake_location
      sortkey: []
      table: location_migration_mapping
    source:
      database: location
      table: location_migration_mapping
  - flow_id: location.location_nomenclature.v1
    topic_name: postgres.location.public.location_nomenclature
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: tenant_id
          type: varchar(255)
        - name: location_component
          type: varchar(255)
        - name: conditions
          type: varchar(max)
        - name: rule_config
          type: varchar(max)
        - name: status
          type: varchar(255)
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: updated_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.location.public.location_nomenclature/
      schema: lake_location
      sortkey: []
      table: location_nomenclature
    source:
      database: location
      table: location_nomenclature
  - flow_id: location.location_type.v1
    topic_name: postgres.location.public.location_type
    redshift_replication: false
    sink:
      column_dtypes:
        - name: name
          type: varchar(255)
        - name: inventory_movement_category
          type: varchar(255)
        - name: tenant_id
          type: varchar(255)
        - name: created_by
          type: varchar(255)
        - name: created_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: meta
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: name,tenant_id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [name,tenant_id]
      s3_url: s3://prod-dse-nessie-output/postgres.location.public.location_type/
      schema: lake_location
      sortkey: []
      table: location_type
    source:
      database: location
      table: location_type
  - flow_id: location.location_zone_mapping.v1
    topic_name: postgres.location.public.location_zone_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: tenant_id
          type: varchar(255)
        - name: outlet_id
          type: varchar(255)
        - name: location_id
          type: varchar(255)
        - name: zone_id
          type: varchar(255)
        - name: status
          type: varchar(255)
        - name: created_by
          type: varchar(255)
        - name: created_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: tenant_id,outlet_id,location_id,zone_id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [tenant_id,outlet_id,location_id,zone_id]
      s3_url: s3://prod-dse-nessie-output/postgres.location.public.location_zone_mapping/
      schema: lake_location
      sortkey: []
      table: location_zone_mapping
    source:
      database: location
      table: location_zone_mapping
  - flow_id: location.location_zone_rule.v1
    topic_name: postgres.location.public.location_zone_rule
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: tenant_id
          type: varchar(255)
        - name: outlet_id
          type: varchar(255)
        - name: rule
          type: varchar(255)
        - name: zone_id
          type: varchar(255)
        - name: status
          type: varchar(255)
        - name: created_by
          type: varchar(255)
        - name: created_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: meta
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.location.public.location_zone_rule/
      schema: lake_location
      sortkey: []
      table: location_zone_rule
    source:
      database: location
      table: location_zone_rule
  - flow_id: location.outlet_layout.v1
    topic_name: postgres.location.public.outlet_layout
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: tenant_id
          type: varchar(225)
        - name: outlet_id
          type: varchar(225)
        - name: file_url
          type: varchar(255)
        - name: processing_status
          type: varchar(255)
        - name: tags
          type: varchar(max)
        - name: rack_ordering_algorithm
          type: varchar(255)
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.location.public.outlet_layout/
      schema: lake_location
      sortkey: []
      table: outlet_layout
    source:
      database: location
      table: outlet_layout
  - flow_id: location.outlet_location.v1
    topic_name: postgres.location.public.outlet_location
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: tenant_id
          type: varchar(255)
        - name: outlet_id
          type: varchar(255)
        - name: location_id
          type: varchar(255)
        - name: location_component_id
          type: int8
        - name: properties
          type: varchar(max)
        - name: coordinates
          type: varchar(max)
        - name: constraints
          type: varchar(max)
        - name: connected_components
          type: varchar(max)
        - name: qr
          type: varchar(max)
        - name: status
          type: varchar(255)
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: updated_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.location.public.outlet_location/
      schema: lake_location
      sortkey: []
      table: outlet_location
    source:
      database: location
      table: outlet_location
  - flow_id: location.outlet_zone.v1
    topic_name: postgres.location.public.outlet_zone
    redshift_replication: false
    sink:
      column_dtypes:
        - name: outlet_id
          type: varchar(255)
        - name: zone_id
          type: varchar(255)
        - name: tenant_id
          type: varchar(255)
        - name: created_by
          type: varchar(255)
        - name: created_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: meta
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: outlet_id,zone_id,tenant_id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [outlet_id,zone_id,tenant_id]
      s3_url: s3://prod-dse-nessie-output/postgres.location.public.outlet_zone/
      schema: lake_location
      sortkey: []
      table: outlet_zone
    source:
      database: location
      table: outlet_zone
  - flow_id: location.zone.v1
    topic_name: postgres.location.public.zone
    redshift_replication: false
    sink:
      column_dtypes:
        - name: zone_id
          type: varchar(255)
        - name: zone_type
          type: varchar(255)
        - name: status
          type: varchar(255)
        - name: tenant_id
          type: varchar(255)
        - name: created_by
          type: varchar(255)
        - name: created_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: meta
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: zone_id,tenant_id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [zone_id,tenant_id]
      s3_url: s3://prod-dse-nessie-output/postgres.location.public.zone/
      schema: lake_location
      sortkey: []
      table: zone
    source:
      database: location
      table: zone
  - flow_id: location.zone_entity_rule.v1
    topic_name: postgres.location.public.zone_entity_rule
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: tenant_id
          type: varchar(255)
        - name: outlet_id
          type: varchar(255)
        - name: entity_id
          type: varchar(255)
        - name: entity_type
          type: varchar(255)
        - name: handling_type
          type: varchar(255)
        - name: zone_id
          type: varchar(255)
        - name: priority
          type: int4
        - name: status
          type: varchar(255)
        - name: created_by
          type: varchar(255)
        - name: created_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: rule_type
          type: varchar(255)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.location.public.zone_entity_rule/
      schema: lake_location
      sortkey: []
      table: zone_entity_rule
    source:
      database: location
      table: zone_entity_rule
  - flow_id: location.outlet_layout_migration_config.v1
    topic_name: postgres.location.public.outlet_layout_migration_config
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: outlet_id
          type: varchar(255)
        - name: layout_migration_status
          type: varchar(255)
        - name: updated_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: created_at
          type: timestamp
        - name: tenant_id
          type: varchar(255)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.location.public.outlet_layout_migration_config/
      schema: lake_location
      sortkey: []
      table: outlet_layout_migration_config
    source:
      database: location
      table: outlet_layout_migration_config
tags:
  - de
  - replicate
  - location_1
template_name: nessie
version: 1
