dag_name: ars_5
dag_type: etl
escalation_priority: low

alert_configs:
  slack:
    - channel: bl-data-alerts-p1
  error_channel_mapping:
    terminated_with_errors: bl-data-spot-interruptions
    spot: bl-data-spot-interruptions

executor:
  config:
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    cpu:
      limit: 1
      request: 0.3
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    memory:
      limit: 500M
      request: 250M
    node_selectors:
      nodetype: airflow-srp-spot
    tolerations:
      - effect: NoSchedule
        key: service
        operator: Equal
        value: airflow-srp
    volume_mounts:
      - mountPath: /usr/local/airflow/plugins
        name: airflow-dags
        subPath: airflow-de/plugins
  type: celery
namespace: de
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ

path: de/replicate/etl/ars_5
paused: false
project_name: replicate
schedule:
  interval: 22,52 * * * *
  start_date: "2024-09-23T00:00:00"
  end_date: '2026-09-15T00:00:00'
schedule_type: fixed
sla: 240 minutes
task_concurrency: 12
poke_interval: 180
redshift_sla_seconds: 14400
emr_sensor: {}
nessie_dag_split: true
spark:
  aws_conn_id: aws_default
  mode: emr
  nessie_jar_version: 2.1.2
  cluster_reuse: true
  emr_version: 6.11.1
support_files: []
tables:
  - flow_id: ars.b2b_item_outlet_mapping.v1
    topic_name: mysql.ars.ars.b2b_item_outlet_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: item_id
          type: int8
        - name: outlet_id
          type: int8
        - name: parent_outlet_id
          type: int8
        - name: ordering
          type: int8
        - name: source_rule_id
          type: int8
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_by
          type: int8
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.b2b_item_outlet_mapping/
      schema: lake_ars
      sortkey: []
      table: b2b_item_outlet_mapping
    source:
      database: ars
      table: b2b_item_outlet_mapping
  - flow_id: ars.b2b_item_outlet_mapping_log.v1
    topic_name: mysql.ars.ars.b2b_item_outlet_mapping_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: item_id
          type: int8
        - name: outlet_id
          type: int8
        - name: parent_outlet_id
          type: int8
        - name: ordering
          type: int8
        - name: source_rule_id
          type: int8
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.b2b_item_outlet_mapping_log/
      schema: lake_ars
      sortkey: []
      table: b2b_item_outlet_mapping_log
    source:
      database: ars
      table: b2b_item_outlet_mapping_log
  - flow_id: ars.b2b_transfer_rule_attributes.v1
    topic_name: mysql.ars.ars.b2b_transfer_rule_attributes
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: attribute_type
          type: varchar(50)
        - name: attribute_value
          type: varchar(50)
        - name: rule_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.b2b_transfer_rule_attributes/
      schema: lake_ars
      sortkey: []
      table: b2b_transfer_rule_attributes
    source:
      database: ars
      table: b2b_transfer_rule_attributes
  - flow_id: ars.b2b_transfer_rules.v1
    topic_name: mysql.ars.ars.b2b_transfer_rules
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: facility_id
          type: int8
        - name: parent_facility_id
          type: int8
        - name: ordering
          type: int8
        - name: start_date
          type: date
        - name: end_date
          type: date
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_by
          type: int8
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.b2b_transfer_rules/
      schema: lake_ars
      sortkey: []
      table: b2b_transfer_rules
    source:
      database: ars
      table: b2b_transfer_rules
  - flow_id: ars.bulk_process_event_planner.v1
    topic_name: mysql.ars.ars.bulk_process_event_planner
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: bu_job_id
          type: int8
        - name: location_id
          type: int8
        - name: location_type
          type: varchar(16)
        - name: item_id
          type: int8
        - name: event_id
          type: int8
        - name: assortment_type
          type: varchar(16)
        - name: planned_qty
          type: int8
        - name: sale_start_date
          type: date
        - name: sale_end_date
          type: date
        - name: cut_off_date
          type: date
        - name: substitute_ptype
          type: varchar(32)
        - name: assortment_launch_type
          type: varchar(16)
        - name: approved_by
          type: int8
        - name: status
          type: varchar(16)
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
        - name: product_type
          type: varchar(256)
        - name: product_type_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.bulk_process_event_planner/
      schema: lake_ars
      sortkey: []
      table: bulk_process_event_planner
    source:
      database: ars
      table: bulk_process_event_planner
  - flow_id: ars.distributed_cart_penetration.v1
    topic_name: mysql.ars.ars.distributed_cart_penetration
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_by
          type: int8
        - name: start_date
          type: date
        - name: end_date
          type: date
        - name: attribute_type
          type: varchar(128)
        - name: attribute_id
          type: varchar(128)
        - name: base_event_name
          type: varchar(128)
        - name: base_product_type_id
          type: int8
        - name: base_percentage
          type: decimal(8, 4)
        - name: store_cluster_type
          type: varchar(128)
        - name: store_cluster_percentage
          type: decimal(8, 4)
        - name: processing_state
          type: varchar(128)
        - name: comments
          type: varchar(max)
        - name: base_event_id
          type: int8
        - name: base_product_type
          type: varchar(256)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.distributed_cart_penetration/
      schema: lake_ars
      sortkey: []
      table: distributed_cart_penetration
    source:
      database: ars
      table: distributed_cart_penetration
  - flow_id: ars.event_outlet_item_distribution.v1
    topic_name: mysql.ars.ars.event_outlet_item_distribution
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: bu_job_id
          type: int8
        - name: outlet_id
          type: int8
        - name: item_id
          type: int8
        - name: city_id
          type: int8
        - name: quantity
          type: int8
        - name: event_id
          type: int8
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.event_outlet_item_distribution/
      schema: lake_ars
      sortkey: []
      table: event_outlet_item_distribution
    source:
      database: ars
      table: event_outlet_item_distribution
  - flow_id: ars.facility_group_flushing_custom_ratio.v1
    topic_name: mysql.ars.ars.facility_group_flushing_custom_ratio
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: for_date
          type: date
        - name: group_id
          type: int8
        - name: frontend_facility_id
          type: int8
        - name: custom_ratio
          type: float8
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: distribution_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.facility_group_flushing_custom_ratio/
      schema: lake_ars
      sortkey: []
      table: facility_group_flushing_custom_ratio
    source:
      database: ars
      table: facility_group_flushing_custom_ratio
  - flow_id: ars.facility_item_flushing_custom_ratio.v1
    topic_name: mysql.ars.ars.facility_item_flushing_custom_ratio
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: for_date
          type: date
        - name: item_id
          type: int8
        - name: frontend_facility_id
          type: int8
        - name: custom_ratio
          type: float8
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: distribution_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.facility_item_flushing_custom_ratio/
      schema: lake_ars
      sortkey: []
      table: facility_item_flushing_custom_ratio
    source:
      database: ars
      table: facility_item_flushing_custom_ratio
  - flow_id: ars.flushing_ptype_custom_ratio.v1
    topic_name: mysql.ars.ars.flushing_ptype_custom_ratio
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: product_type_id
          type: int8
        - name: facility_id
          type: int8
        - name: custom_ratio
          type: float8
        - name: event_name
          type: varchar(256)
        - name: for_date
          type: date
        - name: meta
          type: varchar(max)
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: city_id
          type: int8
        - name: facility_name
          type: varchar(256)
        - name: metric_count
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.flushing_ptype_custom_ratio/
      schema: lake_ars
      sortkey: []
      table: flushing_ptype_custom_ratio
    source:
      database: ars
      table: flushing_ptype_custom_ratio
  - flow_id: ars.item_group_mapping.v3
    topic_name: mysql.ars.ars.item_group_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: group_id
          type: int8
        - name: item_id
          type: int8
        - name: run_id
          type: varchar(128)
        - name: created_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.item_group_mapping/
      schema: lake_ars
      sortkey: []
      table: item_group_mapping
    source:
      database: ars
      table: item_group_mapping
  - flow_id: ars.item_location_exclude_sales.v1
    topic_name: mysql.ars.ars.item_location_exclude_sales
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: item_id
          type: int8
        - name: frontend_facility_id
          type: int8
        - name: backend_facility_id
          type: int8
        - name: city_id
          type: int8
        - name: event_id
          type: int8
        - name: start_date
          type: date
        - name: end_date
          type: date
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_by
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.item_location_exclude_sales/
      schema: lake_ars
      sortkey: []
      table: item_location_exclude_sales
    source:
      database: ars
      table: item_location_exclude_sales
  - flow_id: ars.outlet_group_cpd.v1
    topic_name: mysql.ars.ars.outlet_group_cpd
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: group_id
          type: int8
        - name: outlet_id
          type: int8
        - name: group_cpd
          type: decimal(7, 2)
        - name: created_at
          type: timestamp
        - name: run_id
          type: varchar(128)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.outlet_group_cpd/
      schema: lake_ars
      sortkey: []
      table: outlet_group_cpd
    source:
      database: ars
      table: outlet_group_cpd
  - flow_id: ars.store_cluster_distribution.v1
    topic_name: mysql.ars.ars.store_cluster_distribution
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: active
          type: int4
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_by
          type: int8
        - name: facility_id
          type: int8
        - name: facility_name
          type: varchar(128)
        - name: city_id
          type: int8
        - name: cluster_name
          type: varchar(128)
        - name: custom_ratio
          type: float8
        - name: metric_count
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.store_cluster_distribution/
      schema: lake_ars
      sortkey: []
      table: store_cluster_distribution
    source:
      database: ars
      table: store_cluster_distribution
  - flow_id: ars.transfer_case_size_log.v1
    topic_name: mysql.ars.ars.transfer_case_size_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: backend_facility_id
          type: int8
        - name: item_id
          type: int8
        - name: case_size
          type: int8
        - name: case_flag
          type: varchar(100)
        - name: created_at
          type: timestamp
        - name: created_by
          type: int8
        - name: moq
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.transfer_case_size_log/
      schema: lake_ars
      sortkey: []
      table: transfer_case_size_log
    source:
      database: ars
      table: transfer_case_size_log
  - flow_id: ars.ars_job_run.v1
    topic_name: mysql.ars.ars.ars_job_run
    redshift_replication: false
    sink:
      column_dtypes:
        - name: run_id
          type: varchar(255)
        - name: environment
          type: varchar(30)
        - name: source
          type: varchar(30)
        - name: status
          type: varchar(50)
        - name: meta
          type: varchar(max)
        - name: job_key_id
          type: int8
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_by
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: created_at
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [run_id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.ars_job_run/
      schema: lake_ars
      sortkey: []
      table: ars_job_run
    source:
      database: ars
      table: ars_job_run
  - flow_id: ars.job_run_location.v1
    topic_name: mysql.ars.ars.job_run_location
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: run_id
          type: varchar(255)
        - name: location_id
          type: int8
        - name: location_type
          type: varchar(30)
        - name: status
          type: varchar(50)
        - name: meta
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_by
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: created_at
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.job_run_location/
      schema: lake_ars
      sortkey: []
      table: job_run_location
    source:
      database: ars
      table: job_run_location
  - flow_id: ars.job_run_snapshot.v1
    topic_name: mysql.ars.ars.job_run_snapshot
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: run_id
          type: varchar(255)
        - name: location_hierarchy
          type: varchar(max)
        - name: attribute_hierarchy
          type: varchar(max)
        - name: meta
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: created_by
          type: int8
        - name: updated_by
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: created_at
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/mysql.ars.ars.job_run_snapshot/
      schema: lake_ars
      sortkey: []
      table: job_run_snapshot
    source:
      database: ars
      table: job_run_snapshot
tags:
  - de
  - replicate
  - ars_5
template_name: nessie
version: 1
