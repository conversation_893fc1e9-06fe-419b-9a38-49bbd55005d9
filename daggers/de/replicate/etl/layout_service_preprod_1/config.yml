dag_name: layout_service_preprod_1
dag_type: etl
escalation_priority: low

alert_configs:
  slack:
    - channel: bl-data-alerts-p1
  error_channel_mapping:
    terminated_with_errors: bl-data-spot-interruptions
    spot: bl-data-spot-interruptions

executor:
  config:
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    cpu:
      limit: 1
      request: 0.3
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    memory:
      limit: 500M
      request: 250M
    node_selectors:
      nodetype: airflow-srp-spot
    tolerations:
      - effect: NoSchedule
        key: service
        operator: Equal
        value: airflow-srp
    volume_mounts:
      - mountPath: /usr/local/airflow/plugins
        name: airflow-dags
        subPath: airflow-de/plugins
  type: celery
namespace: de
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ
path: de/replicate/etl/layout_service_preprod_1
paused: false
project_name: replicate
schedule:
  end_date: "2026-08-27T00:00:00"
  interval: 0 */3 * * *
  start_date: "2025-09-01T00:00:00"
schedule_type: fixed
sla: 180 minutes
task_concurrency: 2
poke_interval: 180
redshift_sla_seconds: 10800
emr_sensor: {}
spark:
  aws_conn_id: aws_default
  mode: emr
  nessie_jar_version: 2.1.2
  cluster_reuse: true
  emr_version: 6.11.1
support_files: []
tables:
  - flow_id: layout_service_preprod.published_layouts.v1
    topic_name: postgres.layout_service_preprod.public.published_layouts
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: created_by
          type: varchar(max)
        - name: updated_by
          type: varchar(max)
        - name: deleted_at
          type: timestamp
        - name: name
          type: varchar(max)
        - name: change_log
          type: varchar(max)
        - name: layout
          type: varchar(max)
        - name: version
          type: int4
        - name: tenant_id
          type: int8
        - name: change_log_metadata
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.layout_service_preprod.public.published_layouts/
      schema: lake_layout_service_preprod
      sortkey: []
      table: published_layouts
    source:
      database: layout_service_preprod
      table: published_layouts
  - flow_id: layout_service_preprod.widgets.v1
    topic_name: postgres.layout_service_preprod.public.widgets
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: created_by
          type: varchar(max)
        - name: updated_by
          type: varchar(max)
        - name: deleted_at
          type: timestamp
        - name: name
          type: varchar(max)
        - name: display_name
          type: varchar(max)
        - name: template_name
          type: varchar(max)
        - name: template_id
          type: int8
        - name: tracking_id
          type: varchar(max)
        - name: override_spacing
          type: varchar(max)
        - name: rule
          type: varchar(max)
        - name: schedule
          type: varchar(max)
        - name: components_ordering
          type: varchar(max)
        - name: metadata
          type: varchar(max)
        - name: view_config
          type: varchar(max)
        - name: rule_id
          type: int4
        - name: wireframe_status
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.layout_service_preprod.public.widgets/
      schema: lake_layout_service_preprod
      sortkey: []
      table: widgets
    source:
      database: layout_service_preprod
      table: widgets
tags:
  - de
  - replicate
  - layout_service_preprod_1
template_name: nessie
version: 1
