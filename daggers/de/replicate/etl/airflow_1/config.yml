dag_name: airflow_1
dag_type: etl
escalation_priority: low
alert_configs:
  slack:
    - channel: bl-data-alerts-p1
  error_channel_mapping:
    terminated_with_errors: bl-data-spot-interruptions
    spot: bl-data-spot-interruptions
executor:
  config:
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    cpu:
      limit: 1
      request: 0.3
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    memory:
      limit: 500M
      request: 250M
    node_selectors:
      nodetype: airflow-srp-spot
    tolerations:
      - effect: NoSchedule
        key: service
        operator: Equal
        value: airflow-srp
    volume_mounts:
      - mountPath: /usr/local/airflow/plugins
        name: airflow-dags
        subPath: airflow-de/plugins
  type: celery
namespace: de
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ
slack_alert_configs:
  - channel: bl-data-alerts-p1
path: de/replicate/etl/airflow_1
paused: false
project_name: replicate
schedule:
  end_date: "2026-07-12T00:00:00"
  interval: 0 */3 * * *
  start_date: "2025-07-17T00:00:00"
schedule_type: fixed
sla: 180 minutes
task_concurrency: 4
poke_interval: 180
redshift_sla_seconds: 10800
emr_sensor: {}
spark:
  aws_conn_id: aws_default
  mode: emr
  nessie_jar_version: 2.1.2
  cluster_reuse: true
  emr_version: 6.11.1
support_files: []
tables:
  - flow_id: airflow.dag.v1
    topic_name: postgres.airflow.public.dag
    redshift_replication: false
    sink:
      column_dtypes:
        - name: dag_id
          type: varchar(250)
        - name: is_paused
          type: boolean
        - name: is_subdag
          type: boolean
        - name: is_active
          type: boolean
        - name: last_parsed_time
          type: timestamp
        - name: last_pickled
          type: timestamp
        - name: last_expired
          type: timestamp
        - name: scheduler_lock
          type: boolean
        - name: pickle_id
          type: int4
        - name: fileloc
          type: varchar(2000)
        - name: owners
          type: varchar(2000)
        - name: description
          type: varchar(max)
        - name: default_view
          type: varchar(25)
        - name: schedule_interval
          type: varchar(max)
        - name: root_dag_id
          type: varchar(250)
        - name: next_dagrun
          type: timestamp
        - name: next_dagrun_create_after
          type: timestamp
        - name: max_active_tasks
          type: int4
        - name: has_task_concurrency_limits
          type: boolean
        - name: max_active_runs
          type: int4
        - name: next_dagrun_data_interval_start
          type: timestamp
        - name: next_dagrun_data_interval_end
          type: timestamp
        - name: has_import_errors
          type: boolean
        - name: timetable_description
          type: varchar(1000)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: dag_id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [dag_id]
      s3_url: s3://prod-dse-nessie-output/postgres.airflow.public.dag/
      schema: lake_airflow
      sortkey: []
      table: dag
    source:
      database: airflow
      table: dag
  - flow_id: airflow.dag_run.v1
    topic_name: postgres.airflow.public.dag_run
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: dag_id
          type: varchar(250)
        - name: execution_date
          type: timestamp
        - name: state
          type: varchar(50)
        - name: run_id
          type: varchar(250)
        - name: external_trigger
          type: boolean
        - name: conf
          type: varchar(max)
        - name: end_date
          type: timestamp
        - name: start_date
          type: timestamp
        - name: run_type
          type: varchar(50)
        - name: last_scheduling_decision
          type: timestamp
        - name: dag_hash
          type: varchar(32)
        - name: creating_job_id
          type: int4
        - name: queued_at
          type: timestamp
        - name: data_interval_start
          type: timestamp
        - name: data_interval_end
          type: timestamp
        - name: log_template_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.airflow.public.dag_run/
      schema: lake_airflow
      sortkey: []
      table: dag_run
    source:
      database: airflow
      table: dag_run
  - flow_id: airflow.serialized_dag.v1
    topic_name: postgres.airflow.public.serialized_dag
    redshift_replication: false
    sink:
      column_dtypes:
        - name: dag_id
          type: varchar(250)
        - name: fileloc
          type: varchar(2000)
        - name: fileloc_hash
          type: int8
        - name: data
          type: varchar(max)
        - name: last_updated
          type: timestamp
        - name: dag_hash
          type: varchar(32)
        - name: data_compressed
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: dag_id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [dag_id]
      s3_url: s3://prod-dse-nessie-output/postgres.airflow.public.serialized_dag/
      schema: lake_airflow
      sortkey: []
      table: serialized_dag
    source:
      database: airflow
      table: serialized_dag
  - flow_id: airflow.slot_pool.v1
    topic_name: postgres.airflow.public.slot_pool
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: pool
          type: varchar(256)
        - name: slots
          type: int4
        - name: description
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.airflow.public.slot_pool/
      schema: lake_airflow
      sortkey: []
      table: slot_pool
    source:
      database: airflow
      table: slot_pool
  - flow_id: airflow.task_instance.v1
    topic_name: postgres.airflow.public.task_instance
    redshift_replication: false
    sink:
      column_dtypes:
        - name: task_id
          type: varchar(250)
        - name: dag_id
          type: varchar(250)
        - name: run_id
          type: varchar(250)
        - name: start_date
          type: timestamp
        - name: end_date
          type: timestamp
        - name: duration
          type: float8
        - name: state
          type: varchar(20)
        - name: try_number
          type: int4
        - name: hostname
          type: varchar(1000)
        - name: unixname
          type: varchar(1000)
        - name: job_id
          type: int4
        - name: pool
          type: varchar(256)
        - name: queue
          type: varchar(256)
        - name: priority_weight
          type: int4
        - name: operator
          type: varchar(1000)
        - name: queued_dttm
          type: timestamp
        - name: pid
          type: int4
        - name: max_tries
          type: int4
        - name: executor_config
          type: varchar(max)
        - name: pool_slots
          type: int4
        - name: queued_by_job_id
          type: int4
        - name: external_executor_id
          type: varchar(250)
        - name: trigger_id
          type: int4
        - name: trigger_timeout
          type: timestamp
        - name: next_method
          type: varchar(1000)
        - name: next_kwargs
          type: varchar(max)
        - name: map_index
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: task_id,dag_id,run_id,map_index
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [task_id,dag_id,run_id,map_index]
      s3_url: s3://prod-dse-nessie-output/postgres.airflow.public.task_instance/
      schema: lake_airflow
      sortkey: []
      table: task_instance
    source:
      database: airflow
      table: task_instance
tags:
  - de
  - replicate
  - airflow_1
template_name: nessie
version: 1
