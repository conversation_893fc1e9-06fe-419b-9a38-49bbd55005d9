dag_name: vendor_console_1
dag_type: etl
escalation_priority: low

alert_configs:
  slack:
    - channel: bl-data-alerts-p1
  error_channel_mapping:
    terminated_with_errors: bl-data-spot-interruptions
    spot: bl-data-spot-interruptions

executor:
  config:
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    cpu:
      limit: 1
      request: 0.3
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    memory:
      limit: 500M
      request: 250M
    node_selectors:
      nodetype: airflow-srp-spot
    tolerations:
      - effect: NoSchedule
        key: service
        operator: Equal
        value: airflow-srp
    volume_mounts:
      - mountPath: /usr/local/airflow/plugins
        name: airflow-dags
        subPath: airflow-de/plugins
  type: celery
namespace: de
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ

path: de/replicate/etl/vendor_console_1
paused: false
project_name: replicate
schedule:
  interval: 27,57 0-19,22 * * *
  start_date: "2022-05-09T00:00:00"
  end_date: '2026-08-15T00:00:00'
schedule_type: fixed
sla: 180 minutes
task_concurrency: 15
poke_interval: 180
spark:
  aws_conn_id: aws_default
  mode: emr
  nessie_jar_version: 2.1.2
  cluster_reuse: true
  emr_version: 6.11.1
support_files: []
redshift_sla_seconds: 21600
emr_sensor: {}
nessie_dag_split: true
tables:
  - flow_id: vendor_console.appointment.v1
    topic_name: postgres.vendor_console.public.appointment
    redshift_replication: false
    sink:
      column_dtypes:
        - name: active
          type: boolean
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
        - name: appointment_id
          type: varchar(20)
        - name: vendor_id
          type: int4
        - name: vendor_name
          type: varchar(100)
        - name: facility_id
          type: int4
        - name: facility_name
          type: varchar(100)
        - name: total_units_ordered
          type: int4
        - name: total_units_scheduled
          type: int4
        - name: item_count_ordered
          type: int4
        - name: item_count_scheduled
          type: int4
        - name: status
          type: varchar(30)
        - name: scheduled_date
          type: date
        - name: meta
          type: varchar(max)
        - name: slot_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: appointment_id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [appointment_id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.appointment/
      schema: lake_vendor_console
      sortkey: []
      table: appointment
    source:
      database: vendor_console
      table: appointment
  - flow_id: vendor_console.appointment_capacity_config.v1
    topic_name: postgres.vendor_console.public.appointment_capacity_config
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: active
          type: boolean
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
        - name: facility_id
          type: int4
        - name: start_date
          type: date
        - name: end_date
          type: date
        - name: attribute_name
          type: varchar(255)
        - name: total_capacity
          type: int4
        - name: level
          type: varchar(255)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.appointment_capacity_config/
      schema: lake_vendor_console
      sortkey: []
      table: appointment_capacity_config
    source:
      database: vendor_console
      table: appointment_capacity_config
  - flow_id: vendor_console.appointment_capacity_config_log.v1
    topic_name: postgres.vendor_console.public.appointment_capacity_config_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: created_at
          type: timestamp
        - name: created_by
          type: int4
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int4
        - name: action
          type: varchar(50)
        - name: data
          type: varchar(max)
        - name: capacity_config_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.appointment_capacity_config_log/
      schema: lake_vendor_console
      sortkey: []
      table: appointment_capacity_config_log
    source:
      database: vendor_console
      table: appointment_capacity_config_log
  - flow_id: vendor_console.appointment_facility_day_capacity.v1
    topic_name: postgres.vendor_console.public.appointment_facility_day_capacity
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: active
          type: boolean
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
        - name: facility_id
          type: int4
        - name: date
          type: date
        - name: total_sku_capacity
          type: int4
        - name: remaining_sku_capacity
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.appointment_facility_day_capacity/
      schema: lake_vendor_console
      sortkey: []
      table: appointment_facility_day_capacity
    source:
      database: vendor_console
      table: appointment_facility_day_capacity
  - flow_id: vendor_console.appointment_facility_slot.v1
    topic_name: postgres.vendor_console.public.appointment_facility_slot
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: active
          type: boolean
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
        - name: facility_id
          type: int4
        - name: slot_date
          type: date
        - name: slot_start_time
          type: time
        - name: slot_end_time
          type: time
        - name: total_item_capacity
          type: int4
        - name: remaining_item_capacity
          type: int4
        - name: day_level_capacity_id
          type: int8
      copy_params:
        - ESCAPE
        - REMOVEQUOTES
        - DELIMITER ','
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.appointment_facility_slot/
      schema: lake_vendor_console
      sortkey: []
      table: appointment_facility_slot
    source:
      database: vendor_console
      table: appointment_facility_slot
  - flow_id: vendor_console.appointment_facility_slot_log.v1
    topic_name: postgres.vendor_console.public.appointment_facility_slot_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: created_at
          type: timestamp
        - name: created_by
          type: int4
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int4
        - name: action
          type: varchar(50)
        - name: data
          type: varchar(max)
        - name: slot_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.appointment_facility_slot_log/
      schema: lake_vendor_console
      sortkey: []
      table: appointment_facility_slot_log
    source:
      database: vendor_console
      table: appointment_facility_slot_log
  - flow_id: vendor_console.appointment_log.v1
    topic_name: postgres.vendor_console.public.appointment_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(128)
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int4
        - name: action
          type: varchar(50)
        - name: data
          type: varchar(max)
        - name: appointment_id
          type: varchar(20)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.appointment_log/
      schema: lake_vendor_console
      sortkey: []
      table: appointment_log
    source:
      database: vendor_console
      table: appointment_log
  - flow_id: vendor_console.appointment_po_mapping.v1
    topic_name: postgres.vendor_console.public.appointment_po_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: active
          type: boolean
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
        - name: scheduled_quantity
          type: int4
        - name: appointment_id
          type: varchar(20)
        - name: po_number
          type: varchar(30)
        - name: scheduled_skus
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.appointment_po_mapping/
      schema: lake_vendor_console
      sortkey: []
      table: appointment_po_mapping
    source:
      database: vendor_console
      table: appointment_po_mapping
  - flow_id: vendor_console.appointment_slot_config.v1
    topic_name: postgres.vendor_console.public.appointment_slot_config
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: active
          type: boolean
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
        - name: facility_id
          type: int4
        - name: start_date
          type: date
        - name: end_date
          type: date
        - name: start_time
          type: time
        - name: end_time
          type: time
        - name: slot_duration
          type: varchar(max)
      copy_params:
        - ESCAPE
        - REMOVEQUOTES
        - DELIMITER ','
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.appointment_slot_config/
      schema: lake_vendor_console
      sortkey: []
      table: appointment_slot_config
    source:
      database: vendor_console
      table: appointment_slot_config
  - flow_id: vendor_console.appointment_slot_config_log.v1
    topic_name: postgres.vendor_console.public.appointment_slot_config_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: created_at
          type: timestamp
        - name: created_by
          type: int4
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int4
        - name: action
          type: varchar(50)
        - name: data
          type: varchar(max)
        - name: slot_config_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.appointment_slot_config_log/
      schema: lake_vendor_console
      sortkey: []
      table: appointment_slot_config_log
    source:
      database: vendor_console
      table: appointment_slot_config_log
  - flow_id: vendor_console.appointment_slot_mapping.v1
    topic_name: postgres.vendor_console.public.appointment_slot_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: scheduled_quantity
          type: int4
        - name: scheduled_sku
          type: int4
        - name: meta
          type: varchar(max)
        - name: active
          type: boolean
        - name: appointment_id
          type: varchar(20)
        - name: slot_capacity_id
          type: int8
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.appointment_slot_mapping/
      schema: lake_vendor_console
      sortkey: []
      table: appointment_slot_mapping
    source:
      database: vendor_console
      table: appointment_slot_mapping
  - flow_id: vendor_console.appointment_slot_mapping_log.v1
    topic_name: postgres.vendor_console.public.appointment_slot_mapping_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: action
          type: varchar(50)
        - name: data
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: appointment_slot_mapping_id
          type: int4
        - name: created_by
          type: varchar(255)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.appointment_slot_mapping_log/
      schema: lake_vendor_console
      sortkey: []
      table: appointment_slot_mapping_log
    source:
      database: vendor_console
      table: appointment_slot_mapping_log
  - flow_id: vendor_console.auth_plan.v1
    topic_name: postgres.vendor_console.public.auth_plan
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: active
          type: boolean
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
        - name: name
          type: varchar(255)
        - name: business_type
          type: varchar(20)
        - name: description
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.auth_plan/
      schema: lake_vendor_console
      sortkey: []
      table: auth_plan
    source:
      database: vendor_console
      table: auth_plan
  - flow_id: vendor_console.base_bulk_upload_request_tracker.v1
    topic_name: postgres.vendor_console.public.base_bulk_upload_request_tracker
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: request_type
          type: varchar(255)
        - name: uploaded_file_path
          type: varchar(255)
        - name: processed_file_path
          type: varchar(255)
        - name: state
          type: varchar(255)
        - name: failed_record_count
          type: int4
        - name: processed_record_count
          type: int4
        - name: remarks
          type: varchar(max)
        - name: active
          type: boolean
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.base_bulk_upload_request_tracker/
      schema: lake_vendor_console
      sortkey: []
      table: base_bulk_upload_request_tracker
    source:
      database: vendor_console
      table: base_bulk_upload_request_tracker
  - flow_id: vendor_console.client_po_details.v1
    topic_name: postgres.vendor_console.public.client_po_details
    redshift_replication: false
    sink:
      column_dtypes:
        - name: po_number
          type: varchar(30)
        - name: vendor_id
          type: int4
        - name: vendor_name
          type: varchar(100)
        - name: manufacturer_id
          type: int4
        - name: manufacturer_name
          type: varchar(250)
        - name: facility_id
          type: int4
        - name: facility_name
          type: varchar(100)
        - name: city_name
          type: varchar(30)
        - name: address
          type: varchar(255)
        - name: outlet_id
          type: int4
        - name: po_state
          type: varchar(30)
        - name: issue_date
          type: timestamp
        - name: expiry_date
          type: timestamp
        - name: schedule_date
          type: timestamp
        - name: total_units_ordered
          type: int4
        - name: total_po_amount
          type: numeric(10, 2)
        - name: load_size
          type: varchar(100)
        - name: item_count
          type: int4
        - name: active
          type: boolean
        - name: created_at
          type: timestamp
        - name: created_by
          type: int4
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int4
        - name: download_url
          type: varchar(500)
        - name: total_grn_quantity
          type: int4
        - name: delivery_date
          type: timestamp
        - name: grn_report_excel_url
          type: varchar(500)
        - name: pm_email
          type: varchar(100)
        - name: pm_name
          type: varchar(100)
        - name: pm_phone
          type: varchar(40)
        - name: po_excel_report_url
          type: varchar(500)
        - name: entity_vendor_cin
          type: varchar(100)
        - name: entity_vendor_legal_name
          type: varchar(100)
        - name: scheduled_on
          type: timestamp
        - name: multiple_grn
          type: int4
        - name: delivery_type
          type: varchar(100)
        - name: po_type_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: po_number
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [po_number]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.client_po_details/
      schema: lake_vendor_console
      sortkey: []
      table: client_po_details
    source:
      database: vendor_console
      table: client_po_details
  - flow_id: vendor_console.client_po_items.v1
    topic_name: postgres.vendor_console.public.client_po_items
    redshift_replication: false
    sink:
      column_dtypes:
        - name: po_number
          type: varchar(30)
        - name: item_id
          type: int4
        - name: upc
          type: varchar(50)
        - name: name
          type: varchar(1024)
        - name: uom_text
          type: varchar(80)
        - name: variant_id
          type: varchar(255)
        - name: units_ordered
          type: int4
        - name: cost_price
          type: numeric(10, 2)
        - name: tax_value
          type: numeric(10, 2)
        - name: cgst_value
          type: numeric(10, 2)
        - name: sgst_value
          type: numeric(10, 2)
        - name: igst_value
          type: numeric(10, 2)
        - name: cess_value
          type: numeric(10, 2)
        - name: landing_rate
          type: numeric(10, 2)
        - name: total_amount
          type: numeric(10, 2)
        - name: remaining_quantity
          type: int4
        - name: id
          type: int8
        - name: margin_percentage
          type: numeric(10, 2)
        - name: mrp
          type: numeric(10, 2)
        - name: bucket_type
          type: varchar(2)
        - name: created_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.client_po_items/
      schema: lake_vendor_console
      sortkey: []
      table: client_po_items
    source:
      database: vendor_console
      table: client_po_items
  - flow_id: vendor_console.cms_assortment_request.v1
    topic_name: postgres.vendor_console.public.cms_assortment_request
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: request_id
          type: int8
        - name: item_id
          type: int4
        - name: product_id
          type: int4
        - name: manufacturer_id
          type: int4
        - name: request_type
          type: varchar(50)
        - name: meta_data
          type: varchar(max)
        - name: created_by
          type: varchar(512)
        - name: created_at
          type: timestamp
        - name: updated_by
          type: varchar(512)
        - name: updated_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.cms_assortment_request/
      schema: lake_vendor_console
      sortkey: []
      table: cms_assortment_request
    source:
      database: vendor_console
      table: cms_assortment_request
  - flow_id: vendor_console.cms_assortment_request_log.v1
    topic_name: postgres.vendor_console.public.cms_assortment_request_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: request_id
          type: int8
        - name: request_data
          type: varchar(max)
        - name: response_data
          type: varchar(max)
        - name: created_by
          type: varchar(512)
        - name: created_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.cms_assortment_request_log/
      schema: lake_vendor_console
      sortkey: []
      table: cms_assortment_request_log
    source:
      database: vendor_console
      table: cms_assortment_request_log
  - flow_id: vendor_console.courier_partner_days_config.v1
    topic_name: postgres.vendor_console.public.courier_partner_days_config
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: active
          type: boolean
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
        - name: day_of_week_id
          type: int4
        - name: courier_partner_id
          type: int4
        - name: facility_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.courier_partner_days_config/
      schema: lake_vendor_console
      sortkey: []
      table: courier_partner_days_config
    source:
      database: vendor_console
      table: courier_partner_days_config
  - flow_id: vendor_console.courier_partner_details.v1
    topic_name: postgres.vendor_console.public.courier_partner_details
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: active
          type: boolean
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
        - name: name
          type: varchar(255)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.courier_partner_details/
      schema: lake_vendor_console
      sortkey: []
      table: courier_partner_details
    source:
      database: vendor_console
      table: courier_partner_details
  - flow_id: vendor_console.day_capacity_request_data_tracker.v1
    topic_name: postgres.vendor_console.public.day_capacity_request_data_tracker
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: facility_id
          type: int4
        - name: start_date
          type: date
        - name: end_date
          type: date
        - name: total_quantity_capacity
          type: int4
        - name: reason
          type: varchar(255)
        - name: status
          type: varchar(255)
        - name: remarks
          type: varchar(max)
        - name: meta
          type: varchar(max)
        - name: active
          type: boolean
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
        - name: request_id
          type: int8
        - name: total_sku_capacity
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.day_capacity_request_data_tracker/
      schema: lake_vendor_console
      sortkey: []
      table: day_capacity_request_data_tracker
    source:
      database: vendor_console
      table: day_capacity_request_data_tracker
  - flow_id: vendor_console.entity.v1
    topic_name: postgres.vendor_console.public.entity
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: active
          type: boolean
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
        - name: external_id
          type: int4
        - name: source
          type: varchar(20)
        - name: name
          type: varchar(255)
        - name: type
          type: varchar(20)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.entity/
      schema: lake_vendor_console
      sortkey: []
      table: entity
    source:
      database: vendor_console
      table: entity
  - flow_id: vendor_console.entity_plan_mapping.v1
    topic_name: postgres.vendor_console.public.entity_plan_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: active
          type: boolean
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
        - name: status
          type: varchar(20)
        - name: entity_id
          type: int4
        - name: plan_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.entity_plan_mapping/
      schema: lake_vendor_console
      sortkey: []
      table: entity_plan_mapping
    source:
      database: vendor_console
      table: entity_plan_mapping
  - flow_id: vendor_console.facility_config.v1
    topic_name: postgres.vendor_console.public.facility_config
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: active
          type: boolean
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
        - name: facility_id
          type: int4
        - name: config_key
          type: varchar(50)
        - name: config_value
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.facility_config/
      schema: lake_vendor_console
      sortkey: []
      table: facility_config
    source:
      database: vendor_console
      table: facility_config
  - flow_id: vendor_console.facility_config_log.v1
    topic_name: postgres.vendor_console.public.facility_config_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: action
          type: varchar(50)
        - name: data
          type: varchar(max)
        - name: facility_config_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.facility_config_log/
      schema: lake_vendor_console
      sortkey: []
      table: facility_config_log
    source:
      database: vendor_console
      table: facility_config_log
  - flow_id: vendor_console.facility_day_level_capacity.v1
    topic_name: postgres.vendor_console.public.facility_day_level_capacity
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: active
          type: boolean
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
        - name: facility_id
          type: int4
        - name: date
          type: date
        - name: capacity_type
          type: int4
        - name: capacity_quantity
          type: int4
        - name: sku_capacity
          type: int4
        - name: meta
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.facility_day_level_capacity/
      schema: lake_vendor_console
      sortkey: []
      table: facility_day_level_capacity
    source:
      database: vendor_console
      table: facility_day_level_capacity
  - flow_id: vendor_console.facility_day_level_capacity_log.v1
    topic_name: postgres.vendor_console.public.facility_day_level_capacity_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: action
          type: varchar(50)
        - name: data
          type: varchar(max)
        - name: facility_day_level_capacity_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.facility_day_level_capacity_log/
      schema: lake_vendor_console
      sortkey: []
      table: facility_day_level_capacity_log
    source:
      database: vendor_console
      table: facility_day_level_capacity_log
  - flow_id: vendor_console.facility_slot_time.v1
    topic_name: postgres.vendor_console.public.facility_slot_time
    redshift_replication: false
    sink:
      column_dtypes:
        - name: active
          type: boolean
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
        - name: id
          type: int8
        - name: facility_id
          type: int4
        - name: start_time
          type: time
        - name: end_time
          type: time
        - name: slot_date
          type: date
      copy_params:
        - ESCAPE
        - REMOVEQUOTES
        - DELIMITER ','
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.facility_slot_time/
      schema: lake_vendor_console
      sortkey: []
      table: facility_slot_time
    source:
      database: vendor_console
      table: facility_slot_time
  - flow_id: vendor_console.facility_slot_time_log.v1
    topic_name: postgres.vendor_console.public.facility_slot_time_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: action
          type: varchar(50)
        - name: data
          type: varchar(max)
        - name: facility_slot_time_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.facility_slot_time_log/
      schema: lake_vendor_console
      sortkey: []
      table: facility_slot_time_log
    source:
      database: vendor_console
      table: facility_slot_time_log
  - flow_id: vendor_console.invoice.v1
    topic_name: postgres.vendor_console.public.invoice
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: vendor_invoice_id
          type: varchar(50)
        - name: order_number
          type: varchar(50)
        - name: invoice_status
          type: int4
        - name: due_date
          type: timestamp
        - name: invoice_date
          type: timestamp
        - name: grn_date
          type: timestamp
        - name: reject_reason
          type: varchar(200)
        - name: meta
          type: varchar(max)
        - name: invoice_source
          type: int4
        - name: created_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.invoice/
      schema: lake_vendor_console
      sortkey: []
      table: invoice
    source:
      database: vendor_console
      table: invoice
  - flow_id: vendor_console.invoice_event_log.v1
    topic_name: postgres.vendor_console.public.invoice_event_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: start_status
          type: int4
        - name: end_status
          type: int4
        - name: created_at
          type: timestamp
        - name: invoice_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.invoice_event_log/
      schema: lake_vendor_console
      sortkey: []
      table: invoice_event_log
    source:
      database: vendor_console
      table: invoice_event_log
  - flow_id: vendor_console.invoice_payment_details.v1
    topic_name: postgres.vendor_console.public.invoice_payment_details
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: utr_number
          type: varchar(1024)
        - name: utr_total_payment
          type: float8
        - name: meta
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.invoice_payment_details/
      schema: lake_vendor_console
      sortkey: []
      table: invoice_payment_details
    source:
      database: vendor_console
      table: invoice_payment_details
  - flow_id: vendor_console.invoice_payment_mapping.v1
    topic_name: postgres.vendor_console.public.invoice_payment_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: external_payment_id
          type: varchar(1024)
        - name: payment_amount
          type: float8
        - name: payment_date
          type: timestamp
        - name: payment_status
          type: int4
        - name: invoice_id
          type: int4
        - name: payment_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.invoice_payment_mapping/
      schema: lake_vendor_console
      sortkey: []
      table: invoice_payment_mapping
    source:
      database: vendor_console
      table: invoice_payment_mapping
  - flow_id: vendor_console.item_proxy_category.v1
    topic_name: postgres.vendor_console.public.item_proxy_category
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: l0_id
          type: int4
        - name: l0_name
          type: varchar(250)
        - name: l1_id
          type: int4
        - name: l2_name
          type: varchar(250)
        - name: proxy_category
          type: varchar(250)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.item_proxy_category/
      schema: lake_vendor_console
      sortkey: []
      table: item_proxy_category
    source:
      database: vendor_console
      table: item_proxy_category
  - flow_id: vendor_console.po_reservation.v1
    topic_name: postgres.vendor_console.public.po_reservation
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: status
          type: varchar(30)
        - name: reserved_date
          type: date
        - name: expire_at
          type: timestamp
        - name: meta
          type: varchar(max)
        - name: appointment_id
          type: varchar(20)
        - name: po_number
          type: varchar(30)
        - name: active
          type: boolean
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.po_reservation/
      schema: lake_vendor_console
      sortkey: []
      table: po_reservation
    source:
      database: vendor_console
      table: po_reservation
  - flow_id: vendor_console.po_reservation_log.v1
    topic_name: postgres.vendor_console.public.po_reservation_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: reservation_id
          type: int8
        - name: action
          type: varchar(50)
        - name: data
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(128)
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.po_reservation_log/
      schema: lake_vendor_console
      sortkey: []
      table: po_reservation_log
    source:
      database: vendor_console
      table: po_reservation_log
  - flow_id: vendor_console.report_requests.v1
    topic_name: postgres.vendor_console.public.report_requests
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: type
          type: varchar(30)
        - name: state
          type: varchar(30)
        - name: created_at
          type: timestamp
        - name: updated_at
          type: timestamp
        - name: comments
          type: varchar(max)
        - name: requester_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.report_requests/
      schema: lake_vendor_console
      sortkey: []
      table: report_requests
    source:
      database: vendor_console
      table: report_requests
  - flow_id: vendor_console.reservation_slot_details.v1
    topic_name: postgres.vendor_console.public.reservation_slot_details
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: reserved_quantity
          type: int4
        - name: reserved_sku
          type: int4
        - name: scheduled_quantity
          type: int4
        - name: scheduled_sku
          type: int4
        - name: reservation_id
          type: int8
        - name: active
          type: boolean
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
        - name: slot_capacity_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.reservation_slot_details/
      schema: lake_vendor_console
      sortkey: []
      table: reservation_slot_details
    source:
      database: vendor_console
      table: reservation_slot_details
  - flow_id: vendor_console.reservation_slot_details_log.v1
    topic_name: postgres.vendor_console.public.reservation_slot_details_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: reservation_details_id
          type: int8
        - name: action
          type: varchar(50)
        - name: data
          type: varchar(max)
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(128)
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.reservation_slot_details_log/
      schema: lake_vendor_console
      sortkey: []
      table: reservation_slot_details_log
    source:
      database: vendor_console
      table: reservation_slot_details_log
  - flow_id: vendor_console.shipment.v1
    topic_name: postgres.vendor_console.public.shipment
    redshift_replication: false
    sink:
      column_dtypes:
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
        - name: id
          type: int8
        - name: tracking_id
          type: varchar(255)
        - name: appointment_id
          type: varchar(20)
        - name: courier_partner_name
          type: varchar(255)
        - name: courier_partner_id
          type: int4
        - name: active
          type: boolean
        - name: meta
          type: varchar(max)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.shipment/
      schema: lake_vendor_console
      sortkey: []
      table: shipment
    source:
      database: vendor_console
      table: shipment
  - flow_id: vendor_console.slot_capacity.v1
    topic_name: postgres.vendor_console.public.slot_capacity
    redshift_replication: false
    sink:
      column_dtypes:
        - name: active
          type: boolean
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
        - name: id
          type: int8
        - name: capacity_type
          type: int4
        - name: total_capacity_quantity
          type: int4
        - name: total_sku_quantity
          type: int4
        - name: remaining_capacity_quantity
          type: int4
        - name: remaining_sku_quantity
          type: int4
        - name: slot_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.slot_capacity/
      schema: lake_vendor_console
      sortkey: []
      table: slot_capacity
    source:
      database: vendor_console
      table: slot_capacity
  - flow_id: vendor_console.slot_capacity_config.v1
    topic_name: postgres.vendor_console.public.slot_capacity_config
    redshift_replication: false
    sink:
      column_dtypes:
        - name: active
          type: boolean
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
        - name: id
          type: int8
        - name: config_type
          type: varchar(50)
        - name: config_value
          type: varchar(max)
        - name: slot_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.slot_capacity_config/
      schema: lake_vendor_console
      sortkey: []
      table: slot_capacity_config
    source:
      database: vendor_console
      table: slot_capacity_config
  - flow_id: vendor_console.slot_capacity_log.v1
    topic_name: postgres.vendor_console.public.slot_capacity_log
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: action
          type: varchar(50)
        - name: data
          type: varchar(max)
        - name: slot_capacity_id
          type: int8
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.slot_capacity_log/
      schema: lake_vendor_console
      sortkey: []
      table: slot_capacity_log
    source:
      database: vendor_console
      table: slot_capacity_log
  - flow_id: vendor_console.slot_capacity_request_data_tracker.v1
    topic_name: postgres.vendor_console.public.slot_capacity_request_data_tracker
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int8
        - name: facility_id
          type: int4
        - name: slot_start_date
          type: date
        - name: slot_end_date
          type: date
        - name: slot_start_time
          type: time
        - name: slot_end_time
          type: time
        - name: normal_capacity_percentage
          type: float8
        - name: buffer_capacity_percentage
          type: float8
        - name: priority_capacity_percentage
          type: float8
        - name: courier_capacity_percentage
          type: float8
        - name: status
          type: varchar(255)
        - name: remarks
          type: varchar(max)
        - name: meta
          type: varchar(max)
        - name: active
          type: boolean
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
        - name: request_id
          type: int8
      copy_params:
        - ESCAPE
        - REMOVEQUOTES
        - DELIMITER ','
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.slot_capacity_request_data_tracker/
      schema: lake_vendor_console
      sortkey: []
      table: slot_capacity_request_data_tracker
    source:
      database: vendor_console
      table: slot_capacity_request_data_tracker
  - flow_id: vendor_console.user_details.v2
    topic_name: postgres.vendor_console.public.user_details
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: email
          type: varchar(512)
        - name: vendor_ids
          type: varchar(1024)
        - name: manufacturer_ids
          type: varchar(1024)
        - name: active
          type: boolean
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
        - name: last_login_at
          type: timestamp
        - name: is_internal_admin
          type: boolean
        - name: tnc_accepted_at
          type: timestamp
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.user_details/
      schema: lake_vendor_console
      sortkey: []
      table: user_details
    source:
      database: vendor_console
      table: user_details
  - flow_id: vendor_console.user_entity_mapping.v1
    topic_name: postgres.vendor_console.public.user_entity_mapping
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: active
          type: boolean
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
        - name: entity_id
          type: int4
        - name: user_id
          type: int4
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.user_entity_mapping/
      schema: lake_vendor_console
      sortkey: []
      table: user_entity_mapping
    source:
      database: vendor_console
      table: user_entity_mapping
  - flow_id: vendor_console.vendor_facility_auto_release.v1
    topic_name: postgres.vendor_console.public.vendor_facility_auto_release
    redshift_replication: false
    sink:
      column_dtypes:
        - name: id
          type: int4
        - name: facility_id
          type: int4
        - name: vendor_id
          type: int4
        - name: auto_release_time
          type: int4
        - name: active
          type: boolean
        - name: created_at
          type: timestamp
        - name: created_by
          type: varchar(255)
        - name: updated_at
          type: timestamp
        - name: updated_by
          type: varchar(255)
      copy_params:
        - FORMAT AS PARQUET
      incremental_key: id
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [id]
      s3_url: s3://prod-dse-nessie-output/postgres.vendor_console.public.vendor_facility_auto_release/
      schema: lake_vendor_console
      sortkey: []
      table: vendor_facility_auto_release
    source:
      database: vendor_console
      table: vendor_facility_auto_release
tags:
  - de
  - replicate
  - vendor_console_1
template_name: nessie
version: 1
