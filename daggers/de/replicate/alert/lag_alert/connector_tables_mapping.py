#!/usr/bin/env python3
"""
Connector Tables Mapping Script

This script connects to the Kafka Connect API, retrieves information about all active connectors,
and provides a mapping between connector names and their tables as a pandas DataFrame.
"""

import os
import json
import requests
import pandas as pd
from typing import Dict, List, Any
import pencilbox as pb

# Base URL for the Kafka Connect API
BASE_URL = pb.get_secret("data/services/source-replication/alert/connect")["BASE_URL"]

def get_all_connectors() -> List[str]:
    """
    Fetch all connectors from the Kafka Connect API
    
    Returns:
        List[str]: List of connector names
    """
    try:
        response = requests.get(f"{BASE_URL}/connectors/")
        if response.status_code != 200:
            print(f"Failed to get connectors. Status code: {response.status_code}")
            return []
        return response.json()
    except Exception as e:
        print(f"Error fetching connectors: {str(e)}")
        return []

def filter_active_connectors(connectors: List[str]) -> List[str]:
    """
    Filter out connectors to only include active onesArgs:
        connectors (List[str]): List of all connector names
    
    Returns:
        List[str]: Filtered list of active connector names
    """
    active_connectors = []
    
    for connector in connectors:
        try:
            response = requests.get(f"{BASE_URL}/connectors/{connector}/status")
            if response.status_code == 200:
                status_data = response.json()
                connector_state = status_data.get("connector", {}).get("state", "")
                if connector_state == "RUNNING":
                    active_connectors.append(connector)
        except Exception as e:
            print(f"Error checking status for {connector}: {str(e)}")
            
    return active_connectors

def get_connector_details(connector_name: str) -> Dict[str, Any]:
    """
    Get details for a specific connector
    
    Args:
        connector_name (str): Name of the connector
    
    Returns:
        Dict[str, Any]: Connector configuration details
    """
    try:
        response = requests.get(f"{BASE_URL}/connectors/{connector_name}")
        if response.status_code != 200:
            print(f"Warning: Failed to get details for connector {connector_name}. Status code: {response.status_code}")
            return {}
        
        return response.json()
    except Exception as e:
        print(f"Error getting details for {connector_name}: {str(e)}")
        return {}

def extract_tables(connector_details: Dict[str, Any]) -> List[str]:
    """
    Extract table list from connector details
    
    Args:
        connector_details (Dict[str, Any]): Connector details from API
    
    Returns:
        List[str]: List of tables in the connector
    """
    if not connector_details or "config" not in connector_details:
        return []
    
    config = connector_details.get("config", {})
    
    # Try different possible field names where table lists might be stored
    table_fields = ["table.include.list", "tables", "table.whitelist", "tables.include"]
    for field in table_fields:
        if field in config:
            tables_str = config[field]
            # Split by comma if it's a comma-separated list
            if isinstance(tables_str, str):
                return [table.strip() for table in tables_str.split(",")]
    return []

def get_connector_tables_df() -> pd.DataFrame:
    """
    Get all active connectors and their tables as a DataFrame
    
    Returns:
        pd.DataFrame: DataFrame with connector names and their tables
    """
    # Get all connectors
    all_connectors = get_all_connectors()
    if not all_connectors:
        print("No connectors found.")
        return pd.DataFrame(columns=['connector_name', 'tables'])
    
    # Filter connectors starting with "postgres" or "mysql"
    filtered_connectors = [c for c in all_connectors if (c.startswith("postgres") or c.startswith("mysql"))]

    # Filter for active connectors
    active_connectors = filter_active_connectors(filtered_connectors)
    
    # Process each active connector
    connector_data = []
    for connector_name in active_connectors:
        try:
            # Get connector details
            details = get_connector_details(connector_name)
            
            # Extract tables
            tables = extract_tables(details)
            
            # Store connector name and tables
            connector_data.append({
                'connector_name': connector_name,
                'tables': tables
            })
        except Exception as e:
            print(f"Error processing connector {connector_name}: {str(e)}")
    
    # Convert to DataFrame
    df = pd.DataFrame(connector_data)
    return df

