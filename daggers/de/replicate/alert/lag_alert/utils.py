import pytz
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Union
from croniter import croniter

def parse_cron(cron_expression: str) -> int:
    """Parse cron expression to get frequency in minutes"""
    if not cron_expression:
        return 60# Default to 60 minutes if no cron
    try:
        now = datetime.now()
        cron = croniter(cron_expression, now)
        next_run = cron.get_next(datetime)
        next_next_run = cron.get_next(datetime)
        diff_minutes = int((next_next_run - next_run).total_seconds() / 60)
        return max(diff_minutes, 1)
    except Exception as e:
        print(f"Error parsing cron: {str(e)}")
        return 60  # Default frequency

def get_next_run_time(cron_expression: str) -> Optional[datetime]:
    """Calculate when the next run will occur based on the cron expression"""
    try:
        ist_timezone = pytz.timezone("Asia/Kolkata")
        now = datetime.now(ist_timezone)
        cron = croniter(cron_expression, now)
        next_run = cron.get_next(datetime)
        return next_run
    except Exception as e:
        print(f"Error calculating next run time: {str(e)}")
        return None

def calculate_expected_lag(cron_frequency: int, buffer_time: int) -> int:
    """Calculate expected lag based on cron frequency"""
    return cron_frequency + buffer_time

def calculate_time_to_next_sync(next_run_time: Optional[datetime], 
                                current_time: Optional[datetime] = None) -> Optional[int]:
    """Calculate minutes until the next scheduled sync"""
    if next_run_time is None:
        return None
    if current_time is None:
        ist_timezone = pytz.timezone("Asia/Kolkata")
        current_time = datetime.now(ist_timezone)
    # Ensure both times are timezone-aware
    if next_run_time.tzinfo is None:
        ist_timezone = pytz.timezone("Asia/Kolkata")
        next_run_time = ist_timezone.localize(next_run_time)
    if current_time.tzinfo is None:
        ist_timezone = pytz.timezone("Asia/Kolkata")
        current_time = ist_timezone.localize(current_time)
    # Calculate difference in minutes
    time_diff = next_run_time - current_time
    minutes_diff = int(time_diff.total_seconds() / 60)
    return max(0, minutes_diff)

def get_next_run_time_ist(cron_expression: str) -> Optional[datetime]:
    """
    Calculate the next scheduled run time in IST for a given cron expression.
    """
    try:
        ist_timezone = pytz.timezone("Asia/Kolkata")
        now = datetime.now(ist_timezone)
        cron = croniter(cron_expression, now)
        next_run = cron.get_next(datetime)
        
        # Make sure the datetime is timezone-aware (in IST)
        if next_run.tzinfo is None:
            next_run = ist_timezone.localize(next_run)
        else:
            next_run = next_run.astimezone(ist_timezone)
        
        return next_run
    except Exception as e:
        print(f"Error calculating next run time: {str(e)}")
        return None

def get_database_table_from_topic(topic: str) -> Tuple[str, str, str]:
    """Extract database, schema, and table from topic name"""
    parts = topic.split('.')
    if len(parts) >= 4:
        db_type = parts[0]  # mysql or postgres
        database = parts[1]
        schema = parts[2]
        table = parts[3]
        return database, schema, table
    return "", "", ""