alert_configs:
  slack:
  - channel: bl-data-alerts-p1
dag_name: lag_alert
dag_type: alert
escalation_priority: low
executor:
  config:
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
  type: kubernetes
namespace: de
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ
path: de/replicate/alert/lag_alert
paused: false
project_name: replicate
schedule:
  end_date: '2025-10-15T00:00:00'
  interval: '0 */1 * * *'
  start_date: '2022-08-23T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: ["config_parser.py", "connector_tables_mapping.py", "flow_id_extractor.py", "topic_timestamp_checker.py", "utils.py"]
tags:
- de
- replicate
- alert
template_name: notebook
version: 1
