{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "if \"JUPYTERHUB_USER\" in os.environ:\n", "    cwd = \"/home/<USER>/airflow-de-dags/dags/de/replicate/alert/lag_alert\"\n", "    os.ch<PERSON>(cwd)\n", "else:\n", "    cwd = \"/usr/local/airflow/dags/repo/dags/de/replicate/alert/lag_alert\"\n", "    os.ch<PERSON>(cwd)\n", "    os.environ[\"VAULT_SERVICE_TOKEN\"] = \"\"\n", "    os.environ[\"VAULT_TOKEN\"] = \"\"\n", "    os.environ[\"VAULT_K8S_AUTH_ROLE\"] = \"dse-airflow-default-role\"\n", "    os.environ[\"VAULT_K8S_AUTH_MOUNT\"] = \"eks\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pytz\n", "from datetime import datetime, timedelta\n", "from typing import Dict, List, Optional, Tuple, Union\n", "from croniter import croniter\n", "import pencilbox as pb\n", "from airflow.models import Variable\n", "\n", "from connector_tables_mapping import get_connector_tables_df\n", "from config_parser import get_config_data_df\n", "from topic_timestamp_checker import get_topic_timestamps_df\n", "from flow_id_extractor import get_flow_data\n", "from utils import (\n", "    parse_cron,\n", "    get_next_run_time,\n", "    calculate_time_to_next_sync,\n", "    calculate_expected_lag,\n", "    get_next_run_time_ist,\n", "    get_database_table_from_topic,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["TEST_ENV = False\n", "BUFFER_TIME_MINUTES = 15\n", "if TEST_ENV:\n", "    DEFAULT_CONFIG_DIR = \"/home/<USER>/airflow-de-dags/daggers\"\n", "    SLACK_CHANNEL = \"bl-data-slack-testing\"\n", "else:\n", "    DEFAULT_CONFIG_DIR = \"/usr/local/airflow/dags/repo/daggers\"\n", "    SLACK_CHANNEL = \"bl-data-alerts-p1\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["connector_tables = get_connector_tables_df()\n", "if not connector_tables.empty:\n", "    print(f\"Found {len(connector_tables)} connectors\")\n", "else:\n", "    print(\"No connectors found or error connecting to API\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["connector_tables[:5]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Build a reverse mapping from table -> connector (schema.table -> connector_name)\n", "table_to_connector = {}\n", "\n", "for _, row in connector_tables.iterrows():\n", "    connector_name = row[\"connector_name\"]\n", "    for full_table in row[\"tables\"]:\n", "        parts = full_table.split(\".\")\n", "        if len(parts) == 3:\n", "            schema_table = f\"{parts[1]}.{parts[2]}\"\n", "        elif len(parts) == 2:\n", "            schema_table = f\"{parts[0]}.{parts[1]}\"\n", "        else:\n", "            continue\n", "        table_to_connector[schema_table] = connector_name"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["config_dir = os.environ.get(\"CONFIG_DIR\", DEFAULT_CONFIG_DIR)\n", "print(f\"Loading config data from: {config_dir}\")\n", "config_data = get_config_data_df(config_dir)\n", "if not config_data.empty:\n", "    print(f\"Found {len(config_data)} table configs\")\n", "else:\n", "    print(\"No config data found or error parsing config files\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["config_data[:5]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["srp_tables = []\n", "if not TEST_ENV:\n", "    srp_tables_var = Variable.get(\"srp_lag_check\", deserialize_json=True)\n", "    srp_tables = srp_tables_var[\"tables\"]\n", "else:\n", "    srp_tables = [\n", "        \"kitchen.activity_logs\",\n", "        \"iot.notification_log\",\n", "        \"warehouse_location.warehouse_aisle\",\n", "    ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["srp_tables"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["config_data[\"flow_id_base\"] = config_data[\"flow_id\"].str.replace(r\"\\.v\\d+$\", \"\", regex=True)\n", "config_data_srp = config_data[config_data[\"flow_id_base\"].isin(srp_tables)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["config_data_srp[:5]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_topics = config_data_srp[\"topic_name\"].tolist()\n", "kafka_timestamps_df = get_topic_timestamps_df(all_topics)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kafka_timestamps_df[:5]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["current_time = datetime.now(pytz.timezone(\"Asia/Kolkata\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["flow_ids = config_data_srp[\"flow_id\"].unique().tolist()\n", "\n", "result_data = []\n", "for flow_id in flow_ids:\n", "    flow_data = get_flow_data(flow_id)\n", "\n", "    # Get config data for this flow ID\n", "    config_row = config_data_srp[config_data_srp[\"flow_id\"] == flow_id]\n", "    if config_row.empty:\n", "        print(f\"No config data found for flow ID: {flow_id}\")\n", "        continue\n", "\n", "    cron = config_row[\"cron\"].iloc[0]\n", "    cron_frequency = parse_cron(cron)\n", "    expected_lag_minutes = calculate_expected_lag(cron_frequency, BUFFER_TIME_MINUTES)\n", "\n", "    topic_name = config_row[\"topic_name\"].values[0]\n", "    kafka_row = kafka_timestamps_df[kafka_timestamps_df[\"topic\"] == topic_name]\n", "\n", "    # Initialize default values\n", "    kafka_timestamp = None if kafka_row.empty else kafka_row[\"latest_ist_timestamp\"].iloc[0]\n", "    cdc_timestamp = None\n", "    lag_minutes = None\n", "    lag_hours = None\n", "    lag_status = \"UNKNOWN\"\n", "    lag_severity = \"UNKNOWN\"\n", "    next_run_time = None\n", "    minutes_to_next_sync = None\n", "    database = None\n", "    schema = None\n", "    table = None\n", "    connector_name = None\n", "    last_sync_minutes_ago = None\n", "\n", "    # Check if column exists before accessing it\n", "    if \"max_cdc_timestamp_read_ist\" in flow_data.columns:\n", "        cdc_timestamp = flow_data[\"max_cdc_timestamp_read_ist\"].iloc[0]\n", "\n", "    # Calculate lag metrics if timestamps are available\n", "    if kafka_timestamp and cdc_timestamp:\n", "        if isinstance(kafka_timestamp, str):\n", "            kafka_timestamp = pd.to_datetime(kafka_timestamp)\n", "        if isinstance(cdc_timestamp, str):\n", "            cdc_timestamp = pd.to_datetime(cdc_timestamp)\n", "\n", "        lag_minutes = (kafka_timestamp - cdc_timestamp).total_seconds() / 60\n", "        lag_status = \"OK\" if lag_minutes <= expected_lag_minutes else \"LAGGING\"\n", "\n", "        # Additional metrics\n", "        next_run_time = get_next_run_time_ist(cron)\n", "        minutes_to_next_sync = calculate_time_to_next_sync(next_run_time, current_time)\n", "        database, schema, table = get_database_table_from_topic(topic_name)\n", "        last_sync_minutes_ago = (\n", "            (current_time - cdc_timestamp).total_seconds() / 60 if cdc_timestamp else None\n", "        )\n", "        schema_table = f\"{schema}.{table}\"\n", "        connector_name = table_to_connector.get(schema_table)\n", "\n", "        # Calculate lag severity\n", "        if lag_status == \"LAGGING\":\n", "            lag_ratio = lag_minutes / expected_lag_minutes\n", "            if lag_ratio > 5:  # More than 5x expected lag\n", "                lag_severity = \"CRITICAL\"\n", "            elif lag_ratio > 1.5:  # More than 1.5x expected lag\n", "                lag_severity = \"HIGH\"\n", "            else:\n", "                lag_severity = \"MEDIUM\"\n", "        else:\n", "            lag_severity = \"LOW\"\n", "\n", "    # Get last_successful_timestamp safely\n", "    last_successful_timestamp = None\n", "    if \"last_successful_timestamp_ist\" in flow_data.columns:\n", "        last_successful_timestamp = flow_data[\"last_successful_timestamp_ist\"].iloc[0]\n", "\n", "    # Get last_hudi_commit_timestamp safely\n", "    last_hudi_commit_timestamp = None\n", "    if \"last_hudi_commit_timestamp_ist\" in flow_data.columns:\n", "        last_hudi_commit_timestamp = flow_data[\"last_hudi_commit_timestamp_ist\"].iloc[0]\n", "    # We don't need an else clause as last_hudi_commit_timestamp is already initialized to None\n", "\n", "    result_data.append(\n", "        {\n", "            \"flow_id\": flow_id,\n", "            \"topic\": topic_name,\n", "            \"database\": database,\n", "            \"schema\": schema,\n", "            \"table\": table,\n", "            \"connector_name\": connector_name,\n", "            \"cron\": cron,\n", "            \"cron_frequency_minutes\": cron_frequency,\n", "            \"expected_lag_minutes\": expected_lag_minutes,\n", "            \"kafka_timestamp\": kafka_timestamp,\n", "            \"cdc_timestamp\": cdc_timestamp,\n", "            \"lag_minutes\": round(lag_minutes, 2) if lag_minutes is not None else None,\n", "            \"lag_hours\": round(lag_minutes / 60, 2) if lag_minutes is not None else None,\n", "            \"status\": lag_status,\n", "            \"lag_severity\": lag_severity,\n", "            \"next_run_time\": next_run_time,\n", "            \"minutes_to_next_sync\": minutes_to_next_sync,\n", "            \"last_sync_minutes_ago\": (\n", "                round(last_sync_minutes_ago, 2) if last_sync_minutes_ago else None\n", "            ),\n", "            \"last_successful_timestamp\": last_successful_timestamp,\n", "            \"last_hudi_commit_timestamp\": last_hudi_commit_timestamp,\n", "        }\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result_data[:5]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result_df = pd.DataFrame(result_data)\n", "severity_order = {\"CRITICAL\": 0, \"HIGH\": 1, \"MEDIUM\": 2, \"LOW\": 3}\n", "result_df[\"severity_order\"] = result_df[\"lag_severity\"].map(severity_order)\n", "result_df = result_df.sort_values(by=[\"severity_order\", \"lag_minutes\"], ascending=[True, False])\n", "result_df = result_df.drop(\"severity_order\", axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result_df[:5]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from collections import defaultdict\n", "\n", "# Group rows by severity\n", "grouped = defaultdict(list)\n", "\n", "for _, row in result_df.iterrows():\n", "    grouped[row[\"lag_severity\"]].append(row)\n", "\n", "# Only include HIGH and CRITICAL severities\n", "filtered_severity_order = [\"CRITICAL\", \"HIGH\"]\n", "severity_emojis = {\"CRITICAL\": \"🔴\", \"HIGH\": \"🟠\"}\n", "\n", "\n", "# Prepare summary for main thread and details for thread\n", "summary_message = \"📊 *TABLE SYNC LAG ALERT SUMMARY*\\n\\n\"\n", "details_message = \"\"\n", "\n", "# Build summary and details messages\n", "for severity in filtered_severity_order:\n", "    rows = grouped.get(severity, [])\n", "    if not rows:\n", "        continue\n", "\n", "    emoji = severity_emojis.get(severity, \"⚠️\")\n", "\n", "    # Add to summary\n", "    summary_message += f\"{emoji} *{severity}:* {len(rows)} affected tables\\n\"\n", "\n", "    # Add table names to summary\n", "    for row in rows:\n", "        summary_message += f\"   • `{row['database']}.{row['schema']}.{row['table']}` ({row['lag_minutes']} min lag)\\n\"\n", "\n", "    summary_message += \"\\n\"\n", "\n", "    # Add to details\n", "    details_message += f\"\"\"\n", "━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n", "*TABLE SYNC LAG ALERT*\n", "{emoji} *Severity: {severity}*\n", "📊 *Total Affected Tables:* {len(rows)}\n", "━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n", "\"\"\"\n", "\n", "    for row in rows:\n", "        details_message += f\"\"\"\n", "━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n", "*Connector:* `{row['connector_name']}`\n", "*Table:* `{row['database']}.{row['schema']}.{row['table']}`\n", "*Topic:* `{row['topic']}`\n", "*Lag:* `{row['lag_minutes']} min` ({row['lag_hours']} hr)\n", "*Expected Lag:* `{row['expected_lag_minutes']} min`\n", "*Cron:* `{row['cron']}` (every {row['cron_frequency_minutes']} min)\n", "*Next Run:* `{row['next_run_time']}` (in *{row['minutes_to_next_sync']} min*)\n", "*Last Sync:* `{row['last_sync_minutes_ago']} min ago`\n", "*Last Successful:* `{row['last_successful_timestamp']}`\n", "*Status:* `{row['status']}`\n", "    \"\"\"\n", "    details_message += \"\\n\\n\"\n", "\n", "    summary_message += \"\\n// <!subteam^S089D0GQRCJ>\"\n", "\n", "    response = pb.send_slack_message(channel=SLACK_CHANNEL, text=summary_message.strip())\n", "    if response:\n", "        pb.send_slack_message(\n", "            channel=SLACK_CHANNEL,\n", "            text=details_message.strip(),\n", "            thread_ts=response[0].get(\"ts\", None),\n", "        )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}