#!/usr/bin/env python3
"""
Topic Timestamp Checker

This script checks the timestamp of the latest message in a Kafka topic.
It provides functions to get the latest message timestamp for a given topic
and convert it to proper datetime format.
"""

from confluent_kafka import Consumer, TopicPartition
from datetime import datetime
import json
import pytz
import pandas as pd
from typing import Dict, Optional, Tuple, List
import pencilbox as pb

BOOTSTRAP_SERVERS = pb.get_secret("data/services/source-replication/alert/kafka")["BOOTSTRAP_SERVERS"]


def get_topic_timestamp(topic: str) -> Tuple[Optional[datetime], Optional[datetime]]:
    """
    Get the timestamp of the latest message in a Kafka topic
    Args:
        topic (str): Name of the Kafka topic
    Returns:
        Tuple[Optional[datetime], Optional[datetime]]: 
            - UTC datetime of the latest message
            - IST datetime of the latest message
            - Both will be None if no message is found
    """
    consumer = Consumer({
        'bootstrap.servers': BOOTSTRAP_SERVERS,
        'group.id': 'srp-timestamp-checker',
        'auto.offset.reset': 'latest',
        'enable.auto.commit': False
    })
    
    try:
        # Get list of partitions for the topic
        metadata = consumer.list_topics(topic, timeout=10)
        
        if topic not in metadata.topics:
            print(f"Topic '{topic}' does not exist.")
            return None, None
            
        partitions = metadata.topics[topic].partitions
        tp_list = [TopicPartition(topic, p) for p in partitions]
        
        latest_timestamp = None
        # For each partition, get the latest offset and poll the last message
        for tp in tp_list:
            try:
                # Get (low, high) watermark offsets
                low, high = consumer.get_watermark_offsets(tp, timeout=5.0)
                last_offset = high - 1
                
                if last_offset < 0:
                    # print(f"Partition {tp.partition} is empty.")
                    continue
                
                # Seek to the latest message
                tp.offset = last_offset
                consumer.assign([tp])
                consumer.seek(tp)
                msg = consumer.poll(timeout=5.0)
                if msg is not None and not msg.error():
                    ts_type, kafka_ts = msg.timestamp()
                    msg_timestamp = datetime.utcfromtimestamp(kafka_ts / 1000)
                    if latest_timestamp is None or msg_timestamp > latest_timestamp:
                        latest_timestamp = msg_timestamp
            except Exception as e:
                print(f"Error processing partition {tp.partition}: {str(e)}")
        # Convert to IST
        ist_timestamp = None
        if latest_timestamp:
            utc_timezone = pytz.timezone('UTC')
            ist_timezone = pytz.timezone('Asia/Kolkata')
            utc_dt = utc_timezone.localize(latest_timestamp)
            ist_timestamp = utc_dt.astimezone(ist_timezone)
            return latest_timestamp, ist_timestamp
        return None, None  # Return tuple of None values if no valid timestamp found
    finally:
        consumer.close()

def get_topic_timestamps_df(topics: List[str]) -> pd.DataFrame:
    """
    Get latest timestamps for multiple topics and return as a DataFrame
    
    Args:
        topics (List[str]): List of Kafka topics
    Returns:
        pd.DataFrame: DataFrame with topic timestamps
    """
    results = []
    
    for topic in topics:
        # Skip None values in the topics list
        if topic is None:
            print(f"Skipping None topic in the list")
            continue
            
        utc_timestamp, ist_timestamp = get_topic_timestamp(topic)
        topic_parts = topic.split('.')
        topic_type = topic_parts[0] if len(topic_parts) > 0 else ""
        database = topic_parts[1] if len(topic_parts) > 1 else ""
        schema = topic_parts[2] if len(topic_parts) > 2 else ""
        table = topic_parts[3] if len(topic_parts) > 3 else ""
        results.append({
            'topic': topic,
            'topic_type': topic_type,
            'database': database,
            'schema': schema,
            'table': table,
            'latest_utc_timestamp': utc_timestamp,
            'latest_ist_timestamp': ist_timestamp
        })
    return pd.DataFrame(results)
