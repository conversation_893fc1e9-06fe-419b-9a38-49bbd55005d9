alert_configs:
  slack:
  - channel: bl-data-airflow-p2
dag_name: worst_trino_queries
dag_type: report
escalation_priority: low
execution_timeout: 60
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    node_type: spot
    load_type: tiny 
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
namespace: de
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ
path: de/maintenance/report/worst_trino_queries
paused: false
pool: de_pool
project_name: maintenance
schedule:
  end_date: '2026-08-16T00:00:00'
  interval: 15 0,12 * * *
  start_date: '2024-09-27T00:15:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 2
