alert_configs:
  slack:
  - channel: bl-data-alerts-p1
dag_name: update_de_etls_information_schemas
dag_type: workflow
escalation_priority: low
executor:
  config:
    node_type: spot
    load_type: tiny
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow-de/plugins
  type: kubernetes
namespace: de
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ
path: de/maintenance/workflow/update_de_etls_information_schemas
paused: false
project_name: maintenance
schedule:
  interval: 0 */4 * * *
  start_date: '2023-01-05T00:00:00'
  end_date: '2025-10-23T00:00:00'
schedule_type: fixed
sla: 300 minutes
support_files: []
tags:
- update-de-etls-information-schemas
template_name: notebook
version: 1
