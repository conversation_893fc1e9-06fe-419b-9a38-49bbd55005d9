{"cells": [{"cell_type": "code", "execution_count": null, "id": "02875e2d-e619-4a82-9a86-e09a38ca982d", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import sqlalchemy as sqla\n", "import time"]}, {"cell_type": "code", "execution_count": null, "id": "ebe4bc5c-17b8-4a66-9fe2-584bba2027b8", "metadata": {}, "outputs": [], "source": ["hive_con = pb.get_connection(\"[Prod] Hivemetastore\")"]}, {"cell_type": "code", "execution_count": null, "id": "68541c41-ecc0-499a-8745-a09878c882bd", "metadata": {}, "outputs": [], "source": ["hive_qry = \"\"\"\n", "SELECT 'blinkit' AS table_catalog, source.*, '' AS extra_info\n", "FROM (\n", "    WITH raw AS (\n", "        SELECT \n", "          DBS.NAME AS table_schema,\n", "          TBLS.TBL_NAME AS table_name,\n", "          CONVERT(FROM_BASE64(trim(TRAILING ' */' FROM substring(VIEW_ORIGINAL_TEXT,16))) USING utf8) AS view_structure,\n", "          tp.param_value AS t_type\n", "        FROM TBLS\n", "        LEFT JOIN DBS ON TBLS.DB_ID = DBS.DB_ID\n", "        LEFT JOIN TABLE_PARAMS tp ON (TBLS.TBL_ID = tp.TBL_ID AND tp.PARAM_KEY='table_type')\n", "        WHERE \n", "          VIEW_ORIGINAL_TEXT IS NOT NULL\n", "          AND DBS.NAME NOT LIKE 'lake_%%'\n", "          AND DBS.NAME NOT IN ('spectrum', 'pg_catalog', 'information_schema', 'presto_admin', \n", "                         'playground', 'interim', 'consumer', 'default', 'metrics', \n", "                         'lumber_events', 'admin', 'blinkit_jumbo2', 'jumbo2', 'lumber_events_raw',\n", "                         'misc')\n", "      ),\n", "      hive_tables AS (\n", "        SELECT \n", "           tp.param_value AS t_type,\n", "           DBS.NAME AS table_schema,\n", "           TBLS.TBL_NAME AS table_name,\n", "           COLUMNS_V2.COLUMN_NAME AS column_name,\n", "           COLUMNS_V2.TYPE_NAME AS data_type,\n", "           COLUMNS_V2.COMMENT AS comment,\n", "           FALSE AS is_partition_key,\n", "           COLUMNS_V2.INTEGER_IDX AS ordinal_position\n", "        FROM DBS\n", "        JOIN TBLS ON DBS.DB_ID = TBLS.DB_ID\n", "        JOIN SDS ON TBLS.SD_ID = SDS.SD_ID\n", "        JOIN COLUMNS_V2 ON COLUMNS_V2.CD_ID = SDS.CD_ID\n", "        LEFT JOIN TABLE_PARAMS tp ON (TBLS.TBL_ID = tp.TBL_ID AND tp.PARAM_KEY='table_type')\n", "        WHERE VIEW_ORIGINAL_TEXT IS NULL\n", "          AND DBS.NAME IN ('lake_events', 'lumber_events', 'lumber_events_raw', 'dynamodb', 'feature_store', 'feature_store_bistro')\n", "          AND TBLS.TBL_NAME NOT IN ('blinkit_jumbo_consumer_events_backfill', \n", "                              'jumbo_consumer_click_beta', 'jumbo_consumer_events_v2', \n", "                              'jumbo_consumer_impression_beta')\n", "        UNION ALL\n", "        SELECT \n", "            tp.param_value AS t_type,\n", "            DBS.NAME AS table_schema,\n", "            TBLS.TBL_NAME AS table_name,\n", "            PARTITION_KEYS.PKEY_NAME AS column_name,\n", "            PARTITION_KEYS.PKEY_TYPE AS data_type,\n", "            PARTITION_KEYS.PKEY_COMMENT AS comment,\n", "            TRUE AS is_partition_key,\n", "            PARTITION_KEYS.INTEGER_IDX AS ordinal_position\n", "        FROM DBS\n", "        JOIN TBLS ON DBS.DB_ID = TBLS.DB_ID\n", "        JOIN PARTITION_KEYS ON TBLS.TBL_ID = PARTITION_KEYS.TBL_ID\n", "        LEFT JOIN TABLE_PARAMS tp ON (TBLS.TBL_ID = tp.TBL_ID AND tp.PARAM_KEY='table_type')\n", "        WHERE VIEW_ORIGINAL_TEXT IS NULL\n", "        AND DBS.NAME IN ('lake_events', 'lumber_events', 'lumber_events_raw', 'dynamodb') \n", "        AND TBLS.TBL_NAME NOT IN ('blinkit_jumbo_consumer_events_backfill', \n", "                                      'jumbo_consumer_click_beta', 'jumbo_consumer_events_v2', \n", "                                      'jumbo_consumer_impression_beta')\n", "      )\n", "      SELECT \n", "          raw.t_type,\n", "          raw.table_schema,\n", "          raw.table_name,\n", "          col_data.column_name,\n", "          col_data.data_type,\n", "          '' AS comment,\n", "          FALSE AS is_partition_key,\n", "          0 AS ordinal_position\n", "      FROM raw\n", "      CROSS JOIN JSON_TABLE(json_extract(raw.view_structure, '$.columns'), '$[*]' COLUMNS (\n", "                          column_name varchar(767) PATH '$.name',\n", "                          data_type varchar(767) PATH '$.type'\n", "                          )\n", "                  ) col_data\n", "      UNION ALL\n", "      SELECT \n", "          t_type,\n", "          table_schema,\n", "          table_name,\n", "          column_name,\n", "          data_type,\n", "          comment,\n", "          is_partition_key,\n", "          ordinal_position\n", "      FROM hive_tables\n", ") source\n", "ORDER BY table_name, column_name;\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "92e5ec74-613b-4a9d-803a-1da0a929f995", "metadata": {}, "outputs": [], "source": ["blinkit_df = pd.read_sql(hive_qry, hive_con)"]}, {"cell_type": "code", "execution_count": null, "id": "5d75092c-322e-4b6e-97b4-533f97a8f0bb", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"de_etls\",\n", "    \"table_name\": \"information_schemas\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"table_catalog\", \"type\": \"VARCHAR\", \"description\": \"Catalog name\"},\n", "        {\"name\": \"t_type\", \"type\": \"VARCHAR\", \"description\": \"Table type\"},\n", "        {\"name\": \"table_schema\", \"type\": \"VARCHAR\", \"description\": \"Schema name\"},\n", "        {\"name\": \"table_name\", \"type\": \"VARCHAR\", \"description\": \"Table Name\"},\n", "        {\"name\": \"column_name\", \"type\": \"VARCHAR\", \"description\": \"Column Name\"},\n", "        {\"name\": \"data_type\", \"type\": \"VARCHAR\", \"description\": \"Column Datatype\"},\n", "        {\"name\": \"comment\", \"type\": \"VARCHAR\", \"description\": \"Column description\"},\n", "        {\n", "            \"name\": \"is_partition_key\",\n", "            \"type\": \"BOOLEAN\",\n", "            \"description\": \"Is column a partition key\",\n", "        },\n", "        {\n", "            \"name\": \"ordinal_position\",\n", "            \"type\": \"INTEGER\",\n", "            \"description\": \"Column position in table\",\n", "        },\n", "        {\"name\": \"extra_info\", \"type\": \"VARCHAR\", \"description\": \"Extra information\"},\n", "    ],\n", "    \"primary_key\": [\"table_schema\", \"table_name\", \"column_name\"],\n", "    \"partition_key\": [],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"Holds information schema for de_etls catalog\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "bbab696f-8164-4053-9ed7-ca1b3c1f8da1", "metadata": {}, "outputs": [], "source": ["pb.to_trino(blinkit_df, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "additional-cell", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}