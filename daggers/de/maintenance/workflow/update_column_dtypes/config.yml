alert_configs:
  slack:
  - channel: bl-data-alerts-p1
dag_name: update_column_dtypes
dag_type: workflow
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow-de/plugins
  type: kubernetes
namespace: de
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ
path: de/maintenance/workflow/update_column_dtypes
paused: false
project_name: maintenance
schedule:
  end_date: '2026-08-15T00:00:00'
  interval: 15 * * * *
  start_date: '2022-06-07T00:00:00'
schedule_type: fixed
sla: 60 minutes
support_files: []
tags:
- update-column-dtypes
template_name: notebook
version: 1
