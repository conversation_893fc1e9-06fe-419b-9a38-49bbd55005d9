alert_configs:
  slack:
  - channel: bl-data-alerts-p1
dag_name: user_device_first_and_last_seen
dag_type: etl
escalation_priority: low
executor:
  config:
    node_type: spot
    load_type: low
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow/plugins
  type: kubernetes
namespace: de
notebook:
  parameters: null
  retries: 3
  retry_delay_in_seconds: 15
  retry_exponential_backoff: true
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ
path: de/events/etl/user_device_first_and_last_seen
paused: false
project_name: events
schedule:
  end_date: '2026-08-16T00:00:00'
  interval: 45 * * * *
  start_date: '2024-03-14T00:00:00'
schedule_type: fixed
sla: 121 minutes
support_files: []
tags: []
template_name: notebook
version: 1
