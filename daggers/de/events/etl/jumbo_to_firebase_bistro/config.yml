alert_configs:
  slack:
  - channel: bl-data-airflow-alerts
dag_name: jumbo_to_firebase_bistro
dag_type: etl
escalation_priority: low
execution_timeout: 840
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    load_type: medium
    node_type: od
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    volume_mounts: []
  type: kubernetes
namespace: de
notebook:
  parameters: null
owner:
  email: <EMAIL>
  slack_id: U04UN5M83RQ
path: de/events/etl/jumbo_to_firebase_bistro
paused: false
pool: de_pool
project_name: events
schedule:
  end_date: '2025-10-20T00:00:00'
  interval: 30 */2 * * *
  start_date: '2024-12-26T00:00:00'
schedule_type: fixed
sla: 120 minutes
support_files: []
tags: []
template_name: notebook
version: 1
