alert_configs:
  slack:
  - channel: bl-data-alerts-p2
dag_name: consumer_events_metadata_updated
dag_type: report
escalation_priority: low
executor:
  config:
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    load_type: tiny
    node_type: spot
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    volume_mounts:
    - mountPath: /usr/local/airflow/plugins
      name: airflow-dags
      subPath: airflow-de/plugins
  type: kubernetes
namespace: de
notebook:
  parameters:
    ds: '{{ ds }}'
owner:
  email: <EMAIL>
  slack_id: S089D0GQRCJ
path: de/helpers/report/consumer_events_metadata_updated
paused: false
project_name: helpers
schedule:
  end_date: '2026-08-15T00:00:00'
  interval: 44 02 * * *
  start_date: '2024-07-09T00:00:00'
schedule_type: fixed
sla: 1 day
support_files: []
tags: []
template_name: notebook
version: 1
