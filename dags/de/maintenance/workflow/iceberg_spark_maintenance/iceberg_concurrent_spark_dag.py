# -*- coding: utf-8 -*-

import os
import logging
from contextlib import closing
from datetime import datetime, timedelta

import airflow
from airflow import DAG
from airflow.hooks.base_hook import BaseHook
from airflow.models import Variable
from kubernetes.client import models as k8s
from metrics_plugin import SparkKubernetesOperator, SparkKubernetesSensor
import pencilbox as pb

cwd = os.path.dirname(os.path.realpath(__file__))
kubernetes_cluster_id = "blinkit-analytics-eks"


def slack_failure_alert(context):
    task_instance = context.get("task_instance")
    dag_id = task_instance.dag_id
    run_id = context.get("run_id")
    task_id = task_instance.task_id
    log_url = task_instance.log_url.replace("http://", "https://")
    notebook_url = f"https://dse-commuter.grofer.io/view/{dag_id}/{run_id}"
    
    owner = {"email": "<EMAIL>", "slack_id": "S089D0GQRCJ"}
    slack_id = owner["slack_id"]
    _owner = {
        "s": "s" if len(slack_id.split(" ")) > 1 else "",
        "slack_id": "<@{}>".format(slack_id),
    }

    slack_failure_msg = f"""
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner{_owner["s"]}*: {_owner["slack_id"]}
        *Log Url*: {log_url}
        {f"*Notebook Url*: {notebook_url}" if notebook_url is not None else ""}
    """

    slack_alert_configs = [
        {"channel": "bl-data-airflow-alerts"},
    ]

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(channel=channel, text=slack_failure_msg)
        except Exception as e:
            logging.warning(e)
            message = (
                f"{_owner['slack_id']} `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(channel="bl-data-airflow-alerts", text=message)


def failure_alerts(context):
    slack_failure_alert(context)


args = {
    "owner": "<EMAIL>",
    "on_failure_callback": failure_alerts,
    "start_date": datetime.strptime("2025-07-31T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": datetime.strptime("2026-07-31T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "slack_id": "S089D0GQRCJ",
    "execution_timeout": timedelta(minutes=840),
    "pool": "default_spark_pool",
}

dag = DAG(
    dag_id="de_maintenance_workflow_iceberg_spark_maintenance_concurrent_spark_v1",
    default_args=args,
    schedule_interval="30 9 * * *",
    tags=["de", "misc", "workflow"],
    concurrency=12
)

env = {}
default_env = {
    "AIRFLOW_DAG_ID": "de_maintenance_workflow_iceberg_spark_maintenance_concurrent_spark_v1",
    "AIRFLOW_DAG_OWNER": "<EMAIL>",
    "AIRFLOW_DAG_RUN_ID": "{{ run_id }}",
    "PYTHONPATH": "/usr/local/airflow/dags/repo/dag_utils",
}
env.update(default_env)

allowed_schemas = pb.get_secret("dse/pencilbox/config")["allowed_schemas_write"].split(",")
special_cases = {"interim", "dwh", "dwh_hpl", "segment_computed_traits"}
excluded_schemas = {"finance_etls"}

schema_list = [
    schema for schema in allowed_schemas
    if (schema.endswith("_etls") or schema in special_cases) and schema not in excluded_schemas
]

base_actions = [
    {
        "name": "expireSnapshot",
        "expire_days": 7,
        "max_concurrent_job": 3,
    },
    {
        "name": "removeOrphanFiles",
        "max_concurrent_job": 3,
    }
]


actions = []

for schema in schema_list:
    for base_action in base_actions:
        action_name = base_action["name"]
        action_cfg = {
            "name": action_name,
            "schema": schema,
            "expire_days": base_action.get("expire_days", 30),
            "max_concurrent_job": base_action["max_concurrent_job"],
            "table_query": f"""
               SELECT table_name FROM blinkit_iceberg.de_etls.iceberg_metadata 
               WHERE table_name LIKE '{schema}.%' AND table_name NOT IN ('de_etls.cluster_terminator')
               """,
        }
        actions.append(action_cfg)

def create_maintenance_tasks(action_cfg):
    action = action_cfg["name"]
    schema = action_cfg["schema"]
    suffix = f"{action.lower()}_{schema}"    
    spark_args = {
        "etl_name": "de_maintenance_workflow_iceberg_spark_maintenance_concurrent_spark_v1",
        "driver_file": "s3://blinkit-eks-spark/{environment}/driver_files/run_notebook.py",
        "job_args": [
            "s3://grofers-prod-dse-sgp/repo/{airflow}/dags/de/maintenance/workflow/iceberg_spark_maintenance/notebook.ipynb",
            "s3://grofers-prod-dse-sgp/airflow/dag_runs/de_maintenance_workflow_iceberg_spark_maintenance_concurrent_spark_v1/{{run_id}}/{schema}/{action}_notebook.ipynb".format(schema=schema, action=action),
            "--parameters",
            {
                "action": action,
                "max_concurrent_job": action_cfg["max_concurrent_job"],
                "expire_days": action_cfg.get("expire_days", 7),
                "table_query": action_cfg["table_query"],
                "suffix":suffix,
                "schema":schema
            },
        ],
        "load_type": "medium",
        "node_type": "spot",
        "python_packages": ["slack_sdk"],
        "tenant": "blinkit",
        "env": Variable.get("env", "stage"),
        "spark_conf": {"spark.scheduler.mode": "FAIR","spark.driver.memory": "24g","spark.sql.adaptive.enabled":"true","spark.rpc.message.maxSize":"1024","spark.network.timeout": "1800s"},
    }
    submit_task = SparkKubernetesOperator(
        dag=dag,
        task_id=f"submit_{suffix}",
        log_events_on_failure=True,
        kubernetes_conn_id=kubernetes_cluster_id,
        delete_on_termination=True,
        base_container_name="spark-kubernetes-driver",
        get_logs=True,
        step_command=spark_args,
        queue="celery"
    )
    poll_task = SparkKubernetesSensor(
        dag=dag,
        task_id=f"poll_{suffix}",
        application_name=f"{{{{ task_instance.xcom_pull(task_ids='submit_{suffix}', key='spark_application_name') }}}}",
        namespace=f"{{{{ task_instance.xcom_pull(task_ids='submit_{suffix}', key='spark_application_namespace') }}}}",
        application_file=f"{{{{ task_instance.xcom_pull(task_ids='submit_{suffix}', key='spark_application_template_body') }}}}",
        kubernetes_conn_id=kubernetes_cluster_id,
        poke_interval=2,
        attach_log=True,
        queue="celery",
    )
    submit_task >> poll_task
    return submit_task, poll_task


task_pairs = [create_maintenance_tasks(cfg) for cfg in actions]


if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
