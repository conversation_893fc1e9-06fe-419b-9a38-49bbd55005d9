import os
import json
import logging
from datetime import datetime, timedelta
import pandas as pd
from typing import List

from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from airflow.providers.amazon.aws.operators.emr_add_steps import EmrAddStepsOperator
from airflow.providers.amazon.aws.operators.emr_create_job_flow import EmrCreate<PERSON><PERSON><PERSON><PERSON>Operator
from airflow.providers.amazon.aws.operators.emr_terminate_job_flow import EmrTerminateJ<PERSON><PERSON>lowOperator
from airflow.models import Variable
from metrics_plugin import EmrStateSensorAsync

import pencilbox as pb

cwd = os.path.dirname(os.path.realpath(__file__))


def slack_failure_alert(context):
    task_instance = context.get("task_instance")
    dag_id = task_instance.dag_id
    run_id = context.get("run_id")
    task_id = task_instance.task_id
    log_url = task_instance.log_url.replace("http://", "https://")
    notebook_url = (
        f"https://dse-commuter.grofer.io/view/{dag_id}/{run_id}/{task_id}.ipynb"
        if task_instance.operator == "PapermillOperator"
        else None
    )

    owner = {"email": "<EMAIL>", "slack_id": "S089D0GQRCJ"}
    slack_id = owner["slack_id"]
    _owner = {
        "s": "s" if len(slack_id.split(" ")) > 1 else "",
        "slack_id": "<!subteam^{}>".format(slack_id),
    }

    slack_failure_msg = f"""
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner{_owner["s"]}*: {_owner["slack_id"]}
        *Log Url*: {log_url}
        {f"*Notebook Url*: {notebook_url}" if notebook_url is not None else ""}
    """

    slack_alert_configs = [
        {"channel": "bl-data-alerts-p1"},
    ]

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(channel=channel, text=slack_failure_msg, source_info=False)
        except Exception as e:
            logging.warning(e)
            message = (
                f"{_owner['slack_id']} `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(channel="bl-data-airflow-alerts", text=message, source_info=False)


def failure_alerts(context):
    slack_failure_alert(context)


MAINTENANCE_VARIABLE = "iceberg_delete_file_maintenance_tables"


def check_n_update_maintenance_tables(**kwargs):
    trino_con = pb.get_connection("[Warehouse] Trino")
    sql = """SELECT table_name,
       CAST(total_data_files AS INT) AS data_files,
       CAST(total_delete_files AS INT) AS delete_files
    FROM blinkit_iceberg.de_etls.iceberg_metadata
    WHERE CAST(total_delete_files AS INT) > 200
        AND (CAST(total_delete_files AS INT) >= 5000
            OR CAST(total_delete_files AS INT)/CAST(total_data_files AS INT) >= 5)
    ORDER BY delete_files desc
    """
    df = pd.read_sql(sql, trino_con)
    final_dict = dict()
    for _, row in df.iterrows():
        final_dict[row["table_name"]] = "high" if row["delete_files"] >= 10000 else "medium"
    Variable.set(MAINTENANCE_VARIABLE, json.dumps(final_dict))


def multi_upload_to_s3(object_list: List[List[str]]) -> None:
    for filename, key, bucket_name in object_list:
        pb.to_s3(filename, bucket_name, key)


EMR_SETTINGS_OVERRIDE = {
    "Name": "iceberg_spark_maintenance_v1",
    "LogUri": "s3://grofers-prod-dse-sgp/elasticmapreduce/",
    "ReleaseLabel": "emr-7.5.0",
    "Applications": [{"Name": "Spark"}],
    "Instances": {
        "KeepJobFlowAliveWhenNoSteps": True,
        "Ec2KeyName": "dse-emr",
        "ServiceAccessSecurityGroup": "sg-64858200",
        "Ec2SubnetIds": [
            "subnet-09e5bab68a3640d38",
            "subnet-fe54ac88",
            "subnet-e00fc184",
        ],
        "EmrManagedSlaveSecurityGroup": "sg-587afc3c",
        "EmrManagedMasterSecurityGroup": "sg-587afc3c",
        "InstanceFleets": [
            {
                "Name": "MasterFleet",
                "InstanceFleetType": "MASTER",
                "TargetOnDemandCapacity": 1,
                "TargetSpotCapacity": 0,
                "InstanceTypeConfigs": [
                    {
                        "InstanceType": "m6g.2xlarge",
                        "WeightedCapacity": 1,
                        "BidPriceAsPercentageOfOnDemandPrice": 100,
                        "EbsConfiguration": {
                            "EbsBlockDeviceConfigs": [
                                {
                                    "VolumeSpecification": {
                                        "VolumeType": "gp3",
                                        "Iops": 3000,
                                        "SizeInGB": 32
                                    }
                                }
                            ]
                        },
                    },
                    {
                        "InstanceType": "m7g.2xlarge",
                        "WeightedCapacity": 1,
                        "BidPriceAsPercentageOfOnDemandPrice": 100,
                        "EbsConfiguration": {
                            "EbsBlockDeviceConfigs": [
                                {
                                    "VolumeSpecification": {
                                        "VolumeType": "gp3",
                                        "Iops": 3000,
                                        "SizeInGB": 32
                                    }
                                }
                            ]
                        },
                    },
                ],
                "LaunchSpecifications": {
                    "OnDemandSpecification": {"AllocationStrategy": "lowest-price"}
                },
            },
            {
                "Name": "CoreFleet",
                "InstanceFleetType": "CORE",
                "TargetOnDemandCapacity": 2,  # Increased from 1 to 2 core instances
                "TargetSpotCapacity": 0,
                "InstanceTypeConfigs": [
                    {
                        "InstanceType": "r6g.4xlarge",  # Upgraded from 2xlarge to 4xlarge
                        "EbsConfiguration": {
                            "EbsBlockDeviceConfigs": [
                                {
                                    "VolumeSpecification": {
                                        "VolumeType": "gp3",
                                        "Iops": 3000,
                                        "SizeInGB": 100
                                    }
                                }
                            ]
                        },
                    },
                    {
                        "InstanceType": "r7g.4xlarge",  # Upgraded from 2xlarge to 4xlarge
                        "EbsConfiguration": {
                            "EbsBlockDeviceConfigs": [
                                {
                                    "VolumeSpecification": {
                                        "VolumeType": "gp3",
                                        "Iops": 3000,
                                        "SizeInGB": 100
                                    }
                                }
                            ]
                        },
                    },
                ],
                "LaunchSpecifications": {
                    "OnDemandSpecification": {"AllocationStrategy": "lowest-price"}
                },
            },
            {
                "Name": "TaskFleet",
                "InstanceFleetType": "TASK",
                "TargetSpotCapacity": 25,  # Increased from 15 to 25 spot instances
                "InstanceTypeConfigs": [
                    {
                        "InstanceType": "r6g.2xlarge",
                        "EbsConfiguration": {
                            "EbsBlockDeviceConfigs": [
                                {
                                    "VolumeSpecification": {
                                        "VolumeType": "gp3",
                                        "Iops": 3000,
                                        "SizeInGB": 100
                                    }
                                }
                            ]
                        },
                    },
                    {
                        "InstanceType": "r7g.2xlarge",
                        "EbsConfiguration": {
                            "EbsBlockDeviceConfigs": [
                                {
                                    "VolumeSpecification": {
                                        "VolumeType": "gp3",
                                        "Iops": 3000,
                                        "SizeInGB": 100
                                    }
                                }
                            ]
                        },
                    },
                    {
                        "InstanceType": "m6g.4xlarge",
                        "EbsConfiguration": {
                            "EbsBlockDeviceConfigs": [
                                {
                                    "VolumeSpecification": {
                                        "VolumeType": "gp3",
                                        "Iops": 3000,
                                        "SizeInGB": 100
                                    }
                                }
                            ]
                        },
                    },
                    {
                        "InstanceType": "m7g.4xlarge",
                        "EbsConfiguration": {
                            "EbsBlockDeviceConfigs": [
                                {
                                    "VolumeSpecification": {
                                        "VolumeType": "gp3",
                                        "Iops": 3000,
                                        "SizeInGB": 100
                                    }
                                }
                            ]
                        },
                    },
                    {
                        "InstanceType": "r6gd.2xlarge",
                        "EbsConfiguration": {},
                    },
                    {
                        "InstanceType": "m6gd.4xlarge",
                        "EbsConfiguration": {},
                    },
                    {
                        "InstanceType": "r7gd.2xlarge",
                        "EbsConfiguration": {},
                    },
                ],
                "LaunchSpecifications": {
                    "SpotSpecification": {
                        "TimeoutDurationMinutes": 10,
                        "TimeoutAction": "SWITCH_TO_ON_DEMAND",
                        "AllocationStrategy": "capacity-optimized",
                    }
                },
            },
        ],
    },
    "ManagedScalingPolicy": {
        'ComputeLimits': {
            'UnitType': 'InstanceFleetUnits',
            'MinimumCapacityUnits': 3,  # Increased from 1 to 3
            'MaximumCapacityUnits': 30,  # Increased from 10 to 30
            'MaximumOnDemandCapacityUnits': 3,  # Increased from 1 to 3
            'MaximumCoreCapacityUnits': 2  # Increased from 1 to 2
        }
    },
    "StepConcurrencyLevel": 12,
    "Configurations": [
        {
            "Classification": "spark-hive-site",
            "Properties": {
                "hive.exec.dynamic.partition.mode": "nonstrict",
                "hive.metastore.uris": "thrift://vpce-06233f70360f757f8-enprgsf3.vpce-svc-0dcaa5761ef958410.ap-southeast-1.vpce.amazonaws.com:9083"
            }
        },
        {
            "Classification": "hive-site",
            "Properties": {
                "hive.execution.engine": "tez"
            }
        },
        {
            "Classification": "hiveserver2-site",
            "Properties": {
                "hive.compute.query.using.stats": "false",
                "hive.exec.dynamic.partition.mode": "nonstrict",
                "hive.exec.max.dynamic.partitions": "100000",
                "hive.exec.max.dynamic.partitions.pernode": "100000",
                "hive.execution.engine": "tez",
                "hive.groupby.orderby.position.alias": "true"
            }
        }
    ],
    "BootstrapActions": [
        {
            "Name": "kafka-libs",
            "ScriptBootstrapAction": {
                "Path": "s3://prod-dse-srp-configs/scripts/emr/bootstrap_spark_packages.sh"
            },
        },
    ],
    "VisibleToAllUsers": True,
    "JobFlowRole": "application.zomato.trino-emr",
    "ServiceRole": "EMR_DefaultRole",
    "Tags": [
        {"Key": "Dag", "Value": "de_events_jumbo_consumer_hourly_v1"},
        {"Key": "Name", "Value": "de_events_jumbo_consumer_hourly_v1"},
        {"Key": "grofers.io/service", "Value": "consumer-events"},
        {"Key": "grofers.io/component", "Value": "consumer-events-spark-cluster"},
        {"Key": "grofers.io/component-role", "Value": "data-processing"},
        {"Key": "grofers.io/business-service", "Value": "data-platform"},
        {"Key": "grofers.io/team", "Value": "data-engineering"},
        {"Key": "grofers.io/tribe", "Value": "data"},
        {"Key": "cost:namespace", "Value": "data"},
        {"Key": "cost:application", "Value": "emr"},
    ],
}


def fetch_spark_steps(etl_name, arguments=[]):
    source_python_base_s3_path = "s3://prod-dse-srp-configs/config/spark/iceberg_spark_maintenance"
    spark_submit = (
        "spark-submit --deploy-mode cluster --master yarn"
        " --conf spark.driver.memory=12g"  
        " --conf spark.driver.cores=4"     
        " --conf spark.executor.memory=14g"  
        " --conf spark.executor.cores=4"  
        " --conf spark.dynamicAllocation.enabled=true"
        " --conf spark.yarn.am.waitTime=1800s"
        " --conf spark.yarn.heterogeneousExecutors.enabled=false"
        " --conf spark.sql.iceberg.handle-timestamp-without-timezone=true"
        " --conf spark.sql.extensions=org.apache.iceberg.spark.extensions.IcebergSparkSessionExtensions"
        " --conf spark.sql.catalog.spark_catalog=org.apache.iceberg.spark.SparkSessionCatalog"
        " --conf spark.sql.catalog.spark_catalog.type=hive"
        " --jars s3://prod-dse-srp-configs/jars/spark/iceberg/iceberg-spark-runtime-3.5_2.12-1.7.0.jar"
        f" --py-files {source_python_base_s3_path}/utils.py"
        f" {source_python_base_s3_path}/iceberg_table_spark_sql_procedure.py"
        f" {' '.join(arguments)}"
    )
    return [
        {
            "Name": etl_name,
            "ActionOnFailure": "CONTINUE",
            "HadoopJarStep": {
                "Jar": "command-runner.jar",
                "Args": spark_submit.split(" "),
            },
        }
    ]


args = {
    "owner": "<EMAIL>",
    "on_failure_callback": failure_alerts,
    "start_date": datetime.strptime("2024-11-26T11:40:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": datetime.strptime("2026-06-15T23:00:00", "%Y-%m-%dT%H:%M:%S"),
    "slack_id": "S089D0GQRCJ",
}

dag = DAG(
    dag_id="de_maintenance_workflow_iceberg_spark_maintenance_v1",
    default_args=args,
    schedule_interval="50 */6 * * *",
)

env = {}
default_env = {
    "AIRFLOW_DAG_ID": "de_maintenance_workflow_iceberg_spark_maintenance_v1",
    "AIRFLOW_DAG_OWNER": "<EMAIL>",
}
env.update(default_env)

task_upload_spark_source_s3 = PythonOperator(
    task_id='upload_spark_source_to_s3',
    python_callable=multi_upload_to_s3,
    op_kwargs={'object_list': [
        [
            "/usr/local/airflow/dags/repo/dags/de/maintenance/workflow/iceberg_spark_maintenance/iceberg_table_spark_sql_procedure.py",
            "config/spark/iceberg_spark_maintenance/iceberg_table_spark_sql_procedure.py",
            "prod-dse-srp-configs"
        ],
        [
            "/usr/local/airflow/dags/repo/dags/de/maintenance/workflow/iceberg_spark_maintenance/utils.py",
            "config/spark/iceberg_spark_maintenance/utils.py",
            "prod-dse-srp-configs"
        ]
    ]},
    dag=dag,
    retries=2,
    queue="celery",
)


cluster_create_task = EmrCreateJobFlowOperator(
    task_id="create_emr_cluster",
    job_flow_overrides=EMR_SETTINGS_OVERRIDE,
    aws_conn_id="aws_default",
    emr_conn_id="emr_default",
    dag=dag,
    retries=3,
    queue="celery",
)

cluster_terminate_task = EmrTerminateJobFlowOperator(
    task_id="terminate_emr_cluster",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster', key='return_value') }}",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    queue="celery",
    trigger_rule="all_done",
)

check_n_update_maintenance_tables_task = PythonOperator(
    task_id="create_entity_context_pairs_notebook",
    python_callable=check_n_update_maintenance_tables,
    dag=dag,
    queue="celery",
)

check_n_update_maintenance_tables_task >> task_upload_spark_source_s3 >> cluster_create_task

iceberg_tables = Variable.get(MAINTENANCE_VARIABLE, deserialize_json=True)

for table_name, load_type in iceberg_tables.items():
    job_task_id = f"maintain_{table_name}"

    job_task = EmrAddStepsOperator(
        task_id=job_task_id,
        job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster', key='return_value') }}",
        dag=dag,
        retries=3,
        aws_conn_id="aws_default",
        steps=fetch_spark_steps(
            etl_name=job_task_id,
            arguments=["compactDeleteFiles", "--table_name", f"blinkit.{table_name}",
                       "--load_type", load_type, "--target_size_mb", "256"]
        ),
        queue="celery",
    )

    job_sensor = EmrStateSensorAsync(
        task_id=f"{job_task_id}_sensor",
        step_id="{{ task_instance.xcom_pull(task_ids='" + job_task_id + "', key='return_value')[0] }}",
        cluster_id="{{ task_instance.xcom_pull('create_emr_cluster', key='return_value') }}",
        db_conn_id="nessie_db",
        aws_conn_id="aws_default",
        dag=dag,
        db_poke_interval=60,
        api_poke_interval=300,
        event_sensor_timeout=1800,
        poke_method="events",
        retries=2,
        retry_delay=timedelta(seconds=30),
        retry_exponential_backoff=False,
        queue="celery",
    )

    cluster_create_task >> job_task >> job_sensor >> cluster_terminate_task


if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = timedelta(hours=6)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
