{"cells": [{"cell_type": "code", "execution_count": 1, "id": "8c9e3692-cfc8-40bf-bbbc-fdd9aa2a5a96", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": ["parameters"]}, "outputs": [], "source": ["action=\"expireSnapshot\"\n", "table_query= \"select table_name from blinkit_iceberg.de_etls.iceberg_metadata\"\n", "max_concurrent_job=10\n", "expire_days=7\n", "suffix=\"Maintenance\"\n", "schema=\"interim\""]}, {"cell_type": "code", "execution_count": null, "id": "589c75ed-dc84-4a03-bbf5-3bb2684cf7f6", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["from pyspark.sql import SparkSession\n", "spark = SparkSession.builder.appName(f\"de_maintenance_workflow_iceberg_spark_maintenance_v1_{suffix}\").getOrCreate()\n", "max_concurrency = spark.conf.get(\"spark.dynamicAllocation.maxExecutors\", \"not set\")"]}, {"cell_type": "code", "execution_count": null, "id": "2c9f9623-fd59-416a-a350-6ee423708924", "metadata": {}, "outputs": [], "source": ["spark"]}, {"cell_type": "code", "execution_count": null, "id": "374cfab8-11a1-4859-8d17-4754b3fd1f04", "metadata": {}, "outputs": [], "source": ["import time\n", "from concurrent.futures import ThreadPoolExecutor,as_completed\n", "from datetime import datetime, timedelta\n", "from slack_sdk import WebClient\n", "from slack_sdk.errors import SlackApiError\n", "import zspark\n", "import traceback"]}, {"cell_type": "code", "execution_count": null, "id": "7bf9b509-90f0-4035-aa83-222ef6d51067", "metadata": {}, "outputs": [], "source": ["_client = {\"slack\": {}}\n", "SLACK_CHANNEL = \"bl-data-slack-testing\"\n", "SLACK_BOT = \"bl-analytics-bot\"\n", "SLACK_TOKEN_VAULT_PATH = \"dse/services/slack/tokens\"\n", "\n", "def get_slack_client(user):\n", "    def _slack_client():\n", "        slack_token = zspark.get_secret(SLACK_TOKEN_VAULT_PATH)[user]\n", "        client = WebClient(token=slack_token)\n", "        _client[\"slack\"][user] = client\n", "        return client\n", "\n", "    try:\n", "        client = _client[\"slack\"][user]\n", "        if not client.auth_test().get(\"ok\"):\n", "            print(\"[Slack] Existing client token invalid, re-fetching\")\n", "            client = _slack_client()\n", "    except (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) as e:\n", "        print(f\"[Slack] No cached client or error: {e}, fetching new one\")\n", "        client = _slack_client()\n", "    return client\n", "    \n", "def send_alert(tables: list, action: str):\n", "    \"\"\"\n", "    Sends a Slack alert for permanent failure of a maintenance action on an Iceberg tables.\n", "    \"\"\"\n", "    client = get_slack_client(SLACK_BOT)\n", "    \n", "    alert_text = (\n", "        f\":x: *{action.upper()}* permanently failed on {len(tables)} tables in {schema}:\\n\"\n", "        + \"\\n\".join(tables)\n", "    )\n", "\n", "    try:\n", "        client.chat_postMessage(channel=SLACK_CHANNEL, text=alert_text)\n", "    except SlackApiError as e:\n", "        print(f\"[<PERSON>lackE<PERSON><PERSON>] Could not send Slack alert: {e.response['error']}\")"]}, {"cell_type": "code", "execution_count": null, "id": "62f65304-0177-4d72-8bcc-ae7f9f46abd1", "metadata": {}, "outputs": [], "source": ["def get_expire_snapshot_sql_procedure(CATALOG_NAME: str, schema_name: str, table_name: str, expiry_timestamp_str: str, concurrency: int):\n", "    return f\"\"\"CALL {CATALOG_NAME}.system.expire_snapshots (\n", "        table => '{CATALOG_NAME}.{schema_name}.{table_name}'\n", "        ,older_than => TIMESTAMP '{expiry_timestamp_str}'\n", "        ,max_concurrent_deletes => {concurrency})\"\"\"\n", "\n", "def get_remove_orphan_files_sql_procedure(CATALOG_NAME: str, schema_name: str, table_name: str, concurrency: int = 5):\n", "    return f\"\"\"CALL {CATALOG_NAME}.system.remove_orphan_files (\n", "        table => '{CATALOG_NAME}.{schema_name}.{table_name}'\n", "        ,max_concurrent_deletes => {concurrency})\"\"\"\n", "\n", "def get_expire_timestamp_from_days(days: int = 7):\n", "    return (datetime.utcnow() - timedelta(days=days)).strftime(\"%Y-%m-%d %H:%M:%S\")"]}, {"cell_type": "code", "execution_count": null, "id": "a7d0ca16-a9a9-45d2-972b-d532990a689a", "metadata": {}, "outputs": [], "source": ["FAILED_TABLES = []\n", "RETRY_LIMIT = 3\n", "RETRY_DELAY = 5  # seconds\n", "\n", "ACTION = action \n", "TABLE_QUERY = table_query\n", "MAX_CONCURRENCY = max_concurrency \n", "EXPIRE_TIMESTAMP = get_expire_timestamp_from_days(expire_days)\n", "\n", "\n", "# --- Thread Task ---\n", "def run_action_on_table(table_name: str, action: str, spark: SparkSession):\n", "    catalog, schema, table = table_name.lower().split(\".\")\n", "    retries = 0\n", "    success = False\n", "\n", "    while retries < RETRY_LIMIT and not success:\n", "        try:\n", "            start = time.time()\n", "            if action == \"expireSnapshot\":\n", "                sql = get_expire_snapshot_sql_procedure(catalog, schema, table, EXPIRE_TIMESTAMP, MAX_CONCURRENCY)\n", "            elif action == \"removeOrphanFiles\":\n", "                sql = get_remove_orphan_files_sql_procedure(catalog, schema, table, MAX_CONCURRENCY)\n", "            else:\n", "                raise ValueError(\"Invalid action\")\n", "            \n", "            df = spark.sql(sql)\n", "            end = time.time()\n", "            if action == \"expireSnapshot\":\n", "                row = df.first()\n", "                if row:\n", "                    print(\n", "                        f\"[SUCCESS] {action} on {table_name} - \"\n", "                        f\"data={row['deleted_data_files_count']}, \"\n", "                        f\"pos={row['deleted_position_delete_files_count']}, \"\n", "                        f\"eq={row['deleted_equality_delete_files_count']}, \"\n", "                        f\"manifest={row['deleted_manifest_files_count']}, \"\n", "                        f\"mlist={row['deleted_manifest_lists_count']}, \"\n", "                        f\"stats={row['deleted_statistics_files_count']} \"\n", "                        f\"(in {end - start:.2f}s)\"\n", "                    )\n", "                else:\n", "                    print(f\"[SUCCESS] {action} on {table_name} - No deletions (in {end - start:.2f}s)\")\n", "\n", "            elif action == \"removeOrphanFiles\":\n", "                deleted_rows = df.count()\n", "                print(f\"[SUCCESS] {action} on {table_name} - {deleted_rows} orphan file entries deleted (in {end - start:.2f}s)\")\n", "\n", "            success = True\n", "\n", "        except Exception as e:\n", "            print(f\"[FAILED] {action} on {table_name} (attempt {retries+1}) - {e}\")\n", "            last_trace = traceback.format_exc()\n", "            print(last_trace)\n", "            retries += 1\n", "            time.sleep(RETRY_DELAY)\n", "\n", "    if not success:\n", "        print(f\"[ERROR] {action} failed permanently on {table_name}\")\n", "        FAILED_TABLES.append(table_name)\n", "\n", "# --- Main ---\n", "def main():\n", "    df = spark.sql(TABLE_QUERY)\n", "    tables = [f\"blinkit_iceberg.{row.table_name}\" for row in df.collect()]\n", "    if not tables:\n", "        print(\"No tables found.\")\n", "        return\n", "    max_workers = max_concurrent_job\n", "    with ThreadPoolExecutor(max_workers=max_workers) as pool:\n", "        futures = [pool.submit(run_action_on_table, table, ACTION, spark) for table in tables]\n", "        for future in as_completed(futures):\n", "            try:\n", "                future.result()\n", "            except Exception as exc:\n", "                print(f\"[THREAD FAILED] {exc}\")\n", "                \n", "    if FAILED_TABLES:\n", "        try:\n", "            send_alert(FAILED_TABLES, ACTION)\n", "            print(\"[<PERSON><PERSON><PERSON>] <PERSON><PERSON> sent\")\n", "        except Exception as e:\n", "            print(f\"[<PERSON>lack<PERSON><PERSON><PERSON>] Failed to send alert : {e}\")\n", "    \n", "    print(f\"\\n✅ Completed. Processed {len(tables)} tables. {len(FAILED_TABLES)} failures.\")\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "code", "execution_count": null, "id": "6310b525-e1e0-44cb-858a-54beacbba058", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}}, "nbformat": 4, "nbformat_minor": 5}