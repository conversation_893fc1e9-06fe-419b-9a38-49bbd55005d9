{"cells": [{"cell_type": "code", "execution_count": null, "id": "f1004b95-9041-41b6-96e8-2c646acc5d3f", "metadata": {}, "outputs": [], "source": ["import time\n", "import pencilbox as pb\n", "import os, sys\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime"]}, {"cell_type": "code", "execution_count": null, "id": "599aeaff-289d-4d55-8abb-64d6bf250842", "metadata": {}, "outputs": [], "source": ["trino_con = pb.get_connection(\"[Warehouse] Trino\")\n", "data_catalog = 'blinkit_iceberg'"]}, {"cell_type": "code", "execution_count": null, "id": "0bd30a84-3240-4cf8-a7f4-e00f9a29fcfd", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["cwd = \"./\"\n", "sidefile_dir_name = \"\""]}, {"cell_type": "code", "execution_count": null, "id": "d55aa153-734b-4b07-ace9-9f99abf29912", "metadata": {}, "outputs": [], "source": ["sidefile_path = os.path.join(cwd, sidefile_dir_name)\n", "sys.path.append(sidefile_path)\n", "sys.path.append(cwd)\n", "\n", "print(sidefile_path)\n", "print(cwd)"]}, {"cell_type": "code", "execution_count": null, "id": "03d0f22f-ca32-4fb1-9115-dd6c4273d9fc", "metadata": {}, "outputs": [], "source": ["from column_definitions import (\n", "    festival_table_description,\n", "    imd_festival_column_dtypes,\n", "    festival_column_dtypes\n", ")"]}, {"cell_type": "markdown", "id": "50989914-8fba-4174-adff-dab3abd85074", "metadata": {}, "source": ["### Festival Mapping intermediate Table"]}, {"cell_type": "code", "execution_count": null, "id": "48a28826-686a-4d44-8aec-c5644c3bbc20", "metadata": {}, "outputs": [], "source": ["sheet_id = \"1AdyZW3npZbK_CAafKg-fDxAIaAOYLvNp7KO37ops30w\"\n", "sheet_name = \"Current Festivals\"\n", "festival_df = pb.from_sheets(sheet_id, sheet_name)\n", "\n", "# festival_df = pd.read_csv('Festival Ptype Mapping  - Festivals.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "8904e9cb-5856-4c5a-89e3-edc7dde5cfcd", "metadata": {}, "outputs": [], "source": ["festival_df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "a10c6a79-9993-40b2-b984-d7db614b392f", "metadata": {}, "outputs": [], "source": ["festival_df.columns = festival_df.columns.str.strip().str.lower().str.replace(\"[-, \\, /, ?, #, @, (, ), \\n, .]\", \"_\")\n", "festival_df.drop(labels=[\"\"], axis=1, inplace=True, errors='ignore')"]}, {"cell_type": "code", "execution_count": null, "id": "afc5fa89-9a96-4740-8fc6-a96733586215", "metadata": {}, "outputs": [], "source": ["festival_df['snapshot_timestamp'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "festival_df['snapshot_timestamp'] = pd.to_datetime(festival_df['snapshot_timestamp'])"]}, {"cell_type": "code", "execution_count": null, "id": "67e078b0-3599-4657-8b70-a064873e898e", "metadata": {}, "outputs": [], "source": ["festival_df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "e7b401e7-acdb-4a22-817d-8e353912f5fc", "metadata": {}, "outputs": [], "source": ["trino_kwargs = {\n", "    \"schema_name\": 'interim',\n", "    \"table_name\": \"imd_festival_mapping\",\n", "    \"primary_key\": [],\n", "    \"load_type\": \"truncate\",\n", "    \"force_upsert_without_increment_check\": True,\n", "    \"column_dtypes\": imd_festival_column_dtypes,\n", "    \"table_description\": festival_table_description\n", "}\n", "\n", "pb.to_trino(data_obj = festival_df, **trino_kwargs)"]}, {"cell_type": "markdown", "id": "cb394ce2-ae56-4e3f-a1a3-0e6985cca557", "metadata": {}, "source": ["### Festival Mapping Final Table"]}, {"cell_type": "code", "execution_count": null, "id": "e503b564-35df-4e13-bedd-bbf210a5a8a3", "metadata": {}, "outputs": [], "source": ["festival_mapping_sql = (\n", "    open(f\"{sidefile_path}/festival_mapping.sql\", \"r\")\n", "    .read()\n", "    .format(data_catalog = data_catalog)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "4c69f620-bc9d-4dd9-bc25-c4c0959a5419", "metadata": {}, "outputs": [], "source": ["trino_kwargs = {\n", "    \"schema_name\": 'dwh',\n", "    \"table_name\": \"festival_ptype_category_mapping\",\n", "    \"primary_key\": [],\n", "    \"load_type\": \"truncate\",\n", "    \"force_upsert_without_increment_check\": True,\n", "    \"column_dtypes\": festival_column_dtypes,\n", "    \"table_description\": festival_table_description\n", "}\n", "\n", "pb.to_trino(data_obj = festival_mapping_sql, **trino_kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}