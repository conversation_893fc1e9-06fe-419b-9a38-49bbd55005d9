festival_table_description = "This table stores the frontend merchant to city to zone mapping "

imd_festival_column_dtypes = [
    {
        "name": "festival_name",
        "type": "VARCHAR",
        "description": "Name of the festival for which mapping is enabled",
    },
    {
        "name": "product_type",
        "type": "VARCHAR",
        "description": "Type of the product",
    },
    {
        "name": "snapshot_timestamp",
        "type": "TIMESTAMP(6)",
        "description": "ETL timestamp",
    }
]

festival_column_dtypes = [
    {
        "name": "festival_name",
        "type": "VARCHAR",
        "description": "Name of the festival for which mapping is enabled",
    },
    {
        "name": "product_type",
        "type": "VARCHAR",
        "description": "Type of the product",
    },
    {
        "name": "l0_category",
        "type": "VARCHAR",
        "description": "l0 category of product",
    },
    {
        "name": "l1_category",
        "type": "VARCHAR",
        "description": "l1 category of product",
    },    
    {
        "name": "l2_category",
        "type": "VARCHAR",
        "description": "l2 category of product",
    },
    {
        "name": "l0_category_id",
        "type": "INT",
        "description": "l0 category id of product",
    },
    {
        "name": "l1_category_id",
        "type": "INT",
        "description": "l1 category id of product",
    },    
    {
        "name": "l2_category_id",
        "type": "INT",
        "description": "l2 category id of product",
    },
     {
        "name": "snapshot_timestamp",
        "type": "TIMESTAMP(6)",
        "description": "ETL timestamp",
    }
]
