# -*- coding: utf-8 -*-

import os
import json
import logging
from contextlib import closing
from datetime import datetime, timedelta

import requests

import airflow
from airflow import DAG
from airflow.hooks.base_hook import BaseHook

from metrics_plugin import PapermillOperator
from kubernetes.client import models as k8s
from airflow.models import Variable

import pencilbox as pb


cwd = os.path.dirname(os.path.realpath(__file__))


k8s_executor_config = {
    "pod_override": k8s.V1Pod(
        spec=k8s.V1PodSpec(
            service_account_name="blinkit-prod-airflow-de-primary-eks-role",
            node_selector={"nodetype": "airflow-spot-general"},
            tolerations=[
                k8s.V1Toleration(
                    effect="NoSchedule",
                    key="service",
                    operator="Equal",
                    value="airflow-spot-general",
                ),
            ],
            containers=[
                k8s.V1Container(
                    image=Variable.get("************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable", "************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable"),
                    name="base",
                    volume_mounts=[
                        k8s.V1VolumeMount(
                            mount_path="/usr/local/airflow/plugins",
                            name="airflow-dags",
                            sub_path="airflow-de/plugins",
                        ),
                    ],
                    resources=k8s.V1ResourceRequirements(
                        limits={"cpu": 1, "memory": "4G"},
                        requests={"cpu": 0.5, "memory": "500M"},
                    ),
                )
            ],
        )
    )
}


def slack_failure_alert(context):
    dag_id = context.get("task_instance").dag_id
    run_id = context.get("run_id")
    task_id = context.get("task_instance").task_id
    log_url = context.get("task_instance").log_url.replace("http://", "https://")
    notebook_url = f"https://dse-commuter.grofer.io/view/{dag_id}/{run_id}/{task_id}.ipynb"

    owner = {"email": "<EMAIL>", "slack_id": "S03ST1ENERJ"}
    slack_id = owner["slack_id"]
    _owner = {
        "s": "s" if len(slack_id.split(" ")) > 1 else "",
        "slack_id": "<!subteam^{}>".format(slack_id),
    }

    slack_failure_msg = """
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner{s}*: {slack_id}
        *Log Url*: {log_url}
        *Notebook Url*: {notebook_url}
    """.format(
        dag_id=dag_id,
        run_id=run_id,
        task_id=task_id,
        s=_owner["s"],
        slack_id=_owner["slack_id"],
        log_url=log_url,
        notebook_url=notebook_url,
    )

    slack_alert_configs = [
        {"channel": "bl-dwh-alerts"},
    ]

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(
                channel=channel, text=slack_failure_msg, user="data-plumber"
            )
        except Exception as e:
            logging.warning(e)
            message = (
                f"<!subteam^S089D0GQRCJ> `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(
                channel="bl-data-alerts-p1", text=message, user="data-plumber"
            )


def failure_alerts(context):
    slack_failure_alert(context)


args = {
    "slack_id": "S03ST1ENERJ",
    "owner": "<EMAIL>",
    "on_failure_callback": failure_alerts,
    "start_date": datetime.strptime("2025-09-10T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": datetime.strptime("2025-09-30T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "retries": 1,
    "retry_delay": timedelta(minutes=1),
    "retry_exponential_backoff": True,
}

dag = DAG(
    dag_id="de_dwh_etl_trino_festival_dashboard_mapping_v1",
    default_args=args,
    schedule_interval=None,
)

env = {}
default_env = {
    "AIRFLOW_DAG_ID": "de_dwh_etl_trino_festival_dashboard_mapping_v1",
    "AIRFLOW_DAG_OWNER": "<EMAIL>",
}
env.update(default_env)

notebook_task = PapermillOperator(
    task_id="run_notebook",
    input_nb="{cwd}/notebook.ipynb".format(cwd=cwd),
    output_nb="s3://grofers-prod-dse-sgp/airflow/dag_runs/de_dwh_etl_trino_festival_dashboard_mapping_v1/{{ run_id }}/run_notebook.ipynb",
    parameters={"cwd": "/usr/local/airflow/dags/repo/dags/de/dwh/etl_trino/festival_dashboard_mapping"},
    env=env,
    dag=dag,
    executor_config=k8s_executor_config,
)

if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
