SELECT 
    fmt.festival_name,
    fmt.product_type,
    dp.l0_category,
    dp.l1_category,
    dp.l2_category,
    MAX(dp.l0_category_id) AS l0_category_id,
    MAX(dp.l1_category_id) AS l1_category_id,
    MAX(dp.l2_category_id) AS l2_category_id,
    CAST(CURRENT_TIMESTAMP AS TIMESTAMP(6)) AS snapshot_timestamp

FROM
    {data_catalog}.interim.imd_festival_mapping
        AS fmt
JOIN
    dwh.dim_product 
        AS dp
        ON LOWER(dp.product_type) = LOWER(fmt.product_type)
            AND dp.is_current
            
GROUP BY 
    1, 2, 3, 4, 5
    
    