{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys, os\n", "import time\n", "import pencilbox as pb\n", "import pandas as pd\n", "from dateutil import tz, parser\n", "from datetime import datetime, date, timedelta\n", "from pytz import timezone\n", "\n", "trino_connection = pb.get_connection(\"[Warehouse] Trino\")\n", "table_catalog = 'blinkit_iceberg'\n", "execution_start_time = time.time()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Parameter\n", "\n", "cwd = \"./\"\n", "sidefile_dir_name = \"\"\n", "airflow_ts = datetime.now(timezone('UTC')).strftime(\"%Y-%m-%d %H:%M:%S\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date_filter_parsed = parser.parse(airflow_ts)\n", "date_filter = date_filter_parsed.strftime(\"%Y-%m-%d %H:%M:%S\")\n", "dt_partition_limit = date_filter_parsed.strftime(\"%Y-%m-%d\")\n", "\n", "print(f\"Date filter: {date_filter}\")\n", "print(f\"Date filter Partition Limit: {dt_partition_limit}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sidefile_path = os.path.join(cwd, sidefile_dir_name)\n", "sys.path.append(sidefile_path)\n", "sidefile_path"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# from column_definitions import column_dtypes, trino_to_rds_column_dtypes, rds_to_trino_column_dtypes\n", "\n", "# table_description = (\n", "#     \"This table stores the sales related values and dimensions at order item id level\"\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["time_in_hours = (datetime.strptime(date_filter, \"%Y-%m-%d %H:%M:%S\")).hour\n", "partition_key_filter = (parser.parse(date_filter) - <PERSON><PERSON><PERSON>(days = 5)).strftime(\"%Y-%m-%d\")\n", "\n", "if time_in_hours == 0: # first run in the morning\n", "    hours_back = 5\n", "elif time_in_hours == 20: # reconciliation run\n", "    hours_back = 23\n", "    partition_key_filter = (parser.parse(date_filter) - <PERSON><PERSON><PERSON>(days = 15)).strftime(\"%Y-%m-%d\")\n", "    date_filter_parsed = date_filter_parsed.replace(hour=18, minute=0, second=0, microsecond=0)\n", "    date_filter = date_filter_parsed.strftime(\"%Y-%m-%d %H:%M:%S\")\n", "else:\n", "    hours_back = 3 # hourly run\n", "    \n", "print(f\"Hours back filter: {hours_back}, Partition Key filter: {partition_key_filter}\")\n", "print(f\"Date Filter: {date_filter}\")"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["## To create \"fact_sales_order_item_details\" table in interim layer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## To drop interim table for fact_sales_order_item_details\n", "\n", "trino_connection.execute(f\"DROP TABLE IF EXISTS {table_catalog}.interim.wi_fact_sales_order_item_details\").fetchall()\n", "print(f\"Step 1 - Execution Time: --- {(time.time() - execution_start_time)} seconds ---\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# To create interim table for fact_sales_order_item_details\n", "\n", "query_for_interim_fact_order_item = (\n", "    open(f\"{sidefile_path}/models/interim_table_fact_order_item_query.sql\", \"r\")\n", "    .read()\n", "    .replace('{{','{')\n", "    .replace('}}','}') \n", "    .format(\n", "        table_catalog = table_catalog,\n", "        date_filter = date_filter,\n", "        partition_key_filter = partition_key_filter,\n", "        hours_back = hours_back,\n", "        dt_partition_limit = dt_partition_limit\n", "    )\n", ")\n", "\n", "result_set = trino_connection.execute(query_for_interim_fact_order_item).fetchall()\n", "print(f\"An interim table has been created with a line count of {result_set[0][0]}\")\n", "print(f\"Step 2 - Execution Time: --- {(time.time() - execution_start_time)} seconds ---\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## To query sales margins from \"pricing_v3.pricing_domain_productchangelog\" table "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## To drop interim table for sales_margins_tables\n", "\n", "trino_connection.execute(f\"DROP TABLE IF EXISTS {table_catalog}.interim.wi_fact_sales_productchangelog\").fetchall()\n", "print(f\"Step 2 - Execution Time: --- {(time.time() - execution_start_time)} seconds ---\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# To create interim table for wi_fact_sales_margin_details\n", "\n", "query_for_interim_fact_sales_productchangelog = (\n", "    open(f\"{sidefile_path}/models/interim_table_fact_sales_productchangelog.sql\", \"r\")\n", "    .read()\n", "    .replace('{{','{')\n", "    .replace('}}','}') \n", "    .format(\n", "        table_catalog = table_catalog,\n", "        date_filter = date_filter,\n", "    )\n", ")\n", "\n", "result_set = trino_connection.execute(query_for_interim_fact_sales_productchangelog).fetchall()\n", "print(f\"An interim table for sales productchangelog has been created with a line count of {result_set[0][0]}\")\n", "print(f\"Step 3 - Execution Time: --- {(time.time() - execution_start_time)} seconds ---\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## To query sales margins from \"zomato.blinkit_jumbo2.price_change_events\" table "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## To drop interim table for sales_margins_tables\n", "\n", "trino_connection.execute(f\"DROP TABLE IF EXISTS {table_catalog}.interim.wi_fact_sales_price_change_events\").fetchall()\n", "print(f\"Step 4 - Execution Time: --- {(time.time() - execution_start_time)} seconds ---\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# To create interim table for wi_fact_sales_margin_details\n", "\n", "query_for_interim_fact_sales_price_change_events = (\n", "    open(f\"{sidefile_path}/models/interim_table_fact_sales_price_change_events.sql\", \"r\")\n", "    .read()\n", "    .replace('{{','{')\n", "    .replace('}}','}') \n", "    .format(\n", "        table_catalog = table_catalog,\n", "        date_filter = date_filter,\n", "    )\n", ")\n", "\n", "result_set = trino_connection.execute(query_for_interim_fact_sales_price_change_events).fetchall()\n", "print(f\"An interim table for sales price_change_events has been created with a line count of {result_set[0][0]}\")\n", "print(f\"Step 5 - Execution Time: --- {(time.time() - execution_start_time)} seconds ---\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## combining pricing_domain_productchangelog and price_change_events"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## To drop interim table for sales_margins_tables\n", "\n", "trino_connection.execute(f\"DROP TABLE IF EXISTS {table_catalog}.interim.wi_fact_sales_margin_details\").fetchall()\n", "print(f\"Step 6 - Execution Time: --- {(time.time() - execution_start_time)} seconds ---\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# To create interim table for wi_fact_sales_margin_details\n", "\n", "query_for_interim_fact_sales_margins = (\n", "    open(f\"{sidefile_path}/models/interim_table_fact_sales_margins.sql\", \"r\")\n", "    .read()\n", "    .replace('{{','{')\n", "    .replace('}}','}') \n", "    .format(\n", "        table_catalog = table_catalog,\n", "        date_filter = date_filter,\n", "    )\n", ")\n", "\n", "result_set = trino_connection.execute(query_for_interim_fact_sales_margins).fetchall()\n", "print(f\"An interim table for sales margins has been created with a line count of {result_set[0][0]}\")\n", "print(f\"Step 7 - Execution Time: --- {(time.time() - execution_start_time)} seconds ---\")"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["## To update brand_fund, liquid_fund, self_fund in \"fact_sales_order_item_details\" table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## To update brand fund, liquid fund and self fund in fact_sales_order_item table\n", "\n", "query_to_update_bf_lf_sf_margins = (\n", "    open(f\"{sidefile_path}/models/to_update_bf_lf_sf_margins.sql\", \"r\")\n", "    .read()\n", "    .replace('{{','{')\n", "    .replace('}}','}') \n", "    .format(\n", "        table_catalog = table_catalog\n", "    )\n", ")\n", "\n", "result_set = trino_connection.execute(query_to_update_bf_lf_sf_margins).fetchall()\n", "print(f\"<PERSON><PERSON> has been updated for {result_set[0][0]} rows\")\n", "print(f\"Step 8 - Execution Time: --- {(time.time() - execution_start_time)} seconds ---\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## To query invoice related details and add them in \"fact_sales_order_item_details\" table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## To drop interim table for wi_suborder_invoice_details\n", "\n", "trino_connection.execute(f\"DROP TABLE IF EXISTS {table_catalog}.interim.wi_suborder_invoice_details\").fetchall()\n", "print(f\"Step 9 - Execution Time: --- {(time.time() - execution_start_time)} seconds ---\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# To create interim table for wi_suborder_invoice_details\n", "\n", "query_suborder_invoice_details = (\n", "    open(f\"{sidefile_path}/models/interim_suborder_invoice_details.sql\", \"r\")\n", "    .read()\n", "    .replace('{{','{')\n", "    .replace('}}','}') \n", "    .format(\n", "        table_catalog = table_catalog,\n", "        date_filter = date_filter[:10],\n", "        partition_key_filter = partition_key_filter\n", "    )\n", ")\n", "\n", "result_set = trino_connection.execute(query_suborder_invoice_details).fetchall()\n", "print(f\"An interim table for sales margins has been created with a line count of {result_set[0][0]}\")\n", "print(f\"Step 10 - Execution Time: --- {(time.time() - execution_start_time)} seconds ---\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## To update invoice details in \"fact_sales_order_item_details\" table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## To update brand fund, liquid fund and self fund in fact_sales_order_item table\n", "\n", "query_to_update_inv_details = (\n", "    open(f\"{sidefile_path}/models/to_update_invoice_details.sql\", \"r\")\n", "    .read()\n", "    .replace('{{','{')\n", "    .replace('}}','}') \n", "    .format(\n", "        table_catalog = table_catalog,\n", "        partition_key_filter = partition_key_filter\n", "    )\n", ")\n", "\n", "result_set = trino_connection.execute(query_to_update_inv_details).fetchall()\n", "print(f\"Invoice details are updated for {result_set[0][0]} rows\")\n", "print(f\"Step 11 - Execution Time: --- {(time.time() - execution_start_time)} seconds ---\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}