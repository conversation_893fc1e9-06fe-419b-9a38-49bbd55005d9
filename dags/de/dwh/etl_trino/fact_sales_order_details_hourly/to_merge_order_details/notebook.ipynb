{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys, os\n", "import time\n", "from pytz import timezone\n", "import pencilbox as pb\n", "from dateutil import tz, parser\n", "from datetime import date, datetime, timedelta\n", "\n", "trino_connection = pb.get_connection(\"[Warehouse] Trino\")\n", "table_catalog = 'blinkit_iceberg'\n", "execution_start_time = time.time()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Parameter\n", "\n", "cwd = \"./\"\n", "sidefile_dir_name = \"\"\n", "airflow_ts = datetime.now(timezone('Asia/Kolkata')).strftime(\"%Y-%m-%d %H:%M:%S\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date_filter = parser.parse(airflow_ts)\n", "\n", "# Commented out because of days mismatch between merge and base table update.\n", "# partition_key_filter = (date_filter - timedelta(days = 30)).strftime(\"%Y-%m-%d\")  \n", "# print(partition_key_filter)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date_filter = date_filter.strftime(\"%Y-%m-%d %H:%M:%S\")\n", "time_in_hours = (datetime.strptime(date_filter, \"%Y-%m-%d %H:%M:%S\")).hour\n", "\n", "if time_in_hours == 20: # reconciliation run in the night\n", "    partition_key_filter = (parser.parse(date_filter) - <PERSON><PERSON><PERSON>(days = 15)).strftime(\"%Y-%m-%d\")\n", "else:\n", "    partition_key_filter = (parser.parse(date_filter) - <PERSON><PERSON><PERSON>(days = 5)).strftime(\"%Y-%m-%d\")\n", "    \n", "print(f\"Partition Key filter: {partition_key_filter}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sidefile_path = os.path.join(cwd, sidefile_dir_name)\n", "sys.path.append(sidefile_path)\n", "print(sidefile_path)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## To union and merge records from staging layer to fact_order table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# trino_connection.execute(f\"DROP TABLE IF EXISTS {table_catalog}.interim.wi_fact_products_and_emergency_service_order_details\").fetchall()\n", "# print(f\"Step 1 - Execution Time: --- {(time.time() - execution_start_time)} seconds ---\")\n", "\n", "## Moved to fact_order_hourly ^^"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# query_to_union_order_details = (\n", "#     open(f\"{sidefile_path}/models/to_union_product_and_service_orders.sql\", \"r\")\n", "#     .read()\n", "#     .replace('{{','{')\n", "#     .replace('}}','}') \n", "#     .format(\n", "#         table_catalog = table_catalog,\n", "#         partition_key_filter = partition_key_filter\n", "#     )\n", "# )\n", "\n", "# result_set = trino_connection.execute(query_to_union_order_details).fetchall()\n", "\n", "# print(f\"{result_set[0][0]} rows of interim tables are unioned\")\n", "# print(f\"Step 2A - Execution Time: --- {(time.time() - execution_start_time)} seconds ---\")\n", "\n", "## Moved to fact_order_hourly ^^\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "\n", "query_to_merge_order_details = (\n", "    open(f\"{sidefile_path}/models/to_merge_order_details.sql\", \"r\")\n", "    .read()\n", "    .replace('{{','{')\n", "    .replace('}}','}') \n", "    .format(\n", "        table_catalog = table_catalog,\n", "        partition_key_filter = partition_key_filter\n", "    )\n", ")\n", "\n", "result_set = trino_connection.execute(query_to_merge_order_details).fetchall()\n", "\n", "print(f\"{result_set[0][0]} rows are merged into dwh.fact_sales_order_details from interim table\")\n", "print(f\"Step 1 - Execution Time: --- {(time.time() - execution_start_time)} seconds ---\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from column_definitions import column_dtypes\n", "\n", "pb.to_datahub(\n", "    schema_name = 'dwh',  # Trino schema name\n", "    table_name = \"fact_sales_order_details\",  # Trino table name\n", "    column_dtypes = column_dtypes,\n", "    table_description = 'Order level details such as cart rank, total mrp etc.',  # Description of the table being sent to Trino\n", "    data_platform = \"trino\",# Platform for which you are uploading metadata e.g. : Aurora, Pricing, etc\n", "    primary_keys=['order_id'],\n", "    partition_keys=['order_create_dt_ist'],\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### To Delete test orders from fact_order table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["execution_start_time = time.time()\n", "\n", "\n", "if airflow_ts[11:13] == '23':\n", "    query_to_delete_test_order = f\"\"\"\n", "\n", "        DELETE FROM {table_catalog}.dwh.fact_sales_order_details\n", "        WHERE\n", "            order_create_dt_ist >= CURRENT_DATE - INTERVAL '7' DAY\n", "            AND order_id IN (\n", "                    SELECT \n", "                        oo.id AS order_id\n", "\n", "                    FROM\n", "                        oms_bifrost.oms_order_item\n", "                            AS oid\n", "                    LEFT JOIN \n", "                        oms_bifrost.oms_order\n", "                            AS oo \n", "                            ON oo.id = oid.order_id\n", "                                AND oo.insert_ds_ist >= CAST(CURRENT_DATE - INTERVAL '7' DAY AS VARCHAR)\n", "                    LEFT JOIN \n", "                        oms_bifrost.oms_merchant\n", "                            AS m \n", "                            ON m.id = oo.merchant_id\n", "                               AND m.insert_ds_ist >= CAST(CURRENT_DATE - INTERVAL '7' DAY AS VARCHAR)\n", "\n", "                    WHERE\n", "                        oo.install_ts >= CAST(CURRENT_DATE - INTERVAL '7' DAY AS TIMESTAMP) - INTERVAL '330' MINUTE\n", "                        AND oid.insert_ds_ist >= CAST(CURRENT_DATE - INTERVAL '7' DAY AS VARCHAR)\n", "                        AND (\n", "                                m.external_id IN (28759, 26680, 31149, 33523, 36120)  -- To exclude test orders -- 36120 is new test store for digital orders\n", "                                OR m.city_name IN ('Not in service area','test1207898732') -- To exclude test orders\n", "                        )\n", "\n", "                    GROUP BY \n", "                        1\n", "                )\n", "        \"\"\"\n", "    \n", "    result_set = trino_connection.execute(query_to_delete_test_order).fetchall()\n", "\n", "    print(f\"{result_set[0][0]} rows are deleted from dwh.fact_sales_order_details\")\n", "else:\n", "    print ('Test record deleteion skipped')\n", "print(f\"Step 3 - Execution Time: --- {(time.time() - execution_start_time)} seconds ---\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### To update archived orders in fact sales order details"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["execution_start_time = time.time()\n", "\n", "if int(airflow_ts[11:13]) == 23:\n", "\n", "    ## Bring last date for which archived order happen\n", "    query_for_last_archived_order = f\"\"\"\n", "                    SELECT \n", "                        COALESCE(MIN(order_create_dt_ist), DATE('{partition_key_filter}')) AS last_archived_order_dt \n", "\n", "                    FROM \n", "                        {table_catalog}.interim.wi_archived_orders\n", "                \"\"\"\n", "\n", "    last_archived_order_dt = trino_connection.execute(query_for_last_archived_order).fetchall()[0][0]\n", "    print(f\"Last Archived Order Date: {last_archived_order_dt}\")\n", "    print(f\"Step 4 - Execution Time: --- {(time.time() - execution_start_time)} seconds ---\")\n", "\n", "    ## Step to merge the final records in sales table\n", "    execution_start_time = time.time()\n", "    query_to_update_archived_orders = f\"\"\"\n", "\n", "         MERGE INTO {table_catalog}.dwh.fact_sales_order_details \n", "                     AS dwh\n", "            USING\n", "                {table_catalog}.interim.wi_archived_orders\n", "                     AS interim\n", "\n", "            ON interim.order_id = dwh.order_id\n", "                AND dwh.order_create_dt_ist = interim.order_create_dt_ist\n", "                AND dwh.order_create_dt_ist >= DATE('{last_archived_order_dt}')\n", "\n", "            WHEN MATCHED \n", "                AND COALESCE(dwh.is_archived_order, false) <> true\n", "            THEN UPDATE SET\n", "                is_archived_order = true,\n", "                order_archived_dt_ist = DATE(interim.order_archived_ts_ist)\n", "\n", "        \"\"\"\n", "\n", "    result_set = trino_connection.execute(query_to_update_archived_orders).fetchall()\n", "\n", "    print(f\"{result_set[0][0]} rows are updated for archived_orders in dwh.fact_sales_order_details\")\n", "print(f\"Step 5 - Execution Time: --- {(time.time() - execution_start_time)} seconds ---\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}