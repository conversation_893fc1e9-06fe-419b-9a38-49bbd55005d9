{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os, sys\n", "import time\n", "import pencilbox as pb\n", "import pandas as pd\n", "from dateutil import tz, parser\n", "from datetime import date, datetime, timedelta\n", "\n", "trino_connection = pb.get_connection(\"[Warehouse] Trino\")\n", "table_catalog = 'blinkit_iceberg'\n", "execution_start_time = time.time()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["cwd = \"./\"\n", "sidefile_dir_name = \"\"\n", "airflow_ts = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "days_past = 3\n", "hours_past = 1 "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date_filter_parsed = parser.parse(airflow_ts)\n", "date_filter = date_filter_parsed.strftime(\"%Y-%m-%d %H:%M:%S\")\n", "dt_partition_limit = date_filter_parsed.strftime(\"%Y-%m-%d\")\n", "\n", "print(f\"Date filter: {date_filter}\")\n", "print(f\"Date filter Partition Limit: {dt_partition_limit}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sidefile_path = os.path.join(cwd, sidefile_dir_name)\n", "sys.path.append(sidefile_path)\n", "sys.path.append(cwd)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# from column_definitions import column_dtypes\n", "\n", "# table_description = (\n", "#     \"This table stores the fact_sales_order_details information at hourly level\"\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["time_in_hours = (datetime.strptime(date_filter, \"%Y-%m-%d %H:%M:%S\")).hour\n", "partition_key_filter = (parser.parse(date_filter) - <PERSON><PERSON><PERSON>(days = 5)).strftime(\"%Y-%m-%d\")\n", "\n", "if time_in_hours == 0: # first run in the morning\n", "    hours_back = 5\n", "elif time_in_hours == 20: # reconciliation run\n", "    hours_back = 23\n", "    partition_key_filter = (parser.parse(date_filter) - <PERSON><PERSON><PERSON>(days = 15)).strftime(\"%Y-%m-%d\")\n", "    date_filter_parsed = date_filter_parsed.replace(hour=18, minute=0, second=0, microsecond=0)\n", "    date_filter = date_filter_parsed.strftime(\"%Y-%m-%d %H:%M:%S\")\n", "else:\n", "    hours_back = 3 # hourly run\n", "    \n", "print(f\"Hours back filter: {hours_back}, Partition Key filter: {partition_key_filter}\")\n", "print(f\"Date Filter: {date_filter}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### To extract the order_ids from \"interim.wi_fact_sales_order_item_details\" table which processed in the current run."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trino_connection.execute(f\"DROP TABLE IF EXISTS {table_catalog}.interim.wi_fact_updated_order_item_carts\").fetchall()\n", "print(f\"Step 1 - Execution Time: --- {(time.time() - execution_start_time)} seconds ---\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query_for_interim_updated_orders = (\n", "    open(f\"{sidefile_path}/models/interim_table_fact_updated_orders.sql\", \"r\")\n", "    .read()\n", "    .replace('{{','{')\n", "    .replace('}}','}') \n", "    .format(\n", "        table_catalog = table_catalog\n", "    )\n", ")\n", "\n", "result_set = trino_connection.execute(query_for_interim_updated_orders).fetchall()\n", "print(f\"An interim table has been created with a line count of {result_set[0][0]}\")\n", "print(f\"Step 2 - Execution Time: --- {(time.time() - execution_start_time)} seconds ---\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### To extract the order_ids which are archived"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trino_connection.execute(f\"DROP TABLE IF EXISTS {table_catalog}.interim.wi_archived_orders\").fetchall()\n", "print(f\"Step 3 - Execution Time: --- {(time.time() - execution_start_time)} seconds ---\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query_for_interim_archived_orders = (\n", "    open(f\"{sidefile_path}/models/interim_archived_orders.sql\", \"r\")\n", "    .read()\n", "    .replace('{{','{')\n", "    .replace('}}','}') \n", "    .format(\n", "        table_catalog = table_catalog,\n", "        partition_key_filter = partition_key_filter\n", "    )\n", ")\n", "\n", "result_set = trino_connection.execute(query_for_interim_archived_orders).fetchall()\n", "print(f\"An interim table has been created with a line count of {result_set[0][0]}\")\n", "print(f\"Step 4 - Execution Time: --- {(time.time() - execution_start_time)} seconds ---\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## To create \"fact_sales_order_details\" table in interim layer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trino_connection.execute(f\"DROP TABLE IF EXISTS {table_catalog}.interim.wi_fact_sales_order_details\").fetchall()\n", "print(f\"Step 5A - Execution Time: --- {(time.time() - execution_start_time)} seconds ---\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query_for_interim_fact_orders = (\n", "    open(f\"{sidefile_path}/models/interim_table_fact_order_query.sql\", \"r\")\n", "    .read()\n", "    .replace('{{','{')\n", "    .replace('}}','}') \n", "    .format(\n", "        table_catalog = table_catalog,\n", "        date_filter = date_filter,\n", "        partition_key_filter = partition_key_filter,\n", "        dt_partition_limit = dt_partition_limit\n", "    )\n", ")\n", "\n", "result_set = trino_connection.execute(query_for_interim_fact_orders).fetchall()\n", "print(f\"An interim table has been created with a line count of {result_set[0][0]}\")\n", "print(f\"Step 5B - Execution Time: --- {(time.time() - execution_start_time)} seconds ---\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trino_connection.execute(f\"DROP TABLE IF EXISTS {table_catalog}.interim.wi_fact_sales_emergency_service_order_details\").fetchall()\n", "print(f\"Step 6A - Execution Time: --- {(time.time() - execution_start_time)} seconds ---\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### Emergency Services\n", "\n", "query_for_interim_fact_emergency_service_orders = (\n", "    open(f\"{sidefile_path}/models/interim_table_fact_emergency_service_order_query.sql\", \"r\")\n", "    .read()\n", "    .replace('{{','{')\n", "    .replace('}}','}') \n", "    .format(\n", "        table_catalog = table_catalog,\n", "        date_filter = date_filter,\n", "        partition_key_filter = partition_key_filter,\n", "        hours_back = hours_back,\n", "        dt_partition_limit = dt_partition_limit\n", "    )\n", ")\n", "\n", "result_set_es = trino_connection.execute(query_for_interim_fact_emergency_service_orders).fetchall()\n", "print(f\"An interim table has been created with a line count of {result_set_es[0][0]}\")\n", "print(f\"Step 6B - Execution Time: --- {(time.time() - execution_start_time)} seconds ---\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Union the services records with product ones"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trino_connection.execute(f\"DROP TABLE IF EXISTS {table_catalog}.interim.wi_fact_products_and_emergency_service_order_details\").fetchall()\n", "print(f\"Step 7A - Execution Time: --- {(time.time() - execution_start_time)} seconds ---\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query_to_union_order_details = (\n", "    open(f\"{sidefile_path}/models/to_union_product_and_service_orders.sql\", \"r\")\n", "    .read()\n", "    .replace('{{','{')\n", "    .replace('}}','}') \n", "    .format(\n", "        table_catalog = table_catalog,\n", "        partition_key_filter = partition_key_filter\n", "    )\n", ")\n", "\n", "result_set = trino_connection.execute(query_to_union_order_details).fetchall()\n", "\n", "print(f\"{result_set[0][0]} rows of interim tables are unioned\")\n", "print(f\"Step 7B - Execution Time: --- {(time.time() - execution_start_time)} seconds ---\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## To update cart_rank values in \"interim.fact_sales_order_details\" table."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trino_connection.execute(f\"DROP TABLE IF EXISTS {table_catalog}.interim.wi_fact_sales_order_cart_rank\").fetchall()\n", "print(f\"Step 8 - Execution Time: --- {(time.time() - execution_start_time)} seconds ---\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query_to_create_cart_rank_table = (\n", "    open(f\"{sidefile_path}/models/interim_table_fact_cart_rank_query.sql\", \"r\")\n", "    .read()\n", "    .replace('{{','{')\n", "    .replace('}}','}') \n", "    .format(\n", "        table_catalog = table_catalog,\n", "        date_filter = date_filter,\n", "    )\n", ")\n", "\n", "result_set = trino_connection.execute(query_to_create_cart_rank_table).fetchall()\n", "print(f\"An interim table has been created with a line count of {result_set[0][0]}\")\n", "print(f\"Step 9 - Execution Time: --- {(time.time() - execution_start_time)} seconds ---\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## To update cart_rank in \"interim.fact_sales_order_details\"\n", "\n", "query_to_update_cart_rank = (\n", "    open(f\"{sidefile_path}/models/to_update_cart_rank.sql\", \"r\")\n", "    .read()\n", "    .replace('{{','{')\n", "    .replace('}}','}') \n", "    .format(\n", "        table_catalog = table_catalog\n", "    )\n", ")\n", "\n", "result_set = trino_connection.execute(query_to_update_cart_rank).fetchall()\n", "print(f\"Cart rank has been updated for {result_set[0][0]} rows\")\n", "print(f\"Step 10 - Execution Time: --- {(time.time() - execution_start_time)} seconds ---\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## To update delivered_cart_rank in \"interim.fact_sales_order_details\"\n", "\n", "query_to_update_cart_rank = (\n", "    open(f\"{sidefile_path}/models/to_update_delivered_cart_rank.sql\", \"r\")\n", "    .read()\n", "    .replace('{{','{')\n", "    .replace('}}','}') \n", "    .format(\n", "        table_catalog = table_catalog\n", "    )\n", ")\n", "\n", "result_set = trino_connection.execute(query_to_update_cart_rank).fetchall()\n", "print(f\"Delivered Cart rank has been updated for {result_set[0][0]} rows\")\n", "print(f\"Step 11 - Execution Time: --- {(time.time() - execution_start_time)} seconds ---\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}