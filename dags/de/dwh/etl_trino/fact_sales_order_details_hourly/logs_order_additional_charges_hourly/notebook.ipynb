{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import pencilbox as pb\n", "from datetime import timedelta, datetime\n", "from dateutil import  parser\n", "import time as t"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trino_connection = pb.get_connection(\"[Warehouse] Trino\")\n", "execution_start_time = t.time()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Parameters\n", "cwd = \"./\"\n", "sidefile_dir_name = \"\"\n", "date_filter = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "end_interval = date_filter"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# date_filter = '2025-05-26T08:15:00+00:00'\n", "# date_filter = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "# end_interval = date_filter"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date_filter = parser.parse(date_filter)\n", "end_interval = date_filter.strftime(\"%Y-%m-%d %H:%M:%S\")\n", "date_filter = date_filter.strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["time_in_hours = (datetime.strptime(end_interval, \"%Y-%m-%d %H:%M:%S\")).hour\n", "days_in_past = 5\n", "\n", "if time_in_hours == 0: # first run in the morning\n", "    hours_back = 10\n", "elif time_in_hours == 20: # reconciliation run\n", "    hours_back = 36\n", "    days_in_past = 15\n", "else:\n", "    hours_back = 5 # hourly run\n", "    \n", "print(f\"date_filter: {date_filter}\")\n", "print(f\"end_interval: {end_interval}\")\n", "print(f\"days in past: {days_in_past}\")\n", "print(f\"Hours back filter: {hours_back}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sidefile_path = os.path.join(cwd, sidefile_dir_name)\n", "sys.path.append(sidefile_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["additional_charges_incremental = (\n", "    open(f\"{sidefile_path}/models/order_additional_charges_incremental_v1.sql\", \"r\")\n", "        .read()\n", "        .replace('{{','{')\n", "        .replace('}}','}') \n", "        .format(\n", "            date_filter=date_filter,\n", "            days_in_past = days_in_past,\n", "            hour_in_past = hours_back,\n", "            end_interval = end_interval\n", "        )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["execution_start_time = t.time()\n", "from column_definitions import column_dtypes , table_description"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"interim\",\n", "    \"table_name\": \"logs_order_additional_charges_details\",\n", "    \"primary_key\": [\"order_id\"],\n", "    \"column_dtypes\": column_dtypes,\n", "    \"force_upsert_without_increment_check\": True,\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": table_description,\n", "    \"run_maintenance\": <PERSON><PERSON><PERSON>,\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pb.to_trino(additional_charges_incremental, **kwargs)\n", "print(f\"Step 1 - Execution Time: --- {(t.time() - execution_start_time)} seconds ---\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}