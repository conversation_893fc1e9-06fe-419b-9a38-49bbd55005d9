WITH

lost_order_metrics AS (
    SELECT 
        at_date_ist,
        day_of_week,
        hour,
        merchant_id,
        merchant_name, 
        
        CASE
            WHEN city IN ('CHANDIGARH', 'NEW CHANDIGARH')
            THEN 'CHANDIGARH'
            WHEN city IN ('GHAZIABAD')
            THEN 'UP-NCR'
            ELSE city
        END AS city,
            
        'Blocks' AS loss_type,
        block_reason AS loss_reason,
        CAST(lost_orders AS INT) AS lost_orders,
        CAST(lost_gmv AS DOUBLE) AS lost_gmv

    FROM
        {{catalog}}.interim.lost_order_due_to_block 

    WHERE
        at_date_ist >= DATE('{{date_filter}}') - INTERVAL '1' DAY
        AND at_date_ist < DATE('{{date_filter}}')
        AND hour <= CAST(('{{max_hour}}') AS INT)
    
    GROUP BY 
        1, 2, 3, 4, 5, 6, 7, 8, 9, 10

    UNION ALL

    SELECT 
        at_date_ist,
        day_of_week,
        hour,
        merchant_id,
        merchant_name, 
    
        CASE
            WHEN city IN ('CHANDIGARH', 'NEW CHANDIGARH')
            THEN 'CHANDIGARH'
            WHEN city IN ('GHAZIABAD')
            THEN 'UP-NCR'
            ELSE city
        END AS city,
    
        'High ETA and Surge' AS loss_type,
        delay_reason AS loss_reason,
        CAST(lost_orders AS INT) AS lost_orders,
        CAST(lost_gmv AS DOUBLE) AS lost_gmv

    FROM
        {{catalog}}.interim.lost_order_due_to_high_eta_and_surge

    WHERE
        at_date_ist >= DATE('{{date_filter}}') - INTERVAL '1' DAY
        AND at_date_ist < DATE('{{date_filter}}')
        AND hour <= CAST(('{{max_hour}}') AS INT)
    
    GROUP BY 
        1, 2, 3, 4, 5, 6, 7, 8, 9, 10
),

delivered_order_metrics AS (
    SELECT 
        DATE(cart_checkout_ts_ist) AS checkout_date,
        EXTRACT(HOUR FROM cart_checkout_ts_ist) AS checkout_hour,
        frontend_merchant_id,
        COUNT(DISTINCT order_id) AS delivered_order

    FROM
        dwh.fact_Sales_order_details
    
    WHERE
        order_create_dt_ist >= DATE('{{date_filter}}') - INTERVAL '1' DAY
        AND order_create_dt_ist < DATE('{{date_filter}}')
        AND is_internal_order = FALSE
        AND order_current_status = 'DELIVERED'
        
    GROUP BY
        1, 2, 3
)
    
SELECT 
    lom.at_date_ist,
    lom.day_of_week,
    lom.hour,
    lom.merchant_id,
    lom.merchant_name, 
    lom.city,
    lom.loss_type,
    lom.loss_reason,
    lom.lost_orders,
    lom.lost_gmv,
    dom.delivered_order AS delivered_order_count,
    
    CURRENT_TIMESTAMP AS insert_ts_ist

FROM
    lost_order_metrics
        AS lom
LEFT JOIN
    delivered_order_metrics
        AS dom
        ON lom.at_date_ist = dom.checkout_date
            AND lom.hour = dom.checkout_hour
            AND lom.merchant_id = dom.frontend_merchant_id
WHERE
    lom.city NOT IN ('NORTHGOA', 'SOUTHGOA')

