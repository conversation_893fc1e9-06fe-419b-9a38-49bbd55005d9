import os
import json
import logging
from contextlib import closing
from datetime import datetime, timedelta

import requests

import airflow
from airflow import DAG
from airflow.hooks.base_hook import BaseHook
from airflow.operators.bash_operator import BashOperator


from metrics_plugin import PapermillOperator
from kubernetes.client import models as k8s
from airflow.models import Variable

import pencilbox as pb

cwd = os.path.dirname(os.path.realpath(__file__))

k8s_executor_config = {
    "pod_override": k8s.V1Pod(
        spec=k8s.V1PodSpec(
            service_account_name="blinkit-prod-airflow-de-primary-eks-role",
            node_selector={"nodetype": "airflow-spot-general"},
            tolerations=[
                k8s.V1Toleration(
                    effect="NoSchedule",
                    key="service",
                    operator="Equal",
                    value="airflow-spot-general",
                ),
            ],
            containers=[
                k8s.V1Container(
                    image=Variable.get("************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable", "************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable"),
                    name="base",
                    volume_mounts=[
                        k8s.V1VolumeMount(
                            mount_path="/usr/local/airflow/plugins",
                            name="airflow-dags",
                            sub_path="airflow-de/plugins",
                        ),
                    ],
                    resources=k8s.V1ResourceRequirements(
                        limits={"cpu": 1, "memory": "4G"},
                        requests={"cpu": 0.5, "memory": "500M"},
                    ),
                )
            ],
        )
    )
}


k8s_executor_config_dbt = {
    "KubernetesExecutor": {
        "image": Variable.get("************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de/dbt:stable", "************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de/dbt:stable"),
        "node_selectors": {"nodetype": "airflow-spot-general"},
        "tolerations": [
            {
                "effect": "NoSchedule",
                "key": "service",
                "operator": "Equal",
                "value": "airflow-spot-general",
            }
        ],
        "volume_mounts": [
            {
                "mountPath": "/usr/local/airflow/plugins",
                "name": "airflow-dags",
                "subPath": "airflow-de/plugins",
            },
            {
                "mountPath": "/dbt-models",
                "name": "dbt-models",
                "subPath": "dbt-models",
            },
        ],
        "request_memory": "500M",
        "limit_memory": "4G",
        "request_cpu": 0.5,
        "limit_cpu": 1,
    }
}


def slack_failure_alert(context):
    dag_id = context.get("task_instance").dag_id
    run_id = context.get("run_id")
    task_id = context.get("task_instance").task_id
    log_url = context.get("task_instance").log_url.replace("http://", "https://")
    notebook_url = f"https://dse-commuter.grofer.io/view/{dag_id}/{run_id}/{task_id}.ipynb"

    owner = {"email": "<EMAIL>", "slack_id": "S03ST1ENERJ"}
    slack_id = owner["slack_id"]
    _owner = {
        "s": "s" if len(slack_id.split(" ")) > 1 else "",
        "slack_id": "<!subteam^{}>".format(slack_id),
    }

    slack_failure_msg = """
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner{s}*: {slack_id}
        *Log Url*: {log_url}
        *Notebook Url*: {notebook_url}
    """.format(
        dag_id=dag_id,
        run_id=run_id,
        task_id=task_id,
        s=_owner["s"],
        slack_id=_owner["slack_id"],
        log_url=log_url,
        notebook_url=notebook_url,
    )

    slack_alert_configs = [
        {"channel": "bl-dwh-alerts"},
    ]

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(
                channel=channel, text=slack_failure_msg, user="data-plumber"
            )
        except Exception as e:
            logging.warning(e)
            message = (
                f"<!subteam^S089D0GQRCJ> `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(
                channel="bl-data-alerts-p1", text=message, user="data-plumber"
            )

def failure_alerts(context):
    slack_failure_alert(context)

args = {
    "slack_id": "S03ST1ENERJ",
    "owner": "<EMAIL>",
    "on_failure_callback": failure_alerts,
    "start_date": datetime.strptime("2025-03-07T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": datetime.strptime("2025-10-01T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "retries": 1,
    "retry_delay": timedelta(minutes=1),
    "retry_exponential_backoff": True,
}

dag = DAG(
    dag_id="de_dwh_etl_trino_festive_hawkeye_v1",
    default_args=args,
    schedule_interval= None,
)

env = {}
default_env = {
    "AIRFLOW_DAG_ID": "de_dwh_etl_trino_festive_hawkeye_v1",
    "AIRFLOW_DAG_OWNER": "<EMAIL>",
}
env.update(default_env)


ingestion_event_task = PapermillOperator(
    task_id = "load_events_table_notebook",
    input_nb = "{cwd}/load_events_table_notebook.ipynb".format(cwd=cwd),
    output_nb="s3://grofers-prod-dse-sgp/airflow/dag_runs/de_dwh_etl_trino_festive_hawkeye_v1/{{ run_id }}/load_events_table_notebook.ipynb",
    parameters={
        "cwd": "/usr/local/airflow/dags/repo/dags/de/dwh/etl_trino/festive_hawkeye",
        "dag_run_date": "{{ data_interval_end }}",
    },
    env=env,
    dag=dag,
    executor_config=k8s_executor_config,
)

dbt_base = "cd /dbt-models/trino_dwh"
dbt_run_merchant = BashOperator(
    task_id="festive_hawkeye_merchant_grain",
    dag=dag,
    bash_command=dbt_base
      + " && dbt run --select tag:festive_hawkeye_merchant --exclude staging"
      + " --vars "
      + '\'{{"dag_id": "{}", "dag_run_id": "{}"}}\''.format('{{ dag.dag_id }}', '{{ run_id }}') \
      + " "
      + " --target prod",
    executor_config=k8s_executor_config_dbt,
)

dbt_run_keyword = BashOperator(
    task_id="festive_hawkeye_keyword_grain",
    dag=dag,
    bash_command=dbt_base
      + " && dbt run --select tag:festive_hawkeye_keyword --exclude staging"
      + " --vars "
      + '\'{{"dag_id": "{}", "dag_run_id": "{}"}}\''.format('{{ dag.dag_id }}', '{{ run_id }}') \
      + " "
      + " --target prod",
    executor_config=k8s_executor_config_dbt,
)

dbt_run_l0 = BashOperator(
    task_id="festive_hawkeye_l0_grain",
    dag=dag,
    bash_command=dbt_base
      + " && dbt run --select tag:festive_hawkeye_l0 --exclude staging"
      + " --vars "
      + '\'{{"dag_id": "{}", "dag_run_id": "{}"}}\''.format('{{ dag.dag_id }}', '{{ run_id }}') \
      + " "
      + " --target prod",
    executor_config=k8s_executor_config_dbt,
)

dbt_run_ptype = BashOperator(
    task_id="festive_hawkeye_ptype_grain",
    dag=dag,
    bash_command=dbt_base
      + " && dbt run --select tag:festive_hawkeye_ptype --exclude staging"
      + " --vars "
      + '\'{{"dag_id": "{}", "dag_run_id": "{}"}}\''.format('{{ dag.dag_id }}', '{{ run_id }}') \
      + " "
      + " --target prod",
    executor_config=k8s_executor_config_dbt,
)


update_event_task = PapermillOperator(
    task_id = "update_events_table_notebook",
    input_nb = "{cwd}/update_events_table_notebook.ipynb".format(cwd=cwd),
    output_nb="s3://grofers-prod-dse-sgp/airflow/dag_runs/de_dwh_etl_trino_festive_hawkeye_v1/{{ run_id }}/update_events_table_notebook.ipynb",
    parameters={
        "cwd": "/usr/local/airflow/dags/repo/dags/de/dwh/etl_trino/festive_hawkeye",
        "dag_run_date": "{{ data_interval_end }}",
    },
    env=env,
    dag=dag,
    executor_config=k8s_executor_config,
)


ingestion_event_task >> [dbt_run_merchant, dbt_run_keyword, dbt_run_l0, dbt_run_ptype] >> update_event_task
