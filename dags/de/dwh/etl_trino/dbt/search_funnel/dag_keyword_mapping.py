# -*- coding: utf-8 -*-

import sys, os
import logging
import pandas as pd
from contextlib import closing
from datetime import datetime, timedelta

import requests

import airflow
from airflow import DAG
from airflow.operators.bash_operator import BashOperator

from kubernetes.client import models as k8s
from airflow.models import Variable
import pencilbox as pb

sys.path.append(os.path.dirname(os.path.abspath(__file__)))



k8s_executor_config_dbt = {
    "KubernetesExecutor": {
        "image": Variable.get("************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de/dbt:stable", "************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de/dbt:stable"),
        "node_selectors": {"nodetype": "airflow-spot-general"},
        "tolerations": [
            {
                "effect": "NoSchedule",
                "key": "service",
                "operator": "Equal",
                "value": "airflow-spot-general",
            }
        ],
        "volume_mounts": [
            {
                "mountPath": "/usr/local/airflow/plugins",
                "name": "airflow-dags",
                "subPath": "airflow-de/plugins",
            },
            {
                "mountPath": "/dbt-models",
                "name": "dbt-models",
                "subPath": "dbt-models",
            },
        ],
        "request_memory": "500M",
        "limit_memory": "4G",
        "request_cpu": 0.5,
        "limit_cpu": 1,
    }
}


k8s_executor_config = {
    "pod_override": k8s.V1Pod(
        spec=k8s.V1PodSpec(
            service_account_name="blinkit-prod-airflow-de-primary-eks-role",
            node_selector={"nodetype": "airflow-spot-general"},
            tolerations=[
                k8s.V1Toleration(
                    effect="NoSchedule",
                    key="service",
                    operator="Equal",
                    value="airflow-spot-general",
                ),
            ],
            containers=[
                k8s.V1Container(
                    image= Variable.get("************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable", "************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable"),
                    name="base",
                    volume_mounts=[
                        k8s.V1VolumeMount(
                            mount_path="/usr/local/airflow/plugins",
                            name="airflow-dags",
                            sub_path="airflow-de/plugins",
                        ),
                    ],
                    resources=k8s.V1ResourceRequirements(
                        limits={"cpu": 1, "memory": "4G"},
                        requests={"cpu": 0.5, "memory": "500M"},
                    ),
                )
            ],
        )
    )
}

def slack_failure_alert(context):
    dag_id = context.get("task_instance").dag_id
    run_id = context.get("run_id")
    task_id = context.get("task_instance").task_id
    log_url = context.get("task_instance").log_url.replace("http://", "https://")

    owner = {"email": "<EMAIL>", "slack_id": "S03ST1ENERJ"}
    slack_id = owner["slack_id"]
    _owner = {
        "s": "s" if len(slack_id.split(" ")) > 1 else "",
        "slack_id": "<!subteam^{}>".format(slack_id),
    }

    slack_failure_msg = """
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner{s}*: {slack_id}
        *Log Url*: {log_url}
    """.format(
        dag_id=dag_id,
        run_id=run_id,
        task_id=task_id,
        s=_owner["s"],
        slack_id=_owner["slack_id"],
        log_url=log_url,
    )

    slack_alert_configs = [
        {"channel": "bl-dwh-alerts"},
    ]

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(
                channel=channel, text=slack_failure_msg, user="data-plumber"
            )
        except Exception as e:
            logging.warning(e)
            message = (
                f"<!subteam^S089D0GQRCJ> `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(
                channel="bl-data-alerts-p1", text=message, user="data-plumber"
            )


def failure_alerts(context):
    slack_failure_alert(context)

args = {
    "slack_id": "S03ST1ENERJ",
    "owner": "<EMAIL>",
    "on_failure_callback": failure_alerts,
    "start_date": datetime.strptime("2025-08-20T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": datetime.strptime("2025-09-30T00:00:00", "%Y-%m-%dT%H:%M:%S"),     # datetime.strptime("2025-09-30T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "retries": 1,
    "retry_delay": timedelta(seconds=60),
    "retry_exponential_backoff": True,
}

dag = DAG(
    dag_id = "de_dwh_etl_trino_dbt_search_keyword_ptype_weighted_v1",
    default_args = args,
    schedule_interval="05 23 * * *",
    tags=["dwh", "search", "dbt", "mapping"],
    render_template_as_native_obj=True,
    catchup = True
)

env = {}
default_env = {
    "AIRFLOW_DAG_ID": "de_dwh_etl_trino_dbt_search_keyword_ptype_weighted_v1",
    "AIRFLOW_DAG_OWNER": "<EMAIL>",
}

env.update(default_env)


# Maintenance
dbt_base = "cd /dbt-models/trino_dwh && dbt --cache-selected-only "

search_map_weighted = BashOperator(
    task_id = "search_ptype_npr",
    bash_command = dbt_base
    + " && dbt run --select tag:search_map_weighted "
    + ' --vars ' \
    + '\'{{"dag_id": "{}", "dag_run_id": "{}", "run_date": "{}"}}\''.format('{{ dag.dag_id }}', '{{ run_id }}', '{{ data_interval_end }}')     
    + " "
    + " --target prod",
    dag = dag,
    executor_config = k8s_executor_config_dbt,
)

## Maintenance
dbt_maint_table_config = [
    {
        "table_name":"dwh.map_search_keyword_ptype_weighted_stepA",
        "partition_key": "dag_run_dt_ist",
    },
    {
        "table_name":"dwh.map_search_keyword_ptype_weighted_stepB",
        "partition_key": "dag_run_dt_ist",
    },
    {
        "table_name":"dwh.map_search_keyword_ptype_weighted",
        "partition_key": "dag_run_dt_ist"
    },
]

for i in range(0,len(dbt_maint_table_config)):
    table_name = dbt_maint_table_config[i]["table_name"]
    partition_key = dbt_maint_table_config[i]["partition_key"] if "partition_key" in dbt_maint_table_config[i]  else ""

    model_vars = ' --vars ' + '\'{{"table_name": "{}","partition_key": "{}", "operation_type": "all", "dag_id": "{}", "dag_run_id": "{}"}}\''.format( table_name,partition_key, '{{ dag.dag_id }}', '{{ run_id }}')
    
    dbt_command = (
                dbt_base
                + "run-operation run_maintenance "
                + model_vars
                + " "
                + " --target prod"
            )
    
    dbt_run_maintenance = BashOperator(
        task_id = f"dbt_run_maintenance_{table_name}",
        bash_command = dbt_command,
        dag = dag,
        executor_config = k8s_executor_config_dbt,
    )

    search_map_weighted >> dbt_run_maintenance

search_map_weighted

if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
