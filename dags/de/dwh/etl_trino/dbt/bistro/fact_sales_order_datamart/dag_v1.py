# -*- coding: utf-8 -*-

import sys, os
from datetime import datetime, timedelta
import airflow
from airflow import DAG
from airflow.hooks.base_hook import BaseHook
from airflow.operators.bash_operator import BashOperator
from airflow.utils.trigger_rule import TriggerRule

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dag_config import k8s_executor_config_dbt, slack_failure_alert

args = {
    "slack_id": "S03ST1ENERJ",
    "owner": "<EMAIL>",
    "on_failure_callback": slack_failure_alert,
    "start_date": datetime.strptime("2024-08-23T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": datetime.strptime("2025-09-30T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "retries": 2,
    "retry_delay": timedelta(seconds=60),
    "retry_exponential_backoff": True,
}

dag = DAG(
    dag_id="de_dwh_etl_trino_dbt_bistro_fact_sales_datamart_v1",
    default_args=args,
    schedule_interval="17 0-20 * * *",
    catchup= False,
    max_active_runs= 1,
    tags=["dwh_bistro", "fact_sales_bistro"],
    render_template_as_native_obj=True,
)

env = {
    "AIRFLOW_DAG_ID": "de_dwh_etl_trino_dbt_bistro_fact_sales_datamart_v1",
    "AIRFLOW_DAG_OWNER": "<EMAIL>",
}

## Base Models
dbt_base = "cd /dbt-models/dwh_bistro && dbt"

## Parameters and Profiles
parameters_normal = """ --vars '{"run_date": "{{ data_interval_end }}", "partition_days_filter": "7", "hours_back": "6", "dag_id": "{{ dag.dag_id }}", "dag_run_id": "{{ run_id }}"}'"""

parameters_reconcile = """ --vars '{"run_date": "{{ data_interval_end }}", "partition_days_filter": "30", "hours_back": "23", "dag_id": "{{ dag.dag_id }}", "dag_run_id": "{{ run_id }}"}'"""

profiles = "  --target prod_p0"

dbt_command_sales = (
    'if [[ "$(echo {{ data_interval_end }} | cut -c 12-13)" == "20" ]]; then '
    + dbt_base
    + ' run --select tag:bistro_fact_sales --exclude staging'
    + parameters_reconcile
    + profiles
    + ' ; else '
    + dbt_base
    + ' run --select tag:bistro_fact_sales --exclude staging'
    + parameters_normal
    + profiles
    + ' ; fi'
)

dbt_command_rank = (
    'if [[ "$(echo {{ data_interval_end }} | cut -c 12-13)" == "20" ]]; then '
    + dbt_base
    + ' run --select tag:bistro_fact_sales_rank --exclude staging'
    + parameters_reconcile
    + profiles
    + ' ; else '
    + dbt_base
    + ' run --select tag:bistro_fact_sales_rank --exclude staging'
    + parameters_normal
    + profiles
    + ' ; fi'
)

dbt_command_rank_reconcile = (
     'if [[ "$(( 10#$(echo {{ data_interval_end }} | cut -c 9-10) % 3 ))" == "0" ]] && [[ "$(echo {{ data_interval_end }} | cut -c 12-13)" == "20" ]]; then '
    + dbt_base
    + ' run-operation run_cart_rank_reconcile'
    + profiles
    + ' ; else'\
    + ' exit 99'\
    + ' ; fi'
)

to_create_bistro_fact_sales_datamart = BashOperator(
    task_id="to_create_bistro_fact_sales_datamart",
    bash_command=dbt_command_sales,
    dag=dag,
    executor_config = k8s_executor_config_dbt,
)

to_update_bistro_cart_rank = BashOperator(
    task_id="to_update_bistro_cart_rank",
    bash_command=dbt_command_rank,
    dag=dag,
    executor_config = k8s_executor_config_dbt,
)

to_reconcile_bistro_cart_rank = BashOperator(
    task_id="to_reconcile_bistro_cart_rank",
    bash_command=dbt_command_rank_reconcile,
    dag=dag,
    executor_config = k8s_executor_config_dbt,
)

to_create_bistro_fact_sales_datamart >> to_update_bistro_cart_rank >> to_reconcile_bistro_cart_rank

# table details for capturing while running maintenance via dbt
dbt_maint_table_config = [
    {
        "table_name":"bistro_etls.fact_sales_order_details_bistro",
        "partition_key": "order_create_dt_ist",
    },
    {
        "table_name":"bistro_etls.fact_sales_order_item_details_bistro",
        "partition_key": "order_create_dt_ist",
    },
]


# for table_name in ["fact_sales_order_details_bistro", "fact_sales_order_item_details_bistro"]:
for i in range(0,len(dbt_maint_table_config)):

    table_name = dbt_maint_table_config[i]["table_name"]
    partition_key = dbt_maint_table_config[i]["partition_key"] if "partition_key" in dbt_maint_table_config[i]  else ""

    model_vars = '\'{{"table_name": "{}","partition_key": "{}", "operation_type": "all", "dag_id": "{}", "dag_run_id": "{}"}}\''.format(table_name, partition_key, '{{ dag.dag_id }}', '{{ run_id }}')

    dbt_command = (
        'hr_runs_valid=(14) ' \
        + ' && if [[ ${hr_runs_valid[*]} =~ (^|[[:space:]])"$(echo {{ ts }} | cut -c 12-13 )"($|[[:space:]]) ]]'\
        + ' ; then'\
        + ' cd /dbt-models/dwh_bistro && dbt run-operation run_maintenance ' \
        + ' --vars ' \
        + model_vars \
        
        
        + ' --target prod'\
        + ' ; else'\
        + ' exit 99'\
        + ' ; fi'
    )
    
    dbt_run_maintenance = BashOperator(
        task_id = f"dbt_run_maintenance_{table_name}",
        bash_command = dbt_command,
        dag = dag,
        executor_config = k8s_executor_config_dbt,
        trigger_rule=TriggerRule.NONE_FAILED,
    )

    to_reconcile_bistro_cart_rank >> dbt_run_maintenance

if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
