from kubernetes.client import models as k8s
from airflow.models import Variable
import pencilbox as pb
import logging

k8s_executor_config_dbt = {
    "KubernetesExecutor": {
        "image": Variable.get("442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de/dbt:stable", "442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de/dbt:stable"),
        "node_selectors": {"nodetype": "airflow-spot-general"},
        "tolerations": [
            {
                "effect": "NoSchedule",
                "key": "service",
                "operator": "Equal",
                "value": "airflow-spot-general",
            }
        ],
        "volume_mounts": [
            {
                "mountPath": "/usr/local/airflow/plugins",
                "name": "airflow-dags",
                "subPath": "airflow-de/plugins",
            },
            {
                "mountPath": "/dbt-models",
                "name": "dbt-models",
                "subPath": "dbt-models",
            },
        ],
        "request_memory": "500M",
        "limit_memory": "4G",
        "request_cpu": 0.5,
        "limit_cpu": 1,
    }
}

def slack_failure_alert(context):
    dag_id = context.get("task_instance").dag_id
    run_id = context.get("run_id")
    task_id = context.get("task_instance").task_id
    log_url = context.get("task_instance").log_url.replace("http://", "https://")

    owner = {"email": "<EMAIL>", "slack_id": "S03ST1ENERJ"}
    slack_id = owner["slack_id"]
    _owner = {
        "s": "s" if len(slack_id.split(" ")) > 1 else "",
        "slack_id": "<!subteam^{}>".format(slack_id),
    }

    slack_failure_msg = """
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner{s}*: {slack_id}
        *Log Url*: {log_url}
    """.format(
        dag_id=dag_id,
        run_id=run_id,
        task_id=task_id,
        s=_owner["s"],
        slack_id=_owner["slack_id"],
        log_url=log_url,
    )

    slack_alert_configs = [
        {"channel": "bl-dwh-alerts"},
    ]

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(
                channel=channel, text=slack_failure_msg, user="data-plumber"
            )
        except Exception as e:
            logging.warning(e)
            message = (
                f"<!subteam^S089D0GQRCJ> `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(
                channel="bl-data-alerts-p1", text=message, user="data-plumber"
            )
