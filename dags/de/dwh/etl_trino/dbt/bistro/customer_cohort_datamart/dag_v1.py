# -*- coding: utf-8 -*-

import sys, os
from datetime import datetime, timedelta
import airflow
from airflow import DAG
from airflow.hooks.base_hook import BaseHook
from airflow.operators.bash_operator import BashOperator
from kubernetes.client import models as k8s
from airflow.models import Variable
from airflow.utils.trigger_rule import TriggerRule

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dag_config import k8s_executor_config_dbt, slack_failure_alert

args = {
    "slack_id": "S03ST1ENERJ",
    "owner": "<EMAIL>",
    "on_failure_callback": slack_failure_alert,
    "start_date": datetime.strptime("2025-08-28T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": datetime.strptime("2025-09-30T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "retries": 1,
    "retry_delay": timedelta(seconds=60),
    "retry_exponential_backoff": True,
    "executor_config": k8s_executor_config_dbt,
}

dag = DAG(
    dag_id="de_dwh_etl_trino_dbt_bistro_customer_cohort_datamart_v1",
    default_args=args,
    schedule_interval="25 2-21 * * *",
    tags=["dwh", "bistro", "customer_cohort"],
    render_template_as_native_obj=True,
    catchup = True
)

env = {
    "AIRFLOW_DAG_ID": "de_dwh_etl_trino_dbt_bistro_customer_cohort_datamart_v1",
    "AIRFLOW_DAG_OWNER": "<EMAIL>",
}

dbt_base = "cd /dbt-models/dwh_bistro"
max_hour = " && dbt --cache-selected-only run -m event_fact_max_hour_bistro"
cart_bifurcation_models = " && dbt --cache-selected-only run --select tag:cr_bifurcation_metrics_bistro"
dslo_models = " && dbt --cache-selected-only run --select tag:dslo_metrics_bistro"
dslo_cart_rank_metrics_map = " && dbt --cache-selected-only run --select tag:dslo_cr_map_bistro"

dummy_task = BashOperator(
    task_id = 'dummy_task',
    bash_command = 'date',
    dag = dag,
    trigger_rule=TriggerRule.NONE_FAILED,
    executor_config=k8s_executor_config_dbt,
)

max_hour_task = BashOperator(
    task_id="max_hour_task",
    bash_command=dbt_base
        + max_hour
        + ' --vars \'{"run_date": "{{ data_interval_end }}", "dag_id": "{{ dag.dag_id }}", "dag_run_id": "{{ run_id }}"}\''
        + " "
        + " --target prod",
    dag=dag,
    executor_config=k8s_executor_config_dbt,
)

dslo_cart_rank_base_map_task = BashOperator(
    task_id="dslo_base_map_task",
    bash_command = (
            'if [[ "$(echo {{ data_interval_end }} | cut -c 12-13)" == "02" || "$(echo {{ data_interval_end }} | cut -c 12-13)" == "04" ]]'\
            + ' ; then '\
            + dbt_base
            + dslo_cart_rank_metrics_map
            + ' --vars \'{"run_date": "{{ data_interval_end }}", "dag_id": "{{ dag.dag_id }}", "dag_run_id": "{{ run_id }}"}\''
            + " " \
            + " --target prod" \
            + ' ; else' \
            + ' exit 99' \
            + ' ; fi'
    ),
    dag=dag,
    executor_config=k8s_executor_config_dbt,
)

cart_bifurcation_models_task = BashOperator(
    task_id="cart_bifurcation_models_task",
    bash_command=dbt_base
        + cart_bifurcation_models
        + ' --vars \'{"run_date": "{{ data_interval_end }}", "dag_id": "{{ dag.dag_id }}", "dag_run_id": "{{ run_id }}"}\''
        + " "
        + " --target prod",
    dag=dag,
    executor_config=k8s_executor_config_dbt,
)

dslo_models_task = BashOperator(
    task_id="dslo_models_task",
    bash_command=dbt_base
        + dslo_models
        + ' --vars \'{"run_date": "{{ data_interval_end }}", "dag_id": "{{ dag.dag_id }}", "dag_run_id": "{{ run_id }}"}\''
        + " "
        + " --target prod",
    dag=dag,
    trigger_rule=TriggerRule.NONE_FAILED,
    executor_config=k8s_executor_config_dbt,
)

process_completed = BashOperator(
    task_id = 'process_completed',
    bash_command = 'date',
    dag = dag,
    trigger_rule=TriggerRule.NONE_FAILED,
    executor_config=k8s_executor_config_dbt,
)

[max_hour_task, dslo_cart_rank_base_map_task] >> dummy_task >> [cart_bifurcation_models_task, dslo_models_task] >> process_completed

## MAINTENANCE FLOW ##

dbt_maint_table_config = [
    {
        "table_name":"bistro_etls.agg_daily_cart_bifurcation_metrics_bistro",
        "partition_key": "at_date_ist",
    },
    {
        "table_name":"bistro_etls.agg_daily_dslo_metrics_bistro",
        "partition_key": "at_date_ist",
    },
    {
        "table_name":"bistro_etls.cart_rank_bifurcation_log_bistro",
        "partition_key": "snapshot_date_ist"
    },
     {
        "table_name":"bistro_etls.dslo_bucket_mapping_log_bistro",
        "partition_key": "snapshot_date_ist"
    },
]

for i in range(0,len(dbt_maint_table_config)):
    table_name = dbt_maint_table_config[i]["table_name"]
    partition_key = dbt_maint_table_config[i]["partition_key"] if "partition_key" in dbt_maint_table_config[i]  else ""

    model_vars = ' --vars ' + '\'{{"table_name": "{}", "partition_key": "{}","operation_type": "all", "dag_id": "{}", "dag_run_id": "{}"}}\''.format(table_name, partition_key,'{{ dag.dag_id }}', '{{ run_id }}')
    
    dbt_command = (
                'if [[ "$(echo {{ data_interval_end }} | cut -c 12-13)" == "12" ]]'\
                + ' ; then '\
                + "cd /dbt-models/trino_dwh && dbt "
                + "run-operation run_maintenance "
                + model_vars
                + " "
                + " --target prod"
                + ' ; else' \
                + ' exit 99' \
                + ' ; fi'
            )
    
    dbt_run_maintenance = BashOperator(
        task_id = f"dbt_run_maintenance_{table_name[4:]}",
        bash_command = dbt_command,
        dag = dag,
        executor_config = k8s_executor_config_dbt,
    )
    process_completed >> dbt_run_maintenance

if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
