# -*- coding: utf-8 -*-

import sys, os
from datetime import datetime, timedelta
import airflow
from airflow import DAG
from airflow.hooks.base_hook import BaseHook
from airflow.operators.bash_operator import BashOperator
from kubernetes.client import models as k8s
from airflow.models import Variable
import pencilbox as pb

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import k8s_executor_config_dbt, k8s_executor_config

def slack_failure_alert(context):
    dag_id = context.get("task_instance").dag_id
    run_id = context.get("run_id")
    task_id = context.get("task_instance").task_id
    log_url = context.get("task_instance").log_url.replace("http://", "https://")

    owner = {"email": "<EMAIL>", "slack_id": "S03ST1ENERJ"}
    slack_id = owner["slack_id"]
    _owner = {
        "s": "s" if len(slack_id.split(" ")) > 1 else "",
        "slack_id": "<!subteam^{}>".format(slack_id),
    }

    slack_failure_msg = """
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner{s}*: {slack_id}
        *Log Url*: {log_url}
    """.format(
        dag_id=dag_id,
        run_id=run_id,
        task_id=task_id,
        s=_owner["s"],
        slack_id=_owner["slack_id"],
        log_url=log_url,
    )

    slack_alert_configs = [
        {"channel": "bl-dwh-alerts"},
    ]

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(
                channel=channel, text=slack_failure_msg, user="data-plumber"
            )
        except Exception as e:
            logging.warning(e)
            message = (
                f"<!subteam^S089D0GQRCJ> `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(
                channel="bl-data-alerts-p1", text=message, user="data-plumber"
            )

args = {
    "slack_id": "S03ST1ENERJ",
    "owner": "<EMAIL>",
    "on_failure_callback": slack_failure_alert,
    "start_date": datetime.strptime("2023-08-10T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": datetime.strptime("2025-09-30T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "retries": 1,
    "retry_delay": timedelta(seconds=60),
    "retry_exponential_backoff": True,
}

dag = DAG(
    dag_id="de_dwh_etl_trino_dbt_agg_daily_item_entity_grc_model_v1",
    default_args=args,
    schedule_interval="5 4 1,11,21 * *",
    tags=["dwh"],
    render_template_as_native_obj=True,
)

env = {
    "AIRFLOW_DAG_ID": "de_dwh_etl_trino_dbt_agg_daily_item_entity_grc_model_v1",
    "AIRFLOW_DAG_OWNER": "<EMAIL>",
}

dbt_base = "cd /dbt-models/trino_dwh"

grc_model = BashOperator(
    task_id="grc_model",
    bash_command=dbt_base
      + " && dbt --cache-selected-only run --select tag:item_entity_grc_model --exclude staging"
      + ' --vars \'{"run_date": "{{ data_interval_end }}", "dag_id": "{{ dag.dag_id }}", "dag_run_id": "{{ run_id }}"}\''
      + " "
      + ' --target prod',
    dag=dag,
    executor_config=k8s_executor_config_dbt,
)


dbt_run_maintenance = BashOperator(
    task_id = "dbt_run_maintenance",
    bash_command = dbt_base
    + "&& dbt --cache-selected-only run-operation run_maintenance "
    + ' --vars \'{"table_name": "dwh.agg_daily_item_entity_grc_details", "dag_id": "{{ dag.dag_id }}", "dag_run_id": "{{ run_id }}" }\''
    + " "
    + " --target prod",
    dag = dag,
    executor_config = k8s_executor_config_dbt,
)

grc_model >> dbt_run_maintenance


if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
