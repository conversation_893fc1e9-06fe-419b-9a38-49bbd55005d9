from kubernetes.client import models as k8s
from airflow.models import Variable
import pencilbox as pb
import logging

## To import kubernetes executor for dbT
k8s_executor_config_dbt = {
    "KubernetesExecutor": {
        "image": Variable.get("************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de/dbt:stable", "************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de/dbt:stable"),
        "node_selectors": {"nodetype": "airflow-spot-general"},
        "tolerations": [
            {
                "effect": "NoSchedule",
                "key": "service",
                "operator": "Equal",
                "value": "airflow-spot-general",
            }
        ],
        "volume_mounts": [
            {
                "mountPath": "/usr/local/airflow/plugins",
                "name": "airflow-dags",
                "subPath": "airflow-de/plugins",
            },
            {
                "mountPath": "/dbt-models",
                "name": "dbt-models",
                "subPath": "dbt-models",
            },
        ],
        "request_memory": "500M",
        "limit_memory": "2G",
        "request_cpu": 0.5,
        "limit_cpu": 1,
    }
}

k8s_executor_config = {
    "pod_override": k8s.V1Pod(
        spec=k8s.V1PodSpec(
            service_account_name="blinkit-prod-airflow-de-primary-eks-role",
            node_selector={"nodetype": "airflow-spot-general"},
            tolerations=[
                k8s.V1Toleration(
                    effect="NoSchedule",
                    key="service",
                    operator="Equal",
                    value="airflow-spot-general",
                ),
            ],
            containers=[
                k8s.V1Container(
                    image= Variable.get("************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable", "************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable"),
                    name="base",
                    volume_mounts=[
                        k8s.V1VolumeMount(
                            mount_path="/usr/local/airflow/plugins",
                            name="airflow-dags",
                            sub_path="airflow-de/plugins",
                        ),
                    ],
                    resources=k8s.V1ResourceRequirements(
                        limits={"cpu": 1, "memory": "14G"},
                        requests={"cpu": 0.5, "memory": "12G"},
                    ),
                )
            ],
        )
    )
}