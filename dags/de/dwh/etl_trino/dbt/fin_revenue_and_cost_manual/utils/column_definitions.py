cod_collection_sheet_column_dtypes = [
    {
        "name": "start_date",
        "type": "DATE",
        "description": "From date to which formulas applicable",
    },
    {
        "name": "end_date",
        "type": "DATE",
        "description": "To date to which formulas applicable",
    },
    {
        "name": "cod_collection_charge_perc",
        "type": "REAL",
        "description": "COD collection charge percentage for the Outlet",
    },
]

rider_onboarding_sheet_column_dtypes = [
    {
        "name": "start_date",
        "type": "DATE",
        "description": "From date to which formulas applicable",
    },
    {
        "name": "end_date",
        "type": "DATE",
        "description": "To date to which formulas applicable",
    },
    {
        "name": "rider_onboarding_bgv_pp_cost",
        "type": "REAL",
        "description": "Rider onboading background verification cost per person",
    },
    {
        "name": "rider_onboarding_asset_pp_cost",
        "type": "REAL",
        "description": "Rider onboading asset cost per person",
    },
    {
        "name": "rider_tech_support_fix_cost",
        "type": "REAL",
        "description": "Rider onboading fix cost per person",
    },
]

strategic_sheet_column_dtypes = [
    {
        "name": "start_date",
        "type": "DATE",
        "description": "From date to which formulas applicable",
    },
    {
        "name": "end_date",
        "type": "DATE",
        "description": "To date to which formulas applicable",
    },
    {
        "name": "strategic_partner_cost_perc",
        "type": "REAL",
        "description": "Strategic Partner Cost percentage for the Outlet",
    },
]

hyperpure_sheet_column_dtypes = [
    {
        "name": "start_date",
        "type": "DATE",
        "description": "From date to which formulas applicable",
    },
    {
        "name": "end_date",
        "type": "DATE",
        "description": "To date to which formulas applicable",
    },
    {
        "name": "city",
        "type": "VARCHAR",
        "description": "Name of the city",
    },
    {
        "name": "hyperpure_fleet_cost_monthly",
        "type": "REAL",
        "description": "Hyperpure cost related to the outlet",
    },
    {
        "name": "hyperpure_be_cost_variable",
        "type": "REAL",
        "description": "Hyperpure Backend Variable Cost related to the outlet",
    },
    {
        "name": "hyperpure_be_cost_fixed",
        "type": "REAL",
        "description": "Hyperpure Backend Fixed Cost related to the outlet",
    },
    {
        "name": "hyperpure_packaging_cost_monthly",
        "type": "REAL",
        "description": "Hyperpure packaging cost related to the outlet",
    },
]

post_ebo_sheet_column_dtypes = [
    {
        "name": "start_date",
        "type": "DATE",
        "description": "From date to which formulas applicable",
    },
    {
        "name": "end_date",
        "type": "DATE",
        "description": "To date to which formulas applicable",
    },
    {
        "name": "tech_infra_cost",
        "type": "REAL",
        "description": "Tech infra cost for the outlet",
    },
    {
        "name": "fixed_and_overhead_cost",
        "type": "REAL",
        "description": "Fixed and overhead cost for the outlet",
    },
    {
        "name": "allocated_cost",
        "type": "REAL",
        "description": "Allocated daily cost for the outlet",
    },
]

marketing_cost_sheet_column_dtypes = [
    {
        "name": "start_date",
        "type": "DATE",
        "description": "From date to which formulas applicable",
    },
    {
        "name": "end_date",
        "type": "DATE",
        "description": "To date to which formulas applicable",
    },
    {
        "name": "atl_btl_cost",
        "type": "REAL",
        "description": "Offline marketing cost(absolute value) for pnl",
    },
    {
        "name": "whatsapp_cost",
        "type": "REAL",
        "description": "Whatsapp cost(absolute value) for pnl",
    },
    {
        "name": "crystal_clicks_cost",
        "type": "REAL",
        "description": "Crystal clicks cost(absolute value) for pnl",
    },
    {
        "name": "appsflyer_cost",
        "type": "REAL",
        "description": "Appsflyer cost(absolute value) for pnl",
    },
    {
        "name": "video_production_cost",
        "type": "REAL",
        "description": "Video Production cost(absolute value) for pnl",
    },
]

fc_mapping_column_dtypes= [
    {
        "name": "outlet_name",
        "type": "VARCHAR",
        "description": "Name of the outlet warehouse/scrap/infra",
    },
    {
        "name": "city",
        "type": "VARCHAR",
        "description": "City where outlet is located",
    },
    {
        "name": "retail_outlet_id",
        "type": "INTEGER",
        "description": "Outlet id of the store",
    },
    {
        "name": "facility_id",
        "type": "INTEGER",
        "description": "Facility id of the forntend outlet for which its mapping is created",
    }
]

pnl_other_income_sheet_column_dtypes = [
    {
        "name": "from_date",
        "type": "DATE",
        "description": "From date to which formulas applicable",
    },
    {
        "name": "to_date",
        "type": "DATE",
        "description": "To date to which formulas applicable",
    },
    {
        "name": "total_visibility_income_percentage",
        "type": "REAL",
        "description": "Total Visibility Income Percentage",
    },
    {
        "name": "ad_income_percentage",
        "type": "REAL",
        "description": "Ad Income Percentage",
    },
    {
        "name": "search_income_percentage",
        "type": "REAL",
        "description": "Search Income Percentage",
    },
    {
        "name": "tot_income_percentage",
        "type": "REAL",
        "description": "ToT Income Percentage",
    },
]

one_timer_cost_sheet_column_dtypes = [
    {
        "name": "start_date",
        "type": "DATE",
        "description": "From date to which formulas applicable",
    },
    {
        "name": "end_date",
        "type": "DATE",
        "description": "To date to which formulas applicable",
    },
    {
        "name": "totes_and_crates",
        "type": "DOUBLE",
        "description": "Totes and Crates cost",
    },
    {
        "name": "ds_closure_cost",
        "type": "DOUBLE",
        "description": "Dark Store Closure cost",
    },
    {
        "name": "separator_cost",
        "type": "DOUBLE",
        "description": "Separator cost",
    },
    {
        "name": "paas_cost",
        "type": "DOUBLE",
        "description": "Cost incurred for printer services",
    },
]

pnl_weekly_projections_sheet_column_dtypes = [
    {
        "name": "snapshot_date_ist", 
        "type": "DATE",
        "description": "Snapshot date"
    },
    {
        "name": "total_delivered_orders", 
        "type": "REAL",
        "description": "Number of Delivered Orders"
    },
    {
        "name": "new_customers_orders",
        "type": "REAL",
        "description": "Orders placed by New customers",
    },
    {
        "name": "repeat_customers_orders",
        "type": "REAL",
        "description": "Orders placed by Old customers",
    },
    {
        "name": "total_sold_units",
        "type": "REAL",
        "description": "Number of units sold",
    },
    {
        "name": "gmv_incl_dcsurge",
        "type": "REAL",
        "description": "GMV including DS Surge Charge",
    },
    {
        "name": "gmv_excl_dcsurge",
        "type": "REAL",
        "description": "GMV excluding DS Surge Charge",
    },
    {
        "name": "gross_margin_excl_tax",
        "type": "REAL",
        "description": "Gross Margin excluding tax",
    },
    {
        "name": "strategic_partner_cost",
        "type": "REAL",
        "description": "Startegic Partner Cost",
    },
    {
        "name": "tot_monet",
        "type": "REAL",
        "description": "ToT income",
    },
    {
        "name": "delivery_charges_excl_tax",
        "type": "REAL",
        "description": "Delivery Charges excluding tax",
    },
    {
        "name": "total_slot_charges_excl_tax",
        "type": "REAL",
        "description": "Total Slot Charges excluding tax",
    },
    {
        "name": "total_convenience_charge_excl_tax",
        "type": "REAL",
        "description": "Total Convenience Charges excluding tax",
    },
    {
        "name": "search_income",
        "type": "REAL",
        "description": "Search income",
    },
    {
        "name": "ad_monet",
        "type": "REAL",
        "description": "Ad Income",
    },
    {
        "name": "gross_dump_total",
        "type": "REAL",
        "description": "Gross Dump Total",
    },
    {
        "name": "total_positive_dump_adj",
        "type": "REAL",
        "description": "Total Positive Dump adjustment",
    },
    {
        "name": "return_loss",
        "type": "REAL",
        "description": "Return Loss",
    },
    {
        "name": "pilferage",
        "type": "REAL",
        "description": "Pilferage",
    },
    {
        "name": "bs_damage_amt",
        "type": "REAL",
        "description": "Bad Stock damage amount",
    },
    {
        "name": "wh_variance",
        "type": "REAL",
        "description": "Warehouse variance",
    },
    {
        "name": "total_esto_loss",
        "type": "REAL",
        "description": "Total ESTO Loss",
    },
    {
        "name": "total_pg_charges",
        "type": "REAL",
        "description": "Total PG Charges",
    },
    {
        "name": "total_discount_amount",
        "type": "REAL",
        "description": "Total Discount Amount",
    },
    {
        "name": "last_mile_cost",
        "type": "REAL",
        "description": "Last Mile Cost",
    },
    {
        "name": "total_packaging_cost",
        "type": "REAL",
        "description": "Total Packaging Cost",
    },
    {
        "name": "fleet_cost",
        "type": "REAL",
        "description": "Fleet Cost",
    },
    {
        "name": "fnv_putaway_cost",
        "type": "REAL",
        "description": "FnV Putaway Cost",
    },
    {
        "name": "management_overhead_cost",
        "type": "REAL",
        "description": "Management Overhead Cost",
    },
    {
        "name": "commission_cost",
        "type": "REAL",
        "description": "Commission Cost",
    },
    {
        "name": "total_cash_loss",
        "type": "REAL",
        "description": "Total Cash Loss",
    },
    {
        "name": "refunds",
        "type": "REAL",
        "description": "Cost incurred in Refunds",
    },
    {
        "name": "rider_onboarding_fee",
        "type": "REAL",
        "description": "Rider Onboarding Fee",
    },
    {
        "name": "rider_support_cost",
        "type": "REAL",
        "description": "Rider Support Cost",
    },
    {
        "name": "outsource_manpower_cost",
        "type": "REAL",
        "description": "Outsource Manpower Cost",
    },
    {
        "name": "ds_gna_cost",
        "type": "REAL",
        "description": "Dark Store G&A Cost",
    },
    {
        "name": "ds_rent",
        "type": "REAL",
        "description": "Dark Store Rent",
    },
    {
        "name": "ds_min_guarantee",
        "type": "REAL",
        "description": "Dark Store Minimum Guarantee",
    },
    {
        "name": "total_cpc_backend_cost",
        "type": "REAL",
        "description": "Total Backend Cost",
    },
    {
        "name": "total_digital_marketing_cost",
        "type": "REAL",
        "description": "Total Digital Marketing Cost",
    },
    {
        "name": "total_offline_marketing_cost",
        "type": "REAL",
        "description": "Total Offline Marketing Cost",
    },
    {
        "name": "total_video_production_cost",
        "type": "REAL",
        "description": "Total Video Production Cost",
    },
    {
        "name": "tech_infra_daily_cost",
        "type": "REAL",
        "description": "Tech Infra Cost",
    },
    {
        "name": "fixed_and_overhead_daily_cost",
        "type": "REAL",
        "description": "Fixed & Overhead Cost",
    },
    {
        "name": "allocated_daily_cost",
        "type": "REAL",
        "description": "Allocated Daily Cost",
    },
    {
        "name": "totes_and_crates",
        "type": "REAL",
        "description": "One Timer Cost",
    },
    {
        "name": "fleet_operations_leads",
        "type": "DOUBLE",
        "description": "Fleet Operations Leads Cost",
    },
    {
        "name": "fleet_operations_team",
        "type": "DOUBLE",
        "description": "Fleet Operations Team Cost",
    },
    {
        "name": "people_cost",
        "type": "DOUBLE",
        "description": "People cost",
    },
    {
        "name": "backend_support_cost_blinkit",
        "type": "DOUBLE",
        "description": "Backend Support Cost for Blinkit",
    },
    {
        "name": "backend_support_cost_hyperpure",
        "type": "DOUBLE",
        "description": "Backend Support Cost for Hyperpure",
    },
    {
        "name": "intercity_fleet_cost",
        "type": "DOUBLE",
        "description": "Cost of Intercity Fleet Transfers",
    },
    {
        "name": "b2b_transfer_fleet_cost",
        "type": "DOUBLE",
        "description": "Cost of B2B Fleet Transfers",
    },
    {
        "name": "backend_cost_variable",
        "type": "DOUBLE",
        "description": "Backend Support Cost for Hyperpure",
    }
]

other_salary_components_sheet_column_dtypes = [
    {
        "name": "start_date",
        "type": "DATE",
        "description": "From date to which formulas applicable",
    },
    {
        "name": "end_date",
        "type": "DATE",
        "description": "To date to which formulas applicable",
    },
    {
        "name": "outlet_id",
        "type": "INTEGER",
        "description": "Outlet id of the store",
    },
    {
        "name": "fleet_operations_leads",
        "type": "DOUBLE",
        "description": "Fleet Operations Leads Cost",
    },
    {
        "name": "fleet_operations_team",
        "type": "DOUBLE",
        "description": "Fleet Operations Team Cost",
    },
    {
        "name": "people_cost",
        "type": "DOUBLE",
        "description": "People cost",
    },
    {
        "name": "backend_support_cost_blinkit",
        "type": "DOUBLE",
        "description": "Backend Support Cost for Blinkit",
    },
    {
        "name": "backend_support_cost_hyperpure",
        "type": "DOUBLE",
        "description": "Backend Support Cost for Hyperpure",
    }
]

pg_charge_zppl_column_dtypes = [
    {
        "name": "start_date",
        "type": "DATE",
        "description": "From date to which formulas applicable",
    },
    {
        "name": "end_date",
        "type": "DATE",
        "description": "To date to which formulas applicable",
    },
    {
        "name": "total_zppl_charge",
        "type": "REAL",
        "description": "ZPPL charges related to outlet",
    }
]
