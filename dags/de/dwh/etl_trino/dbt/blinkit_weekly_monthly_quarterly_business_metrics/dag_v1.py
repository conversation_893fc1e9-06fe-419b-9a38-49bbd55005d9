import sys,os
import logging
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.bash_operator import <PERSON><PERSON><PERSON>perator
from airflow.operators.python_operator import PythonOperator
from kubernetes.client import models as k8s
from airflow.models import Variable
from airflow.utils.trigger_rule import TriggerRule
import pencilbox as pb


sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import k8s_executor_config

def slack_failure_alert(context):
    dag_id = context.get("task_instance").dag_id
    run_id = context.get("run_id")
    task_id = context.get("task_instance").task_id
    log_url = context.get("task_instance").log_url.replace("http://", "https://")

    owner = {"email": "<EMAIL>", "slack_id": "S03ST1ENERJ"}
    slack_id = owner["slack_id"]
    _owner = {
        "s": "s" if len(slack_id.split(" ")) > 1 else "",
        "slack_id": "<!subteam^{}>".format(slack_id),
    }

    slack_failure_msg = """
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner{s}*: {slack_id}
        *Log Url*: {log_url}
    """.format(
        dag_id=dag_id,
        run_id=run_id,
        task_id=task_id,
        s=_owner["s"],
        slack_id=_owner["slack_id"],
        log_url=log_url,
    )

    slack_alert_configs = [
        {"channel": "bl-dwh-alerts"},
    ]

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(
                channel=channel, text=slack_failure_msg, user="data-plumber"
            )
        except Exception as e:
            logging.warning(e)
            message = (
                f"<!subteam^S089D0GQRCJ> `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(
                channel="bl-dwh-alerts", text=message, user="data-plumber"
            )


args = {
    "slack_id": "S03ST1ENERJ",
    "owner": "<EMAIL>",
    "on_failure_callback": slack_failure_alert,
    "start_date": datetime.strptime("2023-11-13T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": datetime.strptime("2025-08-20T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "retries": 1,
    "retry_delay": timedelta(seconds=90),
    "retry_exponential_backoff" :True,
}

dag = DAG(
    dag_id="de_dwh_etl_trino_dbt_blinkit_weekly_monthly_quarterly_business_metrics",
    default_args=args,
    schedule_interval="33 00 * * *",
    catchup= False,
    max_active_runs= 1,
)

dbt_base = "cd /dbt-models/trino_dwh && dbt --cache-selected-only "

model_vars_week = '\'{{"run_date": "{}","agg_level": "{}", "dag_id": "{}", "dag_run_id": "{}"}}\''.format( "{{ data_interval_end }}", "week", '{{ dag.dag_id }}', '{{ run_id }}')
dbt_run_weekly = BashOperator(
    task_id="dbt_run_weekly_metrics",
    dag=dag,
    bash_command=dbt_base
      + "run --models +weekly_business_metrics --exclude staging"
      + " --vars "
      + model_vars_week
      + " "
      + " --target prod",
    executor_config=k8s_executor_config,
)

model_vars_month = '\'{{"run_date": "{}","agg_level": "{}", "dag_id": "{}", "dag_run_id": "{}"}}\''.format( "{{ data_interval_end }}", "month", '{{ dag.dag_id }}', '{{ run_id }}')
dbt_run_monthly = BashOperator(
    task_id="dbt_run_monthly_metrics",
    dag=dag,
    bash_command=dbt_base
      + "run --select tag:bv_monthly"
      + " --vars "
      + model_vars_month
      + " "
      + " --target prod",
    executor_config=k8s_executor_config,
)

model_vars_quarter = '\'{{"run_date": "{}","agg_level": "{}", "dag_id": "{}", "dag_run_id": "{}"}}\''.format( "{{ data_interval_end }}", "quarter", '{{ dag.dag_id }}', '{{ run_id }}')
dbt_run_quarterly = BashOperator(
    task_id="dbt_run_quarterly_metrics",
    dag=dag,
    bash_command=dbt_base
      + "run --select tag:bv_quarterly"
      + " --vars "
      + model_vars_quarter
      + " "
      + " --target prod",
    executor_config=k8s_executor_config,
)

# zone split call - start 

model_vars_zone_wtd = '\'{{"run_date": "{}", "dag_id": "{}", "dag_run_id": "{}"}}\''.format( "{{ data_interval_end }}", '{{ dag.dag_id }}', '{{ run_id }}')
dbt_run_city_zone_wtd = BashOperator(
    task_id="dbt_run_city_zone_wtd",
    dag=dag,
    bash_command=dbt_base
      + "run --select tag:city_zone_split__wtd"
      + " --vars "
      + model_vars_zone_wtd
      + " "
      + " --target prod",
    executor_config=k8s_executor_config,
)

model_vars_zone_mtd = '\'{{"run_date": "{}", "dag_id": "{}", "dag_run_id": "{}"}}\''.format( "{{ data_interval_end }}", '{{ dag.dag_id }}', '{{ run_id }}')
dbt_run_city_zone_mtd = BashOperator(
    task_id="dbt_run_city_zone_mtd",
    dag=dag,
    bash_command=dbt_base
      + "run --select tag:city_zone_split__mtd"
      + " --vars "
      + model_vars_zone_mtd
      + " "
      + " --target prod",
    executor_config=k8s_executor_config,
)

process_completed_0 = BashOperator(
    task_id = 'process_completed_v0',
    bash_command = 'date',
    dag = dag,
    trigger_rule=TriggerRule.NONE_FAILED,
    executor_config=k8s_executor_config,
)

# zone split call - end 

process_completed = BashOperator(
    task_id = 'process_completed_v1',
    bash_command = 'date',
    dag = dag,
    trigger_rule=TriggerRule.NONE_FAILED,
    executor_config=k8s_executor_config,
)

dbt_run_weekly >> [dbt_run_monthly, dbt_run_quarterly] >> process_completed_0 >> [dbt_run_city_zone_wtd, dbt_run_city_zone_mtd] >> process_completed

for table_name in ["dwh.imd_mau_overall", "dwh.imd_weekly_availability", "dwh.monthly_business_metrics", "dwh.quarterly_business_metrics", "dwh.weekly_business_metrics","dwh.agg_kpi_city_zone_split_wtd","dwh.agg_kpi_city_zone_split_mtd"]:

    model_vars = ' --vars ' + '\'{{"table_name": "{}", "operation_type": "all", "dag_id": "{}", "dag_run_id": "{}"}}\''.format(table_name, '{{ dag.dag_id }}', '{{ run_id }}')
    
    dbt_command = (
                "cd /dbt-models/trino_dwh && dbt "
                + "run-operation run_maintenance "
                + model_vars
                + " "
                + " --target prod"
            )
    
    dbt_run_maintenance = BashOperator(
        task_id = f"dbt_run_maintenance_{table_name[4:]}",
        bash_command = dbt_command,
        dag = dag,
        executor_config = k8s_executor_config,
    )
    process_completed >> dbt_run_maintenance

if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
