import sys, os
from datetime import datetime
from pytz import timezone
import pencilbox as pb


# To extract the current folder path
CWD = os.path.dirname(os.path.abspath(__file__))
sys.path.append(CWD)

from utils import to_parse_datestring, to_push_into_kafka, query_to_df

def to_push_funnel_metrics(**kwargs):
    # To read from airflow kwargs
    airflow_ts = str(kwargs['airflow_ts']) 
    dag_interval_end_ts = to_parse_datestring(airflow_ts)
    task_name = kwargs['task_name']
    env = kwargs['env']

    if env:
        for key, value in env.items():
            os.environ[key] = value
    
    if task_name[0] == 'daily_city_wise_conversion':
        df = query_to_df(task_name[0], dag_interval_end_ts)
        print(f"DataFrame created successfully for '{task_name[0]}'")
        kafka_topic = "observability.metrics.dwh.agg_daily_city_conversion_v2"
        if df.shape[0] != 0:
            to_push_into_kafka(df, kafka_topic, key_col_name=["city"])
        else:
            print("Data isn't pushed to Kafka because of empty DataFrame")

    if task_name[1] == 'daily_store_wise_conversion':
        df = query_to_df(task_name[1], dag_interval_end_ts)
        print(f"DataFrame created successfully for '{task_name[1]}'")
        kafka_topic = "observability.metrics.dwh.agg_daily_merchant_city_conversion_v2"
        if df.shape[0] != 0:
            to_push_into_kafka(df, kafka_topic, key_col_name=["city"])
        else:
            print("Data isn't pushed to Kafka because of empty DataFrame")

    if task_name[2] == 'hourly_city_wise_conversion':
        df = query_to_df(task_name[2], dag_interval_end_ts)
        print(f"DataFrame created successfully for '{task_name[2]}'")
        kafka_topic = "observability.metrics.dwh.agg_hourly_city_conversion_v2"
        if df.shape[0] != 0:
            to_push_into_kafka(df, kafka_topic, key_col_name=["city"])
        else:
            print("Data isn't pushed to Kafka because of empty DataFrame")

    if task_name[3] == 'hourly_store_wise_conversion':
        df = query_to_df(task_name[3], dag_interval_end_ts)
        print(f"DataFrame created successfully for '{task_name[3]}'")
        kafka_topic = "observability.metrics.dwh.agg_hourly_merchant_city_conversion_v2"
        if df.shape[0] != 0:
            to_push_into_kafka(df, kafka_topic, key_col_name=["city"])
        else:
            print("Data isn't pushed to Kafka because of empty DataFrame")

def get_latest_snapshot_ptype(**kwargs):
    # To read from airflow kwargs
    airflow_ts = str(kwargs['airflow_ts']) 
    dag_interval_end_ts = to_parse_datestring(airflow_ts)
    file_name = kwargs['file_name']
    env = kwargs['env']

    if env:
        for key, value in env.items():
            os.environ[key] = value
    
    df = query_to_df(file_name[0], dag_interval_end_ts)
    max_snapshot_hr_mm = df["max_snapshot_hr_mm"][0]
    print(f"Snapshot to be pushed: {max_snapshot_hr_mm}")
    # Push the max snapshot value to XCom
    kwargs['ti'].xcom_push(key='max_snapshot_hr_mm', value=max_snapshot_hr_mm)
       
def to_push_deepdive_metrics(**kwargs):
    # To read from airflow kwargs
    airflow_ts = str(kwargs['airflow_ts'])
    dag_interval_end_ts = to_parse_datestring(airflow_ts)
    file_name = kwargs['file_name']
    env = kwargs['env']

    if env:
        for key, value in env.items():
            os.environ[key] = value
    
    # Read the max snapshot value from XCom
    snapshot_hr_mm = kwargs['ti'].xcom_pull(task_ids='start_kafka_ingestion', key='max_snapshot_hr_mm')
    print(f"Snapshot to be pushed: {snapshot_hr_mm}")
    
    if file_name[0] == 'daily_merchant_ptype_metrics':
        df = query_to_df(file_name[0], dag_interval_end_ts, snapshot_hr_mm=snapshot_hr_mm)
        print(f"DataFrame created successfully for '{file_name[0]}'")
        kafka_topic = "observability.dwh.agg.daily_merchant_ptype_metrics"
        if df.shape[0] != 0:
            to_push_into_kafka(df, kafka_topic, key_col_name=["city"])
        else:
            print("Data isn't pushed to Kafka because of empty DataFrame")
    
    elif file_name[0] == 'hourly_merchant_ptype_metrics':
        df = query_to_df(file_name[0], dag_interval_end_ts, snapshot_hr_mm=snapshot_hr_mm)
        print(f"DataFrame created successfully for '{file_name[0]}'")
        kafka_topic = "observability.dwh.agg.hourly_merchant_ptype_metrics"
        if df.shape[0] != 0:
            to_push_into_kafka(df, kafka_topic, key_col_name=["city"])
        else:
            print("Data isn't pushed to Kafka because of empty DataFrame")
    
    elif file_name[0] == 'daily_city_ptype_metrics':
        df = query_to_df(file_name[0], dag_interval_end_ts, snapshot_hr_mm=snapshot_hr_mm)
        print(f"DataFrame created successfully for '{file_name[0]}'")
        kafka_topic = "observability.dwh.agg.daily_city_ptype_metrics"
        if df.shape[0] != 0:
            to_push_into_kafka(df, kafka_topic, key_col_name=["city"])
        else:
            print("Data isn't pushed to Kafka because of empty DataFrame")
            
    elif file_name[0] == 'daily_city_l0_category_metrics':
        df = query_to_df(file_name[0], dag_interval_end_ts, snapshot_hr_mm=snapshot_hr_mm)
        print(f"DataFrame created successfully for '{file_name[0]}'")
        kafka_topic = "observability.dwh.agg.daily_city_l0category_metrics"
        if df.shape[0] != 0:
            to_push_into_kafka(df, kafka_topic, key_col_name=["city"])
        else:
            print("Data isn't pushed to Kafka because of empty DataFrame")
    
    elif file_name[0] == 'hourly_city_ptype_metrics':
        df = query_to_df(file_name[0], dag_interval_end_ts, snapshot_hr_mm=snapshot_hr_mm)
        print(f"DataFrame created successfully for '{file_name[0]}'")
        kafka_topic = "observability.dwh.agg.hourly_city_ptype_metrics"
        if df.shape[0] != 0:
            to_push_into_kafka(df, kafka_topic, key_col_name=["city"])
        else:
            print("Data isn't pushed to Kafka because of empty DataFrame")
    
    # Spiking Keywords
    elif file_name[0] == 'daily_city_spiking_keyword':
        df = query_to_df(file_name[0], dag_interval_end_ts, snapshot_hr_mm=snapshot_hr_mm)
        print(f"DataFrame created successfully for '{file_name[0]}'")
        kafka_topic = "observability.dwh.agg_daily_city_keyword_metrics"
        if df.shape[0] != 0:
            to_push_into_kafka(df, kafka_topic, key_col_name=["city"])
        else:
            print("Data isn't pushed to Kafka because of empty DataFrame")

    elif file_name[0] == 'daily_merchant_spiking_keyword':
        df = query_to_df(file_name[0], dag_interval_end_ts, snapshot_hr_mm=snapshot_hr_mm)
        print(f"DataFrame created successfully for '{file_name[0]}'")
        kafka_topic = "observability.dwh.agg_daily_merchant_keyword_metrics"
        if df.shape[0] != 0:
            to_push_into_kafka(df, kafka_topic, key_col_name=["city"])
        else:
            print("Data isn't pushed to Kafka because of empty DataFrame")
            
def feedback_from_deepdive_models(**kwargs):
    # To read from airflow kwargs
    airflow_ts = str(kwargs['airflow_ts'])
    dag_interval_end_ts = to_parse_datestring(airflow_ts)
    task_name = kwargs['task_name']
    on_success_task = kwargs['on_success_task']
    on_failure_task = kwargs['on_failure_task']
    
    env = kwargs['env']

    if env:
        for key, value in env.items():
            os.environ[key] = value
    
    df = query_to_df(task_name[0], dag_interval_end_ts)
    
    downstream_flag = df["downstream_flag"][0]
    if downstream_flag==True:        
        print(f"""Downstrem task: '{on_success_task}' can be executed""")
        return on_success_task
    else:
        print(f"""Downstrem task: '{on_failure_task}' can be executed""")
        return on_failure_task
    
def feedback_from_deepdive_models_backfill(**kwargs):
    # To read from airflow kwargs
    airflow_ts = str(kwargs['airflow_ts'])
    dag_interval_end_ts = to_parse_datestring(airflow_ts)
    task_name = kwargs['task_name']
    on_success_task = kwargs['on_success_task']
    on_failure_task = kwargs['on_failure_task']
    
    env = kwargs['env']

    if env:
        for key, value in env.items():
            os.environ[key] = value
    
    df = query_to_df(task_name[0], dag_interval_end_ts)
    
    downstream_flag = df["downstream_flag"][0]
    if downstream_flag==True:        
        print(f"""Downstrem task: '{on_success_task}' can be executed""")
        return on_success_task
    else:
        print(f"""Downstrem task: '{on_failure_task}' can be executed""")
        return on_failure_task
    
def to_skip_overnight_runs_events_flow(**kwargs):
    airflow_ts_cst = kwargs['airflow_ts']
    airflow_ts_ist = airflow_ts_cst.astimezone(timezone('Asia/Kolkata'))  # To convert date value from UTC to IST
    hour_ist = airflow_ts_ist.hour

    on_success_task = kwargs['on_success_task']
    on_failure_task = kwargs['on_failure_task']
    
    if (hour_ist >= 1) and (hour_ist <= 4):
        # ^^ To skip events data flow for 1st to 4th hour of the day in IST
        print("Sonar Insights - Events flow can be skipped")
        return on_success_task
    else:
        print("Sonar Insights - Events flow should be executed")
        return on_failure_task
