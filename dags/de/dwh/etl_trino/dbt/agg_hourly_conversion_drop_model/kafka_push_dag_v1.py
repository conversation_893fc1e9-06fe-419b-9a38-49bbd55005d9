# -*- coding: utf-8 -*-

import sys, os
import pytz
from datetime import datetime, timedelta
import airflow
from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from kubernetes.client import models as k8s
from airflow.models import Variable

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dag_config import k8s_executor_config_dbt, slack_failure_alert, k8s_executor_config, long_run_slack_alert
from master import to_push_deepdive_metrics, get_latest_snapshot_ptype

args = {
    "slack_id": "S03ST1ENERJ",
    "owner": "<EMAIL>",
    "on_failure_callback": slack_failure_alert,
    "on_success_callback": long_run_slack_alert,
    "start_date": datetime.strptime("2024-02-25T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": datetime.strptime("2025-09-30T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "retries": 2,
    "retry_delay": timedelta(seconds=60),
    "retry_exponential_backoff": True,
}

dag = DAG(
    dag_id="de_dwh_etl_trino_dbt_agg_hourly_conversion_drop_kafka_push_v1",
    default_args=args,
    schedule_interval=None,
    max_active_runs= 1,
    tags=["dwh", "sonar_insights_datamart"],
    render_template_as_native_obj=True,
)

env = {
    "AIRFLOW_DAG_ID": "de_dwh_etl_trino_dbt_agg_hourly_conversion_drop_kafka_push_v1",
    "AIRFLOW_DAG_OWNER": "<EMAIL>",
}

start_kafka_ingestion = PythonOperator(
    task_id="start_kafka_ingestion",
    python_callable=get_latest_snapshot_ptype,
    op_kwargs={
        'file_name': ['get_latest_snapshot_details'],
        "airflow_ts": "{{ data_interval_end }}",
        "env": env,
    },
    provide_context=True,
    dag=dag,
    executor_config=k8s_executor_config,
)

daily_merchant_ptype_metrics = PythonOperator(
    task_id = "daily_merchant_ptype_metrics",
    python_callable = to_push_deepdive_metrics,
    op_kwargs={
        'file_name': ['daily_merchant_ptype_metrics'],
        "airflow_ts": "{{ data_interval_end }}",
        "env": env,
    },
    dag = dag,
    executor_config = k8s_executor_config,
)

hourly_merchant_ptype_metrics = PythonOperator(
    task_id = "hourly_merchant_ptype_metrics",
    python_callable = to_push_deepdive_metrics,
    op_kwargs={
        'file_name': ['hourly_merchant_ptype_metrics'],
        "airflow_ts": "{{ data_interval_end }}",
        "env": env,
    },
    dag = dag,
    executor_config = k8s_executor_config,
)

daily_city_ptype_metrics = PythonOperator(
    task_id = "daily_city_ptype_metrics",
    python_callable = to_push_deepdive_metrics,
    op_kwargs={
        'file_name': ['daily_city_ptype_metrics'],
        "airflow_ts": "{{ data_interval_end }}",
        "env": env,
    },
    dag = dag,
    executor_config = k8s_executor_config,
)

daily_city_l0_category_metrics = PythonOperator(
    task_id = "daily_city_l0_category_metrics",
    python_callable = to_push_deepdive_metrics,
    op_kwargs={
        'file_name': ['daily_city_l0_category_metrics'],
        "airflow_ts": "{{ data_interval_end }}",
        "env": env,
    },
    dag = dag,
    executor_config = k8s_executor_config,
)

hourly_city_ptype_metrics = PythonOperator(
    task_id = "hourly_city_ptype_metrics",
    python_callable = to_push_deepdive_metrics,
    op_kwargs={
        'file_name': ['hourly_city_ptype_metrics'],
        "airflow_ts": "{{ data_interval_end }}",
        "env": env,
    },
    dag = dag,
    executor_config = k8s_executor_config,
)

# Spiking Keywords
daily_city_spiking_keyword = PythonOperator(
    task_id = "daily_city_spiking_keyword",
    python_callable = to_push_deepdive_metrics,
    op_kwargs={
        'file_name': ['daily_city_spiking_keyword'],
        "airflow_ts": "{{ data_interval_end }}",
        "env": env,
    },
    dag = dag,
    executor_config = k8s_executor_config,
)

daily_merchant_spiking_keyword = PythonOperator(
    task_id = "daily_merchant_spiking_keyword",
    python_callable = to_push_deepdive_metrics,
    op_kwargs={
        'file_name': ['daily_merchant_spiking_keyword'],
        "airflow_ts": "{{ data_interval_end }}",
        "env": env,
    },
    dag = dag,
    executor_config = k8s_executor_config,
)

################################ Dag Execution Flow ##############################

start_kafka_ingestion >> [daily_merchant_ptype_metrics, hourly_merchant_ptype_metrics, daily_city_ptype_metrics, daily_city_l0_category_metrics, hourly_city_ptype_metrics, daily_city_spiking_keyword, daily_merchant_spiking_keyword]

################################ Dag Execution Flow ##############################

if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
