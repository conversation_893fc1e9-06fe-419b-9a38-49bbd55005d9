import sys, os
import pandas as pd
import pencilbox as pb
from pytz import timezone
from dateutil import tz, parser
from datetime import datetime, date, timedelta

# To extract the current folder path
CWD = os.path.dirname(os.path.abspath(__file__))
sys.path.append(CWD)

def to_parse_datestring(date_filter):  # To parse datestring values
    print(f" Date filter value from airflow: {date_filter}")
    date_filter = parser.parse(date_filter)
    date_filter = date_filter.astimezone(timezone('Asia/Kolkata'))  # To convert date value from UTC to IST
    print(f" Date filter(IST): {date_filter}")
   
    hour = date_filter.hour
    # To consider t-1 date for first 4 runs post 12 AM
    # update to ^^ on Feb 24'25 --> removed for 2am to 4am --> this was resulting into invenotry not getting updated for midnight hours.
    if hour in (0, 1):  
        date_time_filter = (date_filter - timedelta(days = 1)).replace(hour=23, minute=59, second=59).strftime("%Y-%m-%d %H:%M:%S")
    else:
        date_time_filter = date_filter.strftime("%Y-%m-%d %H:%M:%S")
         
    print(f" Date filter(YYYY-MM-DD HH:MM:SS): {date_time_filter}")
    return date_time_filter

def to_push_into_kafka(df, kafka_topic, key_col_name):
    kafka_connection_id = "[Kafka] prod-data-corestr"
    chunk_size = 500000  # Set the desired chunk size
    total_chunks = len(df) // chunk_size + (len(df) % chunk_size > 0)

    for i in range(total_chunks):
        start_idx = i * chunk_size
        end_idx = min((i + 1) * chunk_size, len(df))
        chunk_data = df.iloc[start_idx:end_idx]
        print(f"Chunk data size: {chunk_data.shape}")
        values=chunk_data.to_dict('records')
        
        for i in key_col_name:
            keys = list(map(str, chunk_data[i]))
        
        kwargs = {
            'batch.size': '1644000',
            'queue.buffering.max.messages': '510000'
        }
        
        if "ptype" in kafka_topic:
            pb.to_kafka(
                kafka_connection_id,
                kafka_topic,
                values = values,
                **kwargs
                # keys = keys
            )
        else:
            pb.to_kafka(
                kafka_connection_id,
                kafka_topic,
                values = values,
                **kwargs,
                keys = keys
            )
        print(f"Data successfully pushed to Kafka topic: '{kafka_topic}'")
    
def query_to_df(file_name, dag_interval_end_ts, snapshot_hr_mm = None):
    if "ptype" in file_name:
        file_path = f"{CWD}/models/merchant_ptype_metrics"
    elif "l0_category" in file_name:
        file_path = f"{CWD}/models/merchant_ptype_metrics"
    elif "feedback" in file_name:
        file_path = f"{CWD}/models/feedback_queries"
    elif "spiking_keyword" in file_name:
        file_path = f"{CWD}/models/spiking_keywords"
    else:
        file_path = f"{CWD}/models"
        
    trino_connection = pb.get_connection("[Warehouse] Trino")
    query = (
        open(f"{file_path}/{file_name}.sql", "r")
        .read()
        .replace('%','%%')
        .replace('{{','{')
        .replace('}}','}')
        .format(
            date_filter = dag_interval_end_ts,
            snapshot_hr_mm = snapshot_hr_mm
        )
    )
    
    df = pd.read_sql_query(
        sql = query,
        con = trino_connection
    )
    return df

    if "feedback" in file_name:
        print(df.shape)
        # print(df.head())
        print(
            f""" Upstream base table is having data till '{df["base_models_ts_ist"][0]}' and downstream table: '{file_name}' is having data till '{df["deepdive_model_ts_ist"][0]}' """
        )
        # return df
    else:    
        print(df.shape)
        # print(df.head())
        print(f"""Snapshot to be pushed : {df["max_snapshot_hr_mm"].unique()}""")
        # return df
