WITH

today AS (
    SELECT
        snapshot_date_ist,
        snapshot_hr_mm,
        city,
        search_keyword,
        total_searches, 
        total_atc,
        updated_till,
        etl_snapshot_ts_ist
    FROM
        blinkit_iceberg.dwh.agg_daily_city_keyword_metrics
    WHERE
        snapshot_date_ist = CAST(TIMESTAMP '{{date_filter}}' AS DATE)
        AND snapshot_hr_mm = {{snapshot_hr_mm}}

    UNION ALL

    SELECT
        snapshot_date_ist,
        snapshot_hr_mm,
        'Overall' AS city,
        search_keyword,
        SUM(total_searches) AS total_searches, 
        SUM(total_atc) AS total_atc,
        MAX(updated_till) AS updated_till,
        MAX(etl_snapshot_ts_ist) AS etl_snapshot_ts_ist
    FROM
        blinkit_iceberg.dwh.agg_daily_city_keyword_metrics
    WHERE
        snapshot_date_ist = CAST(TIMESTAMP '{{date_filter}}' AS DATE)
        AND snapshot_hr_mm = {{snapshot_hr_mm}}
    GROUP BY
        1, 2, 3, 4
),

yesterday AS (
    SELECT
        snapshot_date_ist,
        snapshot_hr_mm,
        city,
        search_keyword,
        total_searches, 
        total_atc,
        updated_till,
        etl_snapshot_ts_ist
    FROM
        blinkit_iceberg.dwh.agg_daily_city_keyword_metrics
    WHERE
        snapshot_date_ist = CAST(TIMESTAMP '{{date_filter}}' AS DATE) - INTERVAL '1' DAY
        AND snapshot_hr_mm = {{snapshot_hr_mm}}
    
    UNION ALL

    SELECT
        snapshot_date_ist,
        snapshot_hr_mm,
        'Overall' AS city,
        search_keyword,
        SUM(total_searches) AS total_searches, 
        SUM(total_atc) AS total_atc,
        MAX(updated_till) AS updated_till,
        MAX(etl_snapshot_ts_ist) AS etl_snapshot_ts_ist
    FROM
        blinkit_iceberg.dwh.agg_daily_city_keyword_metrics
    WHERE
        snapshot_date_ist = CAST(TIMESTAMP '{{date_filter}}' AS DATE) - INTERVAL '1' DAY
        AND snapshot_hr_mm = {{snapshot_hr_mm}}
    GROUP BY
        1, 2, 3, 4
),

spike AS (
    SELECT
        t.snapshot_date_ist,
        t.snapshot_hr_mm,
        t.city,
        t.search_keyword,
        t.total_searches, 
        t.total_atc,
        y.total_searches AS t7_total_searches, 
        y.total_atc AS t7_total_atc,
        t.updated_till,

        (CAST(t.total_searches - y.total_searches AS DOUBLE) * 100) / y.total_searches AS spike_percent,
        (CAST(t.total_atc AS DOUBLE) * 100) / t.total_searches AS t0_conversion_percent,

        t.etl_snapshot_ts_ist
    FROM
        today AS t
    JOIN
        yesterday AS y
            ON t.snapshot_hr_mm = y.snapshot_hr_mm
            AND t.city = y.city
            AND t.search_keyword = y.search_keyword
    WHERE
        CASE
            WHEN t.city = 'Overall' 
            THEN t.total_searches > 150
            ELSE t.total_searches > 9 * CAST(t.snapshot_hr_mm / 400 AS INT)
        END
),

row_ AS (

    SELECT
        snapshot_date_ist,
        snapshot_hr_mm,
        city,
        search_keyword,
        total_searches, 
        t7_total_searches,
        total_atc,
        t7_total_atc,
        updated_till,
        spike_percent,
        t0_conversion_percent,
        etl_snapshot_ts_ist,

        ROW_NUMBER() OVER(PARTITION BY city ORDER BY spike_percent DESC) AS rn
    FROM
        spike
    WHERE
        spike_percent > 0 
)

SELECT
    snapshot_date_ist,
    snapshot_hr_mm,
    city,
    search_keyword,
    total_searches, 
    t7_total_searches,
    total_atc,
    t7_total_atc,
    TO_UNIXTIME(updated_till) AS updated_till_epoch,
    spike_percent,
    t0_conversion_percent,
    CAST((TO_UNIXTIME(CURRENT_TIMESTAMP) * 1000) AS DOUBLE) AS etl_snapshot_ts_ist_epoch
FROM
    row_
WHERE
    rn < 51
