# -*- coding: utf-8 -*-

import sys, os
import json
import logging
from datetime import datetime, timedelta

import requests

from airflow import DAG
from airflow.operators.bash_operator import BashOperator
from airflow.hooks.base_hook import BaseHook
from airflow.operators.python_operator import PythonOperator
import pencilbox as pb

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.config import k8s_executor_config, k8s_executor_config_dbt
from utils import priority_score_norm
from utils import priority_score
from utils.column_definitions import weekly_column_dtypes, weekly_base_column_dtypes

def slack_failure_alert(context):
    dag_id = context.get("task_instance").dag_id
    run_id = context.get("run_id")
    task_id = context.get("task_instance").task_id
    log_url = context.get("task_instance").log_url.replace("http://", "https://")

    owner = {"email": "<EMAIL>", "slack_id": "S03ST1ENERJ"}
    slack_id = owner["slack_id"]
    _owner = {
        "s": "s" if len(slack_id.split(" ")) > 1 else "",
        "slack_id": "<!subteam^{}>".format(slack_id),
    }

    slack_failure_msg = """
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner{s}*: {slack_id}
        *Log Url*: {log_url}
    """.format(
        dag_id=dag_id,
        run_id=run_id,
        task_id=task_id,
        s=_owner["s"],
        slack_id=_owner["slack_id"],
        log_url=log_url,
    )

    slack_alert_configs = [
        {"channel": "bl-dwh-alerts"},
    ]

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(
                channel=channel, text=slack_failure_msg, user="data-plumber"
            )
        except Exception as e:
            logging.warning(e)
            message = (
                f"<!subteam^S089D0GQRCJ> `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(
                channel="bl-data-alerts-p1", text=message, user="data-plumber"
            )

def failure_alerts(context):
    slack_failure_alert(context)

# def add_catalog_user_properties_weekly():
#     pb.to_datahub(
#         schema_name="dwh",
#         table_name="user_properties_weekly",
#         column_dtypes=weekly_column_dtypes,
#         table_description="This table contains properties of an user who trasacts weekly.",
#         data_platform="trino",
#         primary_keys=[],
#         partition_keys=["customer_id"],
#     )
    
# def add_catalog_user_properties_weekly_base():
#     pb.to_datahub(
#         schema_name="dwh",
#         table_name="user_properties_weekly_base",
#         column_dtypes=weekly_base_column_dtypes,
#         table_description="This table contains base properties of an user who trasacts weekly.",
#         data_platform="trino",
#         primary_keys=[],
#         partition_keys=["customer_id"],
#     )

args = {
    "slack_id": "S03ST1ENERJ",
    "owner": "<EMAIL>",  
    "on_failure_callback": failure_alerts,
    "start_date": datetime.strptime("2024-01-01T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": datetime.strptime("2025-09-30T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "execution_timeout":timedelta(hours = 2)
}

dag = DAG(
    dag_id="de_dwh_etl_trino_dbt_user_properties_weekly_v1",
    default_args=args,
    schedule_interval="32 22 * * 0", 
    tags=["dwh", "User Properties"],
)

env = {}
default_env = {
    "AIRFLOW_DAG_ID": "de_dwh_etl_trino_dbt_user_properties_weekly_v1",
    "AIRFLOW_DAG_OWNER": "<EMAIL>",   
}
env.update(default_env)

## Tasks Execution Flow

dbt_base = "cd /dbt-models/trino_dwh && dbt --cache-selected-only "

dbt_run_user_properties_weekly_base = BashOperator(
    task_id="dbt_run_user_properties_weekly_base",
    bash_command=dbt_base
    + "run --select tag:user_properties__weekly"
    + ' --vars \'{"dag_id": "{{ dag.dag_id }}", "dag_run_id": "{{ run_id }}"}\''
    + " "
    + ' --target prod_p0'
    + "; dbt retry "
    + ' --vars \'{"dag_id": "{{ dag.dag_id }}", "dag_run_id": "{{ run_id }}"}\''
    + " "
    + " --target prod_p0",
    dag=dag,
    executor_config=k8s_executor_config_dbt,
)

priority_score_task = PythonOperator(
    task_id="priority_score_task",
    python_callable=priority_score.priority_score_main,
    dag=dag,
    executor_config=k8s_executor_config,
)

# priority_score_norm_task = PythonOperator(
#     task_id="priority_score_norm_task",
#     python_callable=priority_score_norm.priority_score_norm_main,
#     dag=dag,
#     executor_config=k8s_executor_config,
# )

dbt_run_user_properties_weekly_final = BashOperator(
    task_id="dbt_run_user_properties_weekly_final",
    bash_command=dbt_base
    + "run --select tag:user_properties_core__weekly"
    + ' --vars \'{"dag_id": "{{ dag.dag_id }}", "dag_run_id": "{{ run_id }}"}\''
    + " "
    + ' --target prod_p0'
    + "; dbt retry "
    + ' --vars \'{"dag_id": "{{ dag.dag_id }}", "dag_run_id": "{{ run_id }}"}\''
    + " "
    + " --target prod_p0",
    dag=dag,
    executor_config=k8s_executor_config_dbt,
)

# dbt_catalog_user_weekly = PythonOperator(
#     task_id="dbt_catalog_user_weekly",
#     dag=dag,
#     python_callable=add_catalog_user_properties_weekly,
#     executor_config=k8s_executor_config,
# )

# dbt_catalog_user_weekly_base = PythonOperator(
#     task_id="dbt_catalog_user_weekly_base",
#     dag=dag,
#     python_callable=add_catalog_user_properties_weekly_base,
#     executor_config=k8s_executor_config,
# )


## --------------------------------------------- Maintenance Flow ----------------------------------------------------

for table_name in ["user_properties_weekly_base"]:
    
    model_vars = ' --vars ' + '\'{{"table_name": "{}","partition_key": "snapshot_date_ist", "operation_type": "all", "dag_id": "{}", "dag_run_id": "{}"}}\''.format('dwh.' + table_name, '{{ dag.dag_id }}', '{{ run_id }}')
    
    dbt_command = (
                "cd /dbt-models/trino_dwh && dbt "
                + "run-operation run_maintenance "
                + model_vars
                + " "
                + " --target prod_p0"
            )
    
    dbt_run_maintenance = BashOperator(
        task_id = f"dbt_run_maintenance_{table_name}",
        bash_command = dbt_command,
        dag = dag,
        executor_config = k8s_executor_config_dbt,
    )

dbt_run_user_properties_weekly_base >> priority_score_task >> dbt_run_user_properties_weekly_final >> dbt_run_maintenance

if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)