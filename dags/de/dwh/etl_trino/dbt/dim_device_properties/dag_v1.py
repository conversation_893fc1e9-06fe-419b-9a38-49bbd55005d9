# -*- coding: utf-8 -*-

import sys, os
from datetime import datetime, timedelta
import airflow
from airflow import DAG
from airflow.hooks.base_hook import BaseHook
from airflow.operators.bash_operator import BashOperator
from airflow.operators.python_operator import PythonOperator
from kubernetes.client import models as k8s
from airflow.models import Variable
import pencilbox as pb

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import k8s_executor_config_dbt, k8s_executor_config
from column_definitions import column_dtypes

def add_catalog_device_customer_first_seen():
    pb.to_datahub(
        schema_name="dwh",
        table_name="dim_customer_first_seen",
        column_dtypes= column_dtypes,
        table_description="This table Contains details of the customer who visited the app for the first time.",
        data_platform="trino",
        primary_keys=[],
        partition_keys=[],
    )

def slack_failure_alert(context):
    dag_id = context.get("task_instance").dag_id
    run_id = context.get("run_id")
    task_id = context.get("task_instance").task_id
    log_url = context.get("task_instance").log_url.replace("http://", "https://")

    owner = {"email": "<EMAIL>", "slack_id": "S03ST1ENERJ"}
    slack_id = owner["slack_id"]
    _owner = {
        "s": "s" if len(slack_id.split(" ")) > 1 else "",
        "slack_id": "<!subteam^{}>".format(slack_id),
    }

    slack_failure_msg = """
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner{s}*: {slack_id}
        *Log Url*: {log_url}
    """.format(
        dag_id=dag_id,
        run_id=run_id,
        task_id=task_id,
        s=_owner["s"],
        slack_id=_owner["slack_id"],
        log_url=log_url,
    )

    slack_alert_configs = [
        {"channel": "bl-dwh-alerts"},
    ]

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(
                channel=channel, text=slack_failure_msg, user="data-plumber"
            )
        except Exception as e:
            logging.warning(e)
            message = (
                f"<!subteam^S089D0GQRCJ> `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(
                channel="bl-data-alerts-p1", text=message, user="data-plumber"
            )

args = {
    "slack_id": "S03ST1ENERJ",
    "owner": "<EMAIL>",
    "on_failure_callback": slack_failure_alert,
    "start_date": datetime.strptime("2023-10-01T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": datetime.strptime("2025-09-30T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "retries": 2,
    "retry_delay": timedelta(seconds=60),
    "retry_exponential_backoff": True,
}

dag = DAG(
    dag_id="de_dwh_etl_trino_dbt_dim_device_properties_v1",
    default_args=args,
    schedule_interval="17 3-23/6 * * *",
    tags=["dwh", "dbt"],
    render_template_as_native_obj=True,
)

env = {
    "AIRFLOW_DAG_ID": "de_dwh_etl_trino_dbt_dim_device_properties_v1",
    "AIRFLOW_DAG_OWNER": "<EMAIL>",
}

dbt_base = "cd /dbt-models/trino_dwh"
base_model = " && dbt --cache-selected-only run -m +dim_device_properties --exclude staging"
model_vars = '\'{{"dag_id": "{}", "dag_run_id": "{}", "days_in_past": "{}", "run_date": "{}"}}\''.format('{{ dag.dag_id }}', '{{ run_id }}', '7', '{{ data_interval_end }}')
first_seen = BashOperator(
    task_id="first_seen",
    bash_command=dbt_base
        + base_model
         + " --vars "
        + model_vars \
        + " "
        + " --target prod"
        + "; dbt retry "
         + " --vars "
        + model_vars \
        + " --target prod",
    dag=dag,
    executor_config=k8s_executor_config_dbt,
)

base_model_last_seen = " && dbt --cache-selected-only run --select tag:customer_last_seen"
last_seen = BashOperator(
    task_id="customer_last_seen",
    bash_command=dbt_base
        + base_model_last_seen
        + " --vars "
        + model_vars \
        + " "
        + " --target prod"
        + "; dbt retry "
         + " --vars "
        + model_vars \
        + " --target prod",
    dag=dag,
    executor_config=k8s_executor_config_dbt,
)

base_model_map_device_user = " && dbt --cache-selected-only run -m +dim_map_device_user --exclude staging"
map_device_user = BashOperator(
    task_id="map_device_user",
    bash_command=dbt_base
        + base_model_map_device_user
        + " --vars "
        + model_vars \
        + " "
        + " --target prod"
        + "; dbt retry "
         + " --vars "
        + model_vars \
        + " --target prod",
    dag=dag,
    executor_config=k8s_executor_config_dbt,
)

## catalog is build for dim_device_first_seen view ; not the table as dim_device_properties 
# dim_device_properties column info will be exposed via dim_device table 
catalog = PythonOperator(
    task_id="catalog",
    dag=dag,
    python_callable=add_catalog_device_customer_first_seen,
    executor_config=k8s_executor_config,
)

[first_seen, last_seen, map_device_user] >> catalog

## --------------------------------------------- Maintenance Flow ----------------------------------------------------

for table_name in ["dim_device_properties", "dim_map_device_user", "cx_properties_last_seen", "cx_properties_installed_not_transcated"]:
    
    model_vars = ' --vars ' + '\'{{"table_name": "{}", "operation_type": "all", "dag_id": "{}", "dag_run_id": "{}"}}\''.format('dwh.' + table_name, '{{ dag.dag_id }}', '{{ run_id }}')
    
    dbt_command = (
                 "cd /dbt-models/trino_dwh && dbt "
                + "run-operation run_maintenance "
                + model_vars
                + " "
                + " --target prod"
            )
    
    dbt_run_maintenance = BashOperator(
        task_id = f"dbt_run_maintenance_{table_name}",
        bash_command = dbt_command,
        dag = dag,
        executor_config = k8s_executor_config_dbt,
    )
    catalog >> dbt_run_maintenance

if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)