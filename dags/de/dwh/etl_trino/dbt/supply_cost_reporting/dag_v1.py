# -*- coding: utf-8 -*-

import os
import sys
from datetime import datetime, timedelta, date

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from airflow import DAG
from airflow.operators.bash_operator import BashOperator
from airflow.operators.python_operator import Python<PERSON>perator
import pencilbox as pb

from utils.utils import push_data_from_sheets_to_trino

from utils.config import k8s_executor_config, k8s_executor_config_dbt
import logging

from utils.column_definitions import (
    outlet_mapping_details_column_dtypes,
    fc_cost_details,
    fc_cost_details_variable_fixed,
    offroll_ratecard_column_dtypes,
    ds_onroll_manpower_column_dtypes,
    packaging_cost_column_dtypes,
    partner_commission_mg_column_dtypes,
    own_ds_rent_column_dtypes,
    gna_cost_column_dtypes,
    fc_to_ds_dtypes
)

def slack_failure_alert(context):
    dag_id = context.get("task_instance").dag_id
    run_id = context.get("run_id")
    task_id = context.get("task_instance").task_id
    log_url = context.get("task_instance").log_url.replace("http://", "https://")

    owner = {"email": "<EMAIL>", "slack_id": "S03ST1ENERJ"}
    slack_id = owner["slack_id"]
    _owner = {
        "s": "s" if len(slack_id.split(" ")) > 1 else "",
        "slack_id": "<!subteam^{}>".format(slack_id),
    }

    slack_failure_msg = """
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner{s}*: {slack_id}
        *Log Url*: {log_url}
    """.format(
        dag_id=dag_id,
        run_id=run_id,
        task_id=task_id,
        s=_owner["s"],
        slack_id=_owner["slack_id"],
        log_url=log_url,
    )

    slack_alert_configs = [
        {"channel": "bl-dwh-alerts"},
    ]

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(
                channel=channel, text=slack_failure_msg, user="data-plumber"
            )
        except Exception as e:
            logging.warning(e)
            message = (
                f"<!subteam^S089D0GQRCJ> `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(
                channel="bl-data-alerts-p1", text=message, user="data-plumber"
            )

            
def failure_alerts(context):
    slack_failure_alert(context)

def add_catalog_fc_to_ds_percentage():
    pb.to_datahub(
        schema_name="dwh",
        table_name="fc_to_ds_percentage_share_calculation",
        column_dtypes=fc_to_ds_dtypes,
        table_description="This table contains fc to ds percentage share.",
        data_platform="trino",
        primary_keys=[],
        partition_keys=[],
    )

cwd = os.path.dirname(os.path.realpath(__file__))

args = {
    "slack_id": "S03ST1ENERJ",
    "owner": "<EMAIL>",
    "on_failure_callback": failure_alerts,
    "retries": 3,
    "retry_delay": timedelta(seconds=15),
    "retry_exponential_backoff": True,
    "start_date": datetime.strptime("2023-09-19T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": datetime.strptime("2025-09-30T00:00:00", "%Y-%m-%dT%H:%M:%S"),
}

dag = DAG(
    dag_id="de_dwh_etl_trino_dbt_supply_cost_reporting_v1",
    default_args=args,
    schedule_interval="9 0,1,13 * * *",
    tags=["supply_cost_reporting"],
    catchup=False,
)

env = {}
default_env = {
    "AIRFLOW_DAG_ID": "de_dwh_etl_trino_dbt_supply_cost_reporting_v1",
    "AIRFLOW_DAG_OWNER": "<EMAIL>",
}
env.update(default_env)


supply_cost_master_sheet_id = "1_1wXHH0BmjEeUN6E-vTnG53t9sa4yDxjk0fabGHx2W0"


## Define Tasks for execution
push_packaging_cost_to_trino = PythonOperator(
    task_id="push_packaging_cost_to_trino",
    python_callable=push_data_from_sheets_to_trino,
    op_kwargs={
        "supply_cost_master_sheet_id": supply_cost_master_sheet_id,
        "sheet_name": "packaging_cost_outlet_wise",
        "table_name": "ss_supply_cost_packaging_cost_outlet_wise",
        "table_description": "Contains packaging cost at outlet level with date range",
        "column_dtypes": packaging_cost_column_dtypes,
        "primary_key_list": ["updated_at", "outlet_id"],
        "load_type": "truncate",
        "env": env,
    },
    dag=dag,
    executor_config=k8s_executor_config,
)

push_offroll_ratecard_to_trino = PythonOperator(
    task_id="push_offroll_ratecard_to_trino",
    python_callable=push_data_from_sheets_to_trino,
    op_kwargs={
        "supply_cost_master_sheet_id": supply_cost_master_sheet_id,
        "sheet_name": "offroll_rate_card",
        "table_name": "ss_supply_cost_offroll_rate_card",
        "table_description": "Contains rate card(offroll) for different level",
        "column_dtypes": offroll_ratecard_column_dtypes,
        "primary_key_list": ["start_date"],
        "load_type": "truncate",
        "env": env,
    },
    dag=dag,
    executor_config=k8s_executor_config,
)

push_ds_bloc_store_type_mapping_to_trino = PythonOperator(
    task_id="push_ds_bloc_store_type_mapping",
    python_callable=push_data_from_sheets_to_trino,
    op_kwargs={
        "supply_cost_master_sheet_id": supply_cost_master_sheet_id,
        "sheet_name": "ds_bloc_store_type_mapping",
        "table_name": "ss_supply_cost_ds_outlet_bloc_mapping",
        "table_description": "Contains bloc & store type mapping of outlet ",
        "column_dtypes": outlet_mapping_details_column_dtypes,
        "primary_key_list": ["pos_outlet_id"],
        "load_type": "truncate",
        "env": env,
    },
    dag=dag,
    executor_config=k8s_executor_config,
)

push_fc_replenishment_cost_to_trino = PythonOperator(
    task_id="push_fc_replenishment_cost_to_trino",
    python_callable=push_data_from_sheets_to_trino,
    op_kwargs={
        "supply_cost_master_sheet_id": supply_cost_master_sheet_id,
        "sheet_name": "fc_replenishment_cost",
        "table_name": "ss_supply_cost_fc_replenishment_cost",
        "table_description": "Contains Fixed Cost details at Fulfillment Center level",
        "primary_key_list": ["fc_id"],
        "column_dtypes": fc_cost_details,
        "load_type": "truncate",
        "env": env,
    },
    dag=dag,
    executor_config=k8s_executor_config,
)

push_fc_replenishment_cost_fixed_variable_to_trino = PythonOperator(
    task_id="push_fc_replenishment_cost_fixed_variable_to_trino",
    python_callable=push_data_from_sheets_to_trino,
    op_kwargs={
        "supply_cost_master_sheet_id": supply_cost_master_sheet_id,
        "sheet_name": "fc_replenishment_cost_new",
        "table_name": "ss_supply_cost_fc_replenishment_cost_fixed_variable",
        "table_description": "Contains Fixed Cost details at Fulfillment Center level",
        "primary_key_list": ["fc_id"],
        "column_dtypes": fc_cost_details_variable_fixed,
        "load_type": "truncate",
        "env": env,
    },
    dag=dag,
    executor_config=k8s_executor_config,
)

push_ds_onroll_manpower_cost_to_trino = PythonOperator(
    task_id="push_ds_onroll_manpower_cost_to_trino",
    python_callable=push_data_from_sheets_to_trino,
    op_kwargs={
        "supply_cost_master_sheet_id": supply_cost_master_sheet_id,
        "sheet_name": "DS Onroll Manpower Cost",
        "table_name": "ss_supply_cost_ds_onroll_manpower_cost",
        "table_description": "This table contains base data for Dark Store Manpower Cost",
        "column_dtypes": ds_onroll_manpower_column_dtypes,
        "load_type": "truncate",
        "primary_key_list": [
            "city",
            "function",
            "exact_working_location",
            "role",
            "status",
            "start_date",
            "end_date",
        ],
        "env": env,
    },
    dag=dag,
    executor_config=k8s_executor_config,
)

push_partner_commission_mg_to_trino = PythonOperator(
    task_id="push_partner_commission_mg_to_trino",
    python_callable=push_data_from_sheets_to_trino,
    op_kwargs={
        "supply_cost_master_sheet_id": supply_cost_master_sheet_id,
        "sheet_name": "partner_commission_mg",
        "table_name": "ss_supply_cost_partner_commission_mg",
        "table_description": "This table contains the Partner Commission and Minimum Guarantee details of all the outlets",
        "column_dtypes": partner_commission_mg_column_dtypes,
        "load_type": "truncate",
        "env": env,
    },
    dag=dag,
    executor_config=k8s_executor_config,
)

push_own_ds_cost_to_trino = PythonOperator(
    task_id="push_own_ds_cost_to_trino",
    python_callable=push_data_from_sheets_to_trino,
    op_kwargs={
        "supply_cost_master_sheet_id": supply_cost_master_sheet_id,
        "sheet_name": "own_ds_rent",
        "table_name": "ss_supply_cost_own_ds_rent",
        "table_description": "This table contains rent of owned Dark Stores",
        "column_dtypes": own_ds_rent_column_dtypes,
        "load_type": "truncate",
        "env": env,
    },
    dag=dag,
    executor_config=k8s_executor_config,
)

push_gna_cost_to_trino = PythonOperator(
    task_id="push_gna_cost_to_trino",
    python_callable=push_data_from_sheets_to_trino,
    op_kwargs={
        "supply_cost_master_sheet_id": supply_cost_master_sheet_id,
        "sheet_name": "gna_cost",
        "table_name": "ss_supply_cost_gna_cost",
        "table_description": "Contains Merchant GnA Cost",
        "column_dtypes": gna_cost_column_dtypes,
        "env": env,
    },
    dag=dag,
    executor_config=k8s_executor_config,
)

dbt_base = "cd /dbt-models/trino_dwh && dbt --cache-selected-only "
dbt_run_daily_packaging_cost = BashOperator(
    task_id="dbt_run_daily_packaging_cost",
    bash_command=dbt_base
    + "run --models +merchant_level_packaging_cost --exclude staging"
    + ' --vars \'{"run_date": "{{ data_interval_end }}", "dag_id": "{{ dag.dag_id }}", "dag_run_id": "{{ run_id }}"}\''
    + " "
    + ' --target prod_p0',
    dag=dag,
    executor_config=k8s_executor_config_dbt,
)

dbt_run_daily_gna_cost = BashOperator(
    task_id="dbt_run_daily_gna_cost",
    bash_command=dbt_base
    + "run --models +dark_store_daily_gna_cost --exclude staging"
    + ' --vars \'{"run_date": "{{ data_interval_end }}", "dag_id": "{{ dag.dag_id }}", "dag_run_id": "{{ run_id }}"}\''
    + " "
    + ' --target prod_p0',
    dag=dag,
    executor_config=k8s_executor_config_dbt,
)

dbt_run_daily_rent_cost = BashOperator(
    task_id="dbt_run_daily_rent_cost",
    bash_command=dbt_base
    + "run --models +dark_store_daily_rent --exclude staging"
    + ' --vars \'{"run_date": "{{ data_interval_end }}", "dag_id": "{{ dag.dag_id }}", "dag_run_id": "{{ run_id }}"}\''
    + " "
    + ' --target prod_p0',
    dag=dag,
    executor_config=k8s_executor_config_dbt,
)

dbt_run_fc_to_ds_percentage_share = BashOperator(
    task_id="dbt_run_fc_to_ds_percentage_share",
    bash_command=dbt_base
    + "run --models +fc_to_ds_percentage_share_calculation --exclude staging"
    + ' --vars \'{"run_date": "{{ data_interval_end }}", "dag_id": "{{ dag.dag_id }}", "dag_run_id": "{{ run_id }}"}\''
    + " "
    + ' --target prod_p0',
    dag=dag,
    executor_config=k8s_executor_config_dbt,
)

dbt_catalog_fc_to_ds_percentage_share = PythonOperator(
    task_id="dbt_catalog_fc_to_ds_percentage_share",
    dag=dag,
    python_callable=add_catalog_fc_to_ds_percentage,
    executor_config=k8s_executor_config,
)

# Set Execution Flow
(
    [
        push_offroll_ratecard_to_trino,
        push_ds_bloc_store_type_mapping_to_trino,
        push_fc_replenishment_cost_to_trino,
        push_fc_replenishment_cost_fixed_variable_to_trino,
        push_ds_onroll_manpower_cost_to_trino,
        push_packaging_cost_to_trino,
        push_partner_commission_mg_to_trino,
        push_own_ds_cost_to_trino,
        push_gna_cost_to_trino
    ]

    >> dbt_run_daily_packaging_cost
    >> dbt_run_daily_gna_cost
    >> dbt_run_daily_rent_cost
    >> dbt_run_fc_to_ds_percentage_share
    >> dbt_catalog_fc_to_ds_percentage_share
)

if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)