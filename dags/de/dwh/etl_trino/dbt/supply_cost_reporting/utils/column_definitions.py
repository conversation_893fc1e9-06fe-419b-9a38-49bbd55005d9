outlet_mapping_details_column_dtypes = [
    {
        "name": "pos_outlet_id",
        "type": "BIGINT",
        "description": "Outlet Unique ID",
    },
    {
        "name": "store_type",
        "type": "VARCHAR",
        "description": "Type of Outlet: Captive/ 3PL/ Partner Run",
    },
    {
        "name": "city",
        "type": "VARCHAR",
        "description": "Name of the city",
    },
    {
        "name": "bloc",
        "type": "VARCHAR",
        "description": "Bloc of outlet",
    },
    {
        "name": "capex_status",
        "type": "VARCHAR",
        "description": "Capex Status of Outlet",
    },
    {
        "name": "store_zone",
        "type": "VARCHAR",
        "description": "Zone of Outlet",
    },
]

fc_cost_details = [
    {
        "name": "fc_id",
        "type": "BIGINT",
        "description": "Fullfillment Center(FC) Unique-id",
    },
    {
        "name": "fc_identifier",
        "type": "VARCHAR",
        "description": "Fullfillment Center name by Finance Team",
    },
    {
        "name": "fc_location",
        "type": "VARCHAR",
        "description": "Location of Fullfillment Center",
    },
    {
        "name": "type",
        "type": "VARCHAR",
        "description": "Warehouse Type - PC/CPC/FC", 
    },
    {
        "name": "operational_flag",
        "type": "INTEGER",
        "description": "FC operational or not",
    },
    {
        "name": "fc_cost_start_date",
        "type": "DATE",
        "description": "FC Cost starting Date ",
    },
    {
        "name": "fc_cost_end_date",
        "type": "DATE",
        "description": "FC Cost starting Date",
    },
    {
        "name": "fixed_rent",
        "type": "REAL",
        "description": "FC total fixed rent",
    },
    {
        "name": "general_admin_cost",
        "type": "REAL",
        "description": "FC total fixed G&A Cost",
    },
    {
        "name": "it_cost",
        "type": "REAL",
        "description": "FC total IT Cost",
    },
    {
        "name": "hk_and_security_guard_cost",
        "type": "REAL",
        "description": "Total Security Cost",
    },
    {
        "name": "onroll_manpower_cost",
        "type": "REAL",
        "description": "FC: total onroll manpower cost",
    },
    {
        "name": "offroll_manpower_cost",
        "type": "REAL",
        "description": "FC: total offroll manpower cost",
    },
    {
        "name": "transport_cost",
        "type": "REAL",
        "description": "FC: Bus & Feeder Charges ",
    },
    {
        "name": "sc_3pl_cost",
        "type": "REAL",
        "description": "FC: 3PL Cost",
    },
    {
        "name": "outbound_units",
        "type": "REAL",
        "description": "FC Total outbount units",
    },
    {
        "name": "gst_reversal",
        "type": "REAL",
        "description": "FC Total GST Reversal",
    },
    {
        "name": "final_cost",
        "type": "REAL",
        "description": "FC Total Cost",
    },
    {
        "name": "cost_per_unit",
        "type": "REAL",
        "description": "FC Cost per unit",
    },
]

fc_cost_details_variable_fixed = [
    {
        "name": "fc_id",
        "type": "BIGINT",
        "description": "Fullfillment Center(FC) Unique-id",
    },
    {
        "name": "fc_identifier",
        "type": "VARCHAR",
        "description": "Fullfillment Center name by Finance Team",
    },
    {
        "name": "fc_location",
        "type": "VARCHAR",
        "description": "Location of Fullfillment Center",
    },
    {
        "name": "type",
        "type": "VARCHAR",
        "description": "Warehouse Type - PC/CPC/FC", 
    },
    {
        "name": "operational_flag",
        "type": "INTEGER",
        "description": "FC operational or not",
    },
    {
        "name": "fc_cost_start_date",
        "type": "DATE",
        "description": "FC Cost starting Date ",
    },
    {
        "name": "fc_cost_end_date",
        "type": "DATE",
        "description": "FC Cost starting Date",
    },
    {
        "name": "final_cost_variable",
        "type": "REAL",
        "description": "FC Total Cost Variable",
    },
    {
        "name": "final_cost_fixed",
        "type": "REAL",
        "description": "FC Total Cost Fixed",
    }
]

offroll_ratecard_column_dtypes = [
    {
        "name": "start_date",
        "type": "DATE",
        "description": "rate card start_date",
    },
    {
        "name": "end_date",
        "type": "DATE",
        "description": "rate card end date)",
    },
    {
        "name": "updated_at",
        "type": "DATE",
        "description": "rate card updated at",
    },
    {
        "name": "city",
        "type": "VARCHAR",
        "description": "name of the city",
    },
    {
        "name": "biller_rate",
        "type": "REAL",
        "description": "biller daily rate",
    },
    {
        "name": "picker_rate",
        "type": "REAL",
        "description": "picker daily rate",
    },
    {
        "name": "assistant_putter_loader_rate",
        "type": "REAL",
        "description": "assistant_putter_loader daily rate",
    },
    {
        "name": "team_leader_rate",
        "type": "REAL",
        "description": "team_leader daily rate ",
    },
    {
        "name": "fnv_executive_rate",
        "type": "REAL",
        "description": "fnv_executive daily rate ",
    },    
]

ds_onroll_manpower_column_dtypes = [
    {
        "name": "city",
        "type": "VARCHAR",
        "description": "City of outlet",
    },
    {
        "name": "function",
        "type": "VARCHAR",
        "description": "Function of outlet",
    },
    {
        "name": "exact_working_location",
        "type": "VARCHAR",
        "description": "Working location of outlets",
    },
    {
        "name": "role",
        "type": "VARCHAR",
        "description": "Role of employee",
    },
    {
        "name": "status",
        "type": "VARCHAR",
        "description": "Status of employee - active/inactive/resigned",
    },
    {
        "name": "start_date",
        "type": "DATE",
        "description": "Cost record start date",
    },
    {
        "name": "end_date",
        "type": "DATE",
        "description": "Cost record end date",
    },
    {
        "name": "employee_count",
        "type": "REAL",
        "description": "Count of employees in outlet",
    },
    {
        "name": "per_month_salary_cost",
        "type": "REAL",
        "description": "Salary of employee per month",
    },   
]

packaging_cost_column_dtypes = [
    {
        "name": "start_date",
        "type": "DATE",
        "description": "Period when the packaging cost is decided for a city",
    },
    {
        "name": "end_date",
        "type": "DATE",
        "description": "Period till the packaging cost is valid for a city",
    },
    {
        "name": "updated_at",
        "type": "DATE",
        "description": "When the packaging cost is updated in the system for each city",
    },
    {
        "name": "outlet_id",
        "type": "INTEGER",
        "description": "Packaging cost is calculated per outlet",
    },
    {
        "name": "per_order_packaging_cost",
        "type": "REAL",
        "description": "Packaging cost for each order",
    },  
]

partner_commission_mg_column_dtypes = [
    {
        "name": "date",
        "type": "DATE",
        "description": "Date of the snapshot",
    },
    {
        "name": "outlet_id",
        "type": "INTEGER",
        "description": "Outlet ID",
    },
    {
        "name": "outlet_name", 
        "type": "VARCHAR", 
        "description": "Name of the outlet"
    },
    {
        "name": "outlet_city",
        "type": "VARCHAR",
        "description": "City where outlet is located",
    },
    {
        "name": "partner_variable_commission", 
        "type": "REAL", 
        "description": "Commission of the outlet"
    },
    {
        "name": "minimum_guarantee",
        "type": "REAL",
        "description": "Minimum Guarantee for the outlet",
    },
]

own_ds_rent_column_dtypes = [
    {
        "name": "store_name",
        "type": "VARCHAR",
        "description": "Dark Store Name",
    },
    {
        "name": "status",
        "type": "VARCHAR",
        "description": "Status of the Dark Store",
    },
    {
        "name": "property_tyep",
        "type": "VARCHAR",
        "description": "Property Type of the Dark Stores",
    },
    {
        "name": "outlet_id",
        "type": "VARCHAR",
        "description": "Outlet Id",
    },
    {
        "name": "city",
        "type": "VARCHAR",
        "description": "City Name",
    },
    {
        "name": "payment_start_cycle",
        "type": "DATE",
        "description": "Rent start date",
    },
    {
        "name": "payment_end_cycle",
        "type": "DATE",
        "description": "Rent end date",
    },
    {
        "name": "rent_amount",
        "type": "REAL",
        "description": "Rent off Dark Store per month",
    },   
]

gna_cost_column_dtypes = [
    {
        "name": "frontend_merchant_id",
        "type": "INTEGER",
        "description": "Frontend Merchant Id",
    },
    {
        "name": "city_name",
        "type": "VARCHAR",
        "description": "Merchant City Name",
    },
    {
        "name": "frontend_merchant_name",
        "type": "VARCHAR",
        "description": "Frontend Merchant Name",
    },
    {
        "name": "payment_start_cycle",
        "type": "DATE",
        "description": "Payment Start Cycle Date",
    },
    {
        "name": "payment_end_cycle",
        "type": "DATE",
        "description": "Payment End Cycle Date",
    },
    {
        "name": "outlet_id",
        "type": "INTEGER",
        "description": "Outlet Id",
    },
    {
        "name": "security_cost",
        "type": "REAL",
        "description": "Security Cost",
    },
    {
        "name": "house_keeping_cost",
        "type": "REAL",
        "description": "House Keeping Cost",
    },
    {
        "name": "electricity_cost",
        "type": "REAL",
        "description": "Electricity Cost",
    },
    {
        "name": "electricity_cost_cold_infra",
        "type": "REAL",
        "description": "Electricity Cost for Cold Infra",
    },
    {
        "name": "generator_cost",
        "type": "REAL",
        "description": "Generator Cost",
    },
    {
        "name": "diesel_provisioning_cost",
        "type": "REAL",
        "description": "Diesel Provisioning Cost",
    },
    {
        "name": "internet_cost",
        "type": "REAL",
        "description": "Internet Cost",
    },
    {
        "name": "repairs_and_maintenance_cost",
        "type": "REAL",
        "description": "Repairs and Maintenance Cost",
    },
    {
        "name": "drinking_water_cost",
        "type": "REAL",
        "description": "Drinking Water Cost",
    },
    {
        "name": "pest_control_cost",
        "type": "REAL",
        "description": "Pest Control Cost",
    },
    {
        "name": "printing_and_stationary_cost",
        "type": "REAL",
        "description": "Printing and Stationary Cost",
    },
    {
        "name": "loading_unloading_cost",
        "type": "REAL",
        "description": "Loading Unloading Cost",
    },
    {
        "name": "total_gna_cost",
        "type": "REAL",
        "description": "Totak GnA Cost",
    },
]

fc_to_ds_dtypes = [
    {
        "name": "date_ist",
        "type": "DATE",
        "description": "date at which these share from fulfillment center to dark stores are calculated",
    },
    {
        "name": "facility_id",
        "type": "INTEGER",
        "description": "fulfillment center id",
    },
    {
        "name": "fc_name",
        "type": "VARCHAR",
        "description": "fulfillment center name",
    },
    {
        "name": "fc_identifier",
        "type": "VARCHAR",
        "description": "fulfillment center specific identifier",
    },
    {
        "name": "pos_outlet_id",
        "type": "INTEGER",
        "description": "dark store id",
    },
    {
        "name": "total_outbound",
        "type": "DOUBLE",
        "description": "how many total units of quantity went to a dark store",
    },
    {
        "name": "daily_total_fc_outbounds_unit",
        "type": "DOUBLE",
        "description": "how many total units of quantity went out of the FC to all stores",
    },
    {
        "name": "percentage_ds_share",
        "type": "DOUBLE",
        "description": "share of  quantity outbound to a store from FC",
    },
]