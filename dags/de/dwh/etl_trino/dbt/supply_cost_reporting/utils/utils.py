import pencilbox as pb
import pandas as pd
from calendar import monthrange
from datetime import datetime
import os, sys

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.column_definitions import (
    outlet_mapping_details_column_dtypes,
    fc_cost_details,
    fc_cost_details_variable_fixed,
    offroll_ratecard_column_dtypes,
    ds_onroll_manpower_column_dtypes,
    packaging_cost_column_dtypes,
    partner_commission_mg_column_dtypes,
    own_ds_rent_column_dtypes,
    gna_cost_column_dtypes,
    fc_to_ds_dtypes
)

def push_data_from_sheets_to_trino(
    supply_cost_master_sheet_id,
    sheet_name,
    table_name,
    table_description,
    column_dtypes,
    env,
    catalog="blinkit_iceberg",
    schema_name="interim",
    primary_key_list=[],
    sortkey_list=[],
    load_type="truncate"
):

    if env:
        for key, value in env.items():
            os.environ[key] = value

    source_df = pb.from_sheets(supply_cost_master_sheet_id, sheet_name)
    source_df.columns = source_df.columns.str.strip().str.lower().str.replace("[-, \, /, ?, #, @, (, ), \n, .]", "_")
    
    # print("\n") ##Delete
    # print('******************************')
    # print(source_df.dtypes)
    # print(source_df.columns)
    # print('******************************')
    # print(source_df.describe)
    # print("\n")
    # print('******************************')
    # print("Conversion getting Started")
    # print(source_df.dtypes)
    # print("\n") ##Delete
    
    def handling_dtype_conversion(column_name, data_type_str, value):
        try:
            if data_type_str == "DATE":
                return pd.to_datetime(value)
            elif data_type_str == "INTEGER":
                return pd.to_numeric(value, errors='coerce', downcast='integer')
            elif data_type_str == "BIGINT":
                return pd.to_numeric(value, errors='coerce', downcast='integer')
            elif data_type_str == "REAL":
                return pd.to_numeric(value, errors='coerce', downcast='float')
            elif data_type_str == "VARCHAR":
                return str(value)
            else:
                return None
        except ValueError:
            print(f"Conversion failed for column {column_name} with type {data_type_str}")
            return None

    def convert_data_types(source_df, column_data_types):
        def rename_columns_with_numbers(source_df):
            renamed_columns = {}
            for column_name in source_df.columns:
                if column_name[0].isdigit():
                    new_column_name = f"sc_{column_name}"
                    renamed_columns[column_name] = new_column_name
            return source_df.rename(columns=renamed_columns)

        source_df = rename_columns_with_numbers(source_df)
        
        for column_info in column_data_types:
            column_name = column_info["name"]
            data_type_str = column_info["type"]
            description = column_info["description"]

            if column_name not in source_df.columns:
                print(f"Column {column_name} not found in the source DataFrame.")
                continue

            source_df[column_name] = source_df[column_name].apply(
                lambda x: handling_dtype_conversion(column_name, data_type_str, x)
            )
            print(f"Converted column: {column_name} with type {data_type_str}")

        return source_df

    final_df = convert_data_types(source_df, column_dtypes)
    
    # print('******************************') ##Delete
    # print("\n")
    # print('******************************')
    # print(final_df.dtypes)
    # print("Columns converted Successfully")
    # print('******************************') 
    # print("\n") ##Delete
    
    kwargs = {
            "sheet_name": sheet_name,
            "catalog": catalog,
            "schema_name": schema_name,
            "table_name": table_name,
            "table_description": table_description,
            "primary_key": primary_key_list,
            "column_dtypes": column_dtypes,
            "sortkey": sortkey_list,
            "load_type": load_type,
    }
     
    # print("Final Column dtypes") ##Delete
    # print(final_df.dtypes) ##Delete
    
    return pb.to_trino(final_df, **kwargs)