import sys,os
import logging
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.bash_operator import <PERSON>sh<PERSON>perator
from airflow.operators.python_operator import PythonOperator
from kubernetes.client import models as k8s
from airflow.models import Variable
import pencilbox as pb

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import k8s_executor_config, k8s_executor_config_dbt
from column_definitions import column_dtypes, column_dtypes_invoice

def add_table_catalog_fact_invoice():
    pb.to_datahub(
        schema_name="dwh",
        table_name="fact_sales_invoice_item_details",
        column_dtypes= column_dtypes,
        table_description="This table Contains Sales & margin related information at item-variant level from POS system. ",
        data_platform="trino",
        primary_keys=[],
        partition_keys=["order_create_date_ist"],
    )

    pb.to_datahub(
        schema_name="dwh",
        table_name="fact_invoice_item_details",
        column_dtypes= column_dtypes_invoice,
        table_description="This table Contains Sales & margin related information at invoice-item-variant level from POS system. ",
        data_platform="trino",
        primary_keys=[],
        partition_keys=["order_create_date_ist"],
    )


def slack_failure_alert(context):
    dag_id = context.get("task_instance").dag_id
    run_id = context.get("run_id")
    task_id = context.get("task_instance").task_id
    log_url = context.get("task_instance").log_url.replace("http://", "https://")

    owner = {"email": "<EMAIL>", "slack_id": "S03ST1ENERJ"}
    slack_id = owner["slack_id"]
    _owner = {
        "s": "s" if len(slack_id.split(" ")) > 1 else "",
        "slack_id": "<!subteam^{}>".format(slack_id),
    }

    slack_failure_msg = """
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner{s}*: {slack_id}
        *Log Url*: {log_url}
    """.format(
        dag_id=dag_id,
        run_id=run_id,
        task_id=task_id,
        s=_owner["s"],
        slack_id=_owner["slack_id"],
        log_url=log_url,
    )

    slack_alert_configs = [
        {"channel": "bl-dwh-alerts"},
    ]

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(
                channel=channel, text=slack_failure_msg, user="data-plumber"
            )
        except Exception as e:
            logging.warning(e)
            message = (
                f"<!subteam^S089D0GQRCJ> `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(
                channel="bl-dwh-alerts", text=message, user="data-plumber"
            )


args = {
    "slack_id": "S03ST1ENERJ",
    "owner": "<EMAIL>",
    "on_failure_callback": slack_failure_alert,
    "start_date": datetime.strptime("2023-07-15T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": datetime.strptime("2025-09-30T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "slack_id": "S03ST1ENERJ",
    "retries": 1,
    "retry_delay": timedelta(seconds=90),
    "retry_exponential_backoff" :True,
}

dag = DAG(
    dag_id="de_dwh_etl_trino_dbt_fact_sales_invoice_item_details_v1",
    default_args=args,
    schedule_interval="07 0 * * *",
    catchup= False,
    max_active_runs= 1,
)

dbt_base = "cd /dbt-models/trino_dwh && dbt --cache-selected-only "

# run_date is in UTC, need to handle this if the job scheduling is changed.
model_vars_1 = '\'{{"days_in_past": "{}","partition_days_filter": "{}", "run_date": "{}", "dag_id": "{}", "dag_run_id": "{}"}}\''.format(8, 15, "{{ data_interval_end }}", '{{ dag.dag_id }}', '{{ run_id }}')

model_vars_2 = '\'{{"days_in_past": "{}","partition_days_filter": "{}", "run_date": "{}", "dag_id": "{}", "dag_run_id": "{}"}}\''.format(7, 8, "{{ data_interval_end }}", '{{ dag.dag_id }}', '{{ run_id }}')

dbt_run_order_base = BashOperator(
    task_id="fact-invoice-sales-order-grain-base",
    dag=dag,
    bash_command=dbt_base
      + "run --select tag:order_grain_base --exclude staging"
      + " --vars "
      + model_vars_1
      + " "
      + " --target prod_p0",
    executor_config=k8s_executor_config_dbt,
)

dbt_run_order_interim = BashOperator(
    task_id="fact-invoice-sales-order-grain-interim",
    dag=dag,
    bash_command=dbt_base
      + "run --select tag:order_grain_interim --exclude staging"
      + " --vars "
      + model_vars_1
      + " "
      + " --target prod_p0",
    executor_config=k8s_executor_config_dbt,
)

dbt_run_order_core = BashOperator(
    task_id="fact-invoice-sales-order-grain-core",
    dag=dag,
    bash_command=dbt_base
      + "run --select tag:order_grain_core --exclude staging"
      + " --vars "
      + model_vars_1
      + " "
      + " --target prod_p0",
    executor_config=k8s_executor_config_dbt,
)

dbt_run_invoice_grain_interim = BashOperator(
    task_id="fact-invoice-sales-invoice-grain-interim",
    dag=dag,
    bash_command=dbt_base
      + "run --select tag:invoice_grain_interim --exclude staging"
      + " --vars "
      + model_vars_2
      + " "
      + " --target prod_p0",
    executor_config=k8s_executor_config_dbt,
)

dbt_run_invoice_grain_core = BashOperator(
    task_id="fact-invoice-sales-invoice-grain-core",
    dag=dag,
    bash_command=dbt_base
      + "run --select tag:invoice_grain_core --exclude staging"
      + " --vars "
      + model_vars_2
      + " "
      + " --target prod_p0",
    executor_config=k8s_executor_config_dbt,
)

dbt_catalog = PythonOperator(
    task_id="dbt-catalog-fact-invoice",
    dag=dag,
    python_callable=add_table_catalog_fact_invoice,
    executor_config=k8s_executor_config,
)

dbt_run_maintenance = BashOperator(
    task_id = "dbt_run_maintenance",
    bash_command = dbt_base
    + "run-operation run_maintenance "
    + ' --vars \'{"table_name": "dwh.fact_sales_invoice_item_details","partition_key": "order_create_date_ist", "dag_id": "{{ dag.dag_id }}", "dag_run_id": "{{ run_id }}"}\''
    + " "
    + " --target prod_p0",
    dag = dag,
    executor_config = k8s_executor_config_dbt,
)

dbt_run_maintenance_invoice = BashOperator(
    task_id = "dbt_run_maintenance_invoice",
    bash_command = dbt_base
    + "run-operation run_maintenance "
    + ' --vars \'{"table_name": "dwh.fact_invoice_item_details", "partition_key": "order_create_date_ist","dag_id": "{{ dag.dag_id }}", "dag_run_id": "{{ run_id }}"}\''
    + " "
    + " --target prod_p0",
    dag = dag,
    executor_config = k8s_executor_config_dbt,
)

dbt_run_order_base >> dbt_run_order_interim >> dbt_run_order_core >> dbt_catalog

dbt_run_order_base >> dbt_run_invoice_grain_interim >> dbt_run_invoice_grain_core >> dbt_catalog

dbt_catalog >> [dbt_run_maintenance, dbt_run_maintenance_invoice]


if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
