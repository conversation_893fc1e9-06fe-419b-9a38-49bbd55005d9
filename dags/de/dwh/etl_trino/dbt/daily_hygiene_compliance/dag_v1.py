# -*- coding: utf-8 -*-

import os
import sys
from datetime import datetime, timedelta, date

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from airflow import DAG
from airflow.operators.bash_operator import BashOperator
from airflow.operators.python_operator import PythonOperator
import pencilbox as pb

from utils.utils import (
    push_data_from_sheets_to_trino,
)

from utils.config import k8s_executor_config
import logging

from utils.column_definitions import (
    hygiene_data_summary_sheet_column_dtypes,
    hygiene_data_detail_sheet_column_dtypes,
    hygiene_data_cash_difference_sheet_column_dtypes,
    grc_data_summary_sheet_column_dtypes,
    grc_data_view_sheet_column_dtypes,
    warehouse_hygiene_sheet_column_dtypes
)

def slack_failure_alert(context):
    dag_id = context.get("task_instance").dag_id
    run_id = context.get("run_id")
    task_id = context.get("task_instance").task_id
    log_url = context.get("task_instance").log_url.replace("http://", "https://")

    owner = {"email": "<EMAIL>", "slack_id": "S03ST1ENERJ"}
    slack_id = owner["slack_id"]
    _owner = {
        "s": "s" if len(slack_id.split(" ")) > 1 else "",
        "slack_id": "<!subteam^{}>".format(slack_id),
    }

    slack_failure_msg = """
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner{s}*: {slack_id}
        *Log Url*: {log_url}
    """.format(
        dag_id=dag_id,
        run_id=run_id,
        task_id=task_id,
        s=_owner["s"],
        slack_id=_owner["slack_id"],
        log_url=log_url,
    )

    slack_alert_configs = [
        {"channel": "bl-dwh-alerts"},
    ]

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(
                channel=channel, text=slack_failure_msg, user="data-plumber"
            )
        except Exception as e:
            logging.warning(e)
            message = (
                f"<!subteam^S089D0GQRCJ> `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(
                channel="bl-data-alerts-p1", text=message, user="data-plumber"
            )

def failure_alerts(context):
    slack_failure_alert(context)

cwd = os.path.dirname(os.path.realpath(__file__))

args = {
    "slack_id":"S03ST1ENERJ",
    "owner": "<EMAIL>",
    "on_failure_callback": failure_alerts,
    "start_date": datetime.strptime("2023-10-24T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": datetime.strptime("2025-09-30T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "retries": 3,
    "retry_delay": timedelta(seconds=15),
    "retry_exponential_backoff": True,
}

dag = DAG(
    dag_id="de_dwh_etl_trino_dbt_compliance_dashboard",
    default_args=args,
    schedule_interval= "17 4 * * *",
)

env = {}
default_env = {
    "AIRFLOW_DAG_ID": "de_dwh_etl_trino_dbt_compliance_dashboard",
    "AIRFLOW_DAG_OWNER": "<EMAIL>",
}
env.update(default_env)


hygiene_data_master_sheet_id = "1pvaauA78xR-HlkzqGlQIojFCavY4181duWX5wWvK7yk"
grc_data_master_sheet_id = "1RHyU2qXBHzqfgcv6Bkz2VHQ6UwzjX7u-89fTdwwWiic"
warehouse_hygiene_master_sheet_id = "1saRpf-0dw-v6JsfKeFahDG-h4y6ZjDskYc-LOtCwcMk"

## Define Tasks for execution
push_hygiene_summary_to_trino = PythonOperator(
    task_id="push_hygiene_summary_to_trino",
    python_callable=push_data_from_sheets_to_trino,
    op_kwargs={
        "master_sheet_id": hygiene_data_master_sheet_id,
        "sheet_name": "summary",
        "table_name": "ss_hygiene_data_summary",
        "table_description": "Hygiene Summary Data",
        "column_dtypes": hygiene_data_summary_sheet_column_dtypes,
        "primary_key_list":["store_code", "date", "express_ss_name", "store_manager", "cluster_manager"],
        "load_type": "truncate",
        "env": env,
    },
    dag=dag,
    executor_config=k8s_executor_config,
)

push_hygiene_detail_to_trino = PythonOperator(
    task_id="push_hygiene_detail_to_trino",
    python_callable=push_data_from_sheets_to_trino,
    op_kwargs={
        "master_sheet_id": hygiene_data_master_sheet_id,
        "sheet_name": "detail",
        "table_name": "ss_hygiene_data_detail",
        "table_description": "Hygiene Detail Data",
        "column_dtypes": hygiene_data_detail_sheet_column_dtypes,
        "primary_key_list":["store_code", "date", "express_ss_name", "store_manager", "cluster_manager"],
        "load_type": "truncate",
        "env": env,
    },
    dag=dag,
    executor_config=k8s_executor_config,
)

push_hygiene_cash_difference_to_trino = PythonOperator(
    task_id="push_hygiene_cash_difference_to_trino",
    python_callable=push_data_from_sheets_to_trino,
    op_kwargs={
        "master_sheet_id": hygiene_data_master_sheet_id,
        "sheet_name": "cash_difference",
        "table_name": "ss_hygiene_data_cash_difference",
        "table_description": "Hygiene Cash Difference Data",
        "column_dtypes": hygiene_data_cash_difference_sheet_column_dtypes,
        "primary_key_list":["store_code", "date", "express_ss_name", "store_manager", "cluster_manager"],
        "load_type": "truncate",
        "env": env,
    },
    dag=dag,
    executor_config=k8s_executor_config,
)

push_grc_summary_to_trino = PythonOperator(
    task_id="push_grc_summary_to_trino",
    python_callable=push_data_from_sheets_to_trino,
    op_kwargs={
        "master_sheet_id": grc_data_master_sheet_id,
        "sheet_name": "grc_summary",
        "table_name": "ss_grc_data_summary",
        "table_description": "GRC Summary Data",
        "column_dtypes": grc_data_summary_sheet_column_dtypes,
        "primary_key_list":["date", "warehouse_name", "area"],
        "load_type": "truncate",
        "env": env,
    },
    dag=dag,
    executor_config=k8s_executor_config,
)

push_grc_view_to_trino = PythonOperator(
    task_id="push_grc_view_to_trino",
    python_callable=push_data_from_sheets_to_trino,
    op_kwargs={
        "master_sheet_id": grc_data_master_sheet_id,
        "sheet_name": "grc_view",
        "table_name": "ss_grc_data_view",
        "table_description": "GRC View Data",
        "column_dtypes": grc_data_view_sheet_column_dtypes,
        "primary_key_list":["date", "warehouse_name"],
        "load_type": "truncate",
        "env": env,
    },
    dag=dag,
    executor_config=k8s_executor_config,
)

push_warehouse_hygiene_consol_view_to_trino = PythonOperator(
    task_id="push_warehouse_hygiene_consol_view_to_trino",
    python_callable=push_data_from_sheets_to_trino,
    op_kwargs={
        "master_sheet_id": warehouse_hygiene_master_sheet_id,
        "sheet_name": "Consol",
        "table_name": "ss_warehouse_hygiene_consol_data",
        "table_description": "ZHPL Warehouse Hygiene Consol Data",
        "column_dtypes": warehouse_hygiene_sheet_column_dtypes,
        "primary_key_list":["month", "fc_name"],
        "load_type": "truncate",
        "env": env,
    },
    dag=dag,
    executor_config=k8s_executor_config,
)

# Set Execution Flow
(
    [
        push_hygiene_summary_to_trino,
        push_hygiene_detail_to_trino,
        push_hygiene_cash_difference_to_trino,
        push_grc_summary_to_trino,
        push_grc_view_to_trino,
        push_warehouse_hygiene_consol_view_to_trino
    ]
)

if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
