import os
import sys

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils import utils
from utils.column_definitions import (
    hygiene_data_summary_sheet_column_dtypes,
    hygiene_data_detail_sheet_column_dtypes,
    hygiene_data_cash_difference_sheet_column_dtypes,
    grc_data_summary_sheet_column_dtypes,
    warehouse_hygiene_sheet_column_dtypes
)

if __name__ == '__main__':
    print('** I am in Main **')
    
    hygiene_data_master_sheet_id = "1pvaauA78xR-HlkzqGlQIojFCavY4181duWX5wWvK7yk"
    grc_data_master_sheet_id = "1RHyU2qXBHzqfgcv6Bkz2VHQ6UwzjX7u-89fTdwwWiic"
    warehouse_hygiene_master_sheet_id = "1saRpf-0dw-v6JsfKeFahDG-h4y6ZjDskYc-LOtCwcMk"

    env = {
        "AIRFLOW_DAG_ID": "de_dwh_etl_trino_dbt_compliance_dashboard_v1",
        "AIRFLOW_DAG_OWNER": "<EMAIL>",
    }
    
    # op_kwargs={
    #     "master_sheet_id": hygiene_data_master_sheet_id,
    #     "sheet_name": "summary",
    #     "table_name": "ss_hygiene_data_summary",
    #     "table_description": "Hygiene Summary Data",
    #     "column_dtypes": hygiene_data_summary_sheet_column_dtypes,
    #     "primary_key_list":["store_code", "date", "express_ss_name", "store_manager", "cluster_manager"],
    #     "load_type": "truncate",
    #     "env": env,
    # }

#     op_kwargs={
#         "master_sheet_id": hygiene_data_master_sheet_id,
#         "sheet_name": "detail",
#         "table_name": "ss_hygiene_data_detail",
#         "table_description": "Hygiene Detail Data",
#         "column_dtypes": hygiene_data_detail_sheet_column_dtypes,
#         "primary_key_list":["store_code", "date", "express_ss_name", "store_manager", "cluster_manager"],
#         "load_type": "truncate",
#         "env": env,
#     }
    
#     op_kwargs={
#         "master_sheet_id": hygiene_data_master_sheet_id,
#         "sheet_name": "cash_difference",
#         "table_name": "ss_hygiene_data_cash_difference",
#         "table_description": "Hygiene Cash Difference Data",
#         "column_dtypes": hygiene_data_cash_difference_sheet_column_dtypes,
#         "primary_key_list":["store_code", "date", "express_ss_name", "store_manager", "cluster_manager"],
#         "load_type": "truncate",
#         "env": env,
#     }

    # op_kwargs={
    #     "master_sheet_id": grc_data_master_sheet_id,
    #     "sheet_name": "grc_summary",
    #     "table_name": "ss_grc_data_summary",
    #     "table_description": "GRC Summary Data",
    #     "column_dtypes": grc_data_summary_sheet_column_dtypes,
    #     "primary_key_list":["date", "warehouse_name", "area"],
    #     "load_type": "truncate",
    #     "env": env,
    # }
    
    # op_kwargs={
    #     "master_sheet_id": grc_data_master_sheet_id,
    #     "sheet_name": "grc_view",
    #     "table_name": "ss_grc_data_view",
    #     "table_description": "GRC View Data",
    #     "column_dtypes": grc_data_view_sheet_column_dtypes,
    #     "primary_key_list":["date", "warehouse_name"],
    #     "load_type": "truncate",
    #     "env": env,
    # }
    
        
    op_kwargs={
        "master_sheet_id": warehouse_hygiene_master_sheet_id,
        "sheet_name": "Consol",
        "table_name": "ss_warehouse_hygiene_consol_data",
        "table_description": "ZHPL Warehouse Hygiene Consol Data",
        "column_dtypes": warehouse_hygiene_sheet_column_dtypes,
        "primary_key_list":["month", "fc_name"],
        "load_type": "truncate",
        "env": env,
    }
    
    print(op_kwargs)
    
    #call task - 1
    python_callable=utils.push_data_from_sheets_to_trino(**op_kwargs)
    # print(python_callable)
    
    print('** Out of Main **')