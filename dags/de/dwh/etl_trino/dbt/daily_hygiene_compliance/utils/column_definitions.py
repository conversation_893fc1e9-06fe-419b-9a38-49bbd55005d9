hygiene_data_summary_sheet_column_dtypes = [
    {
        "name": "store_code", 
        "type": "INTEGER", 
        "description": "Outlet Id of the store"
    },
    {
        "name": "date", 
        "type": "DATE", 
        "description": "Date of audit"
    },
    {
        "name": "express_ss_name",
        "type": "VARCHAR",
        "description": "Store name",
    },
    {
        "name": "city",
        "type": "VARCHAR",
        "description": "City Name",
    },
    {
        "name": "store_manager",
        "type": "VARCHAR",
        "description": "Store manager's name",
    },
    {
        "name": "cluster_manager",
        "type": "VARCHAR",
        "description": "Cluster manager's name",
    },
    {
        "name": "ops_head",
        "type": "VARCHAR",
        "description": "City ops head's name",
    },
    {
        "name": "city_ceo",
        "type": "VARCHAR",
        "description": "City CEO's name",
    },
    {
        "name": "in_store_staff_hygiene",
        "type": "DOUBLE",
        "description": "Score of In-store staff hygiene",
    },
    {
        "name": "material_storage_and_cleanliness",
        "type": "DOUBLE",
        "description": "Score for Material storage and cleanliness",
    },
    {
        "name": "store_upkeep_and_hygiene",
        "type": "DOUBLE",
        "description": "Score of Store upkeep and hygiene",
    },
    {
        "name": "pest_and_rodent_related_checks",
        "type": "DOUBLE",
        "description": "Score for Pest and rodent related checks",
    },
    {
        "name": "overall_score",
        "type": "DOUBLE",
        "description": "Store's score",
    }
]

hygiene_data_detail_sheet_column_dtypes = [
    {
        "name": "store_code", 
        "type": "INTEGER", 
        "description": "Outlet ID of the store"
    },
    {
        "name": "date", 
        "type": "DATE", 
        "description": "Date of Audit"
    },
    
    {
        "name": "mode_of_operation",
        "type": "VARCHAR",
        "description": "Mode of store operation",
    },
    {
        "name": "express_ss_name",
        "type": "VARCHAR",
        "description": "Store Name",
    },
    {
        "name": "city",
        "type": "VARCHAR",
        "description": "City Name",
    },
    {
        "name": "city_category",
        "type": "VARCHAR",
        "description": "City clubbing",
    },
    {
        "name": "store_manager",
        "type": "VARCHAR",
        "description": "Store manager's name",
    },
    {
        "name": "cluster_manager",
        "type": "VARCHAR",
        "description": "Cluster manager's name",
    },
    {
        "name": "ops_head",
        "type": "VARCHAR",
        "description": "City ops head's name",
    },
    {
        "name": "city_ceo",
        "type": "VARCHAR",
        "description": "City CEO's name",
    },
    {
        "name": "category",
        "type": "VARCHAR",
        "description": "Critical and Non-Critical Category",
    },
    {
        "name": "area",
        "type": "VARCHAR",
        "description": "Area Checklist",
    },
    {
        "name": "parameter",
        "type": "VARCHAR",
        "description": "Evaluation parameter",
    },
    {
        "name": "parameter_summary",
        "type": "VARCHAR",
        "description": "Summary of parameter",
    },
    {
        "name": "rating_criteria",
        "type": "VARCHAR",
        "description": "How the rating was given",
    },
    {
        "name": "maximum_score",
        "type": "INTEGER",
        "description": "Maximum possible score for this parameter",
    },
    {
        "name": "score",
        "type": "INTEGER",
        "description": "Score obtained",
    },
    {
        "name": "weightage",
        "type": "INTEGER",
        "description": "Weightage of the parameter",
    },
    {
        "name": "weighted_score",
        "type": "INTEGER",
        "description": "Weightage score of the parameter",
    },
    {
        "name": "weighted_maximum_score",
        "type": "INTEGER",
        "description": "Maximum possible weighted score for the parameter",
    },
    {
        "name": "remarks",
        "type": "VARCHAR",
        "description": "Auditor remarks",
    },
    {
        "name": "issue_flag",
        "type": "VARCHAR",
        "description": "Check if there was an issue or not",
    }
]

hygiene_data_cash_difference_sheet_column_dtypes = [
     {
        "name": "date",
        "type": "DATE",
        "description": "Audit Date",
     },  
     {
        "name": "store_code",
        "type": "INTEGER",
        "description": "Outlet ID of the store",
     },
     {
        "name": "express_ss_name",
        "type": "VARCHAR",
        "description": "Store's name",
     },
     {
        "name": "city",
        "type": "VARCHAR",
        "description": "City Name",
     },
     {
        "name": "city_category",
        "type": "VARCHAR",
        "description": "City clubbing",
     },
     {
        "name": "city_zone",
        "type": "VARCHAR",
        "description": "City Zone",
     },
     {
        "name": "store_manager",
        "type": "VARCHAR",
        "description": "Store manager's name",
    },
    {
        "name": "cluster_manager",
        "type": "VARCHAR",
        "description": "Cluster manager's name",
    },
    {
        "name": "ops_head",
        "type": "VARCHAR",
        "description": "City ops head's name",
    },
    {
        "name": "city_ceo",
        "type": "VARCHAR",
        "description": "City CEO's name",
    },
    {
        "name": "system_cash",
        "type": "DOUBLE",
        "description": "Cash as per the app",
    },
    {
        "name": "physical_cash",
        "type": "DOUBLE",
        "description": "Cash physically available at the store",
    },
    {
        "name": "difference",
        "type": "DOUBLE",
        "description": "Difference between physical and systerm",
    },
    {
        "name": "type_of_difference",
        "type": "VARCHAR",
        "description": "Excess or shortage",
    },
    {
        "name": "cash_kept_in_safe",
        "type": "VARCHAR",
        "description": "Was the cash kept in safe",
    },
    {
        "name": "cash_custody",
        "type": "VARCHAR",
        "description": "Locker utilization",
    },
    {
        "name": "no_of_keys",
        "type": "DOUBLE",
        "description": "Number of keys of the locker",
    },
    {
        "name": "access_is_with_authorised_person",
        "type": "VARCHAR",
        "description": "Were the keys with authorized person",
    },
    {
        "name": "root_cause",
        "type": "VARCHAR",
        "description": "Reason of variance given at the store",
    },
]

grc_data_summary_sheet_column_dtypes = [
    {
        "name": "date", 
        "type": "DATE", 
        "description": "Date of audit"
    },
    {
        "name": "warehouse_name",
        "type": "VARCHAR",
        "description": "Name of the warehouse",
    },
    {
        "name": "area",
        "type": "VARCHAR",
        "description": "Pillars on which overall scoring of warehouse is depended",
    },
    {
        "name": "parameters",
        "type": "VARCHAR",
        "description": "Checks to be performed",
    },
    {
        "name": "actions_to_be_taken_by_the_auditors",
        "type": "VARCHAR",
        "description": "Actions taken by the auditors",
    },
    {
        "name": "scoring_methodology",
        "type": "VARCHAR",
        "description": "Basis on which each point scoring is decided",
    },
    {
        "name": "sample_locations",
        "type": "DOUBLE",
        "description": "Number of locations validated by the auditor",
    },
    {
        "name": "maximum_score",
        "type": "DOUBLE",
        "description": "Maximum score that can be obtained for a point",
    },
    {
        "name": "weightage",
        "type": "DOUBLE",
        "description": "Weightage of this particular point",
    },
    {
        "name": "weighted_max_score",
        "type": "DOUBLE",
        "description": "This is multiplication of weightage and maximum score obtained",
    },
    {
        "name": "score_obtained",
        "type": "DOUBLE",
        "description": "Actual score received basis physical verification",
    },
    {
        "name": "weighted_score_obtained",
        "type": "DOUBLE",
        "description": "This is multiplication of weight and score",
    },
    {
        "name": "auditors_remarks",
        "type": "VARCHAR",
        "description": "Any remarks provided by the auditors",
    },
    {
        "name": "poc",
        "type": "VARCHAR",
        "description": "POC for the specific area in the WH",
    },
    {
        "name": "reference",
        "type": "VARCHAR",
        "description": "Reference details",
    }
]

grc_data_view_sheet_column_dtypes = [
    {
        "name": "date", 
        "type": "DATE", 
        "description": "Date of audit"
    },
    {
        "name": "warehouse_name",
        "type": "VARCHAR",
        "description": "Name of the warehouse",
    },
    {
        "name": "attributes",
        "type": "VARCHAR",
        "description": "Attributes on which calculations are being done",
    },
    {
        "name": "adjustments",
        "type": "VARCHAR",
        "description": "Adjustments if any",
    },
    {
        "name": "upc",
        "type": "DOUBLE",
        "description": "UPC Number",
    },
    {
        "name": "quantity",
        "type": "DOUBLE",
        "description": "Attributes wise Quantity",
    },
    {
        "name": "value",
        "type": "DOUBLE",
        "description": "Attributes wise Value",
    }
]

warehouse_hygiene_sheet_column_dtypes = [
    {
        "name": "month", 
        "type": "VARCHAR", 
        "description": "Month of the Audit"
    },
    {
        "name": "area__in_sqft_",
        "type": "VARCHAR",
        "description": "Area of the warehouse",
    },
    {
        "name": "fc_name",
        "type": "VARCHAR",
        "description": "FC name",
    },
    {
        "name": "segment",
        "type": "VARCHAR",
        "description": "Segment name in the Warehouse",
    },
    {
        "name": "team",
        "type": "VARCHAR",
        "description": "Team name working in the Warehouse",
    },
    {
        "name": "parameter",
        "type": "VARCHAR",
        "description": "Parameter used to define attributes",
    },
    {
        "name": "instance_noted_during_review",
        "type": "VARCHAR",
        "description": "Instance noted during review",
    },
    {
        "name": "maximum_score",
        "type": "DOUBLE",
        "description": "Maximum Score",
    },
    {
        "name": "score_obtained",
        "type": "DOUBLE",
        "description": "Score obtained",
    },
    {
        "name": "weightage",
        "type": "DOUBLE",
        "description": "Maximum Score",
    },
    {
        "name": "weighted_score_obtained",
        "type": "DOUBLE",
        "description": "Weighted score obtained",
    },
    {
        "name": "weighted_max_score",
        "type": "DOUBLE",
        "description": "Weighted max score",
    }
]