import pencilbox as pb
import numpy as np
import pandas as pd
from calendar import monthrange
from datetime import datetime
import os, sys

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.column_definitions import (
    hygiene_data_summary_sheet_column_dtypes,
    hygiene_data_detail_sheet_column_dtypes,
    hygiene_data_cash_difference_sheet_column_dtypes,
    grc_data_summary_sheet_column_dtypes,
    grc_data_view_sheet_column_dtypes,
    warehouse_hygiene_sheet_column_dtypes
)

def push_data_from_sheets_to_trino(
    master_sheet_id,
    sheet_name,
    table_name,
    table_description,
    column_dtypes,
    env,
    catalog="blinkit_iceberg",
    schema_name="interim",
    primary_key_list=[],
    sortkey_list=[],
    load_type="truncate"
):

    if env:
        for key, value in env.items():
            os.environ[key] = value
            
    source_df = pb.from_sheets(master_sheet_id, sheet_name)
    
    source_df = source_df.loc[:, source_df.columns != '']
    source_df.columns = source_df.columns.str.strip().str.lower().str.replace(r'[-,\\,/,\?,#,@,\(,\),\n,\.]', '_')
    source_df = source_df.rename(columns = {"sm" : "store_manager",
                                            "cm" : "cluster_manager"})
    
    if sheet_name == "summary":
        source_df['in_store_staff_hygiene'] = source_df['in_store_staff_hygiene'].str.replace('%', '')
        source_df['material_storage_and_cleanliness'] = source_df['material_storage_and_cleanliness'].str.replace('%','')
        source_df['store_upkeep_and_hygiene'] = source_df['store_upkeep_and_hygiene'].str.replace('%', '')
        source_df['pest_and_rodent_related_checks'] = source_df['pest_and_rodent_related_checks'].str.replace('%', '')
        source_df = source_df.rename(columns = {"total" : "overall_score"})
        source_df['overall_score'] = source_df['overall_score'].str.replace('%', '')
        
    elif sheet_name == "detail":
        source_df = source_df.rename(columns = {"city_cat" : "city_category"})
    
    elif sheet_name == "cash_difference":
        source_df['no_of_keys'] = (source_df['no_of_keys'].replace('#N/A', np.nan))
        source_df['no_of_keys'] = (source_df['no_of_keys'].replace('#NA', np.nan))
        source_df['system_cash'] = source_df['system_cash'].str.replace(',', '')
        source_df['physical_cash'] = source_df['physical_cash'].str.replace(',', '')
        source_df['difference'] = source_df['difference'].str.replace(',', '')
        source_df = source_df.rename(columns = {"city_cat" : "city_category"})
    
    elif sheet_name == "Consol":
        source_df = source_df.loc[:, source_df.columns != '']        
        source_df.columns = source_df.columns.str.strip().str.lower().str.replace(r'[\s\-,\\\/\?\#\@\(\)\n\.]', '_', regex=True)
        source_df = source_df.drop(columns=['s_no_'], errors='ignore')
        source_df = source_df.rename(columns={
            "instance_noted_during_review__yes_no_": "instance_noted_during_review",
            "weightage__b_": "weightage"
        })
    
    def handling_dtype_conversion(column_name, data_type_str, value):
        try:
            if data_type_str == "DATE":
                return pd.to_datetime(value)
            elif data_type_str == "INTEGER":
                return pd.to_numeric(value, errors='coerce', downcast='integer')
            elif data_type_str == "BIGINT":
                return pd.to_numeric(value, errors='coerce', downcast='integer')
            elif data_type_str == "DOUBLE":
                return pd.to_numeric(value, errors='coerce', downcast='float')
            elif data_type_str == "VARCHAR":
                return str(value)
            else:
                return None
        except ValueError:
            print(f"Conversion failed for column {column_name} with type {data_type_str}")
            return None

    def convert_data_types(source_df, column_data_types):
        def rename_columns_with_numbers(source_df):
            renamed_columns = {}
            for column_name in source_df.columns:
                if column_name[0].isdigit():
                    new_column_name = f"sc_{column_name}"
                    renamed_columns[column_name] = new_column_name
            return source_df.rename(columns=renamed_columns)

        source_df = rename_columns_with_numbers(source_df)
        
        for column_info in column_data_types:
            column_name = column_info["name"]
            data_type_str = column_info["type"]
            description = column_info["description"]

            if column_name not in source_df.columns:
                print(f"Column {column_name} not found in the source DataFrame.")
                continue

            source_df[column_name] = source_df[column_name].apply(
                lambda x: handling_dtype_conversion(column_name, data_type_str, x)
            )
            print(f"Converted column: {column_name} with type {data_type_str}")

        return source_df

    final_df = convert_data_types(source_df, column_dtypes)
    
    kwargs = {
            "sheet_name": sheet_name,
            "catalog": catalog,
            "schema_name": schema_name,
            "table_name": table_name,
            "table_description": table_description,
            "primary_key": primary_key_list,
            "column_dtypes": column_dtypes,
            "sortkey": sortkey_list,
            "load_type": load_type,
    }
    
    return pb.to_trino(final_df, **kwargs)