# -*- coding: utf-8 -*-

import sys, os
import logging
from datetime import datetime, timedelta
import requests
import airflow
import pytz
from pytz import timezone

from airflow import DAG
from airflow.operators.bash_operator import BashOperator
from airflow.operators.python_operator import PythonOperator
from metrics_plugin import UpstreamTaskSensor
import pencilbox as pb

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.config import k8s_executor_config_dbt, k8s_executor_config, slack_failure_alert
from utils.column_definitions import column_dtypes

def add_catalog_brand_item_product_sales():
    pb.to_datahub(
        schema_name="dwh",
        table_name="agg_daily_brand_item_product_sales",
        column_dtypes=column_dtypes,
        table_description="This table Contains Sales related information at item & product level.",
        data_platform="trino",
        primary_keys=[],
        partition_keys=["order_create_date_ist"],
    )

args = {
    "slack_id": "S03ST1ENERJ",
    "owner": "<EMAIL>",  
    "on_failure_callback": slack_failure_alert,
    "start_date": datetime.strptime("2023-11-01T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": datetime.strptime("2025-09-30T00:00:00", "%Y-%m-%dT%H:%M:%S"), 
    "retries": 3,
    "retry_delay": timedelta(seconds=25),
    "retry_exponential_backoff": False,
}

dag = DAG(
    dag_id="de_dwh_etl_trino_dbt_agg_daily_brand_item_product_sales",
    default_args=args,
    schedule_interval="50 1 * * *", 
    tags=["dwh", "brand_item_product_sales"],
)

tz_ist = pytz.timezone("Asia/Kolkata")
source_deps_upstream_start_time = datetime.now(tz_ist).replace(
    second=0, microsecond=0
).astimezone(pytz.utc) - timedelta(minutes=360)

upstream_success_check = UpstreamTaskSensor(
    task_id = "fact_invoice_upstream_success_check",
    external_dag_id_without_version = "de_dwh_etl_trino_dbt_fact_sales_invoice_item_details",
    external_task_id = "fact-invoice-sales-order-grain-core",
    upstream_start_time = source_deps_upstream_start_time,
    dag = dag,
    timeout = 2 * 60,
    poke_interval = 60, # Time that the job should wait in between each try
    executor_config = k8s_executor_config_dbt,
)

dbt_base = "cd /dbt-models/trino_dwh && dbt --cache-selected-only "
dbt_run_daily_brand_item_product_sales_etl = BashOperator(
    task_id = "dbt_run_daily_brand_item_product_sales_etl_v1",
    bash_command = dbt_base
    + "run --models +agg_daily_brand_item_product_sales --exclude staging "
    + ' --vars \'{"run_date": "{{ data_interval_end }}", "days_in_past": "3", "dag_id": "{{ dag.dag_id }}", "dag_run_id": "{{ run_id }}"}\''
    + " "
    + " --target prod_p0",
    dag = dag,
    executor_config = k8s_executor_config_dbt,
)

dbt_catalog = PythonOperator(
    task_id="dbt-catalog",
    dag=dag,
    python_callable=add_catalog_brand_item_product_sales,
    executor_config=k8s_executor_config,
)

dbt_run_maintenance = BashOperator(
    task_id = "dbt_run_maintenance",
    bash_command = dbt_base
    + "run-operation run_maintenance "
    + ' --vars \'{"table_name": "dwh.agg_daily_brand_item_product_sales", "partition_key": "order_create_date_ist","operation_type": "all", "dag_id": "{{ dag.dag_id }}", "dag_run_id": "{{ run_id }}"}\''
    + " "
    + " --target prod_p0",
    dag = dag,
    executor_config = k8s_executor_config_dbt,
)

upstream_success_check >> dbt_run_daily_brand_item_product_sales_etl >> dbt_catalog >> dbt_run_maintenance

if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
