import sys,os
import logging
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.bash_operator import <PERSON>sh<PERSON>perator
from airflow.operators.python_operator import PythonOperator
from kubernetes.client import models as k8s
from airflow.models import Variable
import pencilbox as pb

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import k8s_executor_config, k8s_executor_config_dbt
from column_definitions import fact_sto_invoice_column_dtypes, sto_item_details_column_dtypes, consignment_details_column_dtypes, b2b_reasons_column_dtypes, billed_item_details_column_dtypes, dn_details_column_dtypes, grn_details_column_dtypes, rsto_item_details_column_dtypes, hpl_column_dtypes

DURATION_THRESHOLD = timedelta(minutes=20) #average run time

def add_catalog_fact_sto_invoice_transfer_flow():
    pb.to_datahub(
        schema_name="dwh",
        table_name="fact_inventory_transfer_details",
        column_dtypes= fact_sto_invoice_column_dtypes,
        table_description="This table Contains sales information related to Stock Transfer Order and invoice of an Item",
        data_platform="trino",
        primary_keys=[],
        partition_keys=["invoice_billed_date_ist"],
    )
    
def slack_failure_alert(context):
    dag_id = context.get("task_instance").dag_id
    run_id = context.get("run_id")
    task_id = context.get("task_instance").task_id
    log_url = context.get("task_instance").log_url.replace("http://", "https://")

    owner = {"email": "<EMAIL>", "slack_id": "S03ST1ENERJ"}
    slack_id = owner["slack_id"]
    _owner = {
        "s": "s" if len(slack_id.split(" ")) > 1 else "",
        "slack_id": "<!subteam^{}>".format(slack_id),
    }

    slack_failure_msg = """
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner{s}*: {slack_id}
        *Log Url*: {log_url}
    """.format(
        dag_id=dag_id,
        run_id=run_id,
        task_id=task_id,
        s=_owner["s"],
        slack_id=_owner["slack_id"],
        log_url=log_url,
    )

    slack_alert_configs = [
        {"channel": "bl-dwh-alerts"},
    ]

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(
                channel=channel, text=slack_failure_msg, user="data-plumber"
            )
        except Exception as e:
            logging.warning(e)
            message = (
                f"<!subteam^S089D0GQRCJ> `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(
                channel="bl-dwh-alerts", text=message, user="data-plumber"
            )

def long_run_slack_alert(context):
    dag_id = context.get("task_instance").dag_id
    run_id = context.get("run_id")
    task_id = context.get("task_instance").task_id
    log_url = context.get("task_instance").log_url.replace("http://", "https://")
    log_url_modified = "<" + log_url + "|" + "Link for Logs" + ">"

    state = context.get("task_instance").state
    duration = context.get("task_instance").duration


    owner = {"email": "<EMAIL>", "slack_id": "S03ST1ENERJ"}
    slack_id = owner["slack_id"]
    _owner = {
        "s": "s" if len(slack_id.split(" ")) > 1 else "",
        "slack_id": "<!subteam^{}>".format(slack_id),
    }

    slack_long_run_msg = """
        :snail: Below task took {duration} minutes for completion, whereas SLA: {sla} :snail:
        *Dag ID*: {dag_id}
        *Task ID*: {task_id}
        *Log Url*: {log_url_modified}
    """.format(
        dag_id=dag_id,
        task_id=task_id,
        s=_owner["s"],
        slack_id=_owner["slack_id"],
        log_url_modified=log_url_modified,
        duration = round((duration / 60), 2),
        sla = int(str(DURATION_THRESHOLD)[2:4])
    )

    slack_alert_configs = [
        {"channel": "bl-dwh-slow-dag-alerts"},
    ]

    for config in slack_alert_configs:
        channel = config["channel"]
        if state == 'success' and duration > DURATION_THRESHOLD.total_seconds():
            try:
                pb.send_slack_message(
                    channel=channel, text=slack_long_run_msg, user="data-plumber"
                )
            except Exception as e:
                logging.warning(e)
                message = (
                    f"<!subteam^S03TEACQKDW> `{dag_id}` `{task_id}` took more time for completion but cannot send an alert to `{channel}`."
                    f"\n```{e}```"
                )
                pb.send_slack_message(
                    channel="bl-dwh-alerts", text=message, user="data-plumber"
                )


args = {
    "slack_id": "S03ST1ENERJ",
    "owner": "<EMAIL>",
    "on_failure_callback": slack_failure_alert,
    "on_success_callback": long_run_slack_alert,
    "start_date": datetime.strptime("2024-04-04T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": datetime.strptime("2025-09-30T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "slack_id": "S03ST1ENERJ",
    "retries": 1,
    "retry_delay": timedelta(seconds=90),
    "retry_exponential_backoff" :True,
    "execution_timeout":timedelta(hours = 2)
}

dag = DAG(
    dag_id="de_dwh_etl_trino_dbt_fact_sto_invoice_tranfer_flow_v1",
    default_args=args,
    schedule_interval="07,37 0-19,23 * * *",
    catchup= False,
    max_active_runs= 1,
)


def add_catalog_flat_sto_item():
    pb.to_datahub(
        schema_name="dwh",
        table_name="flat_sto_item_details",
        column_dtypes=sto_item_details_column_dtypes,
        table_description="This table contains flat sto item details related metrics.",
        data_platform="trino",
        primary_keys=[],
        partition_keys=["sto_raised_date_ist"],
    )

def add_catalog_flat_invoice_consignment_details():
    pb.to_datahub(
        schema_name="dwh",
        table_name="flat_invoice_consignment_details",
        column_dtypes=consignment_details_column_dtypes,
        table_description="This table contains flat invoice consignment details related metrics.",
        data_platform="trino",
        primary_keys=[],
        partition_keys=["consignment_date_ist"],
    )

def add_catalog_flat_invoice_item_b2b_reasons():
    pb.to_datahub(
        schema_name="dwh",
        table_name="flat_invoice_item_b2b_reasons",
        column_dtypes=b2b_reasons_column_dtypes,
        table_description="This table contains flat invoice item B2B reasons related metrics.",
        data_platform="trino",
        primary_keys=[],
        partition_keys=["date_ist"],
    )

def add_catalog_flat_invoice_item_billed_details():
    pb.to_datahub(
        schema_name="dwh",
        table_name="flat_invoice_item_billed_details",
        column_dtypes=billed_item_details_column_dtypes,
        table_description="This table contains flat invoice item billed details related metrics.",
        data_platform="trino",
        primary_keys=[],
        partition_keys=["invoice_billed_date_ist"],
    )

def add_catalog_flat_invoice_item_dn_details():
    pb.to_datahub(
        schema_name="dwh",
        table_name="flat_invoice_item_dn_details",
        column_dtypes=dn_details_column_dtypes,
        table_description="This table contains flat invoice item dn details related metrics.",
        data_platform="trino",
        primary_keys=[],
        partition_keys=["dn_date_ist"],
    )

def add_catalog_flat_invoice_item_grn_details():
    pb.to_datahub(
        schema_name="dwh",
        table_name="flat_invoice_item_grn_details",
        column_dtypes=grn_details_column_dtypes,
        table_description="This table contains flat invoice item grn details related metrics.",
        data_platform="trino",
        primary_keys=[],
        partition_keys=["grn_date_ist"],
    )

def add_catalog_flat_invoice_item_rsto_details():
    pb.to_datahub(
        schema_name="dwh",
        table_name="flat_invoice_item_rsto_details",
        column_dtypes=rsto_item_details_column_dtypes,
        table_description="This table contains flat invoice item rsto details related metrics.",
        data_platform="trino",
        primary_keys=[],
        partition_keys=["rsto_date_ist"],
    )

def add_catalog_flat_po_invoice_item_transfer_details_hpl():
    pb.to_datahub(
        schema_name="dwh",
        table_name="flat_po_invoice_item_transfer_details_hpl",
        column_dtypes=hpl_column_dtypes,
        table_description="This table contains flat metrices related to HPL transfer flow",
        data_platform="trino",
        primary_keys=[],
        partition_keys=["po_created_date_ist"],
    )

dbt_base = "cd /dbt-models/trino_dwh && dbt --cache-selected-only "

# run_date is in UTC, need to handle this if the job scheduling is changed.
model_vars = '\'{{"run_date": "{}", "dag_id": "{}", "dag_run_id": "{}"}}\''.format( "{{ data_interval_end }}",'{{ dag.dag_id }}','{{ run_id }}')

dbt_run_sto_billed = BashOperator(
    task_id="dbt_run_sto_billed",
    dag=dag,
    bash_command=dbt_base
      + "run --select tag:inventory_flow_sto_billed" 
      + " --vars "
      + model_vars
      + " "
      + " --target prod_p0",
    executor_config=k8s_executor_config_dbt,
)

dbt_run_grn_dn = BashOperator(
    task_id="dbt_run_grn_dn",
    dag=dag,
    bash_command=dbt_base
      + "run --select tag:inventory_flow_grn_dn"
      + " --vars "
      + model_vars
      + " "
      + " --target prod_p0",
    executor_config=k8s_executor_config_dbt,
)

dbt_run_b2b_rsto = BashOperator(
    task_id="dbt_run_b2b_rsto",
    dag=dag,
    bash_command=dbt_base
      + "run --select tag:inventory_flow_b2b_rsto"
      + " --vars "
      + model_vars
      + " "
      + " --target prod_p0",
    executor_config=k8s_executor_config_dbt,
)

dbt_run_consignment = BashOperator(
    task_id="dbt_run_consignment",
    dag=dag,
    bash_command=dbt_base
      + "run --select tag:inventory_flow_consignment"
      + " --vars "
      + model_vars
      + " "
      + " --target prod_p0",
    executor_config=k8s_executor_config_dbt,
)

dbt_run_hpl_flow = BashOperator(
    task_id="dbt_run_hpl_flow",
    dag=dag,
    bash_command=dbt_base
      + "run --select tag:inventory_flow_hpl"
      + " --vars "
      + model_vars
      + " "
      + " --target prod_p0",
    executor_config=k8s_executor_config_dbt,
)

dbt_run_core_sto_invoice_flow = BashOperator(
    task_id="dbt_run_core_sto_invoice_flow",
    dag=dag,
    bash_command=dbt_base
      + "run --select tag:inventory_transfer_final"
      + " --vars "
      + model_vars
      + " "
      + " --target prod_p0",
    executor_config=k8s_executor_config_dbt,
)

dbt_catalog_fact_sto_invoice = PythonOperator(
    task_id="dbt_catalog_fact_sto_invoice",
    dag=dag,
    python_callable=add_catalog_fact_sto_invoice_transfer_flow,
    executor_config=k8s_executor_config,
)

dbt_catalog_sto_item = PythonOperator(
    task_id="dbt_catalog_sto_item",
    dag=dag,
    python_callable=add_catalog_flat_sto_item,
    executor_config=k8s_executor_config,
)

dbt_catalog_consignment_details = PythonOperator(
    task_id="dbt_catalog_consignment_details",
    dag=dag,
    python_callable=add_catalog_flat_invoice_consignment_details,
    executor_config=k8s_executor_config,
)

dbt_catalog_b2b_reasons = PythonOperator(
    task_id="dbt_catalog_b2b_reasons",
    dag=dag,
    python_callable=add_catalog_flat_invoice_item_b2b_reasons,
    executor_config=k8s_executor_config,
)

dbt_catalog_billed_details = PythonOperator(
    task_id="dbt_catalog_billed_details",
    dag=dag,
    python_callable=add_catalog_flat_invoice_item_billed_details,
    executor_config=k8s_executor_config,
)

dbt_catalog_dn_details = PythonOperator(
    task_id="dbt_catalog_dn_details",
    dag=dag,
    python_callable=add_catalog_flat_invoice_item_dn_details,
    executor_config=k8s_executor_config,
)

dbt_catalog_grn_details = PythonOperator(
    task_id="dbt_catalog_grn_details",
    dag=dag,
    python_callable=add_catalog_flat_invoice_item_grn_details,
    executor_config=k8s_executor_config,
)

dbt_catalog_rsto_details = PythonOperator(
    task_id="dbt_catalog_rsto_details",
    dag=dag,
    python_callable=add_catalog_flat_invoice_item_rsto_details,
    executor_config=k8s_executor_config,
)

dbt_catalog_transfer_details_hpl = PythonOperator(
    task_id="dbt_catalog_transfer_details_hpl",
    dag=dag,
    python_callable=add_catalog_flat_po_invoice_item_transfer_details_hpl,
    executor_config=k8s_executor_config,
)

task_dummy_one = BashOperator(
    task_id='task_dummy_one',
    bash_command='date',
    dag = dag,
    executor_config=k8s_executor_config,
)

task_dummy_two = BashOperator(
    task_id='task_dummy_two',
    bash_command='date',
    dag = dag,
    executor_config=k8s_executor_config,
)

task_end_dummy = BashOperator(
    task_id='end_of_execution',
    bash_command='date',
    dag = dag,
    executor_config=k8s_executor_config,
)

[dbt_run_sto_billed, dbt_run_grn_dn, dbt_run_b2b_rsto, dbt_run_consignment] >> task_dummy_one >> [dbt_run_hpl_flow, dbt_run_core_sto_invoice_flow] >> task_dummy_two >> [dbt_catalog_fact_sto_invoice, dbt_catalog_sto_item, dbt_catalog_consignment_details, dbt_catalog_b2b_reasons, dbt_catalog_billed_details, dbt_catalog_dn_details, dbt_catalog_grn_details, dbt_catalog_rsto_details, dbt_catalog_transfer_details_hpl] >> task_end_dummy

## --------------------------------------------- Maintenance Flow ----------------------------------------------------

dbt_maint_table_config = [
    {
        "table_name":"dwh.fact_inventory_transfer_details",
        "partition_key": "invoice_billed_date_ist",
    },
    {
        "table_name":"dwh.flat_invoice_consignment_details",
        "partition_key": "consignment_date_ist",
    },
    {
        "table_name":"dwh.flat_invoice_item_b2b_reasons",
        "partition_key": "date_ist",
    },
    {
        "table_name":"dwh.flat_invoice_item_billed_details",
        "partition_key": "invoice_billed_date_ist",
    },
    {
        "table_name":"dwh.flat_invoice_item_dn_details",
        "partition_key": "dn_date_ist",
    },
    {
        "table_name":"dwh.flat_invoice_item_grn_details",
        "partition_key": "grn_date_ist",
    },
    {
        "table_name":"dwh.flat_invoice_item_rsto_details",
        "partition_key": "rsto_date_ist",
    },
    {
        "table_name":"dwh.flat_sto_item_details",
        "partition_key": "sto_raised_date_ist",
    },
    {
        "table_name":"dwh.flat_po_invoice_item_transfer_details_hpl",
        "partition_key": "po_created_date_ist",
    },
]


# for table_name in ["fact_inventory_transfer_details" ,"flat_invoice_consignment_details", "flat_invoice_item_b2b_reasons", "flat_invoice_item_billed_details", "flat_invoice_item_dn_details", "flat_invoice_item_grn_details", "flat_invoice_item_rsto_details", "flat_sto_item_details", "flat_po_invoice_item_transfer_details_hpl"]:

for i in range(0,len(dbt_maint_table_config)):
    table_name = dbt_maint_table_config[i]["table_name"]
    partition_key = dbt_maint_table_config[i]["partition_key"] if "partition_key" in dbt_maint_table_config[i]  else ""

    model_vars = ' --vars ' + '\'{{"table_name": "{}", "partition_key": "{}","partition_key_override": {},"operation_type": "all", "dag_id": "{}", "dag_run_id": "{}"}}\''.format(table_name,partition_key, str(True).lower(), '{{ dag.dag_id }}', '{{ run_id }}')
    
    dbt_command = (
                'if [[ "$(echo {{ ts }} | cut -c 12-13)" == "16" || "$(echo {{ ts }} | cut -c 12-13)" == "06" || "$(echo {{ ts }} | cut -c 12-13)" == "10" ]]'\
                + ' ; then '\
                + "cd /dbt-models/trino_dwh && dbt "
                + "run-operation run_maintenance "
                + model_vars
                + " "
                + " --target prod"
                + ' ; else' \
                + ' exit 99' \
                + ' ; fi'
            )
    
    dbt_run_maintenance = BashOperator(
        task_id = f"dbt_run_maintenance_{table_name}",
        bash_command = dbt_command,
        dag = dag,
        executor_config = k8s_executor_config_dbt,
    )
    task_end_dummy >> dbt_run_maintenance

if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
