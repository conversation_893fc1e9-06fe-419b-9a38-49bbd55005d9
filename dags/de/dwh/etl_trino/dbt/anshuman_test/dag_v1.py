import sys,os
import logging
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.bash_operator import <PERSON>sh<PERSON>perator
from airflow.operators.python_operator import PythonOperator
from kubernetes.client import models as k8s
from airflow.models import Variable
import pencilbox as pb

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import k8s_executor_config

def slack_failure_alert(context):
    dag_id = context.get("task_instance").dag_id
    run_id = context.get("run_id")
    task_id = context.get("task_instance").task_id
    log_url = context.get("task_instance").log_url.replace("http://", "https://")

    owner = {"email": "<EMAIL>", "slack_id": "S03ST1ENERJ"}
    slack_id = owner["slack_id"]
    _owner = {
        "s": "s" if len(slack_id.split(" ")) > 1 else "",
        "slack_id": "<!subteam^{}>".format(slack_id),
    }

    slack_failure_msg = """
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner{s}*: {slack_id}
        *Log Url*: {log_url}
    """.format(
        dag_id=dag_id,
        run_id=run_id,
        task_id=task_id,
        s=_owner["s"],
        slack_id=_owner["slack_id"],
        log_url=log_url,
    )

    slack_alert_configs = [
        {"channel": "bl-dwh-alerts"},
    ]

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(
                channel=channel, text=slack_failure_msg, user="data-plumber"
            )
        except Exception as e:
            logging.warning(e)
            message = (
                f"<!subteam^S089D0GQRCJ> `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(
                channel="bl-dwh-alerts", text=message, user="data-plumber"
            )


args = {
    "slack_id": "S03ST1ENERJ",
    "owner": "<EMAIL>",
    "on_failure_callback": slack_failure_alert,
    "start_date": datetime.strptime("2025-07-14T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": datetime.strptime("2025-09-01T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "retries": 1,
    "retry_delay": timedelta(seconds=90),
    "retry_exponential_backoff" :True,
}

dag = DAG(
    dag_id="de_dwh_etl_tino_dbt_anshuman_test_v1",
    default_args=args,
    schedule_interval="30 7 * * *",
    catchup= True,
    max_active_runs= 1,
)

dbt_base = "cd /dbt-models/trino_dwh && dbt --cache-selected-only "

model_vars = '\'{{"run_date": "{}", "dag_id": "{}", "dag_run_id": "{}"}}\''.format( "{{ data_interval_end }}", '{{ dag.dag_id }}', '{{ run_id }}')

dbt_run_overall = BashOperator(
    task_id="dbt_run_overall",
    dag=dag,
    bash_command=dbt_base
      + "run --select tag:mtd_backfill --exclude staging"
      + " --vars "
      + model_vars
      + " "
      + " --target prod",
    executor_config=k8s_executor_config,
)

dbt_run_overall


## --------------------------------------------- Maintenance Flow ----------------------------------------------------

for table_name in ["imd_mtu__mtd_backfill", "imd_mau_overall__mtd_backfill"]:
    
    model_vars = ' --vars ' + '\'{{"table_name": "{}","partition_key": "start_of_month", "operation_type": "all", "dag_id": "{}", "dag_run_id": "{}"}}\''.format('dwh.'+ table_name, '{{ dag.dag_id }}', '{{ run_id }}')
    
    dbt_command = (
                  "cd /dbt-models/trino_dwh && dbt "
                + "run-operation run_maintenance "
                + model_vars
                + " "
                + " --target prod"
            )
    dbt_run_maintenance = BashOperator(
        task_id = f"dbt_run_maintenance_{table_name}",
        bash_command = dbt_command,
        dag = dag,
        executor_config = k8s_executor_config,
    )

    dbt_run_overall >> dbt_run_maintenance

if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
