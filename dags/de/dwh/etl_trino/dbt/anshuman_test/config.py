from kubernetes.client import models as k8s
from airflow.models import Variable

k8s_executor_config = {
    "KubernetesExecutor": {
        "image": Variable.get("442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de/dbt:stable", "442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de/dbt:stable"),
        "node_selectors": {"nodetype": "airflow-spot-general"},
        "tolerations": [
            {
                "effect": "NoSchedule",
                "key": "service",
                "operator": "Equal",
                "value": "airflow-spot-general",
            }
        ],
        "volume_mounts": [
            {
                "mountPath": "/usr/local/airflow/plugins",
                "name": "airflow-dags",
                "subPath": "airflow-de/plugins",
            },
            {
                "mountPath": "/dbt-models",
                "name": "dbt-models",
                "subPath": "dbt-models",
            },
        ],
        "request_memory": "500M",
        "limit_memory": "4G",
        "request_cpu": 0.5,
        "limit_cpu": 1,
    }
}
