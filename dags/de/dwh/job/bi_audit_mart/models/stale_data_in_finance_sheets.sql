WITH

base_data AS (
    -- own_ds_rent
    SELECT 
        'https://docs.google.com/spreadsheets/d/1_1wXHH0BmjEeUN6E-vTnG53t9sa4yDxjk0fabGHx2W0/view#gid=395198874' AS link,
        'own_ds_rent' AS sheet_name,
        MAX(payment_end_cycle) AS date_
        
    FROM 
        blinkit_iceberg.interim.ss_supply_cost_own_ds_rent
    
    UNION ALL
    
    -- partner_commission_and_mg
    SELECT
        'https://docs.google.com/spreadsheets/d/1_1wXHH0BmjEeUN6E-vTnG53t9sa4yDxjk0fabGHx2W0/edit#gid=1044592194' AS link,
        'partner_commission_and_mg' AS sheet_name,
        MAX(date) AS date_
    
    FROM 
        blinkit_iceberg.interim.ss_supply_cost_partner_commission_mg
    
    UNION ALL
    
    -- offroll_rate_card
    SELECT 
        'https://docs.google.com/spreadsheets/d/1_1wXHH0BmjEeUN6E-vTnG53t9sa4yDxjk0fabGHx2W0/view#gid=959665988' AS link,
        'offroll_rate_card' AS sheet_name,
        MAX(end_date) AS date_ 
    
    FROM 
        blinkit_iceberg.interim.ss_supply_cost_offroll_rate_card
    
    UNION ALL
    
    -- gna_cost
    SELECT 
        'https://docs.google.com/spreadsheets/d/1_1wXHH0BmjEeUN6E-vTnG53t9sa4yDxjk0fabGHx2W0/view#gid=1516683974' AS link,
        'gna_cost' AS sheet_name,
        MAX(payment_end_cycle) AS date_ 
    
    FROM 
        blinkit_iceberg.interim.ss_supply_cost_gna_cost
    
    UNION ALL
    
    -- packaging_cost
    SELECT 
        'https://docs.google.com/spreadsheets/d/1_1wXHH0BmjEeUN6E-vTnG53t9sa4yDxjk0fabGHx2W0/view#gid=1737365497' AS link,
        'packaging_cost' AS sheet_name,
        MAX(end_date) AS date_ 
    
    FROM 
        blinkit_iceberg.interim.ss_supply_cost_packaging_cost_outlet_wise
    
    UNION ALL
    
    -- fc_replenishment_cost
    SELECT 
        'https://docs.google.com/spreadsheets/d/1_1wXHH0BmjEeUN6E-vTnG53t9sa4yDxjk0fabGHx2W0/view#gid=1262769376' AS link,
        'fc_replenishment_cost_new' AS sheet_name,
        MAX(fc_cost_end_date) AS date_
    
    FROM 
        blinkit_iceberg.interim.ss_supply_cost_fc_replenishment_cost_fixed_variable
    
    UNION ALL
    
    -- DS Onroll Manpower Cost
    SELECT 
        'https://docs.google.com/spreadsheets/d/1_1wXHH0BmjEeUN6E-vTnG53t9sa4yDxjk0fabGHx2W0/view#gid=309245291' AS link,
        'DS Onroll Manpower Cost' AS sheet_name,   
        MAX(end_date) AS date_ 
    
    FROM 
        blinkit_iceberg.interim.ss_supply_cost_ds_onroll_manpower_cost
    
    UNION ALL
    
    -- Fleet Cost Monthly Sheet IDs
    SELECT
        'https://docs.google.com/spreadsheets/d/1_1wXHH0BmjEeUN6E-vTnG53t9sa4yDxjk0fabGHx2W0/view#gid=337775033' AS link,
        'Fleet Cost Monthly Sheet IDs' AS sheet_name,
        MAX(date_ist) AS date_ 
    
    FROM 
        blinkit_iceberg.interim.wi_scc_daily_outlet_fleet_cost
    
    UNION ALL 
    
    -- PC - Monthly Sheet IDs
    -- SELECT 
    --     'https://docs.google.com/spreadsheets/d/1_1wXHH0BmjEeUN6E-vTnG53t9sa4yDxjk0fabGHx2W0/view#gid=1906301270' AS link,
    --     'PC - Monthly Sheet IDs' AS sheet_name,
    --     MAX(date_ist) AS date_
    
    -- FROM 
    --     staging.scc_daily_pc_attendance
    
    -- UNION ALL
    
    -- CPC - Monthly Sheet IDs
    -- SELECT 
    --     'https://docs.google.com/spreadsheets/d/1_1wXHH0BmjEeUN6E-vTnG53t9sa4yDxjk0fabGHx2W0/view#gid=1779419648' AS link,
    --     'CPC - Monthly Sheet IDs' AS sheet_name,
    --     MAX(date_ist) AS date_
    
    -- FROM 
    --     staging.scc_daily_cpc_attendance   
    
    -- UNION ALL
    
    -- CRM - Outsourced Manpower Cost
    SELECT
        'https://docs.google.com/spreadsheets/d/1VVB4sSmMOZYzU5TX7tBLN6KclNqZmPvZ4zewQRzKX20/view#gid=407853087' AS link,
        'Outsourced Manpower Cost' AS sheet_name,
        MAX(payment_end_date) AS date_
    
    FROM
        blinkit_iceberg.interim.ss_cs_outsourced_manpower_cost
    
    UNION ALL
    
    -- CRM - Inhouse Manpower Cost
    SELECT
        'https://docs.google.com/spreadsheets/d/1VVB4sSmMOZYzU5TX7tBLN6KclNqZmPvZ4zewQRzKX20/view#gid=459728237' AS link,
        'Inhouse Manpower Cost' AS sheet_name,
        MAX(payment_end_date) AS date_
    
    FROM
        blinkit_iceberg.interim.ss_cs_inhouse_manpower_cost
    
    UNION ALL
    
    -- CRM - Platform Cost
    SELECT
        'https://docs.google.com/spreadsheets/d/1VVB4sSmMOZYzU5TX7tBLN6KclNqZmPvZ4zewQRzKX20/view#gid=515772070' AS link,
        'CRM - Platform Cost' AS sheet_name,
        MAX(payment_end_date) AS date_
    
    FROM
        blinkit_iceberg.interim.ss_cs_platform_cost
    
    UNION ALL
    
    -- CRM - Other Manpower Cost
    SELECT
        'https://docs.google.com/spreadsheets/d/1VVB4sSmMOZYzU5TX7tBLN6KclNqZmPvZ4zewQRzKX20/edit#gid=772582441' AS link,
        'CRM - Other Manpower Cost' AS sheet_name,
        MAX(month_end) AS date_
    
    FROM
        blinkit_iceberg.interim.ss_cs_other_outsourced_manpower_cost
    
    UNION ALL
    
    -- Visibility Income Input
    SELECT
        'https://docs.google.com/spreadsheets/d/1VGmmiRvn0Rgp6N5AuUy31GKUeej0ejrzuZGF3mfBLr0/edit#gid=0' AS link,
        'Visibility Income Input' AS sheet_name,
        MAX(to_date) AS date_
    
    FROM
       blinkit_iceberg.interim.ss_frc_pnl_other_income_pct
    
    UNION ALL
    
    -- Strategic Partner Cost
    SELECT
        'https://docs.google.com/spreadsheets/d/1VGmmiRvn0Rgp6N5AuUy31GKUeej0ejrzuZGF3mfBLr0/edit#gid=1021820220' AS link,
        'Strategic Partner Cost' AS sheet_name,
        MAX(end_date) AS date_
    
    FROM
        blinkit_iceberg.interim.ss_frc_strategic_partner_cost
    
    UNION ALL
        
    -- Hyperpure Cost
    SELECT
        'https://docs.google.com/spreadsheets/d/1VGmmiRvn0Rgp6N5AuUy31GKUeej0ejrzuZGF3mfBLr0/edit#gid=1281285922' AS link,
        'Hyperpure Cost' AS sheet_name,
        MAX(end_date) AS date_
    
    FROM
        blinkit_iceberg.interim.ss_frc_hyperpure_cost
        
        
    -- Supply Chain - Last Mile Cost
    -- SELECT
    --     'https://docs.google.com/spreadsheets/d/1_1wXHH0BmjEeUN6E-vTnG53t9sa4yDxjk0fabGHx2W0/view#gid=1417997906' AS link,
    --     'last_mile_cost' AS sheet_name,
    --     MAX(snapshot_date_ist) AS date_
    
    -- FROM
    --     staging.scc_daily_outlet_last_mile_cost
    
    -- UNION ALL
    
    -- Supply Chain - Rider Onboarding and Support Cost
    -- SELECT
    --     'https://docs.google.com/spreadsheets/d/1_1wXHH0BmjEeUN6E-vTnG53t9sa4yDxjk0fabGHx2W0/view#gid=1022191' AS link,
    --     'rider_onboarding_cost' AS sheet_name,
    --     MAX(snapshot_date_ist) AS date_
    
    -- FROM
    --     staging.scc_daily_outlet_rider_onboarding_and_support_cost
)

SELECT
    link,
    sheet_name,
    date_
    
FROM
    base_data
    
WHERE
    date_ < CAST((CURRENT_TIMESTAMP + INTERVAL '330' MINUTE - INTERVAL '1' DAY) AS DATE)
