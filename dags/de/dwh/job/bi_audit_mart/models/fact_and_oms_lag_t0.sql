WITH
fact_data AS (
    SELECT
        CAST(order_create_dt_ist AS DATE) AS dt_fact,
        hour(order_create_ts_ist) as hour_fact,
        SUM(total_selling_price) AS sp_fact,
        count(distinct order_id) as orders_fct,
        count(distinct cart_id) as total_carts_fct
        
    FROM
        dwh.fact_sales_order_item_details
        
    WHERE
        order_create_dt_ist = CURRENT_DATE
        AND order_type not in ( 'HyperpureForwardOrder','BistroForwardOrder', 'BistroInternalForwardOrder')
        
    GROUP BY
        1, 2
),

fact_data_new AS (
    SELECT
        CAST(order_create_dt_ist AS DATE) AS dt_fact_1,
        hour(order_create_ts_ist) as hour_1,
        SUM(total_selling_price) AS sp_fact_1,
        count(distinct order_id) as orders_fct_1,
        count(distinct cart_id) as total_carts_fct_1
        
    FROM
        dwh.fact_sales_order_details
        
    WHERE
        order_create_dt_ist = CURRENT_DATE
        AND order_type not in ( 'HyperpureForwardOrder','BistroForwardOrder', 'BistroInternalForwardOrder')
        AND is_emergency_service_order = false
        
    GROUP BY
        1, 2
),

oms_data AS (
    SELECT
        CAST((insert_ds_ist) AS DATE) AS dt_oms,
        hour(install_ts + interval '330' minute) as hour_oms,
        SUM(total_cost) AS sp_oms,
        count(distinct cast(id as bigint)) as orders_om,
        count(distinct cart_id) as total_carts_om
        
    FROM
        oms_bifrost.oms_order
            as oo
            
    WHERE
        insert_ds_ist = CAST(CURRENT_DATE AS VARCHAR)
        AND shipment_id NOT IN ('28759', '26680', '31149', '36120')
        AND current_status IN ('DELIVERED','CANCELLED', 'DELIVERY_FAILED')
        AND type NOT IN ('HyperpureForwardOrder','BistroForwardOrder','BistroInternalForwardOrder','OnboardingPlatformOrder', 'SellerPlatformOrder')
    
    GROUP BY
        1, 2
)

,final AS (
    SELECT
        dt_fact,
        hour_fact,
        sp_fact,
        sp_oms,
        sp_fact_1,
        orders_fct,
        orders_om,
        orders_fct_1,
        total_carts_fct,
        total_carts_om,
        total_carts_fct_1
        
    FROM
        fact_data
            AS fd
    
    JOIN
        oms_data
            AS od
            ON dt_fact = dt_oms
                AND hour_fact = hour_oms
    
    JOIN
        fact_data_new
            AS od_n
            ON dt_fact_1 = dt_oms
                AND hour_1 = hour_oms
)

,fin AS (
SELECT
    dt_fact,
    hour_fact,
    orders_fct,
    orders_om,
    sp_fact,
    sp_oms,

    (orders_fct - orders_om) AS orders_diff,
    (sp_fact - sp_oms) AS sp_diff

FROM
    final
        AS f

WHERE
    orders_fct <> orders_om
    AND hour_fact < HOUR(CURRENT_TIMESTAMP - INTERVAL '2' HOUR)
)

SELECT
    hour_fact,
    orders_diff,
    sp_diff

FROM
    fin

WHERE
    ABS(orders_diff) > 500