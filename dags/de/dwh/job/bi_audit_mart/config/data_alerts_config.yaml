- maximum_cancel_cart_customer:
    active: true
    channel: bl-dwh-bi-metrics
    sqlcwd: maximum_cancel_cart_customer.sql
    message: Customer ID - *{}* is having *{}* numbers of cart cancel yesterday.
    cron: 50 1 * * *
    threshold: 5
    mode: 'Slack'
    connection: 'trino'
- maximum_gmv_order:
    active: true
    channel: bl-dwh-bi-metrics
    sqlcwd: maximum_gmv_order.sql
    message: Order ID - *{}* and Customer ID - *{}* has Maximum GMV of *{}* Rs yesterday.
    cron: 50 1 * * *
    threshold: 12000
    mode: 'Slack'
    connection: 'trino'
- maximum_quantity_order:
    active: true
    channel: bl-dwh-bi-metrics
    sqlcwd: maximum_quantity_order.sql
    message: Customer ID - *{}* (Order ID - *{}*) ordered a Maximum Quantity of *{}* units of *{}* yesterday.
    cron: 50 1 * * *
    threshold: 150
    mode: 'Slack'
    connection: 'trino'
- most_ordered_item:
    active: true
    channel: bl-dwh-bi-metrics
    sqlcwd: most_ordered_item.sql
    message: Product ID - *{}* and Name - *{}* has Maximum Order Quantity of *{}* units yesterday.
    cron: 50 1 * * *
    mode: 'Slack'
    connection: 'trino'
- master_customer:
    active: true
    channel: bl-dwh-bi-metrics
    sqlcwd: master_customer.sql
    message: Customer ID- *{}* did *{}* (Maximum) Checkout yesterday having GMV of *{}* and AOV of *{}*.
    cron: 50 1 * * *
    mode: 'Slack'
    connection: 'trino'
- abandoned_carts:
    active: true
    channel: bl-dwh-bi-metrics
    sqlcwd: abandoned_carts.sql
    message: We are having *{}* total checkout carts, *{}* abandoned carts basis devices and *{}* total carts for yesterday. Where *{}* is the recovered carts/devices percentage basis day before yesterday and *{}* abandoned carts percentage yesterday.
    cron: 50 1 * * *
    mode: 'Slack'
    connection: 'trino'
- additional_charges:
    active: true
    channel: bl-dwh-bi-metrics
    sqlcwd: audit_addition_charges.sql
    message: calculated additional charges *{}* amount in fact detail vs *{}* amount from fact ext additional charges for yesterday.
    cron: 50 1 * * *
    mode: 'Slack'
    connection: 'trino'
- rider_abuse:
    active: true
    channel: bl-dwh-bi-metrics
    sqlcwd: rider_abuse.sql
    message: Customer ID - *{}* from *{}* has placed *{}* orders with a total GMV of *{}* Rs yesterday.
    cron: 50 1 * * *
    mode: 'Slack'
    connection: 'trino'
- rm_t0_t1_diff_check:
    active: false
    channel: bl-dwh-bi-metrics
    sqlcwd: rm_t0_t1_audit.sql
    message: PID - *{}* is having RM% *{}* value for T as compared to *{}* for T-1 and having a delta of *{}* 
    cron: 50 1 * * *
    mode: 'Slack'
    connection: 'trino'
- blinkit_share_promo_alert:
    active: false
    channel: [bl-dwh-bi-metrics, bl-promo-support]
    sqlcwd: blinkit_share_promo_alert.sql
    message: Promo *{}*, linked with *{}* orders and having total discount of *{}* yesterday, is not updated in *<{}|{}>*.
    cron: 50 2 * * *
    mode: 'Slack'
    connection: 'trino'
- top_5_gmv:
    active: true
    channel: bl-dwh-bi-metrics
    sqlcwd: top_5_gmv.sql
    message: PID - *{}* (*{}*) --> GMV of Rs.*{}* (T-1) with a difference of *{}*% on T-7, *{}*% on T-28 and *{}*% on T-56.
    cron: 50 1 * * *
    mode: 'Slack'  
    connection: 'trino'
- top_5_orders_count:
    active: true
    channel: bl-dwh-bi-metrics
    sqlcwd: top_5_orders_count.sql
    message: PID - *{}* (*{}*) has an order count of *{}* (T-1) with a difference of *{}*% on T-7, *{}*% on T-28 and *{}*% on T-56.
    cron: 50 1 * * *
    mode: 'Slack'
    connection: 'trino'
- missed_hours_for_consumer_conversion_daily_job:
    active: true
    channel: bl-dwh-p0-alerts
    sqlcwd: consumer_conversion_hour_miss.sql
    message: <!subteam^S03TEACQKDW> snapshot_hour_ist- *{}* is missed for today in de_dwh_etl_agg_hourly_consumer_conversion_details_v1 daily job. Please clear same Run Id as missed hour.
    cron: 50 2,10,18 * * *
    mode: 'Slack'
    connection: 'trino'
# - delay_in_rider_login_hours_job:
#     active: true
#     channel: bl-dwh-bi-metrics
#     sqlcwd: partner_login_delay.sql
#     message: metrics.partner_hourly_orders_and_login_realtime is running at delay of *{}* hours.
#     cron: 53 1-17 * * *
#     mode: 'Slack'
#     connection: 'trino'
- duplicates_in_fact_order_item_details_in_trino:
    active: true
    channel: bl-dwh-p0-alerts
    sqlcwd: fact_order_item_mismatch_trino.sql
    message: fact_sales_order_item_details has duplicate values for checkout date *{}*, having distinct_order_item_id as *{}* vs order_item_id as *{}*
    cron: 53 0-17 * * *
    mode: 'Slack'
    connection: 'trino'
- duplicates_in_fact_order_item_details_in_redshift:
    active: false
    channel: bl-dwh-p0-alerts
    sqlcwd: fact_order_item_mismatch_rds.sql
    message: fact_sales_order_item_details has duplicate values for checkout date *{}*, having distinct_order_item_id as *{}* vs order_item_id as *{}*
    cron: 53 0-17 * * *
    mode: 'Slack'
    connection: 'redshift'
- duplicates_in_fact_order_details_in_trino:
    active: true
    channel: bl-dwh-p0-alerts
    sqlcwd: fact_order_mismatch_trino.sql
    message: fact_sales_order_details has duplicate values for checkout date *{}*, having distinct_order_id as *{}* vs order_id as *{}*
    cron: 53 0-17 * * *
    mode: 'Slack'
    connection: 'trino'
- duplicates_in_fact_order_details_in_redshift:
    active: false
    channel: bl-dwh-p0-alerts
    sqlcwd: fact_order_mismatch_rds.sql
    message: fact_sales_order_details has duplicate values for checkout date *{}*, having distinct_order_id as *{}* vs order_id as *{}*
    cron: 53 0-17 * * *
    mode: 'Slack'
    connection: 'redshift'
- fact_order_details_gmv_mismatch_in_trino:
    active: true
    channel: bl-dwh-p0-alerts
    sqlcwd: fact_order_details_gmv_mismatch_trino.sql
    message: fact_sales_order_details has inflated GMV for order create date *{}*, having fact_selling_price as *{}* vs oms_selling_price as *{}* with a delta of *{}*
    cron: 53 0-17 * * *
    mode: 'Slack'
    connection: 'trino'
- fact_order_details_gmv_mismatch_in_redshift:
    active: false
    channel: bl-dwh-p0-alerts
    sqlcwd: fact_order_details_gmv_mismatch_rds.sql
    message: fact_sales_order_details has inflated GMV for order create date *{}*, having fact_selling_price as *{}* vs oms_selling_price as *{}* with a delta of *{}*
    cron: 53 0-17 * * *
    mode: 'Slack'
    connection: 'redshift'
- manual qty update - agg_daily_diff_loss:
    active: true
    channel: bl-dwh-bi-metrics
    sqlcwd: unusual_manual_inputs_daily_diff_loss.sql
    message: Identified unusual manual input for outlet id *{}* and item id *{}* -  *{}* - , having net qty as *{}* and value as ₹ *{}* 
    cron: 50 0 * * *
    mode: 'Slack'
    connection: 'trino'
- fact_sales_order_details_invalid_merchant_key:
    active: true
    channel: bl-dwh-p0-alerts
    # channel: bl-dwh-test
    sqlcwd: fact_sales_order_details_invalid_merchant_key.sql
    message: Found *{}* merchant ids having invalid merchant id in fact_sales_order_details for the hour *{}*. Redash -> https://redash-queries.grofer.io/queries/310208/source
    cron: 15,50 * * * *
    mode: 'Slack'
    connection: 'trino'
- events_jumbo_pipeline_delay:
    active: true
    channel: bl-dwh-p0-alerts
    sqlcwd: events_delay_alert.sql
    message: <!subteam^S03TEACQKDW> events pipeline having delay of *{}* hours.
    cron: 53 0-21 * * *
    mode: 'Slack'
    connection: 'trino'
- consumer_conversion_max_hour_delay:
    active: true
    channel: bl-dwh-p0-alerts
    sqlcwd: consumer_conversion_max_hour.sql
    message: <!subteam^S03TEACQKDW> data in consumer conversion present upto *{}* hour for yesterday, please check.
    cron: 50 0 * * *
    mode: 'Slack'
    connection: 'trino'
- sku_inventory_data_availabilty:
    active: true
    channel: bl-dwh-p0-alerts
    sqlcwd: sku_inventory_data_availabilty.sql
    message: <!subteam^S03TEACQKDW> SKU inventory does not have complete data for Current Date - 1, Kindly pause the refresh on tableau.
    cron: 45 3,5 * * *
    mode: 'Slack'
    connection: 'trino'
- stuck_orders_in_oms:
    active: true
    channel: bl-dwh-p0-alerts
    sqlcwd: stuck_orders_in_oms.sql
    message: OMS has *{}* orders in *{}* state for respective order create date as *{}*
    cron: 0 1 * * *
    mode: 'Slack'
    connection: 'trino'
- unique_pk_in_dimensions:
    active: true
    channel: bl-dwh-p0-alerts
    sqlcwd: unique_pk_in_dimensions.sql
    message: Duplicates on primary key in *{}*, *{}* records affected.
    cron: 30 */6 * * *
    mode: 'Slack'
    connection: 'trino'
- store_level_t_t7_change:
    active: false
    channel: bl-dwh-bi-metrics
    sqlcwd: store_level_t_t7_change.sql
    message: |
        ----- ----- ----- ----- ----- ----- ----- ----- ----- ----- ----- -----
        :blinkit: *{}*
        :small_orange_diamond: Conversion(%) --> *{}* and *{}*
        :small_red_triangle_down: Order volume(%) --> *{}*
        :clock1: Updated --> *{}*
    cron: 1,16,31,46 */3 * * *
    mode: 'Slack'
    connection: 'trino'
- store_level_rider_login_change:
    active: false
    channel: bl-dwh-bi-metrics
    sqlcwd: store_level_rider_login_change.sql
    message: |
        ----- ----- ----- ----- ----- ----- ----- ----- ----- ----- ----- -----
        :blinkit: *{}*
        :racing_motorcycle: Login mins change(%) --> *{}*
        :clock1: Updated --> *{}*
        :book: T0-T7 Login mins --> *{}* & *{}*
    cron: 1,16,31,46 */3 * * *
    mode: 'Slack'
    connection: 'trino'
- Store Level Direct Handover Change - T0 v T7:
    active: false
    channel: bl-dwh-bi-metrics
    sqlcwd: store_level_t0_t7_dh_change.sql
    message: |
        ----- ----- ----- ----- ----- ----- ----- ----- ----- ----- ----- -----
        :blinkit: *{}*
        :handshake: Change(%) --> *{}*
        :clock1: Hour --> *{}*
        :hourglass: T0-T7 DH% --> *{}* & *{}*
    cron: 50 */3 * * *
    mode: 'Slack'
    connection: 'trino'
- impression_jumbo_pipeline_delay:
    active: true
    channel: bl-dwh-p0-alerts
    sqlcwd: impression_delay_alert.sql
    message: <!subteam^S03TEACQKDW> impression pipeline having delay of *{}* hours.
    cron: 53 0-21 * * *
    mode: 'Slack'
    connection: 'trino'
- dim_product_cms_enable_flag_mismatch:
    active: true
    channel: bl-dwh-p0-alerts
    sqlcwd: gr_product_enable_flag_mismatch.sql
    message: <!subteam^S03TEACQKDW> Product id *{}* has enable flag mismatch with cms.gr_product.
    cron: 53 4 * * *
    mode: 'Slack'
    connection: 'trino'
- dim_product_cms_product_type_mismatch:
    active: true
    channel: bl-dwh-p0-alerts
    sqlcwd: gr_product_type_mismatch.sql
    message: <!subteam^S03TEACQKDW> Product id *{}* has product type mismatch with cms.gr_product.
    cron: 53 4 * * *
    mode: 'Slack'
    connection: 'trino'
- fact_supply_duplicate_check:
    active: true
    channel: bl-dwh-p0-alerts
    sqlcwd: fact_supply_duplicate_check.sql
    message: <!subteam^S03TEACQKDW> Checkout date *{}* has duplicates in dwh.fact_supply_chain_order_details.
    cron: 53 4 * * *
    mode: 'Slack'
    connection: 'trino'
- fact_order_details_duplicate_check:
    active: true
    channel: bl-dwh-p0-alerts
    sqlcwd: fact_order_duplicate_check.sql
    message: <!subteam^S03TEACQKDW> Order date *{}* has duplicates in dwh.fact_sales_order_details.
    cron: 53 4 * * *
    mode: 'Slack'
    connection: 'trino'
- fact_order_item_details_duplicate_check:
    active: true
    channel: bl-dwh-p0-alerts
    sqlcwd: fact_order_item_duplicate_check.sql
    message: <!subteam^S03TEACQKDW> Order date *{}* has duplicates in dwh.fact_sales_order_item_details.
    cron: 53 4 * * *
    mode: 'Slack'
    connection: 'trino'
# - velocity_partnersbiz_data_mismatch:
#     active: true
#     channel: bl-dwh-p0-alerts
#     sqlcwd: velocity_partnersbiz_sales_data.sql
#     message: <!subteam^S03TEACQKDW> *{}* is having a diff of {} (*{}* %) in "mrp_gmv" for yesterday.
#     cron: 30 3 * * *
#     mode: 'Slack'
#     connection: 'trino'
- sonar_insight_data_lag_check:
    active: true
    channel: bl-dwh-alerts
    sqlcwd: sonar_insight_lag_check.sql
    message: Table *{}* is at lag of *{}* minutes, please check ASAP!
    cron: 26,59 * * * *
    mode: 'Slack'
    connection: 'trino'
- velocity_partnersbiz_data_mismatch_pos:
    active: true
    channel: bl-dwh-p0-alerts
    sqlcwd: velocity_partnersbiz_pos_sales_data.sql
    message: <!subteam^S03TEACQKDW> *{}* is having a diff of {} (*{}* %) in "mrp_gmv" for yesterday.
    cron: 30 3 * * *
    mode: 'Slack'
    connection: 'trino'
- inventory_transfer_variant_item_mismatch:
    active: true
    channel: bl-dwh-p0-alerts
    sqlcwd: inventory_transfer_variant_item_mismatch.sql
    message: <!subteam^S03TEACQKDW> invoice_billed_dates *{}* are having variant item mismatch for invoices *{}* and items *{}*
    cron: 30 3 * * *
    mode: 'Slack'
    connection: 'trino'
- consignment_dispatch_timestamp_missing:
    active: true
    channel: bl-dwh-p0-alerts
    sqlcwd: consignment_dispatch_timestamp_missing.sql
    message: <!subteam^S03TEACQKDW> invoice_billed_dated *{}* is having dispatch timestamp missing for *{}* invoices and *{}* items.
    cron: 30 3 * * *
    mode: 'Slack'
    connection: 'trino'
- inventory_transfer_current_merge_mismatch:
    active: true
    channel: bl-dwh-p0-alerts
    sqlcwd: inventory_transfer_current_merge_mismatch.sql
    message: <!subteam^S03TEACQKDW> invoice_billed_dated *{}* and receiver_outlet_id *{}* is having absolute_records *{} mismatch with merged_records *{}*
    cron: 30 3 * * *
    mode: 'Slack'
    connection: 'trino'
- dim_product_business_category_mismatch:
    active: true
    channel: bl-dwh-p0-alerts
    sqlcwd: dim_product_business_category_mismatch.sql
    message: <!subteam^S03TEACQKDW> product_id *{}*, business_category_id *{}*, business_category_name *{}* has mismatch with CMS
    cron: 30 3 * * *
    mode: 'Slack'
    connection: 'trino'
- cart_rank_bucket_anomaly:
    active: true
    channel: bl-dwh-bi-metrics
    sqlcwd: cart_rank_bucket_anomaly.sql
    message: Cart rank increased bucket *{}* has *{}* customers for yesterday. 
    cron: 50 2 * * *
    mode: 'Slack'
    connection: 'trino'
- blinkit_combined_promo_share_mapping:
    active: true
    channel: [bl-dwh-bi-metrics, bl-promo-support]
    sqlcwd: blinkit_combined_promo_share_mapping.sql
    message: Promo Id- *{}*, Promo Name- *{}*, Type- *{}*, linked with *{}* carts and having total discount of *{}* yesterday, is missing from promo dashboard mapping.
    cron: 50 2 * * *
    mode: 'Slack'
    connection: 'trino'
- sonar_prev_day_lag:
    active: true
    channel: bl-dwh-p0-alerts
    sqlcwd: sonar_prev_day_lag.sql
    message: <!subteam^S03TEACQKDW> SONAR Table *{}* has data only till *{}*. Please check urgently!!!!
    cron: 55 1 * * *
    mode: 'Slack'
    connection: 'trino'
- fact_and_oms_lag_t0:
    active: true
    channel: bl-dwh-p0-alerts
    sqlcwd: fact_and_oms_lag_t0.sql
    message: <!subteam^S03TEACQKDW> Fact and OMS Table at *{}*th hour has difference of *{}* Orders and *{}* GMV. Please Check!!!!
    cron: 24 1-18 * * *
    mode: 'Slack'
    connection: 'trino'
