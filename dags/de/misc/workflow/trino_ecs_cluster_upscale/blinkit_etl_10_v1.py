import os, sys
import logging
import dateutil
import requests
import json
from typing import Optional
from datetime import datetime, timedelta, timezone

from airflow import DAG
from airflow.models import Variable
from airflow.sensors.date_time import DateTimeSensorAsync

from kubernetes.client import models as k8s

from metrics_plugin import ECSUpdateServiceCount, ApplicationAutoscalingUpdate

import pencilbox as pb

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.gateway import GatewayBackendActions, GatewayBackendManager, GatewayConstants


k8s_executor_config = {
    "pod_override": k8s.V1Pod(
        spec=k8s.V1PodSpec(
            node_selector={"nodetype": "airflow-spot-general"},
            tolerations=[
                k8s.V1Toleration(
                    effect="NoSchedule",
                    key="service",
                    operator="Equal",
                    value="airflow-spot-general",
                ),
            ],
            containers=[
                k8s.V1Container(
                    image=Variable.get(
                        "442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable",
                        "442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable",
                    ),
                    name="base",
                    volume_mounts=[
                        k8s.V1VolumeMount(
                            mount_path="/usr/local/airflow/plugins",
                            name="airflow-dags",
                            sub_path="airflow/plugins",
                        ),
                    ],
                    resources=k8s.V1ResourceRequirements(
                        limits={"cpu": 1, "memory": "3G"},
                        requests={"cpu": 0.5, "memory": "500M"},
                    ),
                )
            ],
            service_account_name="prod-blinkit-airflow-ecs-scaling-role",
        )
    )
}


def pagerduty_failure_alert(context):
    dag_id = context.get("task_instance").dag_id
    run_id = context.get("run_id")
    task_id = context.get("task_instance").task_id
    header = {"Content-Type": "application/json"}
    owner = {"email": "<EMAIL>", "slack_id": "S089D0GQRCJ"}

    def get_payload(service_name):
        payload = {
            "event_action": "trigger",
            "links": [
                {
                    "href": context.get("task_instance").log_url.replace("http://", "https://"),
                    "text": "Logs URL",
                },
                {
                    "href": f"https://dse-commuter.grofer.io/view/{dag_id}/{run_id}/{task_id}.ipynb",
                    "text": "Notebook URL",
                },
            ],
            "client": "Airflow",
            "client_url": context.get("task_instance").log_url.replace("http://", "https://"),
            "payload": {
                "summary": f"DAG {dag_id} failed",
                "source": f"Airflow:{dag_id}:{run_id}:{task_id}",
                "custom_details": {
                    "Dag ID": dag_id,
                    "Run ID": run_id,
                    "Task ID": task_id,
                    "Owner": owner["email"],
                    "service_name": service_name,
                },
                "severity": "critical",
                "service_name": service_name,
            },
        }
        return payload

    alerts = [{"name": "data", "type": "service"}]
    pd_service_names = [alert["name"] for alert in alerts if alert["type"] == "service"]
    for service_name in pd_service_names:
        payload = get_payload(service_name)
        payload["routing_key"] = pb.get_secret("dse/services/pagerduty/routing_keys")["global"]
        response = requests.post(
            "https://events.pagerduty.com/v2/enqueue",
            data=json.dumps(payload),
            headers=header,
        )
        if response.json()["status"] != "success":
            logging.warning(f"Could not send PagerDuty Alert: {response.text}")
            slack_id = owner["slack_id"]
            _owner = {
                "s": "s" if len(slack_id.split(" ")) > 1 else "",
                "slack_id": "<@{}>".format(slack_id),
            }
            message = (
                f"{_owner['slack_id']} `{dag_id}` failed but cannot send pagerduty alert to `{service_name}`."
                f"\n```{response.text}```"
            )
            pb.send_slack_message(channel="bl-data-airflow-alerts", text=message, source_info=False)


def failure_alerts(context):
    pagerduty_failure_alert(context)


AWS_DEFAULT_REGION = "ap-southeast-1"
TRINO_CLUSTER_NAME = "etl-trino"
ETL_10_COORDINATOR_SERVICE = "blinkit-etl-coordinator-10"
ETL_10_COORDINATOR_WORKER_SERVICE = "blinkit-etl-worker-10"
ETL_10_WORKER_RES_ID = f"service/{TRINO_CLUSTER_NAME}/{ETL_10_COORDINATOR_WORKER_SERVICE}"
ETL_GATEWAY_BASE_URL = "https://etl-trino.analytics.blinkit.in"
BLINKIT_ETL_10_COORDINATOR_URL = "http://blinkit-etl-coordinator-10.trino:8889"


args = {
    "owner": "<EMAIL>",
    "on_failure_callback": failure_alerts,
    "start_date": datetime.strptime("2025-01-28T01:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": datetime.strptime("2026-10-30T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "slack_id": "S089D0GQRCJ",
    "execution_timeout": timedelta(minutes=1440),
    "pool": "de_pool",
}

dag = DAG(
    dag_id="de_misc_workflow_trino_ecs_cluster_upscale_blinkit_etl_10_v1",
    default_args=args,
    schedule_interval="30 2 * * *",  # Run at 2:30 AM daily
    tags=["trino", "upscale", "de", "misc", "workflow"],
    catchup=False,
)

env = {}
default_env = {
    "AIRFLOW_DAG_ID": "de_misc_workflow_trino_ecs_cluster_upscale_blinkit_etl_10_v1",
    "AIRFLOW_DAG_OWNER": "<EMAIL>",
    "AIRFLOW_DAG_RUN_ID": "{{ run_id }}",
    "PYTHONPATH": "/usr/local/airflow/dags/repo/dag_utils",
}
env.update(default_env)

class GatewayBackendManagerOperator(GatewayBackendManager):
    def __init__(self,
                 gateway_base_url: str = ETL_GATEWAY_BASE_URL,
                 coordinator_url: str = BLINKIT_ETL_10_COORDINATOR_URL,
                 *args, **kwargs):
        super().__init__(gateway_base_url=gateway_base_url,
                         coordinator_url=coordinator_url,
                         *args, **kwargs)


# Disable scale-in to prevent scaling down during ETL operations
disable_etl_10_scalein = ApplicationAutoscalingUpdate(
    dag=dag,
    task_id="disable_blinkit_etl_10_scalein",
    region_name=AWS_DEFAULT_REGION,
    resource_id=ETL_10_WORKER_RES_ID,
    min_capacity=15, max_capacity=50, suspend_scalein=True,
    suspend_scaleout=False,
    executor_config=k8s_executor_config,
    retries=2
)

# Upscale coordinator service
upscale_etl_10_coordinator = ECSUpdateServiceCount(
    dag=dag,
    task_id="upscale_blinkit_etl_10_coordinator",
    region_name=AWS_DEFAULT_REGION,
    cluster_name=TRINO_CLUSTER_NAME,
    service_name=ETL_10_COORDINATOR_SERVICE,
    desired_count=1, force_update=False,
    executor_config=k8s_executor_config,
    retries=2
)

# Upscale worker service
upscale_etl_10_worker = ECSUpdateServiceCount(
    dag=dag,
    task_id="upscale_blinkit_etl_10_worker",
    region_name=AWS_DEFAULT_REGION,
    cluster_name=TRINO_CLUSTER_NAME,
    service_name=ETL_10_COORDINATOR_WORKER_SERVICE, 
    desired_count=20,  # Start with 20 workers
    force_update=False,
    executor_config=k8s_executor_config,
    retries=2
)

# Wait for cluster to be ready
etl_10_cluster_wait_period = DateTimeSensorAsync(
    dag=dag,
    task_id="cluster_startup_cooldown.wait_task",
    target_time=datetime.now(timezone.utc) + timedelta(minutes=15),
    timeout=1800,
    retries=0,
    poke_interval=5 * 60,
    queue="celery",
)

# Register cluster with gateway
register_cluster: GatewayBackendManagerOperator = GatewayBackendManagerOperator(
    dag=dag,
    task_id="blinkit_etl_10.register_to_gateway",
    backend_name="etl-cluster-10",
    action=GatewayBackendActions.UPDATE_BACKEND.value,
    action_meta={GatewayConstants.BACKEND_STATE: True, GatewayConstants.ROUTING_GROUP: "adhoc"},
    queue="celery",
    retries=2
)

# Enable scale-in after cluster is registered
enable_etl_10_scalein = ApplicationAutoscalingUpdate(
    dag=dag,
    task_id="enable_blinkit_etl_10_scalein",
    region_name=AWS_DEFAULT_REGION,
    resource_id=ETL_10_WORKER_RES_ID,
    min_capacity=15, max_capacity=50, suspend_scalein=False,
    suspend_scaleout=False,
    executor_config=k8s_executor_config,
    retries=2
)

# Wait for ETL operations to complete (5 hours during 2:30-7:30 AM UTC window)
query_executor: DateTimeSensorAsync = DateTimeSensorAsync(
    dag=dag,
    task_id="query_execution.wait_task",
    target_time=datetime.now(timezone.utc) + timedelta(hours=5, minutes=0),  # 5 hours execution window
    timeout=9 * 3600,
    retries=0,
    poke_interval=5 * 60,
    queue="celery",
)

# Deregister cluster from gateway
deregister_cluster: GatewayBackendManagerOperator = GatewayBackendManagerOperator(
    dag=dag,
    task_id="blinkit_etl_10.deregister_from_gateway",
    backend_name="etl-cluster-10",
    action=GatewayBackendActions.UPDATE_BACKEND.value,
    action_meta={GatewayConstants.BACKEND_STATE: False, GatewayConstants.ROUTING_GROUP: "adhoc"},
    queue="celery",
    retries=2
)

# Cool down period before scaling down
cool_down_period: DateTimeSensorAsync = DateTimeSensorAsync(
    dag=dag,
    task_id="cluster_termination_cooldown.wait_task",
    target_time=datetime.now(timezone.utc) + timedelta(minutes=30),
    timeout=2 * 3600,
    retries=0,
    poke_interval=5 * 60,
    queue="celery",
)

# Downscale coordinator service
downscale_etl_10_coordinator = ECSUpdateServiceCount(
    dag=dag,
    task_id="downscale_blinkit_etl_10_coordinator",
    region_name=AWS_DEFAULT_REGION,
    cluster_name=TRINO_CLUSTER_NAME,
    service_name=ETL_10_COORDINATOR_SERVICE,
    desired_count=0, force_update=False,
    executor_config=k8s_executor_config,
    retries=2
)

# Downscale worker service
downscale_etl_10_worker = ECSUpdateServiceCount(
    dag=dag,
    task_id="downscale_blinkit_etl_10_worker",
    region_name=AWS_DEFAULT_REGION,
    cluster_name=TRINO_CLUSTER_NAME,
    service_name=ETL_10_COORDINATOR_WORKER_SERVICE, 
    desired_count=0,
    force_update=False,
    executor_config=k8s_executor_config,
    retries=2
)

# Define task dependencies
disable_etl_10_scalein >> upscale_etl_10_coordinator >> upscale_etl_10_worker >> etl_10_cluster_wait_period >> register_cluster >> enable_etl_10_scalein >> query_executor >> deregister_cluster >> cool_down_period >> downscale_etl_10_coordinator >> downscale_etl_10_worker

if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k) 