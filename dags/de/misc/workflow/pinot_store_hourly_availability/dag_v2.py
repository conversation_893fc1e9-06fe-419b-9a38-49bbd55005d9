# -*- coding: utf-8 -*-

import os
import logging
from contextlib import closing
from datetime import datetime, timedelta

import airflow
from airflow import DAG
from airflow.hooks.base_hook import BaseHook
from airflow.models import Variable
from metrics_plugin import PapermillOperator
from kubernetes.client import models as k8s

import pencilbox as pb


cwd = os.path.dirname(os.path.realpath(__file__))


k8s_executor_config = {
    "pod_override": k8s.V1Pod(
        spec=k8s.V1PodSpec(
            node_selector={"nodetype": "airflow-spot-general"},
            tolerations=[
                k8s.V1Toleration(
                    effect="NoSchedule",
                    key="service",
                    operator="Equal",
                    value="airflow-spot-general",
                ),
            ],
            containers=[
                k8s.V1Container(
                    image=Variable.get(
                        "442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable",
                        "442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable",
                    ),
                    name="base",
                    volume_mounts=[
                        k8s.V1VolumeMount(
                            mount_path="/usr/local/airflow/plugins",
                            name="airflow-dags",
                            sub_path="airflow-de/plugins",
                        ),
                    ],
                    resources=k8s.V1ResourceRequirements(
                        limits={"cpu": 1, "memory": "3Gi"}, requests={"cpu": 0.5, "memory": "1Gi"}
                    ),
                )
            ],
            service_account_name="blinkit-prod-airflow-de-primary-eks-role",
        )
    )
}


def slack_failure_alert(context):
    task_instance = context.get("task_instance")
    dag_id = task_instance.dag_id
    run_id = context.get("run_id")
    task_id = task_instance.task_id
    log_url = task_instance.log_url.replace("http://", "https://")
    notebook_url = (
        f"https://dse-commuter.grofer.io/view/{dag_id}/{run_id}/{task_id}"
        if task_instance.operator == "PapermillOperator"
        else None
    )

    owner = {"email": "<EMAIL>", "slack_id": "S089D0GQRCJ"}
    slack_id = owner["slack_id"]
    _owner = {
        "s": "s" if len(slack_id.split(" ")) > 1 else "",
        "slack_id": "<!subteam^{}>".format(slack_id),
    }

    slack_failure_msg = f"""
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner{_owner["s"]}*: {_owner["slack_id"]}
        *Log Url*: {log_url}
        {f"*Notebook Url*: {notebook_url}" if notebook_url is not None else ""}
    """

    slack_alert_configs = [
        {"channel": "bl-data-alerts-p1"},
    ]

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(channel=channel, text=slack_failure_msg, source_info=False)
        except Exception as e:
            logging.warning(e)
            message = (
                f"{_owner['slack_id']} `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(channel="bl-data-airflow-alerts", text=message, source_info=False)


def failure_alerts(context):
    slack_failure_alert(context)


args = {
    "owner": "<EMAIL>",
    "on_failure_callback": failure_alerts,
    "start_date": datetime.strptime("2022-10-14T06:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": datetime.strptime("2026-08-16T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "slack_id": "S089D0GQRCJ",
    "execution_timeout": timedelta(minutes=840),
    "pool": "de_pool",
}

dag = DAG(
    dag_id="de_misc_workflow_pinot_store_hourly_availability_v2",
    default_args=args,
    schedule_interval="0 * * * *",
    tags=["pinot_store_hourly_availability", "de", "misc", "workflow"],
)

env = {}
default_env = {
    "AIRFLOW_DAG_ID": "de_misc_workflow_pinot_store_hourly_availability_v2",
    "AIRFLOW_DAG_OWNER": "<EMAIL>",
    "AIRFLOW_DAG_RUN_ID": "{{ run_id }}",
    "PYTHONPATH": "/usr/local/airflow/dags/repo/dag_utils",
}
env.update(default_env)

notebook_task = PapermillOperator(
    task_id="run_notebook",
    input_nb="{cwd}/notebook.ipynb".format(cwd=cwd),
    output_nb="s3://grofers-prod-dse-sgp/airflow/dag_runs/de_misc_workflow_pinot_store_hourly_availability_v2/{{ run_id }}/run_notebook/try_{{ task_instance.try_number }}.ipynb",
    parameters={
        "cwd": "/usr/local/airflow/dags/repo/dags/de/misc/workflow/pinot_store_hourly_availability"
    },
    env=env,
    dag=dag,
    priority_weight=1,
    executor_config=k8s_executor_config,
)

if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
