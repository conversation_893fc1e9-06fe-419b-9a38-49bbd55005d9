{"cells": [{"cell_type": "code", "execution_count": null, "id": "abcd02a1-4d94-4504-84ea-b378a769d989", "metadata": {}, "outputs": [], "source": ["# Imports\n", "import json\n", "import requests\n", "import pandas as pd\n", "import pencilbox as pb\n", "import sys\n", "from datetime import date, datetime, timedelta\n", "from slack_sdk import WebClient\n", "from slack_sdk.errors import SlackApiError"]}, {"cell_type": "code", "execution_count": null, "id": "b712677d-6225-492c-9ea7-9650937d3c62", "metadata": {}, "outputs": [], "source": ["sys.path.append(cwd)\n", "from slack_client import SlackClient"]}, {"cell_type": "code", "execution_count": null, "id": "508c8756-c6c0-473b-b0ce-c1a45b4da5c9", "metadata": {}, "outputs": [], "source": ["# START AND END DATE\n", "today = str((datetime.now()).strftime(\"%Y/%m/%d\"))"]}, {"cell_type": "code", "execution_count": null, "id": "7ff56a55-0aea-4964-bc0c-fc1769321784", "metadata": {}, "outputs": [], "source": ["# Connection to Database\n", "conn = pb.get_connection(\"[Warehouse] Trino\")"]}, {"cell_type": "code", "execution_count": null, "id": "5a7e5874-64bf-48a1-8f16-31b9358bd0e3", "metadata": {}, "outputs": [], "source": ["# Creating slack client\n", "SLACK_BOT_API_TOKEN = pb.get_secret(\"dse/services/slack/tokens\")[\"bl-analytics-bot\"]\n", "slack_client = SlackClient(slack_bot_token=SLACK_BOT_API_TOKEN)"]}, {"cell_type": "code", "execution_count": null, "id": "8012ad48-7002-405c-b43d-ca8462bb7e93", "metadata": {}, "outputs": [], "source": ["# Sheet ID where data is being pushed\n", "SHEET_ID = \"12iIRhF9YJ979NfZvaLJGnE_ThCGDM_WiI2zmmuTFGT8\"\n", "GID = \"102673455\""]}, {"cell_type": "code", "execution_count": null, "id": "40e27a6f-4ffe-4650-ab79-8226531d43d3", "metadata": {}, "outputs": [], "source": ["# Creating session for <PERSON><PERSON>\n", "REPORTS_API_KEY = pb.get_secret(\"data/services/redash-reports/access_management/api_keys\")[\"key\"]\n", "report_session = requests.Session()\n", "report_session.headers.update({\"Authorization\": f\"Key {REPORTS_API_KEY}\"})\n", "REPORTS_API_HOST = \"https://dse-redashreports-service-data.prod-sgp-k8s.grofer.io\"\n", "REPORTS_HOST = \"https://reports.grofer.io\""]}, {"cell_type": "markdown", "id": "d5e4e85c-f8f6-4d28-a46e-f4ef772f76d9", "metadata": {}, "source": ["### Function to get slack_ids"]}, {"cell_type": "code", "execution_count": null, "id": "5f5a11c1-15a9-406b-a09e-a6ba9d472310", "metadata": {}, "outputs": [], "source": ["def get_slack_id(slack_client, user_email):\n", "    slack_user_info = slack_client.fetch_slack_user_info(user_email)\n", "    if slack_user_info is None:\n", "        return None\n", "    slack_id = slack_user_info.get(\"id\")\n", "    return slack_id"]}, {"cell_type": "markdown", "id": "945800bd-6158-4724-9210-245ce73b779c", "metadata": {}, "source": ["### Function to fetch and update the query"]}, {"cell_type": "code", "execution_count": null, "id": "d78c59ee-7926-4d15-a8bd-405703182c07", "metadata": {}, "outputs": [], "source": ["def fetch_and_update_query(session, query_id):\n", "    response = session.get(f\"{REPORTS_API_HOST}/api/queries/{query_id}\")\n", "    if response.status_code != 200:\n", "        print(f\"Failed to fetch query details for ID: {query_id}\")\n", "        return None\n", "\n", "    query_data = response.json()\n", "    query_name = query_data.get(\"name\", \"Unknown Name\")\n", "    query_link = f\"{REPORTS_HOST}/queries/{query_id}\"\n", "\n", "    if \"schedule\" in query_data:\n", "        query_data[\"schedule\"] = None\n", "\n", "    update_response = session.post(f\"{REPORTS_API_HOST}/api/queries/{query_id}\", json=query_data)\n", "\n", "    if update_response.status_code != 200:\n", "        print(f\"Failed to update query {query_name} (ID: {query_id})\")\n", "\n", "    return query_name, query_link"]}, {"cell_type": "markdown", "id": "960ef13c-d11d-4db4-9d8d-07f25db8f92f", "metadata": {}, "source": ["### Creating S<PERSON>ck Message"]}, {"cell_type": "code", "execution_count": null, "id": "1cd7546b-b7c8-4921-b08e-004eb06ea8e4", "metadata": {}, "outputs": [], "source": ["def slack_message(slack_ids, SHEET_ID):\n", "    tag_users = \" \".join([f\"<@{slack_id}>\" for slack_id in slack_ids])\n", "    text = (\n", "        f\"*:loudspeaker: Pausing query schedules on Redash*\\n\\n\"\n", "        f\"The schedule of the following queries have been removed because of consecutive failures.\\n\"\n", "        f\"Please review and resolve the issues directly from the Redash UI to prevent future disruptions.\\n\\n\"\n", "        f\"https://docs.google.com/spreadsheets/d/{SHEET_ID}#gid={GID}\\n\\n\"\n", "        f\"{tag_users}\"\n", "    )\n", "\n", "    return text"]}, {"cell_type": "markdown", "id": "d1417125-e680-4c07-b5c9-c0ea7b8eb3a8", "metadata": {}, "source": ["### Getting the queries to pause"]}, {"cell_type": "code", "execution_count": null, "id": "de5b3152-89fd-48ae-a5ae-bcb0fd5bdbd9", "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "WITH query_executions AS (\n", "    SELECT\n", "        SPLIT_PART(SPLIT_PART(query_base, 'query_id: ', 2), ',', 1) AS query_id,\n", "        query_create_dt AS execution_date,\n", "        querystate,\n", "        user,\n", "        ROW_NUMBER() OVER (PARTITION BY SPLIT_PART(SPLIT_PART(query_base, 'query_id: ', 2), ',', 1) ORDER BY executionstarttime DESC) AS rn\n", "    FROM \n", "        blinkit_iceberg.dwh.bl_trino_query_log\n", "    WHERE \n", "       query_create_dt BETWEEN (CURRENT_DATE - INTERVAL '60' DAY)\n", "            AND CURRENT_DATE\n", "        AND catalog IN ('blinkit', 'Blinkit', 'blinkit_iceberg', 'blinkit_iceberg_staging')\n", "        AND clustername = 'blinkit-reporting-cluster'\n", "        AND principal = 'bi_redash'\n", "        AND SUBSTR(TRIM(SPLIT_PART(query_base, 'scheduled:', 2)), 1, 4) = 'true'\n", "        AND TRY_CAST(SPLIT_PART(SPLIT_PART(query_base, 'query_id: ', 2), ',', 1) AS INTEGER) IS NOT NULL\n", ")\n", "\n", "(\n", "SELECT query_id, user\n", "FROM (\n", "    SELECT \n", "        query_id, \n", "        user,\n", "        SUM(CASE WHEN rnk = 1 AND querystate = 'failed' THEN 1 ELSE 0 END) AS last_failure_flag,\n", "        SUM(CASE WHEN querystate = 'failed' THEN 1 ELSE 0 END) AS total_failures\n", "    FROM (\n", "        SELECT \n", "            query_id, \n", "            user, \n", "            querystate,\n", "            ROW_NUMBER() OVER (PARTITION BY query_id ORDER BY execution_date DESC) AS rnk \n", "        FROM \n", "            query_executions\n", "    ) AS subquery\n", "    WHERE rnk <= 5\n", "    GROUP BY\n", "        query_id, user\n", ") AS result\n", "WHERE last_failure_flag = 1\n", "AND total_failures >= 5\n", ")\n", "\n", "INTERSECT\n", "\n", "(\n", "SELECT query_id, user\n", "from\n", "(\n", "SELECT query_id, user\n", "    from\n", "        query_executions\n", "    where\n", "        execution_date >= CURRENT_DATE - INTERVAL '3' DAY\n", "    group by \n", "        query_id, user\n", "    having\n", "        (cast(count(case when querystate = 'failed' then 1 end)as double) / count(1)*100)>90\n", ")\n", ")\n", "\"\"\"\n", "queries_to_pause = pd.read_sql(query, conn)"]}, {"cell_type": "code", "execution_count": null, "id": "17001b4f-8c61-449d-a07f-64fe14518481", "metadata": {}, "outputs": [], "source": ["# Creating new df to store the required results\n", "result_df = pd.DataFrame(columns=[\"query_id\", \"user_email\", \"query_name\"])"]}, {"cell_type": "code", "execution_count": null, "id": "7046563f-903f-4254-a3b5-0325587ffb7b", "metadata": {}, "outputs": [], "source": ["slack_ids = set()\n", "for _, row in queries_to_pause.iterrows():\n", "    query_id = row[\"query_id\"]\n", "    user_email = row[\"user\"]\n", "\n", "    slack_id = get_slack_id(slack_client, user_email)\n", "    if not slack_id:\n", "        continue\n", "    slack_ids.add(slack_id)\n", "\n", "    query_name, query_link = fetch_and_update_query(report_session, query_id)\n", "    if query_name and query_link:\n", "        new_row = pd.DataFrame(\n", "            {\n", "                \"query_id\": [query_link],\n", "                \"user_email\": [user_email],\n", "                \"query_name\": [query_name],\n", "            }\n", "        )\n", "        result_df = pd.concat([result_df, new_row], ignore_index=True)\n", "message = slack_message(slack_ids, SHEET_ID)\n", "if not result_df.empty:\n", "    pb.send_slack_message(channel=\"bl-data-support\", text=message)\n", "    pb.to_sheets(result_df, SHEET_ID, \"redash_pause_queries\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}