{"cells": [{"cell_type": "code", "execution_count": null, "id": "17fc28d5-316e-49e3-8a12-9cfd20017d48", "metadata": {}, "outputs": [], "source": ["! pip install aenum==3.1.11 aiobotocore==2.4.2 aiohttp==3.8.1 awswrangler==2.19.0 backoff==2.2.1 boto3==1.24.59 botocore==1.27.59 cramjam==2.6.2 fastparquet==2023.2.0 fsspec==2023.1.0 gremlinpython==3.6.2 opensearch-py==2.1.1 pandas==1.5.1 pg8000==1.29.4 progressbar2==4.2.0 pymysql==1.0.2 python-utils==3.5.2 requests-aws4auth==1.2.2 s3fs==2023.1.0 s3transfer==0.6.0 scramp==1.4.4 smart-open==6.3.0"]}, {"cell_type": "code", "execution_count": null, "id": "3937bd12-46ab-4490-917a-0c1f38040902", "metadata": {}, "outputs": [], "source": ["import json\n", "import boto3\n", "import itertools\n", "import pandas as pd\n", "import numpy as np\n", "import awswrangler as wr\n", "from smart_open import open\n", "from datetime import datetime\n", "from json.decoder import JSONDecodeError"]}, {"cell_type": "code", "execution_count": null, "id": "0afca3f7", "metadata": {}, "outputs": [], "source": ["client = boto3.client(\"s3\")\n", "s3 = boto3.resource(\"s3\")\n", "paginator = client.get_paginator(\"list_objects\")"]}, {"cell_type": "code", "execution_count": null, "id": "7c39316c-76ab-4aec-9a4e-76d47146aaa8", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["ds = datetime.now().date().strftime(\"%Y-%m-%d\")\n", "SOURCE_BUCKET = \"prod-dse-backend-events-raw\"\n", "SINK_BUCKET = \"prod-dse-backend-events\"\n", "SUBPREFIX = \"application-logs/dragonstone_api_canary-spell_corrected_result/\"\n", "TABLE_NAME = \"dragonstone_api_spell_corrected_result\"\n", "SOURCE_SPLIT = \"daily\"\n", "MAX_ROWS_PER_FILE = 3000000"]}, {"cell_type": "code", "execution_count": null, "id": "678a6f2c-8ee3-41af-9f2b-733084fe7669", "metadata": {}, "outputs": [], "source": ["KEYS = []\n", "if SOURCE_SPLIT == \"hourly\":\n", "    year, month, day = ds.split(\"-\")\n", "    K = f\"year={year}/month={month}/day={day}\"\n", "else:\n", "    K = f\"at_date={ds}\"\n", "if \",\" in SUBPREFIX:\n", "    for prefix in SUBPREFIX.split(\",\"):\n", "        KEYS.append(f\"{prefix}{K}\")\n", "else:\n", "    KEYS.append(f\"{SUBPREFIX}{K}\")\n", "print(KEYS)"]}, {"cell_type": "code", "execution_count": null, "id": "05ff06ff-2f01-422b-a47d-958e08e21ff5", "metadata": {}, "outputs": [], "source": ["def delete_files_from_s3(prefix, bucket):\n", "    if prefix:\n", "        bucket = s3.<PERSON><PERSON>(bucket)\n", "        bucket.objects.filter(Prefix=prefix).delete()"]}, {"cell_type": "code", "execution_count": null, "id": "a2b8a3bb-5a70-4b42-9e46-d48b2c1d9805", "metadata": {}, "outputs": [], "source": ["def fetch_objects(bucket, prefix):\n", "    page_iterator = paginator.paginate(Bucket=bucket, Prefix=prefix)\n", "    for page in page_iterator:\n", "        if page.get(\"Contents\"):\n", "            for key in page[\"Contents\"]:\n", "                if key.get(\"Size\"):\n", "                    yield key\n", "\n", "\n", "def split_list(alist, wanted_parts=1):\n", "    length = len(alist)\n", "    return [\n", "        alist[i * length // wanted_parts : (i + 1) * length // wanted_parts]\n", "        for i in range(wanted_parts)\n", "    ]"]}, {"cell_type": "code", "execution_count": null, "id": "0e79e485-395b-4152-bd60-a4c37f4d7089", "metadata": {}, "outputs": [], "source": ["def fetch_record(bucket, _object):\n", "    all_records = []\n", "    data_location = \"s3://{}/{}\".format(bucket, _object)\n", "    chunks = pd.read_json(open(data_location), lines=True, chunksize=200000)\n", "    for data in chunks:\n", "        for _, row in data.iterrows():\n", "            event_payload = row[\"message\"]\n", "            if event_payload.get(\"grofers.io/message-event\"):\n", "                event_payload.pop(\"grofers.io/message-event\")\n", "            if TABLE_NAME in (\n", "                \"dragonstone_api_high_confidence_query_issue\",\n", "                \"dragonstone_api_spell_corrected_result\",\n", "            ):\n", "                event_payload_filtered = event_payload[\"event\"][\"meta\"]\n", "            else:\n", "                event_payload_filtered = event_payload[\"event\"]\n", "            event_payload_filtered[\"lumber_uuid\"] = event_payload[\"lumber_uuid\"]\n", "            event_payload_filtered[\"lumber_timestamp\"] = event_payload[\"timestamp\"]\n", "            all_records.append(event_payload_filtered)\n", "    return all_records\n", "\n", "\n", "def write_record_to_s3(df, prefix):\n", "    s3_url = f\"s3://{SINK_BUCKET}/{prefix}\"\n", "    print(f\"writing {df.shape} rows to {s3_url}\")\n", "    wr.s3.to_parquet(df=df, path=s3_url, compression=\"snappy\")\n", "\n", "\n", "def fetch_df(_object):\n", "    record = fetch_record(SOURCE_BUCKET, _object[\"Key\"])\n", "    df = pd.<PERSON><PERSON><PERSON><PERSON>(record)\n", "    df.drop_duplicates(subset=[\"lumber_uuid\"], keep=\"last\", inplace=True)\n", "    if TABLE_NAME in (\n", "        \"dragonstone_api_autocomplete_empty_result\",\n", "        \"dragonstone_api_intent_empty_result\",\n", "    ):\n", "        df[\"meta\"] = df[\"meta\"].replace(to_replace={\"{}\": None})\n", "        df[\"disable_sales_boost\"] = df[\"disable_sales_boost\"].astype(\"bool\")\n", "        df = df.applymap((lambda x: None if x == {} else x))\n", "    elif <PERSON>NAME == \"cart_checkout\":\n", "        df[\"cart_id\"] = df[\"cart_id\"].astype(\"Int64\")\n", "        df[\"user_id\"] = df[\"user_id\"].astype(\"Int64\")\n", "    elif <PERSON>NAME == \"tick_tock_api_checkouts_blocked_info\":\n", "        df[\"cart_id\"] = df[\"cart_id\"].astype(\"Int64\")\n", "        df[\"user_id\"] = (\n", "            df[\"user_id\"].apply(lambda x: int(x) if str(x).isdigit() else None).astype(\"Int64\")\n", "        )\n", "        df[\"frontend_merchant_id\"] = df[\"frontend_merchant_id\"].astype(\"Int64\")\n", "        df[\"serviceability\"] = df[\"serviceability\"].apply(lambda x: json.dumps(x))\n", "    elif <PERSON>NAME == \"bots_api_user_bot_journey\":\n", "        df[\"meta\"] = df[\"meta\"].apply(lambda x: json.dumps(x))\n", "        df[\"request\"] = df[\"request\"].apply(lambda x: json.dumps(x))\n", "        df[\"response\"] = df[\"response\"].apply(lambda x: json.dumps(x))\n", "        df[\"issue_items\"] = df[\"issue_items\"].apply(lambda x: json.dumps(x))\n", "    elif <PERSON>NAME == \"espina_api_logs\":\n", "        df[\"user_id\"] = pd.to_numeric(df.user_id, errors=\"coerce\").fillna(0).astype(\"Int64\")\n", "        df[\"meta\"] = df[\"meta\"].apply(lambda x: json.dumps(x))\n", "    elif <PERSON>NAME == \"dragonstone_api_previous_buy_result\":\n", "        df[\"meta\"] = df[\"meta\"].apply(lambda x: json.dumps(x))\n", "        df[\"disable_sales_boost\"] = df[\"disable_sales_boost\"].astype(\"bool\")\n", "        if \"progress_bar_products\" in df.columns:\n", "            df[\"progress_bar_products\"] = df[\"progress_bar_products\"].apply(lambda x: json.dumps(x))\n", "    elif <PERSON>NAME == \"dragonstone_api_entity_recognition_result\":\n", "        df[\"merchant_id\"] = np.nan_to_num(df[\"merchant_id\"]).astype(int)\n", "        df[\"disable_sales_boost\"] = df[\"disable_sales_boost\"].astype(\"bool\")\n", "        df[\"latitude\"] = df[\"latitude\"].astype(\"double\")\n", "        df[\"longitude\"] = df[\"longitude\"].astype(\"double\")\n", "        if \"progress_bar_products\" in df.columns:\n", "            df[\"progress_bar_products\"] = df[\"progress_bar_products\"].apply(lambda x: json.dumps(x))\n", "    elif <PERSON>NAME == \"crm_lifeline_api_event_logs\":\n", "        df[\"meta\"] = df[\"meta\"].apply(lambda x: json.dumps(x))\n", "        df[\"request\"] = df[\"request\"].apply(lambda x: json.dumps(x))\n", "        df[\"karma\"] = df[\"karma\"].apply(lambda x: json.dumps(x))\n", "        df[\"cart_id\"] = df[\"cart_id\"].astype(\"str\")\n", "        df[\"customer_id\"] = df[\"customer_id\"].astype(str)\n", "        df[\"agent_id\"] = df[\"agent_id\"].astype(\"str\")\n", "        df[\"issue_id\"] = df[\"issue_id\"].astype(\"str\")\n", "        df[\"order_id\"] = df[\"order_id\"].astype(\"str\")\n", "        df[\"timestamp\"] = df[\"timestamp\"].astype(\"Int64\")\n", "    elif <PERSON>NAME == \"merchant_visibility_info\":\n", "        df[\"merchant_ids\"] = df[\"merchant_ids\"].apply(lambda x: json.dumps(x))\n", "        df[\"serviceable_merchants\"] = df[\"serviceable_merchants\"].apply(lambda x: json.dumps(x))\n", "        df[\"serviceable_merchant_ids\"] = df[\"serviceable_merchant_ids\"].apply(\n", "            lambda x: json.dumps(x)\n", "        )\n", "        df[\"merchants\"] = df[\"merchants\"].apply(lambda x: json.dumps(x))\n", "    # elif <PERSON>_NAME == \"dragonstone_api_high_confidence_query_issue\":\n", "    #     df[\"app_version\"] = df[\"app_version\"].astype(\"str\")\n", "    elif <PERSON>NAME == \"dragonstone_api_autosuggest_response_event\":\n", "        df[\"results\"] = df[\"results\"].apply(lambda x: json.dumps(x))\n", "    elif <PERSON>NAME == \"tick_tock_serviceability_info\":\n", "\n", "        def filter_list(actual_list, values):\n", "            return [val for val in actual_list if val not in values]\n", "\n", "        df.rename({\"downstream_service\": \"service\", \"lumber_uuid\": \"messageid\"}, inplace=True)\n", "\n", "        _eta = []\n", "        _eta_computed = []\n", "        _eta_override = []\n", "        for _index, row in df.iterrows():\n", "            serviceability = row[\"serviceability\"]\n", "            if serviceability is not None:\n", "                _eta.append(serviceability[\"eta\"])\n", "                _eta_computed.append(\n", "                    serviceability.get(\n", "                        \"computed_eta\",\n", "                        sum(\n", "                            filter_list(\n", "                                [i[\"duration\"] for i in serviceability[\"components\"]],\n", "                                (None, \"Infinity\", \"NaN\"),\n", "                            )\n", "                        ),\n", "                    )\n", "                )\n", "                _eta_override.append(serviceability.get(\"eta_override\"))\n", "            else:\n", "                _eta.append(None)\n", "                _eta_computed.append(None)\n", "                _eta_override.append(None)\n", "        df[\"eta\"] = _eta\n", "        df[\"eta_computed\"] = _eta_computed\n", "        df[\"eta_override\"] = _eta_override\n", "        df[\"cart_id\"] = df[\"cart_id\"].astype(\"Int64\")\n", "        df[\"user_id\"] = (\n", "            df[\"user_id\"].apply(lambda x: int(x) if str(x).isdigit() else None).astype(\"Int64\")\n", "        )\n", "        df[\"serviceability\"] = df[\"serviceability\"].apply(lambda x: json.dumps(x))\n", "    return df\n", "\n", "\n", "def fetch_object_offset(_object):\n", "    objs = _object[\"Key\"].split(\"/\")[-1].split(\"+\")\n", "    return objs[-2] + objs[-1].replace(\".json.gz\", \"\")"]}, {"cell_type": "code", "execution_count": null, "id": "6d706d5f-566f-4775-a178-bc2e51fa0882", "metadata": {}, "outputs": [], "source": ["objects_list = []\n", "for KEY in KEYS:\n", "    key_object = list(fetch_objects(SOURCE_BUCKET, KEY))\n", "    objects_list.extend(key_object)\n", "objects_to_fetch = sorted(objects_list, key=lambda x: x[\"LastModified\"])\n", "# small_elements, big_elements = split_list(objects_to_fetch, 2)\n", "# combined_objects = list(itertools.zip_longest(small_elements, big_elements[::-1]))"]}, {"cell_type": "code", "execution_count": null, "id": "15405cac", "metadata": {}, "outputs": [], "source": ["if TABLE_NAME in (\n", "    \"dragonstone_api_autocomplete_empty_result\",\n", "    \"dragonstone_api_intent_empty_result\",\n", "    \"dragonstone_api_entity_recognition_result\",\n", "    \"dragonstone_api_spell_corrected_result\",\n", "    \"dragonstone_api_high_confidence_query_issue\",\n", "    \"track_order_eta_details\",\n", "    \"track_order_delay_components\",\n", "    \"dragonstone_api_previous_buy_result\",\n", "    \"dragonstone_api_autosuggest_response_event\",\n", "    \"serviceability_manpower_block\",\n", "    \"serviceability_manpower_reopen_prediction\",\n", "    \"tick_tock_serviceability_info\",\n", "    \"crm_lifeline_api_event_logs\",\n", "    \"crm_api_bulk_upload_event_logs\",\n", "    \"merchant_visibility_info\",\n", "    \"espina_api_logs\",\n", "    \"cart_checkout\",\n", "    \"printing_forecast\",\n", "):\n", "    KEY = f\"application-logs/{TABLE_NAME}/at_date={ds}\"\n", "elif <PERSON>AME in (  # add those tables here which are populated through 2 (or more) services\n", "    \"bots_api_user_bot_journey\",\n", "):\n", "    KEY = KEYS[0]  # selects one s3 object (the first one) to put all the data in\n", "\n", "delete_files_from_s3(KEY, SINK_BUCKET)\n", "\n", "parts = 0\n", "list_of_df = []\n", "total_rows = 0\n", "for _object in objects_to_fetch:\n", "    df = fetch_df(_object)\n", "    if not df.empty:\n", "        list_of_df.append(df)\n", "        total_rows += len(df)\n", "    if total_rows >= MAX_ROWS_PER_FILE:\n", "        write_record_to_s3(\n", "            pd.concat(list_of_df, ignore_index=True),\n", "            f\"{KEY}/part_{parts}.snappy.parquet\",\n", "        )\n", "        parts += 1\n", "        list_of_df = []\n", "        total_rows = 0\n", "\n", "if list_of_df:\n", "    write_record_to_s3(\n", "        pd.concat(list_of_df, ignore_index=True),\n", "        f\"{KEY}/part_{parts}.snappy.parquet\",\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "48ae6a86-d31c-4163-a4bc-0522ae12b59e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d8d31a0f-b856-40ac-b9ff-06d10b5bf51a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}, "vscode": {"interpreter": {"hash": "aee8b7b246df8f9039afb4144a1f6fd8d2ca17a180786b69acc140d282b71a49"}}}, "nbformat": 4, "nbformat_minor": 5}