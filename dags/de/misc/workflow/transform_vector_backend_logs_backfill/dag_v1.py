# -*- coding: utf-8 -*-

import os
import logging
from contextlib import closing
from datetime import datetime, timedelta

import airflow
from airflow import DAG
from airflow.hooks.base_hook import BaseHook
from airflow.operators.dummy_operator import DummyOperator
from airflow.models import Variable
from metrics_plugin import PapermillOperator
from kubernetes.client import models as k8s

import pencilbox as pb


cwd = os.path.dirname(os.path.realpath(__file__))


def get_k8s_executor_config(executor_config={}):
    limits = {}
    requests = {}
    if executor_config.get("cpu"):
        limits["cpu"] = executor_config["cpu"]["limit"]
        requests["cpu"] = executor_config["cpu"]["request"]
    if executor_config.get("memory"):
        limits["memory"] = executor_config["memory"]["limit"]
        requests["memory"] = executor_config["memory"]["request"]

    return {
        "pod_override": k8s.V1Pod(
            spec=k8s.V1PodSpec(
                node_selector={"nodetype": executor_config.get("node_selector")},
                tolerations=[
                    k8s.V1Toleration(
                        effect="NoSchedule",
                        key="service",
                        operator="Equal",
                        value=executor_config.get("toleration"),
                    ),
                ],
                containers=[
                    k8s.V1Container(
                        image=Variable.get(
                            "442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable",
                            "442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable",
                        ),
                        name="base",
                        volume_mounts=[
                            k8s.V1VolumeMount(
                                mount_path="/usr/local/airflow/plugins",
                                name="airflow-dags",
                                sub_path="airflow-de/plugins",
                            ),
                        ],
                        resources=k8s.V1ResourceRequirements(limits=limits, requests=requests),
                    )
                ],
                service_account_name="blinkit-prod-airflow-de-primary-eks-role",
            )
        )
    }


def slack_failure_alert(context):
    task_instance = context.get("task_instance")
    dag_id = task_instance.dag_id
    run_id = context.get("run_id")
    task_id = task_instance.task_id
    log_url = task_instance.log_url.replace("http://", "https://")
    notebook_url = (
        f"https://dse-commuter.grofer.io/view/{dag_id}/{run_id}/{task_id}"
        if task_instance.operator == "PapermillOperator"
        else None
    )

    owner = {"email": "<EMAIL>", "slack_id": "S089D0GQRCJ"}
    slack_id = owner["slack_id"]
    _owner = {
        "s": "s" if len(slack_id.split(" ")) > 1 else "",
        "slack_id": "<!subteam^{}>".format(slack_id),
    }

    slack_failure_msg = f"""
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner{_owner["s"]}*: {_owner["slack_id"]}
        *Log Url*: {log_url}
        {f"*Notebook Url*: {notebook_url}" if notebook_url is not None else ""}
    """

    slack_alert_configs = [
        {"channel": "bl-data-alerts-p1"},
    ]

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(channel=channel, text=slack_failure_msg, source_info=False)
        except Exception as e:
            logging.warning(e)
            message = (
                f"{_owner['slack_id']} `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(channel="bl-data-airflow-alerts", text=message, source_info=False)


def failure_alerts(context):
    slack_failure_alert(context)


args = {
    "owner": "<EMAIL>",
    "on_failure_callback": failure_alerts,
    "start_date": datetime.strptime("2022-03-29T10:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": datetime.strptime("2025-08-15T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "slack_id": "S089D0GQRCJ",
    "execution_timeout": timedelta(minutes=840),
    "pool": "de_pool",
}

dag = DAG(
    dag_id="de_misc_workflow_transform_vector_backend_logs_backfill_v1",
    default_args=args,
    schedule_interval="0 0 1 1 1",
    tags=["de", "misc", "workflow"],
    concurrency=3,
)

env = {}
default_env = {
    "AIRFLOW_DAG_ID": "de_misc_workflow_transform_vector_backend_logs_backfill_v1",
    "AIRFLOW_DAG_OWNER": "<EMAIL>",
    "AIRFLOW_DAG_RUN_ID": "{{ run_id }}",
    "PYTHONPATH": "/usr/local/airflow/dags/repo/dag_utils",
}
env.update(default_env)


notebook_task_bag = {}


notebook_task_1 = PapermillOperator(
    task_id="run_dragonstone_api_intent_empty_result",
    input_nb="{cwd}/notebook.ipynb".format(cwd=cwd),
    output_nb="s3://grofers-prod-dse-sgp/airflow/dag_runs/de_misc_workflow_transform_vector_backend_logs_backfill_v1/{{ run_id }}/run_dragonstone_api_intent_empty_result/try_{{ task_instance.try_number }}.ipynb",
    parameters={
        "ds": "2025-08-09",
        "SOURCE_BUCKET": "prod-dse-backend-events-raw",
        "SOURCE_SPLIT": "daily",
        "SINK_BUCKET": "prod-dse-backend-events",
        "SUBPREFIX": "application-logs/dragonstone_api-intent_empty_result/,application-logs/dragonstone_api_canary-intent_empty_result/,application-logs/dragonstone_api_primary-intent_empty_result/",
        "TABLE_NAME": "dragonstone_api_intent_empty_result",
        "MAX_ROWS_PER_FILE": 2000000,
        "cwd": "/usr/local/airflow/dags/repo/dags/de/misc/workflow/transform_vector_backend_logs_backfill",
    },
    env=env,
    dag=dag,
    priority_weight=1,
    retries=3,
    executor_config=get_k8s_executor_config(
        {
            "cpu": {"request": 2, "limit": 4},
            "memory": {"request": "16Gi", "limit": "30Gi"},
            "node_selector": "airflow-spot-high-mem",
            "toleration": "airflow-spot-high-mem",
        }
    ),
)
notebook_task_bag["run_dragonstone_api_intent_empty_result"] = notebook_task_1


notebook_task_2 = PapermillOperator(
    task_id="run_dragonstone_api_autocomplete_empty_result",
    input_nb="{cwd}/notebook.ipynb".format(cwd=cwd),
    output_nb="s3://grofers-prod-dse-sgp/airflow/dag_runs/de_misc_workflow_transform_vector_backend_logs_backfill_v1/{{ run_id }}/run_dragonstone_api_autocomplete_empty_result/try_{{ task_instance.try_number }}.ipynb",
    parameters={
        "ds": "2025-08-09",
        "SOURCE_BUCKET": "prod-dse-backend-events-raw",
        "SOURCE_SPLIT": "daily",
        "SINK_BUCKET": "prod-dse-backend-events",
        "SUBPREFIX": "application-logs/dragonstone_api-autocomplete_empty_result/,application-logs/dragonstone_api_canary-autocomplete_empty_result/,application-logs/dragonstone_api_primary-autocomplete_empty_result/",
        "TABLE_NAME": "dragonstone_api_autocomplete_empty_result",
        "MAX_ROWS_PER_FILE": 250000,
        "cwd": "/usr/local/airflow/dags/repo/dags/de/misc/workflow/transform_vector_backend_logs_backfill",
    },
    env=env,
    dag=dag,
    priority_weight=1,
    retries=3,
    executor_config=get_k8s_executor_config(
        {
            "cpu": {"request": 2, "limit": 4},
            "memory": {"request": "16Gi", "limit": "30Gi"},
            "node_selector": "airflow-spot-high-mem",
            "toleration": "airflow-spot-high-mem",
        }
    ),
)
notebook_task_bag["run_dragonstone_api_autocomplete_empty_result"] = notebook_task_2


notebook_task_3 = PapermillOperator(
    task_id="run_dragonstone_api_spell_corrected_result",
    input_nb="{cwd}/notebook.ipynb".format(cwd=cwd),
    output_nb="s3://grofers-prod-dse-sgp/airflow/dag_runs/de_misc_workflow_transform_vector_backend_logs_backfill_v1/{{ run_id }}/run_dragonstone_api_spell_corrected_result/try_{{ task_instance.try_number }}.ipynb",
    parameters={
        "ds": "2025-08-09",
        "SOURCE_BUCKET": "prod-dse-backend-events-raw",
        "SOURCE_SPLIT": "daily",
        "SINK_BUCKET": "prod-dse-backend-events",
        "SUBPREFIX": "application-logs/dragonstone_api_primary-spell_corrected_result/,application-logs/dragonstone_api_canary-spell_corrected_result/,application-logs/dragonstone_api_secondary-spell_corrected_result/",
        "TABLE_NAME": "dragonstone_api_spell_corrected_result",
        "MAX_ROWS_PER_FILE": 2000000,
        "cwd": "/usr/local/airflow/dags/repo/dags/de/misc/workflow/transform_vector_backend_logs_backfill",
    },
    env=env,
    dag=dag,
    priority_weight=1,
    retries=3,
    executor_config=get_k8s_executor_config(
        {
            "cpu": {"request": 2, "limit": 4},
            "memory": {"request": "16Gi", "limit": "30Gi"},
            "node_selector": "airflow-spot-high-mem",
            "toleration": "airflow-spot-high-mem",
        }
    ),
)
notebook_task_bag["run_dragonstone_api_spell_corrected_result"] = notebook_task_3


notebook_task_4 = PapermillOperator(
    task_id="run_dragonstone_api_entity_recognition_result",
    input_nb="{cwd}/notebook.ipynb".format(cwd=cwd),
    output_nb="s3://grofers-prod-dse-sgp/airflow/dag_runs/de_misc_workflow_transform_vector_backend_logs_backfill_v1/{{ run_id }}/run_dragonstone_api_entity_recognition_result/try_{{ task_instance.try_number }}.ipynb",
    parameters={
        "ds": "2025-08-09",
        "SOURCE_BUCKET": "prod-dse-backend-events-raw",
        "SOURCE_SPLIT": "daily",
        "SINK_BUCKET": "prod-dse-backend-events",
        "SUBPREFIX": "application-logs/dragonstone_api-entity_recognition_result/,application-logs/dragonstone_api_canary-entity_recognition_result/,application-logs/dragonstone_api_primary-entity_recognition_result/,application-logs/dragonstone_api_secondary-entity_recognition_result/",
        "TABLE_NAME": "dragonstone_api_entity_recognition_result",
        "MAX_ROWS_PER_FILE": 250000,
        "cwd": "/usr/local/airflow/dags/repo/dags/de/misc/workflow/transform_vector_backend_logs_backfill",
    },
    env=env,
    dag=dag,
    priority_weight=1,
    retries=3,
    executor_config=get_k8s_executor_config(
        {
            "cpu": {"request": 2, "limit": 4},
            "memory": {"request": "64Gi", "limit": "120Gi"},
            "node_selector": "airflow-spot-ultra-high-mem",
            "toleration": "airflow-spot-ultra-high-mem",
        }
    ),
)
notebook_task_bag["run_dragonstone_api_entity_recognition_result"] = notebook_task_4


notebook_task_5 = PapermillOperator(
    task_id="run_dragonstone_api_high_confidence_query_issue",
    input_nb="{cwd}/notebook.ipynb".format(cwd=cwd),
    output_nb="s3://grofers-prod-dse-sgp/airflow/dag_runs/de_misc_workflow_transform_vector_backend_logs_backfill_v1/{{ run_id }}/run_dragonstone_api_high_confidence_query_issue/try_{{ task_instance.try_number }}.ipynb",
    parameters={
        "ds": "2025-08-09",
        "SOURCE_BUCKET": "prod-dse-backend-events-raw",
        "SOURCE_SPLIT": "daily",
        "SINK_BUCKET": "prod-dse-backend-events",
        "SUBPREFIX": "application-logs/dragonstone_api_primary-high_confidence_query_issue/,application-logs/dragonstone_api_canary-high_confidence_query_issue/,dragonstone_api_secondary-high_confidence_query_issue",
        "TABLE_NAME": "dragonstone_api_high_confidence_query_issue",
        "MAX_ROWS_PER_FILE": 1500000,
        "cwd": "/usr/local/airflow/dags/repo/dags/de/misc/workflow/transform_vector_backend_logs_backfill",
    },
    env=env,
    dag=dag,
    priority_weight=1,
    retries=3,
    executor_config=get_k8s_executor_config(
        {
            "cpu": {"request": 2, "limit": 4},
            "memory": {"request": "16Gi", "limit": "30Gi"},
            "node_selector": "airflow-spot-high-mem",
            "toleration": "airflow-spot-high-mem",
        }
    ),
)
notebook_task_bag["run_dragonstone_api_high_confidence_query_issue"] = notebook_task_5


notebook_task_6 = PapermillOperator(
    task_id="run_dragonstone_api_autosuggest_response_event",
    input_nb="{cwd}/notebook.ipynb".format(cwd=cwd),
    output_nb="s3://grofers-prod-dse-sgp/airflow/dag_runs/de_misc_workflow_transform_vector_backend_logs_backfill_v1/{{ run_id }}/run_dragonstone_api_autosuggest_response_event/try_{{ task_instance.try_number }}.ipynb",
    parameters={
        "ds": "2025-08-09",
        "SOURCE_BUCKET": "prod-dse-backend-events-raw",
        "SOURCE_SPLIT": "daily",
        "SINK_BUCKET": "prod-dse-backend-events",
        "SUBPREFIX": "application-logs/dragonstone_api_primary-autosuggest_response_event/,application-logs/dragonstone_api_canary-autosuggest_response_event/,application-logs/dragonstone_api_secondary-autosuggest_response_event/,application-logs/product_discovery_service_api-autosuggest_response_event/",
        "TABLE_NAME": "dragonstone_api_autosuggest_response_event",
        "MAX_ROWS_PER_FILE": 200000,
        "cwd": "/usr/local/airflow/dags/repo/dags/de/misc/workflow/transform_vector_backend_logs_backfill",
    },
    env=env,
    dag=dag,
    priority_weight=1,
    retries=3,
    executor_config=get_k8s_executor_config(
        {
            "cpu": {"request": 2, "limit": 4},
            "memory": {"request": "16Gi", "limit": "30Gi"},
            "node_selector": "airflow-spot-high-mem",
            "toleration": "airflow-spot-high-mem",
        }
    ),
)
notebook_task_bag["run_dragonstone_api_autosuggest_response_event"] = notebook_task_6


notebook_task_7 = PapermillOperator(
    task_id="run_track_order_eta_details",
    input_nb="{cwd}/notebook.ipynb".format(cwd=cwd),
    output_nb="s3://grofers-prod-dse-sgp/airflow/dag_runs/de_misc_workflow_transform_vector_backend_logs_backfill_v1/{{ run_id }}/run_track_order_eta_details/try_{{ task_instance.try_number }}.ipynb",
    parameters={
        "ds": "2025-08-09",
        "SOURCE_BUCKET": "prod-dse-backend-events-raw",
        "SOURCE_SPLIT": "daily",
        "SINK_BUCKET": "prod-dse-backend-events",
        "SUBPREFIX": "application-logs/track_order-track_order_eta_details/",
        "TABLE_NAME": "track_order_eta_details",
        "MAX_ROWS_PER_FILE": 2000000,
        "cwd": "/usr/local/airflow/dags/repo/dags/de/misc/workflow/transform_vector_backend_logs_backfill",
    },
    env=env,
    dag=dag,
    priority_weight=1,
    retries=3,
    executor_config=get_k8s_executor_config(
        {
            "cpu": {"request": 2, "limit": 4},
            "memory": {"request": "16Gi", "limit": "30Gi"},
            "node_selector": "airflow-spot-high-mem",
            "toleration": "airflow-spot-high-mem",
        }
    ),
)
notebook_task_bag["run_track_order_eta_details"] = notebook_task_7


notebook_task_8 = PapermillOperator(
    task_id="run_track_order_delay_components",
    input_nb="{cwd}/notebook.ipynb".format(cwd=cwd),
    output_nb="s3://grofers-prod-dse-sgp/airflow/dag_runs/de_misc_workflow_transform_vector_backend_logs_backfill_v1/{{ run_id }}/run_track_order_delay_components/try_{{ task_instance.try_number }}.ipynb",
    parameters={
        "ds": "2025-08-09",
        "SOURCE_BUCKET": "prod-dse-backend-events-raw",
        "SOURCE_SPLIT": "daily",
        "SINK_BUCKET": "prod-dse-backend-events",
        "SUBPREFIX": "application-logs/track_order-track_order_delay_components/",
        "TABLE_NAME": "track_order_delay_components",
        "MAX_ROWS_PER_FILE": 2000000,
        "cwd": "/usr/local/airflow/dags/repo/dags/de/misc/workflow/transform_vector_backend_logs_backfill",
    },
    env=env,
    dag=dag,
    priority_weight=1,
    retries=3,
    executor_config=get_k8s_executor_config(
        {
            "cpu": {"request": 2, "limit": 4},
            "memory": {"request": "16Gi", "limit": "30Gi"},
            "node_selector": "airflow-spot-high-mem",
            "toleration": "airflow-spot-high-mem",
        }
    ),
)
notebook_task_bag["run_track_order_delay_components"] = notebook_task_8


notebook_task_9 = PapermillOperator(
    task_id="run_serviceability_manpower_reopen_prediction",
    input_nb="{cwd}/notebook.ipynb".format(cwd=cwd),
    output_nb="s3://grofers-prod-dse-sgp/airflow/dag_runs/de_misc_workflow_transform_vector_backend_logs_backfill_v1/{{ run_id }}/run_serviceability_manpower_reopen_prediction/try_{{ task_instance.try_number }}.ipynb",
    parameters={
        "ds": "2025-08-09",
        "SOURCE_BUCKET": "prod-dse-backend-events-raw",
        "SOURCE_SPLIT": "daily",
        "SINK_BUCKET": "prod-dse-backend-events",
        "SUBPREFIX": "application-logs/serviceability_notebooks_celery-manpower_reopen_prediction/",
        "TABLE_NAME": "serviceability_manpower_reopen_prediction",
        "MAX_ROWS_PER_FILE": 2000000,
        "cwd": "/usr/local/airflow/dags/repo/dags/de/misc/workflow/transform_vector_backend_logs_backfill",
    },
    env=env,
    dag=dag,
    priority_weight=1,
    retries=3,
    executor_config=get_k8s_executor_config(
        {
            "cpu": {"request": 2, "limit": 4},
            "memory": {"request": "16Gi", "limit": "30Gi"},
            "node_selector": "airflow-spot-high-mem",
            "toleration": "airflow-spot-high-mem",
        }
    ),
)
notebook_task_bag["run_serviceability_manpower_reopen_prediction"] = notebook_task_9


notebook_task_10 = PapermillOperator(
    task_id="run_crm_lifeline_api_event_logs",
    input_nb="{cwd}/notebook.ipynb".format(cwd=cwd),
    output_nb="s3://grofers-prod-dse-sgp/airflow/dag_runs/de_misc_workflow_transform_vector_backend_logs_backfill_v1/{{ run_id }}/run_crm_lifeline_api_event_logs/try_{{ task_instance.try_number }}.ipynb",
    parameters={
        "ds": "2025-08-09",
        "SOURCE_BUCKET": "prod-dse-backend-events-raw",
        "SOURCE_SPLIT": "daily",
        "SINK_BUCKET": "prod-dse-backend-events",
        "SUBPREFIX": "application-logs/crm_api-lifeline_api_event_logs/",
        "TABLE_NAME": "crm_lifeline_api_event_logs",
        "MAX_ROWS_PER_FILE": 100000,
        "cwd": "/usr/local/airflow/dags/repo/dags/de/misc/workflow/transform_vector_backend_logs_backfill",
    },
    env=env,
    dag=dag,
    priority_weight=1,
    retries=3,
    executor_config=get_k8s_executor_config(
        {
            "cpu": {"request": 2, "limit": 4},
            "memory": {"request": "16Gi", "limit": "30Gi"},
            "node_selector": "airflow-spot-high-mem",
            "toleration": "airflow-spot-high-mem",
        }
    ),
)
notebook_task_bag["run_crm_lifeline_api_event_logs"] = notebook_task_10


notebook_task_11 = PapermillOperator(
    task_id="run_crm_api_bulk_upload_event_logs",
    input_nb="{cwd}/notebook.ipynb".format(cwd=cwd),
    output_nb="s3://grofers-prod-dse-sgp/airflow/dag_runs/de_misc_workflow_transform_vector_backend_logs_backfill_v1/{{ run_id }}/run_crm_api_bulk_upload_event_logs/try_{{ task_instance.try_number }}.ipynb",
    parameters={
        "ds": "2025-08-09",
        "SOURCE_BUCKET": "prod-dse-backend-events-raw",
        "SOURCE_SPLIT": "daily",
        "SINK_BUCKET": "prod-dse-backend-events",
        "SUBPREFIX": "application-logs/crm_api_celery_bulk-/",
        "TABLE_NAME": "crm_api_bulk_upload_event_logs",
        "MAX_ROWS_PER_FILE": 2000000,
        "cwd": "/usr/local/airflow/dags/repo/dags/de/misc/workflow/transform_vector_backend_logs_backfill",
    },
    env=env,
    dag=dag,
    priority_weight=1,
    retries=3,
    executor_config=get_k8s_executor_config(
        {
            "cpu": {"request": 2, "limit": 4},
            "memory": {"request": "16Gi", "limit": "30Gi"},
            "node_selector": "airflow-spot-high-mem",
            "toleration": "airflow-spot-high-mem",
        }
    ),
)
notebook_task_bag["run_crm_api_bulk_upload_event_logs"] = notebook_task_11


notebook_task_12 = PapermillOperator(
    task_id="run_merchant_visibility_info",
    input_nb="{cwd}/notebook.ipynb".format(cwd=cwd),
    output_nb="s3://grofers-prod-dse-sgp/airflow/dag_runs/de_misc_workflow_transform_vector_backend_logs_backfill_v1/{{ run_id }}/run_merchant_visibility_info/try_{{ task_instance.try_number }}.ipynb",
    parameters={
        "ds": "2025-08-09",
        "SOURCE_BUCKET": "prod-dse-backend-events-raw",
        "SOURCE_SPLIT": "daily",
        "SINK_BUCKET": "prod-dse-backend-events",
        "SUBPREFIX": "application-logs/merchant_visibility-merchant_visibility_info/",
        "TABLE_NAME": "merchant_visibility_info",
        "MAX_ROWS_PER_FILE": 2000000,
        "cwd": "/usr/local/airflow/dags/repo/dags/de/misc/workflow/transform_vector_backend_logs_backfill",
    },
    env=env,
    dag=dag,
    priority_weight=1,
    retries=3,
    executor_config=get_k8s_executor_config(
        {
            "cpu": {"request": 2, "limit": 4},
            "memory": {"request": "16Gi", "limit": "30Gi"},
            "node_selector": "airflow-spot-high-mem",
            "toleration": "airflow-spot-high-mem",
        }
    ),
)
notebook_task_bag["run_merchant_visibility_info"] = notebook_task_12


notebook_task_13 = PapermillOperator(
    task_id="run_cart_checkout",
    input_nb="{cwd}/notebook.ipynb".format(cwd=cwd),
    output_nb="s3://grofers-prod-dse-sgp/airflow/dag_runs/de_misc_workflow_transform_vector_backend_logs_backfill_v1/{{ run_id }}/run_cart_checkout/try_{{ task_instance.try_number }}.ipynb",
    parameters={
        "ds": "2025-08-09",
        "SOURCE_BUCKET": "prod-dse-backend-events-raw",
        "SOURCE_SPLIT": "daily",
        "SINK_BUCKET": "prod-dse-backend-events",
        "SUBPREFIX": "application-logs/checkout_service_canary-cart_checkout/,application-logs/checkout_service_primary-cart_checkout/",
        "TABLE_NAME": "cart_checkout",
        "MAX_ROWS_PER_FILE": 5000000,
        "cwd": "/usr/local/airflow/dags/repo/dags/de/misc/workflow/transform_vector_backend_logs_backfill",
    },
    env=env,
    dag=dag,
    priority_weight=1,
    retries=3,
    executor_config=get_k8s_executor_config(
        {
            "cpu": {"request": 2, "limit": 4},
            "memory": {"request": "16Gi", "limit": "30Gi"},
            "node_selector": "airflow-spot-high-mem",
            "toleration": "airflow-spot-high-mem",
        }
    ),
)
notebook_task_bag["run_cart_checkout"] = notebook_task_13


notebook_task_14 = PapermillOperator(
    task_id="run_printing_forecast",
    input_nb="{cwd}/notebook.ipynb".format(cwd=cwd),
    output_nb="s3://grofers-prod-dse-sgp/airflow/dag_runs/de_misc_workflow_transform_vector_backend_logs_backfill_v1/{{ run_id }}/run_printing_forecast/try_{{ task_instance.try_number }}.ipynb",
    parameters={
        "ds": "2025-08-09",
        "SOURCE_BUCKET": "prod-dse-backend-events-raw",
        "SOURCE_SPLIT": "daily",
        "SINK_BUCKET": "prod-dse-backend-events",
        "SUBPREFIX": "application-logs/print_engine-printing_forecast/,application-logs/printing_service_api-printing_forecast/",
        "TABLE_NAME": "printing_forecast",
        "MAX_ROWS_PER_FILE": 100000,
        "cwd": "/usr/local/airflow/dags/repo/dags/de/misc/workflow/transform_vector_backend_logs_backfill",
    },
    env=env,
    dag=dag,
    priority_weight=1,
    retries=3,
    executor_config=get_k8s_executor_config(
        {
            "cpu": {"request": 2, "limit": 4},
            "memory": {"request": "16Gi", "limit": "30Gi"},
            "node_selector": "airflow-spot-high-mem",
            "toleration": "airflow-spot-high-mem",
        }
    ),
)
notebook_task_bag["run_printing_forecast"] = notebook_task_14


branch_out_1 = DummyOperator(
    task_id="branch_out_1", dag=dag, executor_config=get_k8s_executor_config()
)
notebook_task_bag["branch_out_1"] = branch_out_1


join_1 = DummyOperator(task_id="join_1", dag=dag, executor_config=get_k8s_executor_config())
notebook_task_bag["join_1"] = join_1


branch_out_1 >> notebook_task_bag["run_dragonstone_api_intent_empty_result"]
notebook_task_bag["run_dragonstone_api_intent_empty_result"] >> join_1

branch_out_1 >> notebook_task_bag["run_dragonstone_api_autocomplete_empty_result"]
notebook_task_bag["run_dragonstone_api_autocomplete_empty_result"] >> join_1

branch_out_1 >> notebook_task_bag["run_dragonstone_api_spell_corrected_result"]
notebook_task_bag["run_dragonstone_api_spell_corrected_result"] >> join_1

branch_out_1 >> notebook_task_bag["run_dragonstone_api_entity_recognition_result"]
notebook_task_bag["run_dragonstone_api_entity_recognition_result"] >> join_1

branch_out_1 >> notebook_task_bag["run_dragonstone_api_high_confidence_query_issue"]
notebook_task_bag["run_dragonstone_api_high_confidence_query_issue"] >> join_1

branch_out_1 >> notebook_task_bag["run_dragonstone_api_autosuggest_response_event"]
notebook_task_bag["run_dragonstone_api_autosuggest_response_event"] >> join_1

branch_out_1 >> notebook_task_bag["run_track_order_eta_details"]
notebook_task_bag["run_track_order_eta_details"] >> join_1

branch_out_1 >> notebook_task_bag["run_track_order_delay_components"]
notebook_task_bag["run_track_order_delay_components"] >> join_1

branch_out_1 >> notebook_task_bag["run_serviceability_manpower_reopen_prediction"]
notebook_task_bag["run_serviceability_manpower_reopen_prediction"] >> join_1

branch_out_1 >> notebook_task_bag["run_crm_lifeline_api_event_logs"]
notebook_task_bag["run_crm_lifeline_api_event_logs"] >> join_1

branch_out_1 >> notebook_task_bag["run_crm_api_bulk_upload_event_logs"]
notebook_task_bag["run_crm_api_bulk_upload_event_logs"] >> join_1

branch_out_1 >> notebook_task_bag["run_merchant_visibility_info"]
notebook_task_bag["run_merchant_visibility_info"] >> join_1

branch_out_1 >> notebook_task_bag["run_cart_checkout"]
notebook_task_bag["run_cart_checkout"] >> join_1

branch_out_1 >> notebook_task_bag["run_printing_forecast"]
notebook_task_bag["run_printing_forecast"] >> join_1


if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
