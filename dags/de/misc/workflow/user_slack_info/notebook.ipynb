{"cells": [{"cell_type": "code", "execution_count": null, "id": "53b33fc9-5b61-4486-aa4b-a8c379028fe0", "metadata": {}, "outputs": [], "source": ["import pencilbox as pb\n", "import pandas as pd\n", "import time"]}, {"cell_type": "code", "execution_count": null, "id": "1a0487e7-fa46-478f-8c87-5ea212b43a3f", "metadata": {}, "outputs": [], "source": ["slack_client = pb.connections.get_slack_client(\"bl-analytics-bot\")"]}, {"cell_type": "code", "execution_count": null, "id": "16705678-5374-446e-b340-fd7a3a31d400", "metadata": {}, "outputs": [], "source": ["def fetch_all_users(slack_client):\n", "    \"\"\"\n", "    Fetch all users from the Slack workspace using the Slack client.\n", "\n", "    Args:\n", "        slack_client: Initialized Slack WebClient instance.\n", "\n", "    Returns:\n", "        List of user dictionaries with user information.\n", "    \"\"\"\n", "    try:\n", "        users = []\n", "        next_cursor = None\n", "\n", "        while True:\n", "            # Call the users.list endpoint with pagination support\n", "            response = slack_client.users_list(cursor=next_cursor)\n", "\n", "            if not response[\"ok\"]:\n", "                raise Exception(f\"Slack API error: {response['error']}\")\n", "\n", "            users.extend(response[\"members\"])\n", "            print(f\"{len(users)} users fetched\")\n", "\n", "            # Get the next cursor, if available\n", "            next_cursor = response.get(\"response_metadata\", {}).get(\"next_cursor\")\n", "\n", "            # Break if there's no next page\n", "            if not next_cursor:\n", "                break\n", "\n", "        return users\n", "\n", "    except Exception as e:\n", "        print(f\"Error fetching users: {str(e)}\")\n", "        raise"]}, {"cell_type": "code", "execution_count": null, "id": "92f25c99-8d3e-4c47-9a02-6932d849ae46", "metadata": {}, "outputs": [], "source": ["# Fetch all users\n", "try:\n", "    all_users = fetch_all_users(slack_client)\n", "    print(\"Total Members:\", len(all_users))\n", "except Exception as e:\n", "    print(f\"Failed to fetch users: {str(e)}\")"]}, {"cell_type": "code", "execution_count": null, "id": "aa06ac8f-040e-4312-aeb9-180b6b211e5e", "metadata": {}, "outputs": [], "source": ["# Convert JSON to DataFrame\n", "df = pd.json_normalize(all_users)"]}, {"cell_type": "code", "execution_count": null, "id": "d9426c41-93d4-49f0-8342-4bc5b5291110", "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e67fd761-d118-4a30-88c3-fe43ca7e3c02", "metadata": {}, "outputs": [], "source": ["# Select required fields\n", "df_selected = df[\n", "    [\n", "        \"id\",\n", "        \"profile.email\",\n", "        \"profile.first_name\",\n", "        \"profile.last_name\",\n", "        \"deleted\",\n", "        \"is_bot\",\n", "        \"enterprise_user.id\",\n", "        \"profile.display_name\",\n", "    ]\n", "]\n", "\n", "# Rename columns\n", "df_selected.rename(\n", "    columns={\n", "        \"profile.first_name\": \"first_name\",\n", "        \"profile.last_name\": \"last_name\",\n", "        \"profile.email\": \"email\",\n", "        \"enterprise_user.id\": \"enterprise_user_id\",\n", "        \"profile.display_name\": \"profile_display_name\",\n", "    },\n", "    inplace=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e2704ff0-3b01-4778-ba8a-0447e5dbead5", "metadata": {}, "outputs": [], "source": ["df_selected.head()"]}, {"cell_type": "code", "execution_count": null, "id": "984584d7-721f-4a2d-a3af-8ef6ad40feef", "metadata": {}, "outputs": [], "source": ["kwargs = {\n", "    \"schema_name\": \"de_etls\",\n", "    \"table_name\": \"user_slack_info\",\n", "    \"column_dtypes\": [\n", "        {\"name\": \"id\", \"type\": \"VARCHAR\", \"description\": \"Slack ID\"},\n", "        {\"name\": \"email\", \"type\": \"VARCHAR\", \"description\": \"Email of user\"},\n", "        {\"name\": \"first_name\", \"type\": \"VARCHAR\", \"description\": \"First name of user\"},\n", "        {\"name\": \"last_name\", \"type\": \"VARCHAR\", \"description\": \"Last name of user\"},\n", "        {\"name\": \"deleted\", \"type\": \"BOOLEAN\", \"description\": \"If user exist or not\"},\n", "        {\"name\": \"is_bot\", \"type\": \"BOOLEAN\", \"description\": \"If user is bot or not\"},\n", "        {\"name\": \"enterprise_user_id\", \"type\": \"VARCHAR\", \"description\": \"Slack Enterprise ID\"},\n", "        {\"name\": \"profile_display_name\", \"type\": \"VARCHAR\", \"description\": \"Profile Display name\"},\n", "    ],\n", "    \"primary_key\": [\"id\"],\n", "    \"load_type\": \"truncate\",\n", "    \"table_description\": \"Basic slack info of eternal users\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "4425e3eb-0a72-4c5f-8f4f-a365df27e520", "metadata": {}, "outputs": [], "source": ["pb.to_trino(data_obj=df_selected, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "870a9ff1-fb34-4a25-9b1a-dd2b1bdf2da7", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 5}