checkpoint_table_schema = "de_etls"
flat_table_schema = "de_etls"
checkpoint_table_name = "flywheel_product_catalog_events_checkpoint"
merchant_product_mapping_flat_table_name = "merchant_product_mapping"
invalid_event_logging_table_name = "flywheel_invalid_events_log"
pricing_details_lake_table = "pricing_v3.pricing_domain_prices"
merchant_outlet_facility_mapping_table = "dwh.dim_merchant_outlet_facility_mapping"
dwh_dim_product = "dwh.dim_product"
product_details_flat_table_name = "product_details"
checkpoint_update_sql = "SELECT '{}' AS event_name, '{}' AS latest_checkpoint_ts"

def generate_checkpoint_update_sql(event_names, checkpoint_ts):
    values_clause = ",\n    ".join(
        f"('{event}', '{checkpoint_ts}')" for event in event_names
    )
    return f"""
SELECT * FROM (
    VALUES
    {values_clause}
) AS t(event_name, latest_checkpoint_ts)
"""


checkpoint_table_kwargs = {
    "schema_name": checkpoint_table_schema,
    "table_name": checkpoint_table_name,
    "column_dtypes": [
        {
            "name": "event_name",
            "type": "VARCHAR",
            "description": "event name which checkpoint time is being captured",
        },
        {
            "name": "latest_checkpoint_ts",
            "type": "VARCHAR",
            "description": "Latest checkpoint timestamp (UTC)",
        },
    ],
    "primary_key": ["event_name"],
    "load_type": "upsert",
    "table_description": "This table stores the time till when data has been processed for each of the inventory/price/product update events (specifically for the flywheel product catalog events job)",
}

merchant_product_mapping_flat_table_ts_columns = [
    "pricing_update_ts",
    "inventory_update_ts",
    "mapping_disable_ts"
]

merchant_product_mapping_flat_table_kwargs = {
    "schema_name": flat_table_schema,
    "table_name": merchant_product_mapping_flat_table_name,
    "column_dtypes": [
        {
            "name": "product_id",
            "type": "BIGINT",
            "description": "product id",
        },
        {
            "name": "merchant_id",
            "type": "BIGINT",
            "description": "merchant id",
        },
        {
            "name": "mrp_price",
            "type": "DOUBLE",
            "description": "mrp",
        },
        {
            "name": "price",
            "type": "DOUBLE",
            "description": "price",
        },
        {
            "name": "availability",
            "type": "VARCHAR",
            "description": "availability (IN_STOCK / OUT_OF_STOCK)",
        },
        {
            "name": "enabled_flag",
            "type": "BOOLEAN",
            "description": "product merchant mapping enabled flag",
        },
        {
            "name": "pricing_update_ts",
            "type": "TIMESTAMP(6)",
            "description": "last time price/mrp was updated (UTC)",
        },
        {
            "name": "inventory_update_ts",
            "type": "TIMESTAMP(6)",
            "description": "last time availability was updated (UTC)",
        },
        {
            "name": "mapping_disable_ts",
            "type": "TIMESTAMP(6)",
            "description": "last time product merchant mapping was disabled (UTC)",
        },
        {
            "name": "tenant",
            "type": "VARCHAR",
            "description": "blinkit or bistro product identifier",
        },
        {
            "name": "tags",
            "type": "VARCHAR",
            "description": "JSON string containing array of product tags. Each tag object has id, name, type, and properties fields.",
        }
    ],
    "primary_key": ["product_id", "merchant_id", "tenant"],
    "load_type": "upsert",
    "table_description": "This flat table stores product x merchant level data on the latest price, availability and CMS mapping.",
    "run_maintenance": False
}

product_details_flat_table_kwargs = {
    "schema_name": flat_table_schema,
    "table_name": product_details_flat_table_name,
    "column_dtypes": [
        {"name": "product_id", "type": "BIGINT", "description": "product id"},
        {"name": "brand_id", "type": "BIGINT", "description": "brand id"},
        {"name": "frame_image_link", "type": "VARCHAR", "description": "product frame image link"},
        {"name": "l1_category_id", "type": "BIGINT", "description": "level 1 category id"},
        {"name": "image_link", "type": "VARCHAR", "description": "product image link"},
        {"name": "description", "type": "VARCHAR", "description": "product description"},
        {"name": "l2_category_id", "type": "BIGINT", "description": "level 2 category id"},
        {"name": "l0_category", "type": "VARCHAR", "description": "level 0 category name"},
        {"name": "l0_category_id", "type": "BIGINT", "description": "level 0 category id"},
        {"name": "l2_category", "type": "VARCHAR", "description": "level 2 category name"},
        {"name": "product_name", "type": "VARCHAR", "description": "product name"},
        {"name": "brand_name", "type": "VARCHAR", "description": "brand name"},
        {"name": "l1_category", "type": "VARCHAR", "description": "level 1 category name"}
    ],
    "primary_key": ["product_id"],
    "load_type": "upsert",
    "table_description": "This flat table stores product level data",
}


product_logging_flat_table_kwargs = {
    "schema_name": flat_table_schema,
    "table_name": invalid_event_logging_table_name,
    "column_dtypes": [
        {"name": "event_type", "type": "VARCHAR", "description": "type of event like inventory update or pricing update"},
        {"name": "product_id", "type": "BIGINT", "description": "product id"},
        {"name": "merchant_id", "type": "BIGINT", "description": "merchant id"},
        {"name": "enabled_flag", "type": "BOOLEAN", "description": "flag indicating if product is enabled"},
        {"name": "mapping_disable_ts", "type": "TIMESTAMP(6)", "description": "timestamp when mapping was disabled"},
        {"name": "price", "type": "DOUBLE", "description": "current price"},
        {"name": "mrp_price", "type": "DOUBLE", "description": "maximum retail price"},
        {"name": "pricing_update_ts", "type": "TIMESTAMP(6)", "description": "timestamp of last pricing update"},
        {"name": "availability", "type": "VARCHAR", "description": "product availability status"},
        {"name": "inventory_update_ts", "type": "TIMESTAMP(6)", "description": "timestamp of last inventory update"},
        {"name": "brand_id", "type": "BIGINT", "description": "brand id"},
        {"name": "product_name", "type": "VARCHAR", "description": "product name"},
        {"name": "description", "type": "VARCHAR", "description": "product description"},
        {"name": "brand_name", "type": "VARCHAR", "description": "brand name"},
        {"name": "image_link", "type": "VARCHAR", "description": "product image link"},
        {"name": "frame_image_link", "type": "VARCHAR", "description": "frame image link for product"},
        {"name": "categories", "type": "VARCHAR", "description": "product categories"},
        {"name": "destination_link", "type": "VARCHAR", "description": "URL to product page"},
        {"name": "tenant", "type": "VARCHAR", "description": "tenant identifier"},
        {"name": "dt", "type": "VARCHAR", "description": "partition date"},
        {"name": "new_checkpoint_ts", "type": "VARCHAR", "description": "checkpoint timestamp for event ingestion"},
    ],
    "partition_key": ["dt"],
    "load_type": "append",  # or "upsert" if using primary key deduplication
    "table_description": "This flat table stores logging data for product, pricing, and inventory updates",
}



#keep this at very bottom
table_kwargs_mapping = {
    checkpoint_table_name:checkpoint_table_kwargs,
    merchant_product_mapping_flat_table_name: merchant_product_mapping_flat_table_kwargs,
    product_details_flat_table_name: product_details_flat_table_kwargs,
    invalid_event_logging_table_name: product_logging_flat_table_kwargs
}
