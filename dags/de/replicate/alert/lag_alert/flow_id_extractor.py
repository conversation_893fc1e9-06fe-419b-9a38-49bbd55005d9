#!/usr/bin/env python3
"""
Flow ID Data Extractor

This script connects to the database and extracts data for a specific flow_id from the 's3_hudi_flow_logs' table.
It supports conversion between UTC and IST timestamps and handles both ISO and custom numeric timestamp formats.
"""

import psycopg2
import pandas as pd
import pencilbox as pb
from datetime import datetime
import pytz
from typing import Dict, Optional


def get_db_credentials() -> Dict[str, str]:
    """
    Get database credentials from Python-Bigfoot secrets
    """
    try:
        database = pb.get_secret(
            "data/services/source-replication/alert/nessie_db"
        )
        return {
            "host": database["host"],
            "database": database["database"],
            "user": database["user"],
            "password": database["password"],
            "port": database.get("port", 5432)
        }
    except Exception as e:
        print(f"Error getting database credentials: {str(e)}")
        return {}


def convert_utc_to_ist(utc_timestamp: Optional[datetime]) -> Optional[datetime]:
    """
    Convert UTC timestamp to IST (Indian Standard Time)
    """
    if utc_timestamp is None:
        return None

    utc_timezone = pytz.timezone('UTC')
    ist_timezone = pytz.timezone('Asia/Kolkata')

    if utc_timestamp.tzinfo is None:
        utc_dt = utc_timezone.localize(utc_timestamp)
    else:
        utc_dt = utc_timestamp

    return utc_dt.astimezone(ist_timezone)


def parse_numeric_timestamp(ts: str) -> Optional[datetime]:
    """
    Parse custom numeric string timestamp like '20250805100837608' to datetime
    """
    if not isinstance(ts, str) or len(ts) < 14:
        return None
    try:
        return datetime.strptime(ts[:14], "%Y%m%d%H%M%S")
    except Exception:
        return None


def get_flow_data(flow_id: str) -> pd.DataFrame:
    """
    Get data for a specific flow_id from the s3_hudi_flow_logs table
    """
    db_credentials = get_db_credentials()
    if not db_credentials:
        print("Failed to get database credentials")
        return pd.DataFrame()

    conn = None
    try:
        conn = psycopg2.connect(**db_credentials)
        with conn.cursor() as cursor:
            query = "SELECT * FROM s3_hudi_flow_logs WHERE flow_id = %s"
            cursor.execute(query, (flow_id,))
            columns = [desc[0] for desc in cursor.description]
            rows = cursor.fetchall()
            df = pd.DataFrame(rows, columns=columns)

            timestamp_columns = [
                'last_successful_timestamp',
                'last_hudi_commit_timestamp',
                'max_cdc_timestamp_read'
            ]

            for col in timestamp_columns:
                if col in df.columns and not df[col].empty:
                    if df[col].dtype == 'object':
                        if col == 'last_hudi_commit_timestamp':
                            df[col] = df[col].apply(lambda x: parse_numeric_timestamp(str(x)))
                        else:
                            df[col] = pd.to_datetime(df[col], errors='coerce')
                    df[f"{col}_ist"] = df[col].apply(convert_utc_to_ist)

            return df

    except Exception as e:
        print(f"Database error: {str(e)}")
        return pd.DataFrame()

    finally:
        if conn:
            conn.close()
