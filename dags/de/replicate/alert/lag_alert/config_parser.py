#!/usr/bin/env python3
"""
Config <PERSON><PERSON><PERSON>

This script parses configuration files to extract information about tables, cron schedules, 
start dates, end dates, and flow IDs.
"""

import os
import yaml
import pandas as pd
from typing import List, Dict, Any

def find_config_files(directory: str) -> List[str]:
    """
    Find all config.yml files recursively in a directory
    Args:
        directory (str): Directory to search for config files
    Returns:
        List[str]: List of file paths to config files
    """
    config_files = []

    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.yml'):
                config_files.append(os.path.join(root, file))

    return config_files

def parse_config_file(file_path: str) -> List[Dict[str, Any]]:
    """
    Parse a config file and extract table information
    Args:
        file_path (str): Path to the config file
    Returns:
        List[Dict[str, Any]]: List of dictionaries with table information
    """
    try:
        with open(file_path, 'r') as file:
            config = yaml.safe_load(file)
        # Extract schedule information
        schedule = config.get('schedule', {})
        cron = schedule.get('interval')
        start_date = schedule.get('start_date')
        end_date = schedule.get('end_date')
        # Extract tables information
        tables = config.get('tables', [])
        
        result = []
        for table in tables:
            flow_id = table.get('flow_id')
            topic_name = table.get('topic_name')
            source = table.get('source', {})
            table_name = source.get('table')
            database = source.get('database')
            
            
            if table_name and flow_id:
                result.append({
                    'table_name': table_name,
                    'database': database,
                    'flow_id': flow_id,
                    'cron': cron,
                    'start_date': start_date,
                    'end_date': end_date,
                    'topic_name':topic_name,
                    'config_file': os.path.basename(file_path)
                })
        
        return result
    except Exception as e:
        print(f"Error parsing config file {file_path}: {str(e)}")
        return []

def get_config_data_df(config_dir: str) -> pd.DataFrame:
    """
    Parse all config files and return a DataFrame with the extracted information
    
    Args:
        config_dir (str): Directory containing config files
        
    Returns:
        pd.DataFrame: DataFrame with table information
    """
    config_files = find_config_files(config_dir)
    
    all_tables = []
    for file_path in config_files:
        tables = parse_config_file(file_path)
        all_tables.extend(tables)
    
    df = pd.DataFrame(all_tables)
    
    # Ensure all required columns exist, even if empty
    required_columns = ['table_name', 'database', 'flow_id', 'cron', 'start_date', 'end_date', 'config_file']
    for col in required_columns:
        if col not in df.columns:
            df[col] = None
    
    return df
