release_label: emr-6.11.1

applications:
- Name: Spark
- Name: Hadoop

launch_specifications:
  on_demand_specification:
    AllocationStrategy: LOWEST_PRICE
  spot_specification:
    AllocationStrategy: PRICE_CAPACITY_OPTIMIZED
    TimeoutAction: SWITCH_TO_ON_DEMAND
    TimeoutDurationMinutes: 5

cluster_types:
  - type: medium
    emr_configs:
    - step_concurrency_level: 18
      instance_fleets:
      - name: MasterFleet
        instance_fleet_type: MASTER
        target_on_demand_capacity: 1
        instance_type_configs:
        - instance_type: c6g.2xlarge
          ebs_volume_size_gb: 32
          volume_type: gp3
        - instance_type: c7g.2xlarge
          ebs_volume_size_gb: 32
          volume_type: gp3
        - instance_type: c5a.2xlarge
          ebs_volume_size_gb: 32
          volume_type: gp3
        - instance_type: m6g.2xlarge
          ebs_volume_size_gb: 32
          volume_type: gp3
        - instance_type: c5.2xlarge
          ebs_volume_size_gb: 32
          volume_type: gp3
      - name: <PERSON>Fleet
        instance_fleet_type: CORE
        target_spot_capacity: 2
        instance_type_configs:
        - instance_type: r5b.2xlarge
          ebs_volume_size_gb: 200
          volume_type: gp3
        - instance_type: r5n.2xlarge
          ebs_volume_size_gb: 200
          volume_type: gp3
        - instance_type: r5ad.2xlarge
          ebs_volume_size_gb: 0
        - instance_type: r5dn.2xlarge
          ebs_volume_size_gb: 0
        - instance_type: r6idn.2xlarge
          ebs_volume_size_gb: 0
      - name: TaskFleet
        instance_fleet_type: TASK
        target_spot_capacity: 8
        instance_type_configs:
        - instance_type: r5b.2xlarge
          ebs_volume_size_gb: 200
          volume_type: gp3
        - instance_type: r5n.2xlarge
          ebs_volume_size_gb: 200
          volume_type: gp3
        - instance_type: r5ad.2xlarge
          ebs_volume_size_gb: 0
        - instance_type: r5dn.2xlarge
          ebs_volume_size_gb: 0
        - instance_type: r6idn.2xlarge
          ebs_volume_size_gb: 0
  - type: large
    emr_configs:
    - step_concurrency_level: 4
      instance_fleets:
      - name: MasterFleet
        instance_fleet_type: MASTER
        target_on_demand_capacity: 1
        instance_type_configs:
        - instance_type: c6g.2xlarge
          ebs_volume_size_gb: 32
          volume_type: gp3
        - instance_type: c7g.2xlarge
          ebs_volume_size_gb: 32
          volume_type: gp3
        - instance_type: c5a.2xlarge
          ebs_volume_size_gb: 32
          volume_type: gp3
        - instance_type: m6g.2xlarge
          ebs_volume_size_gb: 32
          volume_type: gp3
        - instance_type: c5.2xlarge
          ebs_volume_size_gb: 32
          volume_type: gp3
      - name: CoreFleet
        instance_fleet_type: CORE
        target_spot_capacity: 1
        instance_type_configs:
        - instance_type: r5a.4xlarge
          ebs_volume_size_gb: 200
          volume_type: gp3
        - instance_type: r5b.4xlarge
          ebs_volume_size_gb: 200
          volume_type: gp3
        - instance_type: m5a.8xlarge
          ebs_volume_size_gb: 200
          volume_type: gp3
        - instance_type: r6idn.4xlarge
          ebs_volume_size_gb: 0
      - name: TaskFleet
        instance_fleet_type: TASK
        target_spot_capacity: 2
        instance_type_configs:
        - instance_type: r5a.4xlarge
          ebs_volume_size_gb: 200
          volume_type: gp3
        - instance_type: m6gd.8xlarge
          ebs_volume_size_gb: 0
        - instance_type: r5b.4xlarge
          ebs_volume_size_gb: 200
          volume_type: gp3
        - instance_type: m5a.8xlarge
          ebs_volume_size_gb: 200
          volume_type: gp3
        - instance_type: r6idn.4xlarge
          ebs_volume_size_gb: 0
    - step_concurrency_level: 4
      instance_fleets:
      - name: MasterFleet
        instance_fleet_type: MASTER
        target_on_demand_capacity: 1
        instance_type_configs:
        - instance_type: c6g.2xlarge
          ebs_volume_size_gb: 32
          volume_type: gp3
        - instance_type: c7g.2xlarge
          ebs_volume_size_gb: 32
          volume_type: gp3
        - instance_type: c5a.2xlarge
          ebs_volume_size_gb: 32
          volume_type: gp3
        - instance_type: m6g.2xlarge
          ebs_volume_size_gb: 32
          volume_type: gp3
        - instance_type: c5.2xlarge
          ebs_volume_size_gb: 32
          volume_type: gp3
      - name: CoreFleet
        instance_fleet_type: CORE
        target_spot_capacity: 2
        instance_type_configs:
        - instance_type: r5b.2xlarge
          ebs_volume_size_gb: 200
          volume_type: gp3
        - instance_type: r5n.2xlarge
          ebs_volume_size_gb: 200
          volume_type: gp3
        - instance_type: r5ad.2xlarge
          ebs_volume_size_gb: 0
        - instance_type: r5dn.2xlarge
          ebs_volume_size_gb: 0
        - instance_type: r6idn.2xlarge
          ebs_volume_size_gb: 0
      - name: TaskFleet
        instance_fleet_type: TASK
        target_spot_capacity: 4
        instance_type_configs:
        - instance_type: r5b.2xlarge
          ebs_volume_size_gb: 200
          volume_type: gp3
        - instance_type: r5n.2xlarge
          ebs_volume_size_gb: 200
          volume_type: gp3
        - instance_type: r5ad.2xlarge
          ebs_volume_size_gb: 0
        - instance_type: r5dn.2xlarge
          ebs_volume_size_gb: 0
        - instance_type: r6idn.2xlarge
          ebs_volume_size_gb: 0
