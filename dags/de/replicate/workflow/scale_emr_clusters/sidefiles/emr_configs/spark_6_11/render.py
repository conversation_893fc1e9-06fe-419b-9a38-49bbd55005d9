EMR_CLUSTER_CONFIG = {
    "medium": [
        {
            # "Name": "emr-cluster-identifier", set at the time of cluster launch
            "LogUri": "s3://grofers-prod-dse-sgp/elasticmapreduce/",
            "ReleaseLabel": "emr-6.11.1",
            "Applications": [{"Name": "Spark"}, {"Name": "Hadoop"}],
            "StepConcurrencyLevel": 18,
            "Instances": {
                "KeepJobFlowAliveWhenNoSteps": True,
                "Ec2KeyName": "dse-emr",
                "ServiceAccessSecurityGroup": "sg-64858200",
                "Ec2SubnetIds": [
                    "subnet-09e5bab68a3640d38",
                    "subnet-fe54ac88",
                    "subnet-e00fc184",
                ],
                "EmrManagedSlaveSecurityGroup": "sg-587afc3c",
                "EmrManagedMasterSecurityGroup": "sg-587afc3c",
                "InstanceFleets": [
                    {
                        "Name": "MasterFleet",
                        "InstanceFleetType": "MASTER",
                        "TargetOnDemandCapacity": 1,
                        "InstanceTypeConfigs": [
                            {
                                "InstanceType": "c6g.2xlarge",
                                "EbsConfiguration": {
                                    "EbsBlockDeviceConfigs": [
                                        {
                                            "VolumeSpecification": {
                                                "SizeInGB": 32,
                                                "VolumeType": "gp3",
                                                "Iops": 3000,
                                            },
                                        }
                                    ],
                                },
                            },
                            {
                                "InstanceType": "c7g.2xlarge",
                                "EbsConfiguration": {
                                    "EbsBlockDeviceConfigs": [
                                        {
                                            "VolumeSpecification": {
                                                "SizeInGB": 32,
                                                "VolumeType": "gp3",
                                                "Iops": 3000,
                                            },
                                        }
                                    ],
                                },
                            },
                            {
                                "InstanceType": "c5a.2xlarge",
                                "EbsConfiguration": {
                                    "EbsBlockDeviceConfigs": [
                                        {
                                            "VolumeSpecification": {
                                                "SizeInGB": 32,
                                                "VolumeType": "gp3",
                                                "Iops": 3000,
                                            },
                                        }
                                    ],
                                },
                            },
                            {
                                "InstanceType": "m6g.2xlarge",
                                "EbsConfiguration": {
                                    "EbsBlockDeviceConfigs": [
                                        {
                                            "VolumeSpecification": {
                                                "SizeInGB": 32,
                                                "VolumeType": "gp3",
                                                "Iops": 3000,
                                            },
                                        }
                                    ],
                                },
                            },
                            {
                                "InstanceType": "c5.2xlarge",
                                "EbsConfiguration": {
                                    "EbsBlockDeviceConfigs": [
                                        {
                                            "VolumeSpecification": {
                                                "SizeInGB": 32,
                                                "VolumeType": "gp3",
                                                "Iops": 3000,
                                            },
                                        }
                                    ],
                                },
                            },
                        ],
                        "LaunchSpecifications": {
                            "OnDemandSpecification": {
                                "AllocationStrategy": "LOWEST_PRICE"
                            },
                        },
                    },
                    {
                        "Name": "CoreFleet",
                        "InstanceFleetType": "CORE",
                        "TargetSpotCapacity": 2,
                        "InstanceTypeConfigs": [
                            {
                                "InstanceType": "r5b.2xlarge",
                                "EbsConfiguration": {
                                    "EbsBlockDeviceConfigs": [
                                        {
                                            "VolumeSpecification": {
                                                "SizeInGB": 200,
                                                "VolumeType": "gp3",
                                                "Iops": 3000,
                                            },
                                        }
                                    ],
                                },
                            },
                            {
                                "InstanceType": "r5n.2xlarge",
                                "EbsConfiguration": {
                                    "EbsBlockDeviceConfigs": [
                                        {
                                            "VolumeSpecification": {
                                                "SizeInGB": 200,
                                                "VolumeType": "gp3",
                                                "Iops": 3000,
                                            },
                                        }
                                    ],
                                },
                            },
                            {
                                "InstanceType": "r5ad.2xlarge",
                                "EbsConfiguration": {},
                            },
                            {
                                "InstanceType": "r5dn.2xlarge",
                                "EbsConfiguration": {},
                            },
                            {
                                "InstanceType": "r6idn.2xlarge",
                                "EbsConfiguration": {},
                            },
                        ],
                        "LaunchSpecifications": {
                            "SpotSpecification": {
                                "AllocationStrategy": "PRICE_CAPACITY_OPTIMIZED",
                                "TimeoutAction": "SWITCH_TO_ON_DEMAND",
                                "TimeoutDurationMinutes": 5,
                            },
                        },
                    },
                    {
                        "Name": "TaskFleet",
                        "InstanceFleetType": "TASK",
                        "TargetSpotCapacity": 8,
                        "InstanceTypeConfigs": [
                            {
                                "InstanceType": "r5b.2xlarge",
                                "EbsConfiguration": {
                                    "EbsBlockDeviceConfigs": [
                                        {
                                            "VolumeSpecification": {
                                                "SizeInGB": 200,
                                                "VolumeType": "gp3",
                                                "Iops": 3000,
                                            },
                                        }
                                    ],
                                },
                            },
                            {
                                "InstanceType": "r5n.2xlarge",
                                "EbsConfiguration": {
                                    "EbsBlockDeviceConfigs": [
                                        {
                                            "VolumeSpecification": {
                                                "SizeInGB": 200,
                                                "VolumeType": "gp3",
                                                "Iops": 3000,
                                            },
                                        }
                                    ],
                                },
                            },
                            {
                                "InstanceType": "r5ad.2xlarge",
                                "EbsConfiguration": {},
                            },
                            {
                                "InstanceType": "r5dn.2xlarge",
                                "EbsConfiguration": {},
                            },
                            {
                                "InstanceType": "r6idn.2xlarge",
                                "EbsConfiguration": {},
                            },
                        ],
                        "LaunchSpecifications": {
                            "SpotSpecification": {
                                "AllocationStrategy": "PRICE_CAPACITY_OPTIMIZED",
                                "TimeoutAction": "SWITCH_TO_ON_DEMAND",
                                "TimeoutDurationMinutes": 5,
                            },
                        },
                    },
                ],
            },
            "Configurations": [
                {
                    "Classification": "spark-hive-site",
                    "Properties": {
                        "hive.metastore.uris": "thrift://datalake-hive-metastore-service-data.prod-sgp-k8s.grofer.io:9083"
                    },
                },
                {
                    "Classification": "emrfs-site",
                    "Properties": {"fs.s3.maxRetries": "20"},
                },
            ],
            "VisibleToAllUsers": True,
            "JobFlowRole": "EMR_EC2_DefaultRole",
            "ServiceRole": "EMR_DefaultRole",
            "Tags": [
                {"Key": "Role", "Value": "source-replication"},
                {"Key": "Environment", "Value": "prod"},
                {"Key": "Product", "Value": "dse"},
                {"Key": "Service", "Value": "dse-emr-nessie"},
                {"Key": "Name", "Value": "<cluster_name>"},
                {"Key": "grofers.io/service", "Value": "source-replication"},
                {
                    "Key": "grofers.io/component",
                    "Value": "source-replication-spark-long-running",
                },
                {"Key": "grofers.io/component-role", "Value": "data-processing"},
                {"Key": "grofers.io/business-service", "Value": "data-platform"},
                {"Key": "grofers.io/team", "Value": "data-engineering"},
                {"Key": "grofers.io/tribe", "Value": "data"},
                {"Key": "cost:namespace", "Value": "data"},
                {"Key": "cost:application", "Value": "emr"},
            ],
        },
    ],
    "large": [
        {
            # "Name": "emr-cluster-identifier", set at the time of cluster launch
            "LogUri": "s3://grofers-prod-dse-sgp/elasticmapreduce/",
            "ReleaseLabel": "emr-6.11.1",
            "Applications": [{"Name": "Spark"}, {"Name": "Hadoop"}],
            "StepConcurrencyLevel": 4,
            "Instances": {
                "KeepJobFlowAliveWhenNoSteps": True,
                "Ec2KeyName": "dse-emr",
                "ServiceAccessSecurityGroup": "sg-64858200",
                "Ec2SubnetIds": [
                    "subnet-09e5bab68a3640d38",
                    "subnet-fe54ac88",
                    "subnet-e00fc184",
                ],
                "EmrManagedSlaveSecurityGroup": "sg-587afc3c",
                "EmrManagedMasterSecurityGroup": "sg-587afc3c",
                "InstanceFleets": [
                    {
                        "Name": "MasterFleet",
                        "InstanceFleetType": "MASTER",
                        "TargetOnDemandCapacity": 1,
                        "InstanceTypeConfigs": [
                            {
                                "InstanceType": "c6g.2xlarge",
                                "EbsConfiguration": {
                                    "EbsBlockDeviceConfigs": [
                                        {
                                            "VolumeSpecification": {
                                                "SizeInGB": 32,
                                                "VolumeType": "gp3",
                                                "Iops": 3000,
                                            },
                                        }
                                    ],
                                },
                            },
                            {
                                "InstanceType": "c7g.2xlarge",
                                "EbsConfiguration": {
                                    "EbsBlockDeviceConfigs": [
                                        {
                                            "VolumeSpecification": {
                                                "SizeInGB": 32,
                                                "VolumeType": "gp3",
                                                "Iops": 3000,
                                            },
                                        }
                                    ],
                                },
                            },
                            {
                                "InstanceType": "c5a.2xlarge",
                                "EbsConfiguration": {
                                    "EbsBlockDeviceConfigs": [
                                        {
                                            "VolumeSpecification": {
                                                "SizeInGB": 32,
                                                "VolumeType": "gp3",
                                                "Iops": 3000,
                                            },
                                        }
                                    ],
                                },
                            },
                            {
                                "InstanceType": "m6g.2xlarge",
                                "EbsConfiguration": {
                                    "EbsBlockDeviceConfigs": [
                                        {
                                            "VolumeSpecification": {
                                                "SizeInGB": 32,
                                                "VolumeType": "gp3",
                                                "Iops": 3000,
                                            },
                                        }
                                    ],
                                },
                            },
                            {
                                "InstanceType": "c5.2xlarge",
                                "EbsConfiguration": {
                                    "EbsBlockDeviceConfigs": [
                                        {
                                            "VolumeSpecification": {
                                                "SizeInGB": 32,
                                                "VolumeType": "gp3",
                                                "Iops": 3000,
                                            },
                                        }
                                    ],
                                },
                            },
                        ],
                        "LaunchSpecifications": {
                            "OnDemandSpecification": {
                                "AllocationStrategy": "LOWEST_PRICE"
                            },
                        },
                    },
                    {
                        "Name": "CoreFleet",
                        "InstanceFleetType": "CORE",
                        "TargetSpotCapacity": 1,
                        "InstanceTypeConfigs": [
                            {
                                "InstanceType": "r5a.4xlarge",
                                "EbsConfiguration": {
                                    "EbsBlockDeviceConfigs": [
                                        {
                                            "VolumeSpecification": {
                                                "SizeInGB": 200,
                                                "VolumeType": "gp3",
                                                "Iops": 3000,
                                            },
                                        }
                                    ],
                                },
                            },
                            {
                                "InstanceType": "r5b.4xlarge",
                                "EbsConfiguration": {
                                    "EbsBlockDeviceConfigs": [
                                        {
                                            "VolumeSpecification": {
                                                "SizeInGB": 200,
                                                "VolumeType": "gp3",
                                                "Iops": 3000,
                                            },
                                        }
                                    ],
                                },
                            },
                            {
                                "InstanceType": "m5a.8xlarge",
                                "EbsConfiguration": {
                                    "EbsBlockDeviceConfigs": [
                                        {
                                            "VolumeSpecification": {
                                                "SizeInGB": 200,
                                                "VolumeType": "gp3",
                                                "Iops": 3000,
                                            },
                                        }
                                    ],
                                },
                            },
                            {
                                "InstanceType": "r6idn.4xlarge",
                                "EbsConfiguration": {},
                            },
                        ],
                        "LaunchSpecifications": {
                            "SpotSpecification": {
                                "AllocationStrategy": "PRICE_CAPACITY_OPTIMIZED",
                                "TimeoutAction": "SWITCH_TO_ON_DEMAND",
                                "TimeoutDurationMinutes": 5,
                            },
                        },
                    },
                    {
                        "Name": "TaskFleet",
                        "InstanceFleetType": "TASK",
                        "TargetSpotCapacity": 2,
                        "InstanceTypeConfigs": [
                            {
                                "InstanceType": "r5a.4xlarge",
                                "EbsConfiguration": {
                                    "EbsBlockDeviceConfigs": [
                                        {
                                            "VolumeSpecification": {
                                                "SizeInGB": 200,
                                                "VolumeType": "gp3",
                                                "Iops": 3000,
                                            },
                                        }
                                    ],
                                },
                            },
                            {
                                "InstanceType": "m6gd.8xlarge",
                                "EbsConfiguration": {},
                            },
                            {
                                "InstanceType": "r5b.4xlarge",
                                "EbsConfiguration": {
                                    "EbsBlockDeviceConfigs": [
                                        {
                                            "VolumeSpecification": {
                                                "SizeInGB": 200,
                                                "VolumeType": "gp3",
                                                "Iops": 3000,
                                            },
                                        }
                                    ],
                                },
                            },
                            {
                                "InstanceType": "m5a.8xlarge",
                                "EbsConfiguration": {
                                    "EbsBlockDeviceConfigs": [
                                        {
                                            "VolumeSpecification": {
                                                "SizeInGB": 200,
                                                "VolumeType": "gp3",
                                                "Iops": 3000,
                                            },
                                        }
                                    ],
                                },
                            },
                            {
                                "InstanceType": "r6idn.4xlarge",
                                "EbsConfiguration": {},
                            },
                        ],
                        "LaunchSpecifications": {
                            "SpotSpecification": {
                                "AllocationStrategy": "PRICE_CAPACITY_OPTIMIZED",
                                "TimeoutAction": "SWITCH_TO_ON_DEMAND",
                                "TimeoutDurationMinutes": 5,
                            },
                        },
                    },
                ],
            },
            "Configurations": [
                {
                    "Classification": "spark-hive-site",
                    "Properties": {
                        "hive.metastore.uris": "thrift://datalake-hive-metastore-service-data.prod-sgp-k8s.grofer.io:9083"
                    },
                },
                {
                    "Classification": "emrfs-site",
                    "Properties": {"fs.s3.maxRetries": "20"},
                },
            ],
            "VisibleToAllUsers": True,
            "JobFlowRole": "EMR_EC2_DefaultRole",
            "ServiceRole": "EMR_DefaultRole",
            "Tags": [
                {"Key": "Role", "Value": "source-replication"},
                {"Key": "Environment", "Value": "prod"},
                {"Key": "Product", "Value": "dse"},
                {"Key": "Service", "Value": "dse-emr-nessie"},
                {"Key": "Name", "Value": "<cluster_name>"},
                {"Key": "grofers.io/service", "Value": "source-replication"},
                {
                    "Key": "grofers.io/component",
                    "Value": "source-replication-spark-long-running",
                },
                {"Key": "grofers.io/component-role", "Value": "data-processing"},
                {"Key": "grofers.io/business-service", "Value": "data-platform"},
                {"Key": "grofers.io/team", "Value": "data-engineering"},
                {"Key": "grofers.io/tribe", "Value": "data"},
                {"Key": "cost:namespace", "Value": "data"},
                {"Key": "cost:application", "Value": "emr"},
            ],
        },
        {
            # "Name": "emr-cluster-identifier", set at the time of cluster launch
            "LogUri": "s3://grofers-prod-dse-sgp/elasticmapreduce/",
            "ReleaseLabel": "emr-6.11.1",
            "Applications": [{"Name": "Spark"}, {"Name": "Hadoop"}],
            "StepConcurrencyLevel": 4,
            "Instances": {
                "KeepJobFlowAliveWhenNoSteps": True,
                "Ec2KeyName": "dse-emr",
                "ServiceAccessSecurityGroup": "sg-64858200",
                "Ec2SubnetIds": [
                    "subnet-09e5bab68a3640d38",
                    "subnet-fe54ac88",
                    "subnet-e00fc184",
                ],
                "EmrManagedSlaveSecurityGroup": "sg-587afc3c",
                "EmrManagedMasterSecurityGroup": "sg-587afc3c",
                "InstanceFleets": [
                    {
                        "Name": "MasterFleet",
                        "InstanceFleetType": "MASTER",
                        "TargetOnDemandCapacity": 1,
                        "InstanceTypeConfigs": [
                            {
                                "InstanceType": "c6g.2xlarge",
                                "EbsConfiguration": {
                                    "EbsBlockDeviceConfigs": [
                                        {
                                            "VolumeSpecification": {
                                                "SizeInGB": 32,
                                                "VolumeType": "gp3",
                                                "Iops": 3000,
                                            },
                                        }
                                    ],
                                },
                            },
                            {
                                "InstanceType": "c7g.2xlarge",
                                "EbsConfiguration": {
                                    "EbsBlockDeviceConfigs": [
                                        {
                                            "VolumeSpecification": {
                                                "SizeInGB": 32,
                                                "VolumeType": "gp3",
                                                "Iops": 3000,
                                            },
                                        }
                                    ],
                                },
                            },
                            {
                                "InstanceType": "c5a.2xlarge",
                                "EbsConfiguration": {
                                    "EbsBlockDeviceConfigs": [
                                        {
                                            "VolumeSpecification": {
                                                "SizeInGB": 32,
                                                "VolumeType": "gp3",
                                                "Iops": 3000,
                                            },
                                        }
                                    ],
                                },
                            },
                            {
                                "InstanceType": "m6g.2xlarge",
                                "EbsConfiguration": {
                                    "EbsBlockDeviceConfigs": [
                                        {
                                            "VolumeSpecification": {
                                                "SizeInGB": 32,
                                                "VolumeType": "gp3",
                                                "Iops": 3000,
                                            },
                                        }
                                    ],
                                },
                            },
                            {
                                "InstanceType": "c5.2xlarge",
                                "EbsConfiguration": {
                                    "EbsBlockDeviceConfigs": [
                                        {
                                            "VolumeSpecification": {
                                                "SizeInGB": 32,
                                                "VolumeType": "gp3",
                                                "Iops": 3000,
                                            },
                                        }
                                    ],
                                },
                            },
                        ],
                        "LaunchSpecifications": {
                            "OnDemandSpecification": {
                                "AllocationStrategy": "LOWEST_PRICE"
                            },
                        },
                    },
                    {
                        "Name": "CoreFleet",
                        "InstanceFleetType": "CORE",
                        "TargetSpotCapacity": 2,
                        "InstanceTypeConfigs": [
                            {
                                "InstanceType": "r5b.2xlarge",
                                "EbsConfiguration": {
                                    "EbsBlockDeviceConfigs": [
                                        {
                                            "VolumeSpecification": {
                                                "SizeInGB": 200,
                                                "VolumeType": "gp3",
                                                "Iops": 3000,
                                            },
                                        }
                                    ],
                                },
                            },
                            {
                                "InstanceType": "r5n.2xlarge",
                                "EbsConfiguration": {
                                    "EbsBlockDeviceConfigs": [
                                        {
                                            "VolumeSpecification": {
                                                "SizeInGB": 200,
                                                "VolumeType": "gp3",
                                                "Iops": 3000,
                                            },
                                        }
                                    ],
                                },
                            },
                            {
                                "InstanceType": "r5ad.2xlarge",
                                "EbsConfiguration": {},
                            },
                            {
                                "InstanceType": "r5dn.2xlarge",
                                "EbsConfiguration": {},
                            },
                            {
                                "InstanceType": "r6idn.2xlarge",
                                "EbsConfiguration": {},
                            },
                        ],
                        "LaunchSpecifications": {
                            "SpotSpecification": {
                                "AllocationStrategy": "PRICE_CAPACITY_OPTIMIZED",
                                "TimeoutAction": "SWITCH_TO_ON_DEMAND",
                                "TimeoutDurationMinutes": 5,
                            },
                        },
                    },
                    {
                        "Name": "TaskFleet",
                        "InstanceFleetType": "TASK",
                        "TargetSpotCapacity": 4,
                        "InstanceTypeConfigs": [
                            {
                                "InstanceType": "r5b.2xlarge",
                                "EbsConfiguration": {
                                    "EbsBlockDeviceConfigs": [
                                        {
                                            "VolumeSpecification": {
                                                "SizeInGB": 200,
                                                "VolumeType": "gp3",
                                                "Iops": 3000,
                                            },
                                        }
                                    ],
                                },
                            },
                            {
                                "InstanceType": "r5n.2xlarge",
                                "EbsConfiguration": {
                                    "EbsBlockDeviceConfigs": [
                                        {
                                            "VolumeSpecification": {
                                                "SizeInGB": 200,
                                                "VolumeType": "gp3",
                                                "Iops": 3000,
                                            },
                                        }
                                    ],
                                },
                            },
                            {
                                "InstanceType": "r5ad.2xlarge",
                                "EbsConfiguration": {},
                            },
                            {
                                "InstanceType": "r5dn.2xlarge",
                                "EbsConfiguration": {},
                            },
                            {
                                "InstanceType": "r6idn.2xlarge",
                                "EbsConfiguration": {},
                            },
                        ],
                        "LaunchSpecifications": {
                            "SpotSpecification": {
                                "AllocationStrategy": "PRICE_CAPACITY_OPTIMIZED",
                                "TimeoutAction": "SWITCH_TO_ON_DEMAND",
                                "TimeoutDurationMinutes": 5,
                            },
                        },
                    },
                ],
            },
            "Configurations": [
                {
                    "Classification": "spark-hive-site",
                    "Properties": {
                        "hive.metastore.uris": "thrift://datalake-hive-metastore-service-data.prod-sgp-k8s.grofer.io:9083"
                    },
                },
                {
                    "Classification": "emrfs-site",
                    "Properties": {"fs.s3.maxRetries": "20"},
                },
            ],
            "VisibleToAllUsers": True,
            "JobFlowRole": "EMR_EC2_DefaultRole",
            "ServiceRole": "EMR_DefaultRole",
            "Tags": [
                {"Key": "Role", "Value": "source-replication"},
                {"Key": "Environment", "Value": "prod"},
                {"Key": "Product", "Value": "dse"},
                {"Key": "Service", "Value": "dse-emr-nessie"},
                {"Key": "Name", "Value": "<cluster_name>"},
                {"Key": "grofers.io/service", "Value": "source-replication"},
                {
                    "Key": "grofers.io/component",
                    "Value": "source-replication-spark-long-running",
                },
                {"Key": "grofers.io/component-role", "Value": "data-processing"},
                {"Key": "grofers.io/business-service", "Value": "data-platform"},
                {"Key": "grofers.io/team", "Value": "data-engineering"},
                {"Key": "grofers.io/tribe", "Value": "data"},
                {"Key": "cost:namespace", "Value": "data"},
                {"Key": "cost:application", "Value": "emr"},
            ],
        },
    ],
}
