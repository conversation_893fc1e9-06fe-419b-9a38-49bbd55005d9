# -*- coding: utf-8 -*-

import json
import datetime as dt
import ast

import requests
import logging

import airflow
from airflow import DAG
from airflow.hooks.base_hook import BaseHook
from airflow.exceptions import AirflowException
from airflow.providers.amazon.aws.hooks.emr import EmrHook
from airflow.operators.python_operator import PythonOperator, ShortCircuitOperator
from airflow.providers.postgres.operators.postgres import PostgresOperator
from airflow.providers.amazon.aws.operators.emr_add_steps import EmrAddStepsOperator
from airflow.providers.amazon.aws.operators.emr_create_job_flow import EmrCreateJobFlowOperator
from airflow.providers.amazon.aws.operators.emr_terminate_job_flow import (
    EmrTerminateJobFlowOperator,
)
from metrics_plugin import (
    S3ToRedshiftOperator,
    BranchSRPOperator,
    DynamicClusterConfigOperator,
    PrestoCreateViewOperator,
    EmrStateSensorAsync,
)
from kubernetes.client import models as k8s

import pencilbox as pb


branch_op_flows = {
    "kitchen.activity_logs.v1": {
        "kafka_topic": "postgres.kitchen.public.activity_logs",
        "downstream_task_id": "s3_to_hudi_kitchen_activity_logs",
    },
    "kitchen.attendance_summary.v1": {
        "kafka_topic": "postgres.kitchen.public.attendance_summary",
        "downstream_task_id": "s3_to_hudi_kitchen_attendance_summary",
    },
    "kitchen.attribute.v1": {
        "kafka_topic": "postgres.kitchen.public.attribute",
        "downstream_task_id": "s3_to_hudi_kitchen_attribute",
    },
    "kitchen.dish.v1": {
        "kafka_topic": "postgres.kitchen.public.dish",
        "downstream_task_id": "s3_to_hudi_kitchen_dish",
    },
    "kitchen.entity.v1": {
        "kafka_topic": "postgres.kitchen.public.entity",
        "downstream_task_id": "s3_to_hudi_kitchen_entity",
    },
    "kitchen.entity_attribute_mapping.v1": {
        "kafka_topic": "postgres.kitchen.public.entity_attribute_mapping",
        "downstream_task_id": "s3_to_hudi_kitchen_entity_attribute_mapping",
    },
    "kitchen.entity_schedule.v1": {
        "kafka_topic": "postgres.kitchen.public.entity_schedule",
        "downstream_task_id": "s3_to_hudi_kitchen_entity_schedule",
    },
    "kitchen.foodware_mapping.v1": {
        "kafka_topic": "postgres.kitchen.public.foodware_mapping",
        "downstream_task_id": "s3_to_hudi_kitchen_foodware_mapping",
    },
    "kitchen.ingredient.v1": {
        "kafka_topic": "postgres.kitchen.public.ingredient",
        "downstream_task_id": "s3_to_hudi_kitchen_ingredient",
    },
    "kitchen.item_mapping.v1": {
        "kafka_topic": "postgres.kitchen.public.item_mapping",
        "downstream_task_id": "s3_to_hudi_kitchen_item_mapping",
    },
    "kitchen.order_states.v1": {
        "kafka_topic": "postgres.kitchen.public.order_states",
        "downstream_task_id": "s3_to_hudi_kitchen_order_states",
    },
    "kitchen.packaging_bags.v1": {
        "kafka_topic": "postgres.kitchen.public.packaging_bags",
        "downstream_task_id": "s3_to_hudi_kitchen_packaging_bags",
    },
    "kitchen.preparation_task_log.v1": {
        "kafka_topic": "postgres.kitchen.public.preparation_task_log",
        "downstream_task_id": "s3_to_hudi_kitchen_preparation_task_log",
    },
    "kitchen.product.v1": {
        "kafka_topic": "postgres.kitchen.public.product",
        "downstream_task_id": "s3_to_hudi_kitchen_product",
    },
    "kitchen.product_mapping.v1": {
        "kafka_topic": "postgres.kitchen.public.product_mapping",
        "downstream_task_id": "s3_to_hudi_kitchen_product_mapping",
    },
    "kitchen.product_transformation.v1": {
        "kafka_topic": "postgres.kitchen.public.product_transformation",
        "downstream_task_id": "s3_to_hudi_kitchen_product_transformation",
    },
    "kitchen.return_order.v1": {
        "kafka_topic": "postgres.kitchen.public.return_order",
        "downstream_task_id": "s3_to_hudi_kitchen_return_order",
    },
    "kitchen.roster_planner.v1": {
        "kafka_topic": "postgres.kitchen.public.roster_planner",
        "downstream_task_id": "s3_to_hudi_kitchen_roster_planner",
    },
    "kitchen.shift_record.v1": {
        "kafka_topic": "postgres.kitchen.public.shift_record",
        "downstream_task_id": "s3_to_hudi_kitchen_shift_record",
    },
    "kitchen.station.v1": {
        "kafka_topic": "postgres.kitchen.public.station",
        "downstream_task_id": "s3_to_hudi_kitchen_station",
    },
    "kitchen.station_product_mapping.v1": {
        "kafka_topic": "postgres.kitchen.public.station_product_mapping",
        "downstream_task_id": "s3_to_hudi_kitchen_station_product_mapping",
    },
    "kitchen.store_product_mapping.v1": {
        "kafka_topic": "postgres.kitchen.public.store_product_mapping",
        "downstream_task_id": "s3_to_hudi_kitchen_store_product_mapping",
    },
    "kitchen.store_station.v1": {
        "kafka_topic": "postgres.kitchen.public.store_station",
        "downstream_task_id": "s3_to_hudi_kitchen_store_station",
    },
    "kitchen.subdish.v1": {
        "kafka_topic": "postgres.kitchen.public.subdish",
        "downstream_task_id": "s3_to_hudi_kitchen_subdish",
    },
    "kitchen.user_entity_mapping.v1": {
        "kafka_topic": "postgres.kitchen.public.user_entity_mapping",
        "downstream_task_id": "s3_to_hudi_kitchen_user_entity_mapping",
    },
}


def slack_failure_alert(context):
    dag_id = context.get("task_instance").dag_id
    run_id = context.get("run_id")
    task_id = context.get("task_instance").task_id
    log_url = context.get("task_instance").log_url.replace("http://", "https://")
    exception = str(context.get("exception"))
    override_channel_mapping = {
        "terminated_with_errors": "bl-data-spot-interruptions",
        "user_request": "bl-data-spot-interruptions",
        "spot": "bl-data-spot-interruptions",
    }

    slack_failure_msg = """
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner*: <!subteam^S089D0GQRCJ>
        *Log Url*: {log_url}
        {exception}
    """.format(
        dag_id=dag_id,
        run_id=run_id,
        task_id=task_id,
        log_url=log_url,
        exception=exception,
    )

    slack_alert_configs = [
        {"channel": "bl-data-alerts-p1"},
    ]

    # Check if exception exists and contains any key from override_channel_mapping
    if exception and override_channel_mapping:
        for error_keyword, override_channel in override_channel_mapping.items():
            if error_keyword.lower() in exception.lower():
                # Override all channels with the mapped channel
                slack_alert_configs = [{"channel": override_channel}]
                break

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(channel=channel, text=slack_failure_msg, source_info=False)
        except Exception as e:
            logging.warning(e)
            message = (
                f"<!subteam^S089D0GQRCJ> `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(channel="bl-data-alerts-p1", text=message, source_info=False)


EMR_SETTINGS_OVERRIDE = {
    # "Name": "emr_launch_airflow_test",
    "LogUri": "s3n://grofers-prod-dse-sgp/elasticmapreduce/",
    "ReleaseLabel": "emr-6.11.1",
    "Applications": [{"Name": "Hadoop"}, {"Name": "Spark"}],
    "Instances": {
        "KeepJobFlowAliveWhenNoSteps": True,
        "Ec2KeyName": "dse-emr",
        "ServiceAccessSecurityGroup": "sg-64858200",
        "Ec2SubnetIds": [
            "subnet-09e5bab68a3640d38",
            "subnet-fe54ac88",
            "subnet-e00fc184",
        ],
        "EmrManagedSlaveSecurityGroup": "sg-587afc3c",
        "EmrManagedMasterSecurityGroup": "sg-587afc3c",
        "InstanceFleets": [],
    },
    "Configurations": [
        {
            "Classification": "spark-hive-site",
            "Properties": {
                "hive.metastore.uris": "thrift://datalake-hive-metastore-service-data.prod-sgp-k8s.grofer.io:9083"
            },
        },
        {
            "Classification": "emrfs-site",
            "Properties": {"fs.s3.maxRetries": "20"},
        },
    ],
    "VisibleToAllUsers": True,
    "JobFlowRole": "EMR_EC2_DefaultRole",
    "ServiceRole": "EMR_DefaultRole",
    "Tags": [
        {"Key": "Role", "Value": "source-replication"},
        {"Key": "Environment", "Value": "prod"},
        {"Key": "Product", "Value": "dse"},
        {"Key": "Service", "Value": "dse-emr-nessie"},
        {"Key": "Dag", "Value": "kitchen_1"},
        {"Key": "Database", "Value": "kitchen"},
        {"Key": "Name", "Value": "dse_kitchen_1_nessie"},
        {"Key": "grofers.io/service", "Value": "source-replication"},
        {"Key": "grofers.io/component", "Value": "source-replication-spark-cluster"},
        {"Key": "grofers.io/component-role", "Value": "data-processing"},
        {"Key": "grofers.io/business-service", "Value": "data-platform"},
        {"Key": "grofers.io/team", "Value": "data-engineering"},
        {"Key": "grofers.io/tribe", "Value": "data"},
        {"Key": "cost:namespace", "Value": "data"},
        {"Key": "cost:application", "Value": "emr"},
    ],
}


def get_emr_settings_override(**kwargs):
    EMR_SETTINGS_OVERRIDE["Name"] = "dse_{table}_nessie".format(table=kwargs["table"])
    # string -> dictionary
    kwargs["emr_settings_override"] = ast.literal_eval(kwargs["emr_settings_override"])
    EMR_SETTINGS_OVERRIDE["Instances"]["InstanceFleets"] = kwargs["emr_settings_override"][
        "emr_instance_fleets_config"
    ]
    EMR_SETTINGS_OVERRIDE["StepConcurrencyLevel"] = kwargs["emr_settings_override"][
        "step_concurrency_level"
    ]
    return EMR_SETTINGS_OVERRIDE


def config_validator(**kwargs):
    try:
        config = ast.literal_eval(kwargs["config"])
        logging.info("Validating the config string", config)
        if config["step_concurrency_level"]:
            return True
        else:
            return False
    except Exception as e:
        logging.exception("Error: ", e)
        return False


def fetch_spark_steps(flow_id, flow_type, nessie_version, spark_submit_mode="cluster", **kwargs):

    logging.info(f"Creating Spark Submit Step for flow_id {flow_id} in {spark_submit_mode} mode")

    if spark_submit_mode == "client":
        spark_submit = [
            "spark-submit",
            "--deploy-mode",
            f"{spark_submit_mode}",
            "--master",
            "yarn",
            "--conf",
            "spark.serializer=org.apache.spark.serializer.KryoSerializer",
            "--conf",
            "spark.sql.hive.convertMetastoreParquet=false",
            "--conf",
            "spark.driver.memory=3g",
            "--conf",
            "spark.driver.extraJavaOptions=-XX:NewSize=1g -XX:SurvivorRatio=2 -XX:+UseCompressedOops -XX:+UseConcMarkSweepGC -XX:+UseParNewGC -XX:CMSInitiatingOccupancyFraction=70 -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -XX:+PrintGCDateStamps -XX:+PrintGCApplicationStoppedTime -XX:+PrintGCApplicationConcurrentTime -XX:+PrintTenuringDistribution -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/tmp/hoodie-heapdump.hprof",
            "--conf",
            "spark.yarn.heterogeneousExecutors.enabled=false",
            "--conf",
            "spark.kryo.registrator=org.apache.spark.HoodieSparkKryoRegistrar",
            "--conf",
            "spark.sql.catalog.spark_catalog=org.apache.spark.sql.hudi.catalog.HoodieCatalog",
            "--conf",
            "spark.sql.extensions=org.apache.spark.sql.hudi.HoodieSparkSessionExtension",
            "--class",
            "com.grofers.nessie.Nessie",
            f"s3://prod-dse-srp-configs/jars/nessie/Nessie-assembly-{nessie_version}.jar",
            f"{flow_type}",
            "--flowId",
            f"{flow_id}",
        ]

    elif spark_submit_mode == "cluster":
        spark_submit = (
            "spark-submit",
            "--name",
            f"{flow_type}-{flow_id}",
            "--deploy-mode",
            f"{spark_submit_mode}",
            "--master",
            "yarn",
            "--conf",
            "spark.serializer=org.apache.spark.serializer.KryoSerializer",
            "--conf",
            "spark.sql.hive.convertMetastoreParquet=false",
            "--conf",
            "spark.yarn.heterogeneousExecutors.enabled=false",
            "--conf",
            "spark.kryo.registrator=org.apache.spark.HoodieSparkKryoRegistrar",
            "--conf",
            "spark.sql.catalog.spark_catalog=org.apache.spark.sql.hudi.catalog.HoodieCatalog",
            "--conf",
            "spark.sql.extensions=org.apache.spark.sql.hudi.HoodieSparkSessionExtension",
            "--class",
            "com.grofers.nessie.Nessie",
            f"s3://prod-dse-srp-configs/jars/nessie/Nessie-assembly-{nessie_version}.jar",
            f"{flow_type}",
            "--flowId",
            f"{flow_id}",
        )

    else:
        raise NotImplementedError("Incorrect spark submit mode provided")

    return [
        {
            "Name": f"""{flow_id}-{flow_type}-{dt.datetime.now().timestamp()}""",
            "ActionOnFailure": "CONTINUE",
            "HadoopJarStep": {
                "Jar": "command-runner.jar",
                "Args": spark_submit,
            },
        }
    ]


def failure_alerts(context):
    slack_failure_alert(context)


args = {
    "owner": "<EMAIL>",
    "on_failure_callback": failure_alerts,
    "start_date": dt.datetime.strptime("2025-06-13T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": dt.datetime.strptime("2026-06-08T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "slack_id": "S089D0GQRCJ",
}

dag = DAG(
    dag_id="de_replicate_etl_kitchen_1_v1",
    default_args=args,
    schedule_interval="0 * * * *",
    tags=["de", "replicate", "kitchen_1", "de", "replicate", "etl"],
    catchup=False,
    concurrency=15,
    max_active_runs=1,
)

cluster_config_task = DynamicClusterConfigOperator(
    task_id="cluster_config_task_kitchen_1",
    dag=dag,
    flows_to_run=branch_op_flows,
    connection_alias="nessie",
    queue="celery",
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
)


short_circuit_task = ShortCircuitOperator(
    task_id="short_circuit_kitchen_1",
    op_kwargs={
        "config": "{{ task_instance.xcom_pull('cluster_config_task_kitchen_1', key='return_value') }}"
    },
    python_callable=config_validator,
    dag=dag,
    queue="celery",
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
)


emr_cluster_config_task = PythonOperator(
    task_id="emr_cluster_config_task_kitchen_1",
    op_kwargs={
        "table": "kitchen_1",
        "emr_settings_override": "{{ task_instance.xcom_pull('cluster_config_task_kitchen_1', key='return_value') }}",
    },
    python_callable=get_emr_settings_override,
    dag=dag,
    queue="celery",
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
)


cluster_create_task = EmrCreateJobFlowOperator(
    task_id="create_emr_cluster_kitchen_1",
    job_flow_overrides="{{ task_instance.xcom_pull('emr_cluster_config_task_kitchen_1', key='return_value') }}",
    aws_conn_id="aws_default",
    emr_conn_id="emr_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


cluster_terminate_task = EmrTerminateJobFlowOperator(
    task_id="terminate_emr_cluster_kitchen_1",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    trigger_rule="all_done",
    priority_weight=3,
)


flows_branching_task = BranchSRPOperator(
    task_id="flows_branching_kitchen_1",
    dag=dag,
    flows_to_run=branch_op_flows,
    connection_alias="nessie",
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


s3_to_hudi_task_1 = EmrAddStepsOperator(
    task_id="s3_to_hudi_kitchen_activity_logs",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="kitchen.activity_logs.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_1 = EmrStateSensorAsync(
    task_id="load_to_lake_kitchen_activity_logs_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_kitchen_activity_logs', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_1 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_kitchen_activity_logs",
    topic_name="postgres.kitchen.public.activity_logs",
    database="kitchen",
    lake_schemaname="lake_kitchen",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_1
    >> s3_to_hudi_sensor_1
    >> cluster_terminate_task
)

s3_to_hudi_sensor_1 >> create_view_on_presto_1


s3_to_hudi_task_2 = EmrAddStepsOperator(
    task_id="s3_to_hudi_kitchen_attendance_summary",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="kitchen.attendance_summary.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_2 = EmrStateSensorAsync(
    task_id="load_to_lake_kitchen_attendance_summary_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_kitchen_attendance_summary', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_2 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_kitchen_attendance_summary",
    topic_name="postgres.kitchen.public.attendance_summary",
    database="kitchen",
    lake_schemaname="lake_kitchen",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_2
    >> s3_to_hudi_sensor_2
    >> cluster_terminate_task
)

s3_to_hudi_sensor_2 >> create_view_on_presto_2


s3_to_hudi_task_3 = EmrAddStepsOperator(
    task_id="s3_to_hudi_kitchen_attribute",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="kitchen.attribute.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_3 = EmrStateSensorAsync(
    task_id="load_to_lake_kitchen_attribute_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_kitchen_attribute', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_3 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_kitchen_attribute",
    topic_name="postgres.kitchen.public.attribute",
    database="kitchen",
    lake_schemaname="lake_kitchen",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_3
    >> s3_to_hudi_sensor_3
    >> cluster_terminate_task
)

s3_to_hudi_sensor_3 >> create_view_on_presto_3


s3_to_hudi_task_4 = EmrAddStepsOperator(
    task_id="s3_to_hudi_kitchen_dish",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="kitchen.dish.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_4 = EmrStateSensorAsync(
    task_id="load_to_lake_kitchen_dish_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_kitchen_dish', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_4 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_kitchen_dish",
    topic_name="postgres.kitchen.public.dish",
    database="kitchen",
    lake_schemaname="lake_kitchen",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_4
    >> s3_to_hudi_sensor_4
    >> cluster_terminate_task
)

s3_to_hudi_sensor_4 >> create_view_on_presto_4


s3_to_hudi_task_5 = EmrAddStepsOperator(
    task_id="s3_to_hudi_kitchen_entity",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="kitchen.entity.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_5 = EmrStateSensorAsync(
    task_id="load_to_lake_kitchen_entity_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_kitchen_entity', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_5 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_kitchen_entity",
    topic_name="postgres.kitchen.public.entity",
    database="kitchen",
    lake_schemaname="lake_kitchen",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_5
    >> s3_to_hudi_sensor_5
    >> cluster_terminate_task
)

s3_to_hudi_sensor_5 >> create_view_on_presto_5


s3_to_hudi_task_6 = EmrAddStepsOperator(
    task_id="s3_to_hudi_kitchen_entity_attribute_mapping",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="kitchen.entity_attribute_mapping.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_6 = EmrStateSensorAsync(
    task_id="load_to_lake_kitchen_entity_attribute_mapping_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_kitchen_entity_attribute_mapping', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_6 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_kitchen_entity_attribute_mapping",
    topic_name="postgres.kitchen.public.entity_attribute_mapping",
    database="kitchen",
    lake_schemaname="lake_kitchen",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_6
    >> s3_to_hudi_sensor_6
    >> cluster_terminate_task
)

s3_to_hudi_sensor_6 >> create_view_on_presto_6


s3_to_hudi_task_7 = EmrAddStepsOperator(
    task_id="s3_to_hudi_kitchen_entity_schedule",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="kitchen.entity_schedule.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_7 = EmrStateSensorAsync(
    task_id="load_to_lake_kitchen_entity_schedule_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_kitchen_entity_schedule', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_7 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_kitchen_entity_schedule",
    topic_name="postgres.kitchen.public.entity_schedule",
    database="kitchen",
    lake_schemaname="lake_kitchen",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_7
    >> s3_to_hudi_sensor_7
    >> cluster_terminate_task
)

s3_to_hudi_sensor_7 >> create_view_on_presto_7


s3_to_hudi_task_8 = EmrAddStepsOperator(
    task_id="s3_to_hudi_kitchen_foodware_mapping",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="kitchen.foodware_mapping.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_8 = EmrStateSensorAsync(
    task_id="load_to_lake_kitchen_foodware_mapping_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_kitchen_foodware_mapping', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_8 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_kitchen_foodware_mapping",
    topic_name="postgres.kitchen.public.foodware_mapping",
    database="kitchen",
    lake_schemaname="lake_kitchen",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_8
    >> s3_to_hudi_sensor_8
    >> cluster_terminate_task
)

s3_to_hudi_sensor_8 >> create_view_on_presto_8


s3_to_hudi_task_9 = EmrAddStepsOperator(
    task_id="s3_to_hudi_kitchen_ingredient",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="kitchen.ingredient.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_9 = EmrStateSensorAsync(
    task_id="load_to_lake_kitchen_ingredient_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_kitchen_ingredient', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_9 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_kitchen_ingredient",
    topic_name="postgres.kitchen.public.ingredient",
    database="kitchen",
    lake_schemaname="lake_kitchen",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_9
    >> s3_to_hudi_sensor_9
    >> cluster_terminate_task
)

s3_to_hudi_sensor_9 >> create_view_on_presto_9


s3_to_hudi_task_10 = EmrAddStepsOperator(
    task_id="s3_to_hudi_kitchen_item_mapping",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="kitchen.item_mapping.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_10 = EmrStateSensorAsync(
    task_id="load_to_lake_kitchen_item_mapping_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_kitchen_item_mapping', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_10 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_kitchen_item_mapping",
    topic_name="postgres.kitchen.public.item_mapping",
    database="kitchen",
    lake_schemaname="lake_kitchen",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_10
    >> s3_to_hudi_sensor_10
    >> cluster_terminate_task
)

s3_to_hudi_sensor_10 >> create_view_on_presto_10


s3_to_hudi_task_11 = EmrAddStepsOperator(
    task_id="s3_to_hudi_kitchen_order_states",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="kitchen.order_states.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_11 = EmrStateSensorAsync(
    task_id="load_to_lake_kitchen_order_states_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_kitchen_order_states', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_11 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_kitchen_order_states",
    topic_name="postgres.kitchen.public.order_states",
    database="kitchen",
    lake_schemaname="lake_kitchen",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_11
    >> s3_to_hudi_sensor_11
    >> cluster_terminate_task
)

s3_to_hudi_sensor_11 >> create_view_on_presto_11


s3_to_hudi_task_12 = EmrAddStepsOperator(
    task_id="s3_to_hudi_kitchen_packaging_bags",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="kitchen.packaging_bags.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_12 = EmrStateSensorAsync(
    task_id="load_to_lake_kitchen_packaging_bags_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_kitchen_packaging_bags', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_12 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_kitchen_packaging_bags",
    topic_name="postgres.kitchen.public.packaging_bags",
    database="kitchen",
    lake_schemaname="lake_kitchen",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_12
    >> s3_to_hudi_sensor_12
    >> cluster_terminate_task
)

s3_to_hudi_sensor_12 >> create_view_on_presto_12


s3_to_hudi_task_13 = EmrAddStepsOperator(
    task_id="s3_to_hudi_kitchen_preparation_task_log",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="kitchen.preparation_task_log.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_13 = EmrStateSensorAsync(
    task_id="load_to_lake_kitchen_preparation_task_log_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_kitchen_preparation_task_log', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_13 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_kitchen_preparation_task_log",
    topic_name="postgres.kitchen.public.preparation_task_log",
    database="kitchen",
    lake_schemaname="lake_kitchen",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_13
    >> s3_to_hudi_sensor_13
    >> cluster_terminate_task
)

s3_to_hudi_sensor_13 >> create_view_on_presto_13


s3_to_hudi_task_14 = EmrAddStepsOperator(
    task_id="s3_to_hudi_kitchen_product",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="kitchen.product.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_14 = EmrStateSensorAsync(
    task_id="load_to_lake_kitchen_product_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_kitchen_product', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_14 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_kitchen_product",
    topic_name="postgres.kitchen.public.product",
    database="kitchen",
    lake_schemaname="lake_kitchen",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_14
    >> s3_to_hudi_sensor_14
    >> cluster_terminate_task
)

s3_to_hudi_sensor_14 >> create_view_on_presto_14


s3_to_hudi_task_15 = EmrAddStepsOperator(
    task_id="s3_to_hudi_kitchen_product_mapping",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="kitchen.product_mapping.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_15 = EmrStateSensorAsync(
    task_id="load_to_lake_kitchen_product_mapping_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_kitchen_product_mapping', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_15 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_kitchen_product_mapping",
    topic_name="postgres.kitchen.public.product_mapping",
    database="kitchen",
    lake_schemaname="lake_kitchen",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_15
    >> s3_to_hudi_sensor_15
    >> cluster_terminate_task
)

s3_to_hudi_sensor_15 >> create_view_on_presto_15


s3_to_hudi_task_16 = EmrAddStepsOperator(
    task_id="s3_to_hudi_kitchen_product_transformation",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="kitchen.product_transformation.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_16 = EmrStateSensorAsync(
    task_id="load_to_lake_kitchen_product_transformation_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_kitchen_product_transformation', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_16 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_kitchen_product_transformation",
    topic_name="postgres.kitchen.public.product_transformation",
    database="kitchen",
    lake_schemaname="lake_kitchen",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_16
    >> s3_to_hudi_sensor_16
    >> cluster_terminate_task
)

s3_to_hudi_sensor_16 >> create_view_on_presto_16


s3_to_hudi_task_17 = EmrAddStepsOperator(
    task_id="s3_to_hudi_kitchen_return_order",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="kitchen.return_order.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_17 = EmrStateSensorAsync(
    task_id="load_to_lake_kitchen_return_order_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_kitchen_return_order', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_17 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_kitchen_return_order",
    topic_name="postgres.kitchen.public.return_order",
    database="kitchen",
    lake_schemaname="lake_kitchen",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_17
    >> s3_to_hudi_sensor_17
    >> cluster_terminate_task
)

s3_to_hudi_sensor_17 >> create_view_on_presto_17


s3_to_hudi_task_18 = EmrAddStepsOperator(
    task_id="s3_to_hudi_kitchen_roster_planner",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="kitchen.roster_planner.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_18 = EmrStateSensorAsync(
    task_id="load_to_lake_kitchen_roster_planner_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_kitchen_roster_planner', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_18 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_kitchen_roster_planner",
    topic_name="postgres.kitchen.public.roster_planner",
    database="kitchen",
    lake_schemaname="lake_kitchen",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_18
    >> s3_to_hudi_sensor_18
    >> cluster_terminate_task
)

s3_to_hudi_sensor_18 >> create_view_on_presto_18


s3_to_hudi_task_19 = EmrAddStepsOperator(
    task_id="s3_to_hudi_kitchen_shift_record",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="kitchen.shift_record.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_19 = EmrStateSensorAsync(
    task_id="load_to_lake_kitchen_shift_record_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_kitchen_shift_record', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_19 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_kitchen_shift_record",
    topic_name="postgres.kitchen.public.shift_record",
    database="kitchen",
    lake_schemaname="lake_kitchen",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_19
    >> s3_to_hudi_sensor_19
    >> cluster_terminate_task
)

s3_to_hudi_sensor_19 >> create_view_on_presto_19


s3_to_hudi_task_20 = EmrAddStepsOperator(
    task_id="s3_to_hudi_kitchen_station",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="kitchen.station.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_20 = EmrStateSensorAsync(
    task_id="load_to_lake_kitchen_station_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_kitchen_station', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_20 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_kitchen_station",
    topic_name="postgres.kitchen.public.station",
    database="kitchen",
    lake_schemaname="lake_kitchen",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_20
    >> s3_to_hudi_sensor_20
    >> cluster_terminate_task
)

s3_to_hudi_sensor_20 >> create_view_on_presto_20


s3_to_hudi_task_21 = EmrAddStepsOperator(
    task_id="s3_to_hudi_kitchen_station_product_mapping",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="kitchen.station_product_mapping.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_21 = EmrStateSensorAsync(
    task_id="load_to_lake_kitchen_station_product_mapping_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_kitchen_station_product_mapping', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_21 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_kitchen_station_product_mapping",
    topic_name="postgres.kitchen.public.station_product_mapping",
    database="kitchen",
    lake_schemaname="lake_kitchen",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_21
    >> s3_to_hudi_sensor_21
    >> cluster_terminate_task
)

s3_to_hudi_sensor_21 >> create_view_on_presto_21


s3_to_hudi_task_22 = EmrAddStepsOperator(
    task_id="s3_to_hudi_kitchen_store_product_mapping",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="kitchen.store_product_mapping.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_22 = EmrStateSensorAsync(
    task_id="load_to_lake_kitchen_store_product_mapping_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_kitchen_store_product_mapping', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_22 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_kitchen_store_product_mapping",
    topic_name="postgres.kitchen.public.store_product_mapping",
    database="kitchen",
    lake_schemaname="lake_kitchen",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_22
    >> s3_to_hudi_sensor_22
    >> cluster_terminate_task
)

s3_to_hudi_sensor_22 >> create_view_on_presto_22


s3_to_hudi_task_23 = EmrAddStepsOperator(
    task_id="s3_to_hudi_kitchen_store_station",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="kitchen.store_station.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_23 = EmrStateSensorAsync(
    task_id="load_to_lake_kitchen_store_station_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_kitchen_store_station', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_23 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_kitchen_store_station",
    topic_name="postgres.kitchen.public.store_station",
    database="kitchen",
    lake_schemaname="lake_kitchen",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_23
    >> s3_to_hudi_sensor_23
    >> cluster_terminate_task
)

s3_to_hudi_sensor_23 >> create_view_on_presto_23


s3_to_hudi_task_24 = EmrAddStepsOperator(
    task_id="s3_to_hudi_kitchen_subdish",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="kitchen.subdish.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_24 = EmrStateSensorAsync(
    task_id="load_to_lake_kitchen_subdish_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_kitchen_subdish', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_24 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_kitchen_subdish",
    topic_name="postgres.kitchen.public.subdish",
    database="kitchen",
    lake_schemaname="lake_kitchen",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_24
    >> s3_to_hudi_sensor_24
    >> cluster_terminate_task
)

s3_to_hudi_sensor_24 >> create_view_on_presto_24


s3_to_hudi_task_25 = EmrAddStepsOperator(
    task_id="s3_to_hudi_kitchen_user_entity_mapping",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="kitchen.user_entity_mapping.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_25 = EmrStateSensorAsync(
    task_id="load_to_lake_kitchen_user_entity_mapping_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_kitchen_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_kitchen_user_entity_mapping', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_25 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_kitchen_user_entity_mapping",
    topic_name="postgres.kitchen.public.user_entity_mapping",
    database="kitchen",
    lake_schemaname="lake_kitchen",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_25
    >> s3_to_hudi_sensor_25
    >> cluster_terminate_task
)

s3_to_hudi_sensor_25 >> create_view_on_presto_25


if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = dt.timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
