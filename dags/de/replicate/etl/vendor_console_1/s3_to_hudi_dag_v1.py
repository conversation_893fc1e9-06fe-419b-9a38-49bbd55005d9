# -*- coding: utf-8 -*-

import datetime as dt
import logging

from airflow import DAG
from airflow.operators.dagrun_operator import TriggerDagRunOperator
from kubernetes.client import models as k8s
from metrics_plugin import (
    EmrStateSensorAsync,
    SRPClusterMappingOperatorV2,
    SRPEmrBulkAddStepsOperator,
    PrestoCreateViewOperator,
)

import pencilbox as pb


branch_op_flows = {
    "vendor_console.appointment.v1": {
        "kafka_topic": "postgres.vendor_console.public.appointment",
        "downstream_task_id": "vendor_console_appointment_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.appointment.v1",
        ),
    },
    "vendor_console.appointment_capacity_config.v1": {
        "kafka_topic": "postgres.vendor_console.public.appointment_capacity_config",
        "downstream_task_id": "vendor_console_appointment_capacity_config_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.appointment_capacity_config.v1",
        ),
    },
    "vendor_console.appointment_capacity_config_log.v1": {
        "kafka_topic": "postgres.vendor_console.public.appointment_capacity_config_log",
        "downstream_task_id": "vendor_console_appointment_capacity_config_log_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.appointment_capacity_config_log.v1",
        ),
    },
    "vendor_console.appointment_facility_day_capacity.v1": {
        "kafka_topic": "postgres.vendor_console.public.appointment_facility_day_capacity",
        "downstream_task_id": "vendor_console_appointment_facility_day_capacity_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.appointment_facility_day_capacity.v1",
        ),
    },
    "vendor_console.appointment_facility_slot.v1": {
        "kafka_topic": "postgres.vendor_console.public.appointment_facility_slot",
        "downstream_task_id": "vendor_console_appointment_facility_slot_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.appointment_facility_slot.v1",
        ),
    },
    "vendor_console.appointment_facility_slot_log.v1": {
        "kafka_topic": "postgres.vendor_console.public.appointment_facility_slot_log",
        "downstream_task_id": "vendor_console_appointment_facility_slot_log_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.appointment_facility_slot_log.v1",
        ),
    },
    "vendor_console.appointment_log.v1": {
        "kafka_topic": "postgres.vendor_console.public.appointment_log",
        "downstream_task_id": "vendor_console_appointment_log_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.appointment_log.v1",
        ),
    },
    "vendor_console.appointment_po_mapping.v1": {
        "kafka_topic": "postgres.vendor_console.public.appointment_po_mapping",
        "downstream_task_id": "vendor_console_appointment_po_mapping_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.appointment_po_mapping.v1",
        ),
    },
    "vendor_console.appointment_slot_config.v1": {
        "kafka_topic": "postgres.vendor_console.public.appointment_slot_config",
        "downstream_task_id": "vendor_console_appointment_slot_config_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.appointment_slot_config.v1",
        ),
    },
    "vendor_console.appointment_slot_config_log.v1": {
        "kafka_topic": "postgres.vendor_console.public.appointment_slot_config_log",
        "downstream_task_id": "vendor_console_appointment_slot_config_log_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.appointment_slot_config_log.v1",
        ),
    },
    "vendor_console.appointment_slot_mapping.v1": {
        "kafka_topic": "postgres.vendor_console.public.appointment_slot_mapping",
        "downstream_task_id": "vendor_console_appointment_slot_mapping_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.appointment_slot_mapping.v1",
        ),
    },
    "vendor_console.appointment_slot_mapping_log.v1": {
        "kafka_topic": "postgres.vendor_console.public.appointment_slot_mapping_log",
        "downstream_task_id": "vendor_console_appointment_slot_mapping_log_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.appointment_slot_mapping_log.v1",
        ),
    },
    "vendor_console.auth_plan.v1": {
        "kafka_topic": "postgres.vendor_console.public.auth_plan",
        "downstream_task_id": "vendor_console_auth_plan_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.auth_plan.v1",
        ),
    },
    "vendor_console.base_bulk_upload_request_tracker.v1": {
        "kafka_topic": "postgres.vendor_console.public.base_bulk_upload_request_tracker",
        "downstream_task_id": "vendor_console_base_bulk_upload_request_tracker_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.base_bulk_upload_request_tracker.v1",
        ),
    },
    "vendor_console.client_po_details.v1": {
        "kafka_topic": "postgres.vendor_console.public.client_po_details",
        "downstream_task_id": "vendor_console_client_po_details_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.client_po_details.v1",
        ),
    },
    "vendor_console.client_po_items.v1": {
        "kafka_topic": "postgres.vendor_console.public.client_po_items",
        "downstream_task_id": "vendor_console_client_po_items_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.client_po_items.v1",
        ),
    },
    "vendor_console.cms_assortment_request.v1": {
        "kafka_topic": "postgres.vendor_console.public.cms_assortment_request",
        "downstream_task_id": "vendor_console_cms_assortment_request_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.cms_assortment_request.v1",
        ),
    },
    "vendor_console.cms_assortment_request_log.v1": {
        "kafka_topic": "postgres.vendor_console.public.cms_assortment_request_log",
        "downstream_task_id": "vendor_console_cms_assortment_request_log_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.cms_assortment_request_log.v1",
        ),
    },
    "vendor_console.courier_partner_days_config.v1": {
        "kafka_topic": "postgres.vendor_console.public.courier_partner_days_config",
        "downstream_task_id": "vendor_console_courier_partner_days_config_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.courier_partner_days_config.v1",
        ),
    },
    "vendor_console.courier_partner_details.v1": {
        "kafka_topic": "postgres.vendor_console.public.courier_partner_details",
        "downstream_task_id": "vendor_console_courier_partner_details_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.courier_partner_details.v1",
        ),
    },
    "vendor_console.day_capacity_request_data_tracker.v1": {
        "kafka_topic": "postgres.vendor_console.public.day_capacity_request_data_tracker",
        "downstream_task_id": "vendor_console_day_capacity_request_data_tracker_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.day_capacity_request_data_tracker.v1",
        ),
    },
    "vendor_console.entity.v1": {
        "kafka_topic": "postgres.vendor_console.public.entity",
        "downstream_task_id": "vendor_console_entity_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.entity.v1",
        ),
    },
    "vendor_console.entity_plan_mapping.v1": {
        "kafka_topic": "postgres.vendor_console.public.entity_plan_mapping",
        "downstream_task_id": "vendor_console_entity_plan_mapping_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.entity_plan_mapping.v1",
        ),
    },
    "vendor_console.facility_config.v1": {
        "kafka_topic": "postgres.vendor_console.public.facility_config",
        "downstream_task_id": "vendor_console_facility_config_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.facility_config.v1",
        ),
    },
    "vendor_console.facility_config_log.v1": {
        "kafka_topic": "postgres.vendor_console.public.facility_config_log",
        "downstream_task_id": "vendor_console_facility_config_log_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.facility_config_log.v1",
        ),
    },
    "vendor_console.facility_day_level_capacity.v1": {
        "kafka_topic": "postgres.vendor_console.public.facility_day_level_capacity",
        "downstream_task_id": "vendor_console_facility_day_level_capacity_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.facility_day_level_capacity.v1",
        ),
    },
    "vendor_console.facility_day_level_capacity_log.v1": {
        "kafka_topic": "postgres.vendor_console.public.facility_day_level_capacity_log",
        "downstream_task_id": "vendor_console_facility_day_level_capacity_log_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.facility_day_level_capacity_log.v1",
        ),
    },
    "vendor_console.facility_slot_time.v1": {
        "kafka_topic": "postgres.vendor_console.public.facility_slot_time",
        "downstream_task_id": "vendor_console_facility_slot_time_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.facility_slot_time.v1",
        ),
    },
    "vendor_console.facility_slot_time_log.v1": {
        "kafka_topic": "postgres.vendor_console.public.facility_slot_time_log",
        "downstream_task_id": "vendor_console_facility_slot_time_log_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.facility_slot_time_log.v1",
        ),
    },
    "vendor_console.invoice.v1": {
        "kafka_topic": "postgres.vendor_console.public.invoice",
        "downstream_task_id": "vendor_console_invoice_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.invoice.v1",
        ),
    },
    "vendor_console.invoice_event_log.v1": {
        "kafka_topic": "postgres.vendor_console.public.invoice_event_log",
        "downstream_task_id": "vendor_console_invoice_event_log_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.invoice_event_log.v1",
        ),
    },
    "vendor_console.invoice_payment_details.v1": {
        "kafka_topic": "postgres.vendor_console.public.invoice_payment_details",
        "downstream_task_id": "vendor_console_invoice_payment_details_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.invoice_payment_details.v1",
        ),
    },
    "vendor_console.invoice_payment_mapping.v1": {
        "kafka_topic": "postgres.vendor_console.public.invoice_payment_mapping",
        "downstream_task_id": "vendor_console_invoice_payment_mapping_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.invoice_payment_mapping.v1",
        ),
    },
    "vendor_console.item_proxy_category.v1": {
        "kafka_topic": "postgres.vendor_console.public.item_proxy_category",
        "downstream_task_id": "vendor_console_item_proxy_category_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.item_proxy_category.v1",
        ),
    },
    "vendor_console.po_reservation.v1": {
        "kafka_topic": "postgres.vendor_console.public.po_reservation",
        "downstream_task_id": "vendor_console_po_reservation_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.po_reservation.v1",
        ),
    },
    "vendor_console.po_reservation_log.v1": {
        "kafka_topic": "postgres.vendor_console.public.po_reservation_log",
        "downstream_task_id": "vendor_console_po_reservation_log_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.po_reservation_log.v1",
        ),
    },
    "vendor_console.report_requests.v1": {
        "kafka_topic": "postgres.vendor_console.public.report_requests",
        "downstream_task_id": "vendor_console_report_requests_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.report_requests.v1",
        ),
    },
    "vendor_console.reservation_slot_details.v1": {
        "kafka_topic": "postgres.vendor_console.public.reservation_slot_details",
        "downstream_task_id": "vendor_console_reservation_slot_details_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.reservation_slot_details.v1",
        ),
    },
    "vendor_console.reservation_slot_details_log.v1": {
        "kafka_topic": "postgres.vendor_console.public.reservation_slot_details_log",
        "downstream_task_id": "vendor_console_reservation_slot_details_log_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.reservation_slot_details_log.v1",
        ),
    },
    "vendor_console.shipment.v1": {
        "kafka_topic": "postgres.vendor_console.public.shipment",
        "downstream_task_id": "vendor_console_shipment_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.shipment.v1",
        ),
    },
    "vendor_console.slot_capacity.v1": {
        "kafka_topic": "postgres.vendor_console.public.slot_capacity",
        "downstream_task_id": "vendor_console_slot_capacity_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.slot_capacity.v1",
        ),
    },
    "vendor_console.slot_capacity_config.v1": {
        "kafka_topic": "postgres.vendor_console.public.slot_capacity_config",
        "downstream_task_id": "vendor_console_slot_capacity_config_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.slot_capacity_config.v1",
        ),
    },
    "vendor_console.slot_capacity_log.v1": {
        "kafka_topic": "postgres.vendor_console.public.slot_capacity_log",
        "downstream_task_id": "vendor_console_slot_capacity_log_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.slot_capacity_log.v1",
        ),
    },
    "vendor_console.slot_capacity_request_data_tracker.v1": {
        "kafka_topic": "postgres.vendor_console.public.slot_capacity_request_data_tracker",
        "downstream_task_id": "vendor_console_slot_capacity_request_data_tracker_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.slot_capacity_request_data_tracker.v1",
        ),
    },
    "vendor_console.user_details.v2": {
        "kafka_topic": "postgres.vendor_console.public.user_details",
        "downstream_task_id": "vendor_console_user_details_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.user_details.v2",
        ),
    },
    "vendor_console.user_entity_mapping.v1": {
        "kafka_topic": "postgres.vendor_console.public.user_entity_mapping",
        "downstream_task_id": "vendor_console_user_entity_mapping_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.user_entity_mapping.v1",
        ),
    },
    "vendor_console.vendor_facility_auto_release.v1": {
        "kafka_topic": "postgres.vendor_console.public.vendor_facility_auto_release",
        "downstream_task_id": "vendor_console_vendor_facility_auto_release_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "vendor_console.vendor_facility_auto_release.v1",
        ),
    },
}

# Default values for all flow ids, to override pass `spark_application_jar` in `branch_op_flows` for respective flow id
spark_application_jar = "s3://prod-dse-srp-configs/jars/nessie/Nessie-assembly-2.1.2.jar"

# Default values for all flow ids, to override any value pass `spark_submit_options` in `branch_op_flows` for respective flow id
spark_submit_options = {
    "--deploy-mode": "cluster",
    "--master": "yarn",
    "--conf": {
        "spark.serializer": "org.apache.spark.serializer.KryoSerializer",
        "spark.kryo.registrator": "org.apache.spark.HoodieSparkKryoRegistrar",
        "spark.sql.catalog.spark_catalog": "org.apache.spark.sql.hudi.catalog.HoodieCatalog",
        "spark.sql.extensions": "org.apache.spark.sql.hudi.HoodieSparkSessionExtension",
        "spark.yarn.heterogeneousExecutors.enabled": "false",
        "spark.sql.hive.convertMetastoreParquet": "false",
    },
    "--class": "com.grofers.nessie.Nessie",
}


def slack_failure_alert(context):
    dag_id = context.get("task_instance").dag_id
    run_id = context.get("run_id")
    task_id = context.get("task_instance").task_id
    log_url = context.get("task_instance").log_url.replace("http://", "https://")
    exception = str(context.get("exception"))
    override_channel_mapping = {
        "terminated_with_errors": "bl-data-spot-interruptions",
        "spot": "bl-data-spot-interruptions",
    }

    slack_failure_msg = """
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner*: <!subteam^S089D0GQRCJ>
        *Log Url*: {log_url}
        {exception}
    """.format(
        dag_id=dag_id,
        run_id=run_id,
        task_id=task_id,
        log_url=log_url,
        exception=exception,
    )

    slack_alert_configs = [
        {"channel": "bl-data-alerts-p1"},
    ]

    # Check if exception exists and contains any key from override_channel_mapping
    if exception and override_channel_mapping:
        for error_keyword, override_channel in override_channel_mapping.items():
            if error_keyword.lower() in exception.lower():
                # Override all channels with the mapped channel
                slack_alert_configs = [{"channel": override_channel}]
                break

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(channel=channel, text=slack_failure_msg, source_info=False)
        except Exception as e:
            logging.warning(e)
            message = (
                f"<!subteam^S089D0GQRCJ> `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(channel="bl-data-alerts-p1", text=message, source_info=False)


def failure_alerts(context):
    slack_failure_alert(context)


args = {
    "owner": "<EMAIL>",
    "on_failure_callback": failure_alerts,
    "start_date": dt.datetime.strptime("2022-05-09T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": dt.datetime.strptime("2026-08-15T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "slack_id": "S089D0GQRCJ",
}

dag = DAG(
    dag_id="de_replicate_etl_vendor_console_1_s3_to_hudi_v1",
    default_args=args,
    schedule_interval="27,57 0-19,22 * * *",
    tags=["de", "replicate", "vendor_console_1"],
    catchup=False,
    concurrency=15,
    max_active_runs=1,
    render_template_as_native_obj=True,
)


s3_hudi_cluster_allocation_task = SRPClusterMappingOperatorV2(
    task_id="cluster_allocation_vendor_console_1_s3_to_hudi",
    dag=dag,
    connection_alias="nessie",
    flows_to_run=branch_op_flows,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


s3_hudi_bulk_submit_emr_steps_task = SRPEmrBulkAddStepsOperator(
    task_id="bulk_step_submission_vendor_console_1_s3_to_hudi",
    dag=dag,
    aws_conn_id="aws_default",
    srp_flows_info="{{ task_instance.xcom_pull('cluster_allocation_vendor_console_1_s3_to_hudi', key='flows_info') }}",
    spark_application_jar=spark_application_jar,
    spark_submit_options=spark_submit_options,
    srp_task_id_cluster_mapping="{{ task_instance.xcom_pull('cluster_allocation_vendor_console_1_s3_to_hudi', key='return_value') }}",
    queue="celery",
)


s3_to_hudi_emr_sensor_1 = EmrStateSensorAsync(
    task_id="vendor_console_appointment_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.appointment.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.appointment.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_1 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_appointment",
    topic_name="postgres.vendor_console.public.appointment",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_1
    >> create_view_on_presto_1
)


s3_to_hudi_emr_sensor_2 = EmrStateSensorAsync(
    task_id="vendor_console_appointment_capacity_config_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.appointment_capacity_config.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.appointment_capacity_config.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_2 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_appointment_capacity_config",
    topic_name="postgres.vendor_console.public.appointment_capacity_config",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_2
    >> create_view_on_presto_2
)


s3_to_hudi_emr_sensor_3 = EmrStateSensorAsync(
    task_id="vendor_console_appointment_capacity_config_log_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.appointment_capacity_config_log.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.appointment_capacity_config_log.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_3 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_appointment_capacity_config_log",
    topic_name="postgres.vendor_console.public.appointment_capacity_config_log",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_3
    >> create_view_on_presto_3
)


s3_to_hudi_emr_sensor_4 = EmrStateSensorAsync(
    task_id="vendor_console_appointment_facility_day_capacity_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.appointment_facility_day_capacity.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.appointment_facility_day_capacity.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_4 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_appointment_facility_day_capacity",
    topic_name="postgres.vendor_console.public.appointment_facility_day_capacity",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_4
    >> create_view_on_presto_4
)


s3_to_hudi_emr_sensor_5 = EmrStateSensorAsync(
    task_id="vendor_console_appointment_facility_slot_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.appointment_facility_slot.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.appointment_facility_slot.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_5 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_appointment_facility_slot",
    topic_name="postgres.vendor_console.public.appointment_facility_slot",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_5
    >> create_view_on_presto_5
)


s3_to_hudi_emr_sensor_6 = EmrStateSensorAsync(
    task_id="vendor_console_appointment_facility_slot_log_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.appointment_facility_slot_log.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.appointment_facility_slot_log.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_6 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_appointment_facility_slot_log",
    topic_name="postgres.vendor_console.public.appointment_facility_slot_log",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_6
    >> create_view_on_presto_6
)


s3_to_hudi_emr_sensor_7 = EmrStateSensorAsync(
    task_id="vendor_console_appointment_log_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.appointment_log.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.appointment_log.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_7 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_appointment_log",
    topic_name="postgres.vendor_console.public.appointment_log",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_7
    >> create_view_on_presto_7
)


s3_to_hudi_emr_sensor_8 = EmrStateSensorAsync(
    task_id="vendor_console_appointment_po_mapping_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.appointment_po_mapping.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.appointment_po_mapping.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_8 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_appointment_po_mapping",
    topic_name="postgres.vendor_console.public.appointment_po_mapping",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_8
    >> create_view_on_presto_8
)


s3_to_hudi_emr_sensor_9 = EmrStateSensorAsync(
    task_id="vendor_console_appointment_slot_config_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.appointment_slot_config.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.appointment_slot_config.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_9 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_appointment_slot_config",
    topic_name="postgres.vendor_console.public.appointment_slot_config",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_9
    >> create_view_on_presto_9
)


s3_to_hudi_emr_sensor_10 = EmrStateSensorAsync(
    task_id="vendor_console_appointment_slot_config_log_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.appointment_slot_config_log.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.appointment_slot_config_log.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_10 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_appointment_slot_config_log",
    topic_name="postgres.vendor_console.public.appointment_slot_config_log",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_10
    >> create_view_on_presto_10
)


s3_to_hudi_emr_sensor_11 = EmrStateSensorAsync(
    task_id="vendor_console_appointment_slot_mapping_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.appointment_slot_mapping.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.appointment_slot_mapping.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_11 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_appointment_slot_mapping",
    topic_name="postgres.vendor_console.public.appointment_slot_mapping",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_11
    >> create_view_on_presto_11
)


s3_to_hudi_emr_sensor_12 = EmrStateSensorAsync(
    task_id="vendor_console_appointment_slot_mapping_log_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.appointment_slot_mapping_log.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.appointment_slot_mapping_log.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_12 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_appointment_slot_mapping_log",
    topic_name="postgres.vendor_console.public.appointment_slot_mapping_log",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_12
    >> create_view_on_presto_12
)


s3_to_hudi_emr_sensor_13 = EmrStateSensorAsync(
    task_id="vendor_console_auth_plan_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.auth_plan.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.auth_plan.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_13 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_auth_plan",
    topic_name="postgres.vendor_console.public.auth_plan",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_13
    >> create_view_on_presto_13
)


s3_to_hudi_emr_sensor_14 = EmrStateSensorAsync(
    task_id="vendor_console_base_bulk_upload_request_tracker_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.base_bulk_upload_request_tracker.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.base_bulk_upload_request_tracker.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_14 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_base_bulk_upload_request_tracker",
    topic_name="postgres.vendor_console.public.base_bulk_upload_request_tracker",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_14
    >> create_view_on_presto_14
)


s3_to_hudi_emr_sensor_15 = EmrStateSensorAsync(
    task_id="vendor_console_client_po_details_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.client_po_details.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.client_po_details.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_15 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_client_po_details",
    topic_name="postgres.vendor_console.public.client_po_details",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_15
    >> create_view_on_presto_15
)


s3_to_hudi_emr_sensor_16 = EmrStateSensorAsync(
    task_id="vendor_console_client_po_items_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.client_po_items.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.client_po_items.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_16 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_client_po_items",
    topic_name="postgres.vendor_console.public.client_po_items",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_16
    >> create_view_on_presto_16
)


s3_to_hudi_emr_sensor_17 = EmrStateSensorAsync(
    task_id="vendor_console_cms_assortment_request_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.cms_assortment_request.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.cms_assortment_request.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_17 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_cms_assortment_request",
    topic_name="postgres.vendor_console.public.cms_assortment_request",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_17
    >> create_view_on_presto_17
)


s3_to_hudi_emr_sensor_18 = EmrStateSensorAsync(
    task_id="vendor_console_cms_assortment_request_log_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.cms_assortment_request_log.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.cms_assortment_request_log.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_18 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_cms_assortment_request_log",
    topic_name="postgres.vendor_console.public.cms_assortment_request_log",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_18
    >> create_view_on_presto_18
)


s3_to_hudi_emr_sensor_19 = EmrStateSensorAsync(
    task_id="vendor_console_courier_partner_days_config_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.courier_partner_days_config.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.courier_partner_days_config.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_19 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_courier_partner_days_config",
    topic_name="postgres.vendor_console.public.courier_partner_days_config",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_19
    >> create_view_on_presto_19
)


s3_to_hudi_emr_sensor_20 = EmrStateSensorAsync(
    task_id="vendor_console_courier_partner_details_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.courier_partner_details.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.courier_partner_details.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_20 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_courier_partner_details",
    topic_name="postgres.vendor_console.public.courier_partner_details",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_20
    >> create_view_on_presto_20
)


s3_to_hudi_emr_sensor_21 = EmrStateSensorAsync(
    task_id="vendor_console_day_capacity_request_data_tracker_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.day_capacity_request_data_tracker.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.day_capacity_request_data_tracker.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_21 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_day_capacity_request_data_tracker",
    topic_name="postgres.vendor_console.public.day_capacity_request_data_tracker",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_21
    >> create_view_on_presto_21
)


s3_to_hudi_emr_sensor_22 = EmrStateSensorAsync(
    task_id="vendor_console_entity_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.entity.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.entity.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_22 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_entity",
    topic_name="postgres.vendor_console.public.entity",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_22
    >> create_view_on_presto_22
)


s3_to_hudi_emr_sensor_23 = EmrStateSensorAsync(
    task_id="vendor_console_entity_plan_mapping_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.entity_plan_mapping.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.entity_plan_mapping.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_23 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_entity_plan_mapping",
    topic_name="postgres.vendor_console.public.entity_plan_mapping",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_23
    >> create_view_on_presto_23
)


s3_to_hudi_emr_sensor_24 = EmrStateSensorAsync(
    task_id="vendor_console_facility_config_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.facility_config.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.facility_config.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_24 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_facility_config",
    topic_name="postgres.vendor_console.public.facility_config",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_24
    >> create_view_on_presto_24
)


s3_to_hudi_emr_sensor_25 = EmrStateSensorAsync(
    task_id="vendor_console_facility_config_log_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.facility_config_log.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.facility_config_log.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_25 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_facility_config_log",
    topic_name="postgres.vendor_console.public.facility_config_log",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_25
    >> create_view_on_presto_25
)


s3_to_hudi_emr_sensor_26 = EmrStateSensorAsync(
    task_id="vendor_console_facility_day_level_capacity_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.facility_day_level_capacity.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.facility_day_level_capacity.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_26 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_facility_day_level_capacity",
    topic_name="postgres.vendor_console.public.facility_day_level_capacity",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_26
    >> create_view_on_presto_26
)


s3_to_hudi_emr_sensor_27 = EmrStateSensorAsync(
    task_id="vendor_console_facility_day_level_capacity_log_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.facility_day_level_capacity_log.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.facility_day_level_capacity_log.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_27 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_facility_day_level_capacity_log",
    topic_name="postgres.vendor_console.public.facility_day_level_capacity_log",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_27
    >> create_view_on_presto_27
)


s3_to_hudi_emr_sensor_28 = EmrStateSensorAsync(
    task_id="vendor_console_facility_slot_time_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.facility_slot_time.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.facility_slot_time.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_28 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_facility_slot_time",
    topic_name="postgres.vendor_console.public.facility_slot_time",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_28
    >> create_view_on_presto_28
)


s3_to_hudi_emr_sensor_29 = EmrStateSensorAsync(
    task_id="vendor_console_facility_slot_time_log_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.facility_slot_time_log.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.facility_slot_time_log.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_29 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_facility_slot_time_log",
    topic_name="postgres.vendor_console.public.facility_slot_time_log",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_29
    >> create_view_on_presto_29
)


s3_to_hudi_emr_sensor_30 = EmrStateSensorAsync(
    task_id="vendor_console_invoice_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.invoice.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.invoice.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_30 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_invoice",
    topic_name="postgres.vendor_console.public.invoice",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_30
    >> create_view_on_presto_30
)


s3_to_hudi_emr_sensor_31 = EmrStateSensorAsync(
    task_id="vendor_console_invoice_event_log_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.invoice_event_log.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.invoice_event_log.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_31 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_invoice_event_log",
    topic_name="postgres.vendor_console.public.invoice_event_log",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_31
    >> create_view_on_presto_31
)


s3_to_hudi_emr_sensor_32 = EmrStateSensorAsync(
    task_id="vendor_console_invoice_payment_details_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.invoice_payment_details.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.invoice_payment_details.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_32 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_invoice_payment_details",
    topic_name="postgres.vendor_console.public.invoice_payment_details",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_32
    >> create_view_on_presto_32
)


s3_to_hudi_emr_sensor_33 = EmrStateSensorAsync(
    task_id="vendor_console_invoice_payment_mapping_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.invoice_payment_mapping.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.invoice_payment_mapping.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_33 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_invoice_payment_mapping",
    topic_name="postgres.vendor_console.public.invoice_payment_mapping",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_33
    >> create_view_on_presto_33
)


s3_to_hudi_emr_sensor_34 = EmrStateSensorAsync(
    task_id="vendor_console_item_proxy_category_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.item_proxy_category.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.item_proxy_category.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_34 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_item_proxy_category",
    topic_name="postgres.vendor_console.public.item_proxy_category",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_34
    >> create_view_on_presto_34
)


s3_to_hudi_emr_sensor_35 = EmrStateSensorAsync(
    task_id="vendor_console_po_reservation_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.po_reservation.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.po_reservation.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_35 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_po_reservation",
    topic_name="postgres.vendor_console.public.po_reservation",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_35
    >> create_view_on_presto_35
)


s3_to_hudi_emr_sensor_36 = EmrStateSensorAsync(
    task_id="vendor_console_po_reservation_log_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.po_reservation_log.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.po_reservation_log.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_36 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_po_reservation_log",
    topic_name="postgres.vendor_console.public.po_reservation_log",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_36
    >> create_view_on_presto_36
)


s3_to_hudi_emr_sensor_37 = EmrStateSensorAsync(
    task_id="vendor_console_report_requests_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.report_requests.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.report_requests.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_37 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_report_requests",
    topic_name="postgres.vendor_console.public.report_requests",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_37
    >> create_view_on_presto_37
)


s3_to_hudi_emr_sensor_38 = EmrStateSensorAsync(
    task_id="vendor_console_reservation_slot_details_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.reservation_slot_details.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.reservation_slot_details.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_38 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_reservation_slot_details",
    topic_name="postgres.vendor_console.public.reservation_slot_details",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_38
    >> create_view_on_presto_38
)


s3_to_hudi_emr_sensor_39 = EmrStateSensorAsync(
    task_id="vendor_console_reservation_slot_details_log_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.reservation_slot_details_log.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.reservation_slot_details_log.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_39 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_reservation_slot_details_log",
    topic_name="postgres.vendor_console.public.reservation_slot_details_log",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_39
    >> create_view_on_presto_39
)


s3_to_hudi_emr_sensor_40 = EmrStateSensorAsync(
    task_id="vendor_console_shipment_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.shipment.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.shipment.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_40 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_shipment",
    topic_name="postgres.vendor_console.public.shipment",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_40
    >> create_view_on_presto_40
)


s3_to_hudi_emr_sensor_41 = EmrStateSensorAsync(
    task_id="vendor_console_slot_capacity_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.slot_capacity.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.slot_capacity.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_41 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_slot_capacity",
    topic_name="postgres.vendor_console.public.slot_capacity",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_41
    >> create_view_on_presto_41
)


s3_to_hudi_emr_sensor_42 = EmrStateSensorAsync(
    task_id="vendor_console_slot_capacity_config_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.slot_capacity_config.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.slot_capacity_config.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_42 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_slot_capacity_config",
    topic_name="postgres.vendor_console.public.slot_capacity_config",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_42
    >> create_view_on_presto_42
)


s3_to_hudi_emr_sensor_43 = EmrStateSensorAsync(
    task_id="vendor_console_slot_capacity_log_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.slot_capacity_log.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.slot_capacity_log.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_43 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_slot_capacity_log",
    topic_name="postgres.vendor_console.public.slot_capacity_log",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_43
    >> create_view_on_presto_43
)


s3_to_hudi_emr_sensor_44 = EmrStateSensorAsync(
    task_id="vendor_console_slot_capacity_request_data_tracker_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.slot_capacity_request_data_tracker.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.slot_capacity_request_data_tracker.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_44 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_slot_capacity_request_data_tracker",
    topic_name="postgres.vendor_console.public.slot_capacity_request_data_tracker",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_44
    >> create_view_on_presto_44
)


s3_to_hudi_emr_sensor_45 = EmrStateSensorAsync(
    task_id="vendor_console_user_details_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.user_details.v2', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.user_details.v2', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_45 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_user_details",
    topic_name="postgres.vendor_console.public.user_details",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_45
    >> create_view_on_presto_45
)


s3_to_hudi_emr_sensor_46 = EmrStateSensorAsync(
    task_id="vendor_console_user_entity_mapping_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.user_entity_mapping.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.user_entity_mapping.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_46 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_user_entity_mapping",
    topic_name="postgres.vendor_console.public.user_entity_mapping",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_46
    >> create_view_on_presto_46
)


s3_to_hudi_emr_sensor_47 = EmrStateSensorAsync(
    task_id="vendor_console_vendor_facility_auto_release_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.vendor_facility_auto_release.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_vendor_console_1_s3_to_hudi', key='flow_id_job_mapping').get('vendor_console.vendor_facility_auto_release.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_47 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vendor_console_vendor_facility_auto_release",
    topic_name="postgres.vendor_console.public.vendor_facility_auto_release",
    database="vendor_console",
    lake_schemaname="lake_vendor_console",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_47
    >> create_view_on_presto_47
)


if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = dt.timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
