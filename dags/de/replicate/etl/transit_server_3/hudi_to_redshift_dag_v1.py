# -*- coding: utf-8 -*-

import datetime as dt
import logging

from airflow import DAG
from airflow.operators.python_operator import ShortCircuitOperator
from airflow.operators.postgres_operator import PostgresOperator
from metrics_plugin import (
    S3ToRedshiftOperator,
    SRPClusterMappingOperatorV2,
    SRPEmrBulkAddStepsOperator,
    PrestoCreateViewOperator,
    HudiToRedshiftFlowIdsHook,
    EmrStateSensorAsync,
)
from kubernetes.client import models as k8s

import pencilbox as pb


branch_op_flows = {}

# Default values for all flow ids, to override pass `spark_application_jar` in `branch_op_flows` for respective flow id
spark_application_jar = "s3://prod-dse-srp-configs/jars/nessie/Nessie-assembly-2.1.2.jar"

# Default values for all flow ids, to override any value pass `spark_submit_options` in `branch_op_flows` for respective flow id
spark_submit_options = {
    "--deploy-mode": "cluster",
    "--master": "yarn",
    "--conf": {
        "spark.yarn.heterogeneousExecutors.enabled": "false",
        "spark.serializer": "org.apache.spark.serializer.KryoSerializer",
        "spark.sql.hive.convertMetastoreParquet": "false",
    },
    "--jars": f"/usr/lib/spark/external/lib/spark-avro.jar,{spark_application_jar}",
    "--class": "com.grofers.nessie.Nessie",
}


def slack_failure_alert(context):
    dag_id = context.get("task_instance").dag_id
    run_id = context.get("run_id")
    task_id = context.get("task_instance").task_id
    log_url = context.get("task_instance").log_url.replace("http://", "https://")

    slack_failure_msg = """
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner*: <!subteam^S089D0GQRCJ>
        *Log Url*: {log_url}
    """.format(
        dag_id=dag_id,
        run_id=run_id,
        task_id=task_id,
        log_url=log_url,
    )

    slack_alert_configs = [
        {"channel": "bl-data-alerts-p1"},
    ]

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(channel=channel, text=slack_failure_msg, source_info=False)
        except Exception as e:
            logging.warning(e)
            message = (
                f"<!subteam^S089D0GQRCJ> `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(channel="bl-data-alerts-p1", text=message, source_info=False)


def verify_flow_conditions(**kwargs):
    """
    Checks if the conditions to run the hudi to redshift flow are appropriate.
    Conditions are considered appropriate when the table are actually required
    to be updated on Redshift.
    """

    def is_update_required(**kwargs):
        hook = HudiToRedshiftFlowIdsHook(
            candidate_flow_ids=branch_op_flows.keys(),
            redshift_sla_seconds=10800,
            db_engine=pb.get_connection("nessie"),
        )
        return len(hook.get_updated_table_flow_ids()) > 0

    return is_update_required(**kwargs)


def failure_alerts(context):
    slack_failure_alert(context)


args = {
    "owner": "<EMAIL>",
    "on_failure_callback": failure_alerts,
    "start_date": dt.datetime.strptime("2024-08-20T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": dt.datetime.strptime("2025-11-15T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "slack_id": "S089D0GQRCJ",
}

dag = DAG(
    dag_id="de_replicate_etl_transit_server_3_hudi_to_redshift_v1",
    default_args=args,
    schedule_interval=None,
    tags=["de", "replicate", "transit_server_3"],
    catchup=False,
    concurrency=14,
    max_active_runs=1,
    render_template_as_native_obj=True,
)


short_circuit_task = ShortCircuitOperator(
    task_id="short_circuit_transit_server_3_hudi_to_redshift",
    python_callable=verify_flow_conditions,
    dag=dag,
    queue="celery",
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
)


hudi_to_redshift_cluster_allocation_task = SRPClusterMappingOperatorV2(
    task_id="cluster_allocation_transit_server_3_hudi_to_redshift",
    dag=dag,
    flows_to_run=branch_op_flows,
    connection_alias="nessie",
    redshift_sla_seconds=10800,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


hudi_to_redshift_bulk_submit_emr_steps_task = SRPEmrBulkAddStepsOperator(
    task_id="bulk_step_submission_transit_server_3_hudi_to_redshift",
    dag=dag,
    aws_conn_id="aws_default",
    srp_flows_info="{{ task_instance.xcom_pull('cluster_allocation_transit_server_3_hudi_to_redshift', key='flows_info') }}",
    spark_application_jar=spark_application_jar,
    spark_submit_options=spark_submit_options,
    srp_task_id_cluster_mapping="{{ task_instance.xcom_pull('cluster_allocation_transit_server_3_hudi_to_redshift', key='return_value') }}",
    queue="celery",
)


if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = dt.timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
