# -*- coding: utf-8 -*-

import datetime as dt
import logging

from airflow import DAG
from airflow.operators.dagrun_operator import TriggerDagRunOperator
from kubernetes.client import models as k8s
from metrics_plugin import (
    EmrStateSensorAsync,
    SRPClusterMappingOperatorV2,
    SRPEmrBulkAddStepsOperator,
    PrestoCreateViewOperator,
)

import pencilbox as pb


branch_op_flows = {
    "transit_server.transit_allocation_fulfillment_queue.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_allocation_fulfillment_queue",
        "downstream_task_id": "transit_server_transit_allocation_fulfillment_queue_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_allocation_fulfillment_queue.v1",
        ),
    },
    "transit_server.transit_courier_order.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_courier_order",
        "downstream_task_id": "transit_server_transit_courier_order_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_courier_order.v1",
        ),
    },
    "transit_server.transit_courier_order_details.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_courier_order_details",
        "downstream_task_id": "transit_server_transit_courier_order_details_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_courier_order_details.v1",
        ),
    },
    "transit_server.transit_courier_order_log.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_courier_order_log",
        "downstream_task_id": "transit_server_transit_courier_order_log_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_courier_order_log.v1",
        ),
    },
    "transit_server.transit_courier_pickup.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_courier_pickup",
        "downstream_task_id": "transit_server_transit_courier_pickup_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_courier_pickup.v1",
        ),
    },
    "transit_server.transit_delivery_challan.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_delivery_challan",
        "downstream_task_id": "transit_server_transit_delivery_challan_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_delivery_challan.v1",
        ),
    },
    "transit_server.transit_delivery_challan_item.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_delivery_challan_item",
        "downstream_task_id": "transit_server_transit_delivery_challan_item_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_delivery_challan_item.v1",
        ),
    },
    "transit_server.transit_discrepancy_notification_mapping.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_discrepancy_notification_mapping",
        "downstream_task_id": "transit_server_transit_discrepancy_notification_mapping_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_discrepancy_notification_mapping.v1",
        ),
    },
    "transit_server.transit_ewaybill.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_ewaybill",
        "downstream_task_id": "transit_server_transit_ewaybill_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_ewaybill.v1",
        ),
    },
    "transit_server.transit_metrics.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_metrics",
        "downstream_task_id": "transit_server_transit_metrics_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_metrics.v1",
        ),
    },
    "transit_server.transit_metrics_group.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_metrics_group",
        "downstream_task_id": "transit_server_transit_metrics_group_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_metrics_group.v1",
        ),
    },
    "transit_server.transit_metrics_group_v2.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_metrics_group_v2",
        "downstream_task_id": "transit_server_transit_metrics_group_v2_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_metrics_group_v2.v1",
        ),
    },
    "transit_server.transit_metrics_v2.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_metrics_v2",
        "downstream_task_id": "transit_server_transit_metrics_v2_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_metrics_v2.v1",
        ),
    },
    "transit_server.transit_node_mapping.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_node_mapping",
        "downstream_task_id": "transit_server_transit_node_mapping_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_node_mapping.v1",
        ),
    },
    "transit_server.transit_notification.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_notification",
        "downstream_task_id": "transit_server_transit_notification_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_notification.v1",
        ),
    },
    "transit_server.transit_notification_user_mapping.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_notification_user_mapping",
        "downstream_task_id": "transit_server_transit_notification_user_mapping_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_notification_user_mapping.v1",
        ),
    },
    "transit_server.transit_projection_dispatch_demand.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_projection_dispatch_demand",
        "downstream_task_id": "transit_server_transit_projection_dispatch_demand_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_projection_dispatch_demand.v1",
        ),
    },
    "transit_server.transit_projection_plan_vehicle.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_projection_plan_vehicle",
        "downstream_task_id": "transit_server_transit_projection_plan_vehicle_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_projection_plan_vehicle.v1",
        ),
    },
    "transit_server.transit_projection_plan_vehicle_destination.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_projection_plan_vehicle_destination",
        "downstream_task_id": "transit_server_transit_projection_plan_vehicle_destination_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_projection_plan_vehicle_destination.v1",
        ),
    },
    "transit_server.transit_projection_trip_duration.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_projection_trip_duration",
        "downstream_task_id": "transit_server_transit_projection_trip_duration_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_projection_trip_duration.v1",
        ),
    },
    "transit_server.transit_task_node_detail.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_task_node_detail",
        "downstream_task_id": "transit_server_transit_task_node_detail_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_task_node_detail.v1",
        ),
    },
    "transit_server.transit_travel_segment.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_travel_segment",
        "downstream_task_id": "transit_server_transit_travel_segment_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_travel_segment.v1",
        ),
    },
    "transit_server.transit_user_group.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_user_group",
        "downstream_task_id": "transit_server_transit_user_group_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_user_group.v1",
        ),
    },
    "transit_server.transit_user_group_view_mapping.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_user_group_view_mapping",
        "downstream_task_id": "transit_server_transit_user_group_view_mapping_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_user_group_view_mapping.v1",
        ),
    },
    "transit_server.transit_user_profile_group.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_user_profile_group",
        "downstream_task_id": "transit_server_transit_user_profile_group_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_user_profile_group.v1",
        ),
    },
    "transit_server.transit_user_session.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_user_session",
        "downstream_task_id": "transit_server_transit_user_session_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_user_session.v1",
        ),
    },
}

# Default values for all flow ids, to override pass `spark_application_jar` in `branch_op_flows` for respective flow id
spark_application_jar = "s3://prod-dse-srp-configs/jars/nessie/Nessie-assembly-2.1.2.jar"

# Default values for all flow ids, to override any value pass `spark_submit_options` in `branch_op_flows` for respective flow id
spark_submit_options = {
    "--deploy-mode": "cluster",
    "--master": "yarn",
    "--conf": {
        "spark.serializer": "org.apache.spark.serializer.KryoSerializer",
        "spark.kryo.registrator": "org.apache.spark.HoodieSparkKryoRegistrar",
        "spark.sql.catalog.spark_catalog": "org.apache.spark.sql.hudi.catalog.HoodieCatalog",
        "spark.sql.extensions": "org.apache.spark.sql.hudi.HoodieSparkSessionExtension",
        "spark.yarn.heterogeneousExecutors.enabled": "false",
        "spark.sql.hive.convertMetastoreParquet": "false",
    },
    "--class": "com.grofers.nessie.Nessie",
}


def slack_failure_alert(context):
    dag_id = context.get("task_instance").dag_id
    run_id = context.get("run_id")
    task_id = context.get("task_instance").task_id
    log_url = context.get("task_instance").log_url.replace("http://", "https://")
    exception = str(context.get("exception"))
    override_channel_mapping = {
        "terminated_with_errors": "bl-data-spot-interruptions",
        "user_request": "bl-data-spot-interruptions",
        "spot": "bl-data-spot-interruptions",
    }

    slack_failure_msg = """
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner*: <!subteam^S089D0GQRCJ>
        *Log Url*: {log_url}
        {exception}
    """.format(
        dag_id=dag_id,
        run_id=run_id,
        task_id=task_id,
        log_url=log_url,
        exception=exception,
    )

    slack_alert_configs = [
        {"channel": "bl-data-alerts-p1"},
    ]

    # Check if exception exists and contains any key from override_channel_mapping
    if exception and override_channel_mapping:
        for error_keyword, override_channel in override_channel_mapping.items():
            if error_keyword.lower() in exception.lower():
                # Override all channels with the mapped channel
                slack_alert_configs = [{"channel": override_channel}]
                break

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(channel=channel, text=slack_failure_msg, source_info=False)
        except Exception as e:
            logging.warning(e)
            message = (
                f"<!subteam^S089D0GQRCJ> `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(channel="bl-data-alerts-p1", text=message, source_info=False)


def failure_alerts(context):
    slack_failure_alert(context)


args = {
    "owner": "<EMAIL>",
    "on_failure_callback": failure_alerts,
    "start_date": dt.datetime.strptime("2024-08-20T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": dt.datetime.strptime("2025-11-15T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "slack_id": "S089D0GQRCJ",
}

dag = DAG(
    dag_id="de_replicate_etl_transit_server_3_s3_to_hudi_v1",
    default_args=args,
    schedule_interval="15,45 * * * *",
    tags=["de", "replicate", "transit_server_3"],
    catchup=False,
    concurrency=14,
    max_active_runs=1,
    render_template_as_native_obj=True,
)


s3_hudi_cluster_allocation_task = SRPClusterMappingOperatorV2(
    task_id="cluster_allocation_transit_server_3_s3_to_hudi",
    dag=dag,
    connection_alias="nessie",
    flows_to_run=branch_op_flows,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


s3_hudi_bulk_submit_emr_steps_task = SRPEmrBulkAddStepsOperator(
    task_id="bulk_step_submission_transit_server_3_s3_to_hudi",
    dag=dag,
    aws_conn_id="aws_default",
    srp_flows_info="{{ task_instance.xcom_pull('cluster_allocation_transit_server_3_s3_to_hudi', key='flows_info') }}",
    spark_application_jar=spark_application_jar,
    spark_submit_options=spark_submit_options,
    srp_task_id_cluster_mapping="{{ task_instance.xcom_pull('cluster_allocation_transit_server_3_s3_to_hudi', key='return_value') }}",
    queue="celery",
)


s3_to_hudi_emr_sensor_1 = EmrStateSensorAsync(
    task_id="transit_server_transit_allocation_fulfillment_queue_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_allocation_fulfillment_queue.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_allocation_fulfillment_queue.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_1 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_allocation_fulfillment_queue",
    topic_name="postgres.transit_server.public.transit_allocation_fulfillment_queue",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_1
    >> create_view_on_presto_1
)


s3_to_hudi_emr_sensor_2 = EmrStateSensorAsync(
    task_id="transit_server_transit_courier_order_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_courier_order.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_courier_order.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_2 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_courier_order",
    topic_name="postgres.transit_server.public.transit_courier_order",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_2
    >> create_view_on_presto_2
)


s3_to_hudi_emr_sensor_3 = EmrStateSensorAsync(
    task_id="transit_server_transit_courier_order_details_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_courier_order_details.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_courier_order_details.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_3 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_courier_order_details",
    topic_name="postgres.transit_server.public.transit_courier_order_details",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_3
    >> create_view_on_presto_3
)


s3_to_hudi_emr_sensor_4 = EmrStateSensorAsync(
    task_id="transit_server_transit_courier_order_log_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_courier_order_log.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_courier_order_log.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_4 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_courier_order_log",
    topic_name="postgres.transit_server.public.transit_courier_order_log",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_4
    >> create_view_on_presto_4
)


s3_to_hudi_emr_sensor_5 = EmrStateSensorAsync(
    task_id="transit_server_transit_courier_pickup_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_courier_pickup.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_courier_pickup.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_5 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_courier_pickup",
    topic_name="postgres.transit_server.public.transit_courier_pickup",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_5
    >> create_view_on_presto_5
)


s3_to_hudi_emr_sensor_6 = EmrStateSensorAsync(
    task_id="transit_server_transit_delivery_challan_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_delivery_challan.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_delivery_challan.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_6 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_delivery_challan",
    topic_name="postgres.transit_server.public.transit_delivery_challan",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_6
    >> create_view_on_presto_6
)


s3_to_hudi_emr_sensor_7 = EmrStateSensorAsync(
    task_id="transit_server_transit_delivery_challan_item_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_delivery_challan_item.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_delivery_challan_item.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_7 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_delivery_challan_item",
    topic_name="postgres.transit_server.public.transit_delivery_challan_item",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_7
    >> create_view_on_presto_7
)


s3_to_hudi_emr_sensor_8 = EmrStateSensorAsync(
    task_id="transit_server_transit_discrepancy_notification_mapping_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_discrepancy_notification_mapping.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_discrepancy_notification_mapping.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_8 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_discrepancy_notification_mapping",
    topic_name="postgres.transit_server.public.transit_discrepancy_notification_mapping",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_8
    >> create_view_on_presto_8
)


s3_to_hudi_emr_sensor_9 = EmrStateSensorAsync(
    task_id="transit_server_transit_ewaybill_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_ewaybill.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_ewaybill.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_9 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_ewaybill",
    topic_name="postgres.transit_server.public.transit_ewaybill",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_9
    >> create_view_on_presto_9
)


s3_to_hudi_emr_sensor_10 = EmrStateSensorAsync(
    task_id="transit_server_transit_metrics_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_metrics.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_metrics.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_10 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_metrics",
    topic_name="postgres.transit_server.public.transit_metrics",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_10
    >> create_view_on_presto_10
)


s3_to_hudi_emr_sensor_11 = EmrStateSensorAsync(
    task_id="transit_server_transit_metrics_group_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_metrics_group.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_metrics_group.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_11 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_metrics_group",
    topic_name="postgres.transit_server.public.transit_metrics_group",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_11
    >> create_view_on_presto_11
)


s3_to_hudi_emr_sensor_12 = EmrStateSensorAsync(
    task_id="transit_server_transit_metrics_group_v2_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_metrics_group_v2.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_metrics_group_v2.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_12 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_metrics_group_v2",
    topic_name="postgres.transit_server.public.transit_metrics_group_v2",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_12
    >> create_view_on_presto_12
)


s3_to_hudi_emr_sensor_13 = EmrStateSensorAsync(
    task_id="transit_server_transit_metrics_v2_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_metrics_v2.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_metrics_v2.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_13 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_metrics_v2",
    topic_name="postgres.transit_server.public.transit_metrics_v2",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_13
    >> create_view_on_presto_13
)


s3_to_hudi_emr_sensor_14 = EmrStateSensorAsync(
    task_id="transit_server_transit_node_mapping_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_node_mapping.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_node_mapping.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_14 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_node_mapping",
    topic_name="postgres.transit_server.public.transit_node_mapping",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_14
    >> create_view_on_presto_14
)


s3_to_hudi_emr_sensor_15 = EmrStateSensorAsync(
    task_id="transit_server_transit_notification_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_notification.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_notification.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_15 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_notification",
    topic_name="postgres.transit_server.public.transit_notification",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_15
    >> create_view_on_presto_15
)


s3_to_hudi_emr_sensor_16 = EmrStateSensorAsync(
    task_id="transit_server_transit_notification_user_mapping_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_notification_user_mapping.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_notification_user_mapping.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_16 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_notification_user_mapping",
    topic_name="postgres.transit_server.public.transit_notification_user_mapping",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_16
    >> create_view_on_presto_16
)


s3_to_hudi_emr_sensor_17 = EmrStateSensorAsync(
    task_id="transit_server_transit_projection_dispatch_demand_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_projection_dispatch_demand.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_projection_dispatch_demand.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_17 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_projection_dispatch_demand",
    topic_name="postgres.transit_server.public.transit_projection_dispatch_demand",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_17
    >> create_view_on_presto_17
)


s3_to_hudi_emr_sensor_18 = EmrStateSensorAsync(
    task_id="transit_server_transit_projection_plan_vehicle_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_projection_plan_vehicle.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_projection_plan_vehicle.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_18 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_projection_plan_vehicle",
    topic_name="postgres.transit_server.public.transit_projection_plan_vehicle",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_18
    >> create_view_on_presto_18
)


s3_to_hudi_emr_sensor_19 = EmrStateSensorAsync(
    task_id="transit_server_transit_projection_plan_vehicle_destination_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_projection_plan_vehicle_destination.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_projection_plan_vehicle_destination.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_19 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_projection_plan_vehicle_destination",
    topic_name="postgres.transit_server.public.transit_projection_plan_vehicle_destination",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_19
    >> create_view_on_presto_19
)


s3_to_hudi_emr_sensor_20 = EmrStateSensorAsync(
    task_id="transit_server_transit_projection_trip_duration_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_projection_trip_duration.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_projection_trip_duration.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_20 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_projection_trip_duration",
    topic_name="postgres.transit_server.public.transit_projection_trip_duration",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_20
    >> create_view_on_presto_20
)


s3_to_hudi_emr_sensor_21 = EmrStateSensorAsync(
    task_id="transit_server_transit_task_node_detail_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_task_node_detail.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_task_node_detail.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_21 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_task_node_detail",
    topic_name="postgres.transit_server.public.transit_task_node_detail",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_21
    >> create_view_on_presto_21
)


s3_to_hudi_emr_sensor_22 = EmrStateSensorAsync(
    task_id="transit_server_transit_travel_segment_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_travel_segment.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_travel_segment.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_22 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_travel_segment",
    topic_name="postgres.transit_server.public.transit_travel_segment",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_22
    >> create_view_on_presto_22
)


s3_to_hudi_emr_sensor_23 = EmrStateSensorAsync(
    task_id="transit_server_transit_user_group_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_user_group.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_user_group.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_23 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_user_group",
    topic_name="postgres.transit_server.public.transit_user_group",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_23
    >> create_view_on_presto_23
)


s3_to_hudi_emr_sensor_24 = EmrStateSensorAsync(
    task_id="transit_server_transit_user_group_view_mapping_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_user_group_view_mapping.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_user_group_view_mapping.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_24 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_user_group_view_mapping",
    topic_name="postgres.transit_server.public.transit_user_group_view_mapping",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_24
    >> create_view_on_presto_24
)


s3_to_hudi_emr_sensor_25 = EmrStateSensorAsync(
    task_id="transit_server_transit_user_profile_group_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_user_profile_group.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_user_profile_group.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_25 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_user_profile_group",
    topic_name="postgres.transit_server.public.transit_user_profile_group",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_25
    >> create_view_on_presto_25
)


s3_to_hudi_emr_sensor_26 = EmrStateSensorAsync(
    task_id="transit_server_transit_user_session_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_user_session.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_3_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_user_session.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_26 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_user_session",
    topic_name="postgres.transit_server.public.transit_user_session",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_26
    >> create_view_on_presto_26
)


if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = dt.timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
