# -*- coding: utf-8 -*-

import json
import datetime as dt
import ast

import requests
import logging

import airflow
from airflow import DAG
from airflow.hooks.base_hook import BaseHook
from airflow.exceptions import AirflowException
from airflow.providers.amazon.aws.hooks.emr import EmrHook
from airflow.operators.python_operator import PythonOperator, ShortCircuitOperator
from airflow.providers.postgres.operators.postgres import PostgresOperator
from airflow.providers.amazon.aws.operators.emr_add_steps import EmrAddStepsOperator
from airflow.providers.amazon.aws.operators.emr_create_job_flow import EmrCreateJobFlowOperator
from airflow.providers.amazon.aws.operators.emr_terminate_job_flow import (
    EmrTerminateJobFlowOperator,
)
from metrics_plugin import (
    S3ToRedshiftOperator,
    BranchSRPOperator,
    DynamicClusterConfigOperator,
    PrestoCreateViewOperator,
    EmrStateSensorAsync,
)
from kubernetes.client import models as k8s

import pencilbox as pb


branch_op_flows = {
    "seller.apob_facility.v1": {
        "kafka_topic": "postgres.seller.public.apob_facility",
        "downstream_task_id": "s3_to_hudi_seller_apob_facility",
    },
    "seller.campaigns.v1": {
        "kafka_topic": "postgres.seller.public.campaigns",
        "downstream_task_id": "s3_to_hudi_seller_campaigns",
    },
    "seller.cart.v1": {
        "kafka_topic": "postgres.seller.public.cart",
        "downstream_task_id": "s3_to_hudi_seller_cart",
    },
    "seller.cart_order_mapping.v1": {
        "kafka_topic": "postgres.seller.public.cart_order_mapping",
        "downstream_task_id": "s3_to_hudi_seller_cart_order_mapping",
    },
    "seller.cart_transaction_log.v1": {
        "kafka_topic": "postgres.seller.public.cart_transaction_log",
        "downstream_task_id": "s3_to_hudi_seller_cart_transaction_log",
    },
    "seller.facilities.v1": {
        "kafka_topic": "postgres.seller.public.facilities",
        "downstream_task_id": "s3_to_hudi_seller_facilities",
    },
    "seller.faq.v1": {
        "kafka_topic": "postgres.seller.public.faq",
        "downstream_task_id": "s3_to_hudi_seller_faq",
    },
    "seller.item_facility_mapping.v1": {
        "kafka_topic": "postgres.seller.public.item_facility_mapping",
        "downstream_task_id": "s3_to_hudi_seller_item_facility_mapping",
    },
    "seller.item_outlet_inventory.v1": {
        "kafka_topic": "postgres.seller.public.item_outlet_inventory",
        "downstream_task_id": "s3_to_hudi_seller_item_outlet_inventory",
    },
    "seller.product_expansion_assessment.v1": {
        "kafka_topic": "postgres.seller.public.product_expansion_assessment",
        "downstream_task_id": "s3_to_hudi_seller_product_expansion_assessment",
    },
    "seller.queue_items.v1": {
        "kafka_topic": "postgres.seller.public.queue_items",
        "downstream_task_id": "s3_to_hudi_seller_queue_items",
    },
    "seller.rate_card_item.v1": {
        "kafka_topic": "postgres.seller.public.rate_card_item",
        "downstream_task_id": "s3_to_hudi_seller_rate_card_item",
    },
    "seller.sales_summary.v1": {
        "kafka_topic": "postgres.seller.public.sales_summary",
        "downstream_task_id": "s3_to_hudi_seller_sales_summary",
    },
    "seller.scheduled_jobs.v1": {
        "kafka_topic": "postgres.seller.public.scheduled_jobs",
        "downstream_task_id": "s3_to_hudi_seller_scheduled_jobs",
    },
    "seller.seller.v1": {
        "kafka_topic": "postgres.seller.public.seller",
        "downstream_task_id": "s3_to_hudi_seller_seller",
    },
    "seller.seller_advertiser_mapping.v1": {
        "kafka_topic": "postgres.seller.public.seller_advertiser_mapping",
        "downstream_task_id": "s3_to_hudi_seller_seller_advertiser_mapping",
    },
    "seller.seller_apob.v1": {
        "kafka_topic": "postgres.seller.public.seller_apob",
        "downstream_task_id": "s3_to_hudi_seller_seller_apob",
    },
    "seller.seller_brand_detail.v1": {
        "kafka_topic": "postgres.seller.public.seller_brand_detail",
        "downstream_task_id": "s3_to_hudi_seller_seller_brand_detail",
    },
    "seller.seller_document_info.v1": {
        "kafka_topic": "postgres.seller.public.seller_document_info",
        "downstream_task_id": "s3_to_hudi_seller_seller_document_info",
    },
    "seller.seller_order_level_pos_sales_info.v1": {
        "kafka_topic": "postgres.seller.public.seller_order_level_pos_sales_info",
        "downstream_task_id": "s3_to_hudi_seller_seller_order_level_pos_sales_info",
    },
    "seller.seller_order_level_sales_info.v1": {
        "kafka_topic": "postgres.seller.public.seller_order_level_sales_info",
        "downstream_task_id": "s3_to_hudi_seller_seller_order_level_sales_info",
    },
    "seller.seller_payout_commission.v1": {
        "kafka_topic": "postgres.seller.public.seller_payout_commission",
        "downstream_task_id": "s3_to_hudi_seller_seller_payout_commission",
    },
    "seller.seller_product_mappings.v1": {
        "kafka_topic": "postgres.seller.public.seller_product_mappings",
        "downstream_task_id": "s3_to_hudi_seller_seller_product_mappings",
    },
    "seller.seller_product_request_mappings.v1": {
        "kafka_topic": "postgres.seller.public.seller_product_request_mappings",
        "downstream_task_id": "s3_to_hudi_seller_seller_product_request_mappings",
    },
    "seller.seller_t_and_c.v1": {
        "kafka_topic": "postgres.seller.public.seller_t_and_c",
        "downstream_task_id": "s3_to_hudi_seller_seller_t_and_c",
    },
    "seller.store_group_definitions.v1": {
        "kafka_topic": "postgres.seller.public.store_group_definitions",
        "downstream_task_id": "s3_to_hudi_seller_store_group_definitions",
    },
    "seller.task.v1": {
        "kafka_topic": "postgres.seller.public.task",
        "downstream_task_id": "s3_to_hudi_seller_task",
    },
    "seller.user_seller_mapping.v1": {
        "kafka_topic": "postgres.seller.public.user_seller_mapping",
        "downstream_task_id": "s3_to_hudi_seller_user_seller_mapping",
    },
    "seller.workflow.v1": {
        "kafka_topic": "postgres.seller.public.workflow",
        "downstream_task_id": "s3_to_hudi_seller_workflow",
    },
    "seller.recall_request_batch_pro_detail.v1": {
        "kafka_topic": "postgres.seller.public.recall_request_batch_pro_detail",
        "downstream_task_id": "s3_to_hudi_seller_recall_request_batch_pro_detail",
    },
    "seller.recall_request_batch_log.v1": {
        "kafka_topic": "postgres.seller.public.recall_request_batch_log",
        "downstream_task_id": "s3_to_hudi_seller_recall_request_batch_log",
    },
    "seller.recall_request_batch_item.v1": {
        "kafka_topic": "postgres.seller.public.recall_request_batch_item",
        "downstream_task_id": "s3_to_hudi_seller_recall_request_batch_item",
    },
    "seller.recall_request_batch.v1": {
        "kafka_topic": "postgres.seller.public.recall_request_batch",
        "downstream_task_id": "s3_to_hudi_seller_recall_request_batch",
    },
    "seller.recall_request.v1": {
        "kafka_topic": "postgres.seller.public.recall_request",
        "downstream_task_id": "s3_to_hudi_seller_recall_request",
    },
}


def slack_failure_alert(context):
    dag_id = context.get("task_instance").dag_id
    run_id = context.get("run_id")
    task_id = context.get("task_instance").task_id
    log_url = context.get("task_instance").log_url.replace("http://", "https://")
    exception = str(context.get("exception"))
    override_channel_mapping = {
        "terminated_with_errors": "bl-data-spot-interruptions",
        "spot": "bl-data-spot-interruptions",
    }

    slack_failure_msg = """
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner*: <!subteam^S089D0GQRCJ>
        *Log Url*: {log_url}
        {exception}
    """.format(
        dag_id=dag_id,
        run_id=run_id,
        task_id=task_id,
        log_url=log_url,
        exception=exception,
    )

    slack_alert_configs = [
        {"channel": "bl-data-alerts-p1"},
    ]

    # Check if exception exists and contains any key from override_channel_mapping
    if exception and override_channel_mapping:
        for error_keyword, override_channel in override_channel_mapping.items():
            if error_keyword.lower() in exception.lower():
                # Override all channels with the mapped channel
                slack_alert_configs = [{"channel": override_channel}]
                break

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(channel=channel, text=slack_failure_msg, source_info=False)
        except Exception as e:
            logging.warning(e)
            message = (
                f"<!subteam^S089D0GQRCJ> `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(channel="bl-data-alerts-p1", text=message, source_info=False)


EMR_SETTINGS_OVERRIDE = {
    # "Name": "emr_launch_airflow_test",
    "LogUri": "s3n://grofers-prod-dse-sgp/elasticmapreduce/",
    "ReleaseLabel": "emr-6.11.1",
    "Applications": [{"Name": "Hadoop"}, {"Name": "Spark"}],
    "Instances": {
        "KeepJobFlowAliveWhenNoSteps": True,
        "Ec2KeyName": "dse-emr",
        "ServiceAccessSecurityGroup": "sg-64858200",
        "Ec2SubnetIds": [
            "subnet-09e5bab68a3640d38",
            "subnet-fe54ac88",
            "subnet-e00fc184",
        ],
        "EmrManagedSlaveSecurityGroup": "sg-587afc3c",
        "EmrManagedMasterSecurityGroup": "sg-587afc3c",
        "InstanceFleets": [],
    },
    "Configurations": [
        {
            "Classification": "spark-hive-site",
            "Properties": {
                "hive.metastore.uris": "thrift://datalake-hive-metastore-service-data.prod-sgp-k8s.grofer.io:9083"
            },
        },
        {
            "Classification": "emrfs-site",
            "Properties": {"fs.s3.maxRetries": "20"},
        },
    ],
    "VisibleToAllUsers": True,
    "JobFlowRole": "EMR_EC2_DefaultRole",
    "ServiceRole": "EMR_DefaultRole",
    "Tags": [
        {"Key": "Role", "Value": "source-replication"},
        {"Key": "Environment", "Value": "prod"},
        {"Key": "Product", "Value": "dse"},
        {"Key": "Service", "Value": "dse-emr-nessie"},
        {"Key": "Dag", "Value": "seller_1"},
        {"Key": "Database", "Value": "seller"},
        {"Key": "Name", "Value": "dse_seller_1_nessie"},
        {"Key": "grofers.io/service", "Value": "source-replication"},
        {"Key": "grofers.io/component", "Value": "source-replication-spark-cluster"},
        {"Key": "grofers.io/component-role", "Value": "data-processing"},
        {"Key": "grofers.io/business-service", "Value": "data-platform"},
        {"Key": "grofers.io/team", "Value": "data-engineering"},
        {"Key": "grofers.io/tribe", "Value": "data"},
        {"Key": "cost:namespace", "Value": "data"},
        {"Key": "cost:application", "Value": "emr"},
    ],
}


def get_emr_settings_override(**kwargs):
    EMR_SETTINGS_OVERRIDE["Name"] = "dse_{table}_nessie".format(table=kwargs["table"])
    # string -> dictionary
    kwargs["emr_settings_override"] = ast.literal_eval(kwargs["emr_settings_override"])
    EMR_SETTINGS_OVERRIDE["Instances"]["InstanceFleets"] = kwargs["emr_settings_override"][
        "emr_instance_fleets_config"
    ]
    EMR_SETTINGS_OVERRIDE["StepConcurrencyLevel"] = kwargs["emr_settings_override"][
        "step_concurrency_level"
    ]
    return EMR_SETTINGS_OVERRIDE


def config_validator(**kwargs):
    try:
        config = ast.literal_eval(kwargs["config"])
        logging.info("Validating the config string", config)
        if config["step_concurrency_level"]:
            return True
        else:
            return False
    except Exception as e:
        logging.exception("Error: ", e)
        return False


def fetch_spark_steps(flow_id, flow_type, nessie_version, spark_submit_mode="cluster", **kwargs):

    logging.info(f"Creating Spark Submit Step for flow_id {flow_id} in {spark_submit_mode} mode")

    if spark_submit_mode == "client":
        spark_submit = [
            "spark-submit",
            "--deploy-mode",
            f"{spark_submit_mode}",
            "--master",
            "yarn",
            "--conf",
            "spark.serializer=org.apache.spark.serializer.KryoSerializer",
            "--conf",
            "spark.sql.hive.convertMetastoreParquet=false",
            "--conf",
            "spark.driver.memory=3g",
            "--conf",
            "spark.driver.extraJavaOptions=-XX:NewSize=1g -XX:SurvivorRatio=2 -XX:+UseCompressedOops -XX:+UseConcMarkSweepGC -XX:+UseParNewGC -XX:CMSInitiatingOccupancyFraction=70 -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -XX:+PrintGCDateStamps -XX:+PrintGCApplicationStoppedTime -XX:+PrintGCApplicationConcurrentTime -XX:+PrintTenuringDistribution -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/tmp/hoodie-heapdump.hprof",
            "--conf",
            "spark.yarn.heterogeneousExecutors.enabled=false",
            "--conf",
            "spark.kryo.registrator=org.apache.spark.HoodieSparkKryoRegistrar",
            "--conf",
            "spark.sql.catalog.spark_catalog=org.apache.spark.sql.hudi.catalog.HoodieCatalog",
            "--conf",
            "spark.sql.extensions=org.apache.spark.sql.hudi.HoodieSparkSessionExtension",
            "--class",
            "com.grofers.nessie.Nessie",
            f"s3://prod-dse-srp-configs/jars/nessie/Nessie-assembly-{nessie_version}.jar",
            f"{flow_type}",
            "--flowId",
            f"{flow_id}",
        ]

    elif spark_submit_mode == "cluster":
        spark_submit = (
            "spark-submit",
            "--name",
            f"{flow_type}-{flow_id}",
            "--deploy-mode",
            f"{spark_submit_mode}",
            "--master",
            "yarn",
            "--conf",
            "spark.serializer=org.apache.spark.serializer.KryoSerializer",
            "--conf",
            "spark.sql.hive.convertMetastoreParquet=false",
            "--conf",
            "spark.yarn.heterogeneousExecutors.enabled=false",
            "--conf",
            "spark.kryo.registrator=org.apache.spark.HoodieSparkKryoRegistrar",
            "--conf",
            "spark.sql.catalog.spark_catalog=org.apache.spark.sql.hudi.catalog.HoodieCatalog",
            "--conf",
            "spark.sql.extensions=org.apache.spark.sql.hudi.HoodieSparkSessionExtension",
            "--class",
            "com.grofers.nessie.Nessie",
            f"s3://prod-dse-srp-configs/jars/nessie/Nessie-assembly-{nessie_version}.jar",
            f"{flow_type}",
            "--flowId",
            f"{flow_id}",
        )

    else:
        raise NotImplementedError("Incorrect spark submit mode provided")

    return [
        {
            "Name": f"""{flow_id}-{flow_type}-{dt.datetime.now().timestamp()}""",
            "ActionOnFailure": "CONTINUE",
            "HadoopJarStep": {
                "Jar": "command-runner.jar",
                "Args": spark_submit,
            },
        }
    ]


def failure_alerts(context):
    slack_failure_alert(context)


args = {
    "owner": "<EMAIL>",
    "on_failure_callback": failure_alerts,
    "start_date": dt.datetime.strptime("2024-06-28T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": dt.datetime.strptime("2026-06-28T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "slack_id": "S089D0GQRCJ",
}

dag = DAG(
    dag_id="de_replicate_etl_seller_1_v1",
    default_args=args,
    schedule_interval="30 */1 * * *",
    tags=["de", "replicate", "seller_1", "de", "replicate", "etl"],
    catchup=False,
    concurrency=15,
    max_active_runs=1,
)

cluster_config_task = DynamicClusterConfigOperator(
    task_id="cluster_config_task_seller_1",
    dag=dag,
    flows_to_run=branch_op_flows,
    connection_alias="nessie",
    queue="celery",
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
)


short_circuit_task = ShortCircuitOperator(
    task_id="short_circuit_seller_1",
    op_kwargs={
        "config": "{{ task_instance.xcom_pull('cluster_config_task_seller_1', key='return_value') }}"
    },
    python_callable=config_validator,
    dag=dag,
    queue="celery",
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
)


emr_cluster_config_task = PythonOperator(
    task_id="emr_cluster_config_task_seller_1",
    op_kwargs={
        "table": "seller_1",
        "emr_settings_override": "{{ task_instance.xcom_pull('cluster_config_task_seller_1', key='return_value') }}",
    },
    python_callable=get_emr_settings_override,
    dag=dag,
    queue="celery",
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
)


cluster_create_task = EmrCreateJobFlowOperator(
    task_id="create_emr_cluster_seller_1",
    job_flow_overrides="{{ task_instance.xcom_pull('emr_cluster_config_task_seller_1', key='return_value') }}",
    aws_conn_id="aws_default",
    emr_conn_id="emr_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


cluster_terminate_task = EmrTerminateJobFlowOperator(
    task_id="terminate_emr_cluster_seller_1",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    trigger_rule="all_done",
    priority_weight=3,
)


flows_branching_task = BranchSRPOperator(
    task_id="flows_branching_seller_1",
    dag=dag,
    flows_to_run=branch_op_flows,
    connection_alias="nessie",
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


s3_to_hudi_task_1 = EmrAddStepsOperator(
    task_id="s3_to_hudi_seller_apob_facility",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="seller.apob_facility.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_1 = EmrStateSensorAsync(
    task_id="load_to_lake_seller_apob_facility_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_seller_apob_facility', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_1 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_seller_apob_facility",
    topic_name="postgres.seller.public.apob_facility",
    database="seller",
    lake_schemaname="lake_seller",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_1
    >> s3_to_hudi_sensor_1
    >> cluster_terminate_task
)

s3_to_hudi_sensor_1 >> create_view_on_presto_1


s3_to_hudi_task_2 = EmrAddStepsOperator(
    task_id="s3_to_hudi_seller_campaigns",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="seller.campaigns.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_2 = EmrStateSensorAsync(
    task_id="load_to_lake_seller_campaigns_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_seller_campaigns', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_2 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_seller_campaigns",
    topic_name="postgres.seller.public.campaigns",
    database="seller",
    lake_schemaname="lake_seller",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_2
    >> s3_to_hudi_sensor_2
    >> cluster_terminate_task
)

s3_to_hudi_sensor_2 >> create_view_on_presto_2


s3_to_hudi_task_3 = EmrAddStepsOperator(
    task_id="s3_to_hudi_seller_cart",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="seller.cart.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_3 = EmrStateSensorAsync(
    task_id="load_to_lake_seller_cart_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_seller_cart', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_3 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_seller_cart",
    topic_name="postgres.seller.public.cart",
    database="seller",
    lake_schemaname="lake_seller",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_3
    >> s3_to_hudi_sensor_3
    >> cluster_terminate_task
)

s3_to_hudi_sensor_3 >> create_view_on_presto_3


s3_to_hudi_task_4 = EmrAddStepsOperator(
    task_id="s3_to_hudi_seller_cart_order_mapping",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="seller.cart_order_mapping.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_4 = EmrStateSensorAsync(
    task_id="load_to_lake_seller_cart_order_mapping_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_seller_cart_order_mapping', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_4 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_seller_cart_order_mapping",
    topic_name="postgres.seller.public.cart_order_mapping",
    database="seller",
    lake_schemaname="lake_seller",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_4
    >> s3_to_hudi_sensor_4
    >> cluster_terminate_task
)

s3_to_hudi_sensor_4 >> create_view_on_presto_4


s3_to_hudi_task_5 = EmrAddStepsOperator(
    task_id="s3_to_hudi_seller_cart_transaction_log",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="seller.cart_transaction_log.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_5 = EmrStateSensorAsync(
    task_id="load_to_lake_seller_cart_transaction_log_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_seller_cart_transaction_log', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_5 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_seller_cart_transaction_log",
    topic_name="postgres.seller.public.cart_transaction_log",
    database="seller",
    lake_schemaname="lake_seller",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_5
    >> s3_to_hudi_sensor_5
    >> cluster_terminate_task
)

s3_to_hudi_sensor_5 >> create_view_on_presto_5


s3_to_hudi_task_6 = EmrAddStepsOperator(
    task_id="s3_to_hudi_seller_facilities",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="seller.facilities.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_6 = EmrStateSensorAsync(
    task_id="load_to_lake_seller_facilities_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_seller_facilities', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_6 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_seller_facilities",
    topic_name="postgres.seller.public.facilities",
    database="seller",
    lake_schemaname="lake_seller",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_6
    >> s3_to_hudi_sensor_6
    >> cluster_terminate_task
)

s3_to_hudi_sensor_6 >> create_view_on_presto_6


s3_to_hudi_task_7 = EmrAddStepsOperator(
    task_id="s3_to_hudi_seller_faq",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="seller.faq.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_7 = EmrStateSensorAsync(
    task_id="load_to_lake_seller_faq_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_seller_faq', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_7 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_seller_faq",
    topic_name="postgres.seller.public.faq",
    database="seller",
    lake_schemaname="lake_seller",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_7
    >> s3_to_hudi_sensor_7
    >> cluster_terminate_task
)

s3_to_hudi_sensor_7 >> create_view_on_presto_7


s3_to_hudi_task_8 = EmrAddStepsOperator(
    task_id="s3_to_hudi_seller_item_facility_mapping",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="seller.item_facility_mapping.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_8 = EmrStateSensorAsync(
    task_id="load_to_lake_seller_item_facility_mapping_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_seller_item_facility_mapping', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_8 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_seller_item_facility_mapping",
    topic_name="postgres.seller.public.item_facility_mapping",
    database="seller",
    lake_schemaname="lake_seller",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_8
    >> s3_to_hudi_sensor_8
    >> cluster_terminate_task
)

s3_to_hudi_sensor_8 >> create_view_on_presto_8


s3_to_hudi_task_9 = EmrAddStepsOperator(
    task_id="s3_to_hudi_seller_item_outlet_inventory",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="seller.item_outlet_inventory.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_9 = EmrStateSensorAsync(
    task_id="load_to_lake_seller_item_outlet_inventory_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_seller_item_outlet_inventory', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_9 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_seller_item_outlet_inventory",
    topic_name="postgres.seller.public.item_outlet_inventory",
    database="seller",
    lake_schemaname="lake_seller",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_9
    >> s3_to_hudi_sensor_9
    >> cluster_terminate_task
)

s3_to_hudi_sensor_9 >> create_view_on_presto_9


s3_to_hudi_task_10 = EmrAddStepsOperator(
    task_id="s3_to_hudi_seller_product_expansion_assessment",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="seller.product_expansion_assessment.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_10 = EmrStateSensorAsync(
    task_id="load_to_lake_seller_product_expansion_assessment_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_seller_product_expansion_assessment', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_10 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_seller_product_expansion_assessment",
    topic_name="postgres.seller.public.product_expansion_assessment",
    database="seller",
    lake_schemaname="lake_seller",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_10
    >> s3_to_hudi_sensor_10
    >> cluster_terminate_task
)

s3_to_hudi_sensor_10 >> create_view_on_presto_10


s3_to_hudi_task_11 = EmrAddStepsOperator(
    task_id="s3_to_hudi_seller_queue_items",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="seller.queue_items.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_11 = EmrStateSensorAsync(
    task_id="load_to_lake_seller_queue_items_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_seller_queue_items', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_11 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_seller_queue_items",
    topic_name="postgres.seller.public.queue_items",
    database="seller",
    lake_schemaname="lake_seller",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_11
    >> s3_to_hudi_sensor_11
    >> cluster_terminate_task
)

s3_to_hudi_sensor_11 >> create_view_on_presto_11


s3_to_hudi_task_12 = EmrAddStepsOperator(
    task_id="s3_to_hudi_seller_rate_card_item",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="seller.rate_card_item.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_12 = EmrStateSensorAsync(
    task_id="load_to_lake_seller_rate_card_item_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_seller_rate_card_item', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_12 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_seller_rate_card_item",
    topic_name="postgres.seller.public.rate_card_item",
    database="seller",
    lake_schemaname="lake_seller",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_12
    >> s3_to_hudi_sensor_12
    >> cluster_terminate_task
)

s3_to_hudi_sensor_12 >> create_view_on_presto_12


s3_to_hudi_task_13 = EmrAddStepsOperator(
    task_id="s3_to_hudi_seller_sales_summary",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="seller.sales_summary.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_13 = EmrStateSensorAsync(
    task_id="load_to_lake_seller_sales_summary_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_seller_sales_summary', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_13 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_seller_sales_summary",
    topic_name="postgres.seller.public.sales_summary",
    database="seller",
    lake_schemaname="lake_seller",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_13
    >> s3_to_hudi_sensor_13
    >> cluster_terminate_task
)

s3_to_hudi_sensor_13 >> create_view_on_presto_13


s3_to_hudi_task_14 = EmrAddStepsOperator(
    task_id="s3_to_hudi_seller_scheduled_jobs",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="seller.scheduled_jobs.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_14 = EmrStateSensorAsync(
    task_id="load_to_lake_seller_scheduled_jobs_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_seller_scheduled_jobs', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_14 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_seller_scheduled_jobs",
    topic_name="postgres.seller.public.scheduled_jobs",
    database="seller",
    lake_schemaname="lake_seller",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_14
    >> s3_to_hudi_sensor_14
    >> cluster_terminate_task
)

s3_to_hudi_sensor_14 >> create_view_on_presto_14


s3_to_hudi_task_15 = EmrAddStepsOperator(
    task_id="s3_to_hudi_seller_seller",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="seller.seller.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_15 = EmrStateSensorAsync(
    task_id="load_to_lake_seller_seller_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_seller_seller', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_15 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_seller_seller",
    topic_name="postgres.seller.public.seller",
    database="seller",
    lake_schemaname="lake_seller",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_15
    >> s3_to_hudi_sensor_15
    >> cluster_terminate_task
)

s3_to_hudi_sensor_15 >> create_view_on_presto_15


s3_to_hudi_task_16 = EmrAddStepsOperator(
    task_id="s3_to_hudi_seller_seller_advertiser_mapping",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="seller.seller_advertiser_mapping.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_16 = EmrStateSensorAsync(
    task_id="load_to_lake_seller_seller_advertiser_mapping_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_seller_seller_advertiser_mapping', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_16 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_seller_seller_advertiser_mapping",
    topic_name="postgres.seller.public.seller_advertiser_mapping",
    database="seller",
    lake_schemaname="lake_seller",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_16
    >> s3_to_hudi_sensor_16
    >> cluster_terminate_task
)

s3_to_hudi_sensor_16 >> create_view_on_presto_16


s3_to_hudi_task_17 = EmrAddStepsOperator(
    task_id="s3_to_hudi_seller_seller_apob",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="seller.seller_apob.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_17 = EmrStateSensorAsync(
    task_id="load_to_lake_seller_seller_apob_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_seller_seller_apob', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_17 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_seller_seller_apob",
    topic_name="postgres.seller.public.seller_apob",
    database="seller",
    lake_schemaname="lake_seller",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_17
    >> s3_to_hudi_sensor_17
    >> cluster_terminate_task
)

s3_to_hudi_sensor_17 >> create_view_on_presto_17


s3_to_hudi_task_18 = EmrAddStepsOperator(
    task_id="s3_to_hudi_seller_seller_brand_detail",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="seller.seller_brand_detail.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_18 = EmrStateSensorAsync(
    task_id="load_to_lake_seller_seller_brand_detail_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_seller_seller_brand_detail', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_18 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_seller_seller_brand_detail",
    topic_name="postgres.seller.public.seller_brand_detail",
    database="seller",
    lake_schemaname="lake_seller",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_18
    >> s3_to_hudi_sensor_18
    >> cluster_terminate_task
)

s3_to_hudi_sensor_18 >> create_view_on_presto_18


s3_to_hudi_task_19 = EmrAddStepsOperator(
    task_id="s3_to_hudi_seller_seller_document_info",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="seller.seller_document_info.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_19 = EmrStateSensorAsync(
    task_id="load_to_lake_seller_seller_document_info_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_seller_seller_document_info', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_19 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_seller_seller_document_info",
    topic_name="postgres.seller.public.seller_document_info",
    database="seller",
    lake_schemaname="lake_seller",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_19
    >> s3_to_hudi_sensor_19
    >> cluster_terminate_task
)

s3_to_hudi_sensor_19 >> create_view_on_presto_19


s3_to_hudi_task_20 = EmrAddStepsOperator(
    task_id="s3_to_hudi_seller_seller_order_level_pos_sales_info",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="seller.seller_order_level_pos_sales_info.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_20 = EmrStateSensorAsync(
    task_id="load_to_lake_seller_seller_order_level_pos_sales_info_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_seller_seller_order_level_pos_sales_info', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_20 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_seller_seller_order_level_pos_sales_info",
    topic_name="postgres.seller.public.seller_order_level_pos_sales_info",
    database="seller",
    lake_schemaname="lake_seller",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_20
    >> s3_to_hudi_sensor_20
    >> cluster_terminate_task
)

s3_to_hudi_sensor_20 >> create_view_on_presto_20


s3_to_hudi_task_21 = EmrAddStepsOperator(
    task_id="s3_to_hudi_seller_seller_order_level_sales_info",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="seller.seller_order_level_sales_info.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_21 = EmrStateSensorAsync(
    task_id="load_to_lake_seller_seller_order_level_sales_info_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_seller_seller_order_level_sales_info', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_21 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_seller_seller_order_level_sales_info",
    topic_name="postgres.seller.public.seller_order_level_sales_info",
    database="seller",
    lake_schemaname="lake_seller",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_21
    >> s3_to_hudi_sensor_21
    >> cluster_terminate_task
)

s3_to_hudi_sensor_21 >> create_view_on_presto_21


s3_to_hudi_task_22 = EmrAddStepsOperator(
    task_id="s3_to_hudi_seller_seller_payout_commission",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="seller.seller_payout_commission.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_22 = EmrStateSensorAsync(
    task_id="load_to_lake_seller_seller_payout_commission_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_seller_seller_payout_commission', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_22 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_seller_seller_payout_commission",
    topic_name="postgres.seller.public.seller_payout_commission",
    database="seller",
    lake_schemaname="lake_seller",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_22
    >> s3_to_hudi_sensor_22
    >> cluster_terminate_task
)

s3_to_hudi_sensor_22 >> create_view_on_presto_22


s3_to_hudi_task_23 = EmrAddStepsOperator(
    task_id="s3_to_hudi_seller_seller_product_mappings",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="seller.seller_product_mappings.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_23 = EmrStateSensorAsync(
    task_id="load_to_lake_seller_seller_product_mappings_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_seller_seller_product_mappings', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_23 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_seller_seller_product_mappings",
    topic_name="postgres.seller.public.seller_product_mappings",
    database="seller",
    lake_schemaname="lake_seller",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_23
    >> s3_to_hudi_sensor_23
    >> cluster_terminate_task
)

s3_to_hudi_sensor_23 >> create_view_on_presto_23


s3_to_hudi_task_24 = EmrAddStepsOperator(
    task_id="s3_to_hudi_seller_seller_product_request_mappings",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="seller.seller_product_request_mappings.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_24 = EmrStateSensorAsync(
    task_id="load_to_lake_seller_seller_product_request_mappings_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_seller_seller_product_request_mappings', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_24 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_seller_seller_product_request_mappings",
    topic_name="postgres.seller.public.seller_product_request_mappings",
    database="seller",
    lake_schemaname="lake_seller",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_24
    >> s3_to_hudi_sensor_24
    >> cluster_terminate_task
)

s3_to_hudi_sensor_24 >> create_view_on_presto_24


s3_to_hudi_task_25 = EmrAddStepsOperator(
    task_id="s3_to_hudi_seller_seller_t_and_c",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="seller.seller_t_and_c.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_25 = EmrStateSensorAsync(
    task_id="load_to_lake_seller_seller_t_and_c_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_seller_seller_t_and_c', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_25 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_seller_seller_t_and_c",
    topic_name="postgres.seller.public.seller_t_and_c",
    database="seller",
    lake_schemaname="lake_seller",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_25
    >> s3_to_hudi_sensor_25
    >> cluster_terminate_task
)

s3_to_hudi_sensor_25 >> create_view_on_presto_25


s3_to_hudi_task_26 = EmrAddStepsOperator(
    task_id="s3_to_hudi_seller_store_group_definitions",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="seller.store_group_definitions.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_26 = EmrStateSensorAsync(
    task_id="load_to_lake_seller_store_group_definitions_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_seller_store_group_definitions', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_26 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_seller_store_group_definitions",
    topic_name="postgres.seller.public.store_group_definitions",
    database="seller",
    lake_schemaname="lake_seller",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_26
    >> s3_to_hudi_sensor_26
    >> cluster_terminate_task
)

s3_to_hudi_sensor_26 >> create_view_on_presto_26


s3_to_hudi_task_27 = EmrAddStepsOperator(
    task_id="s3_to_hudi_seller_task",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="seller.task.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_27 = EmrStateSensorAsync(
    task_id="load_to_lake_seller_task_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_seller_task', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_27 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_seller_task",
    topic_name="postgres.seller.public.task",
    database="seller",
    lake_schemaname="lake_seller",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_27
    >> s3_to_hudi_sensor_27
    >> cluster_terminate_task
)

s3_to_hudi_sensor_27 >> create_view_on_presto_27


s3_to_hudi_task_28 = EmrAddStepsOperator(
    task_id="s3_to_hudi_seller_user_seller_mapping",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="seller.user_seller_mapping.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_28 = EmrStateSensorAsync(
    task_id="load_to_lake_seller_user_seller_mapping_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_seller_user_seller_mapping', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_28 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_seller_user_seller_mapping",
    topic_name="postgres.seller.public.user_seller_mapping",
    database="seller",
    lake_schemaname="lake_seller",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_28
    >> s3_to_hudi_sensor_28
    >> cluster_terminate_task
)

s3_to_hudi_sensor_28 >> create_view_on_presto_28


s3_to_hudi_task_29 = EmrAddStepsOperator(
    task_id="s3_to_hudi_seller_workflow",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="seller.workflow.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_29 = EmrStateSensorAsync(
    task_id="load_to_lake_seller_workflow_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_seller_workflow', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_29 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_seller_workflow",
    topic_name="postgres.seller.public.workflow",
    database="seller",
    lake_schemaname="lake_seller",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_29
    >> s3_to_hudi_sensor_29
    >> cluster_terminate_task
)

s3_to_hudi_sensor_29 >> create_view_on_presto_29


s3_to_hudi_task_30 = EmrAddStepsOperator(
    task_id="s3_to_hudi_seller_recall_request_batch_pro_detail",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="seller.recall_request_batch_pro_detail.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_30 = EmrStateSensorAsync(
    task_id="load_to_lake_seller_recall_request_batch_pro_detail_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_seller_recall_request_batch_pro_detail', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_30 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_seller_recall_request_batch_pro_detail",
    topic_name="postgres.seller.public.recall_request_batch_pro_detail",
    database="seller",
    lake_schemaname="lake_seller",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_30
    >> s3_to_hudi_sensor_30
    >> cluster_terminate_task
)

s3_to_hudi_sensor_30 >> create_view_on_presto_30


s3_to_hudi_task_31 = EmrAddStepsOperator(
    task_id="s3_to_hudi_seller_recall_request_batch_log",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="seller.recall_request_batch_log.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_31 = EmrStateSensorAsync(
    task_id="load_to_lake_seller_recall_request_batch_log_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_seller_recall_request_batch_log', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_31 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_seller_recall_request_batch_log",
    topic_name="postgres.seller.public.recall_request_batch_log",
    database="seller",
    lake_schemaname="lake_seller",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_31
    >> s3_to_hudi_sensor_31
    >> cluster_terminate_task
)

s3_to_hudi_sensor_31 >> create_view_on_presto_31


s3_to_hudi_task_32 = EmrAddStepsOperator(
    task_id="s3_to_hudi_seller_recall_request_batch_item",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="seller.recall_request_batch_item.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_32 = EmrStateSensorAsync(
    task_id="load_to_lake_seller_recall_request_batch_item_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_seller_recall_request_batch_item', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_32 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_seller_recall_request_batch_item",
    topic_name="postgres.seller.public.recall_request_batch_item",
    database="seller",
    lake_schemaname="lake_seller",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_32
    >> s3_to_hudi_sensor_32
    >> cluster_terminate_task
)

s3_to_hudi_sensor_32 >> create_view_on_presto_32


s3_to_hudi_task_33 = EmrAddStepsOperator(
    task_id="s3_to_hudi_seller_recall_request_batch",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="seller.recall_request_batch.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_33 = EmrStateSensorAsync(
    task_id="load_to_lake_seller_recall_request_batch_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_seller_recall_request_batch', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_33 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_seller_recall_request_batch",
    topic_name="postgres.seller.public.recall_request_batch",
    database="seller",
    lake_schemaname="lake_seller",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_33
    >> s3_to_hudi_sensor_33
    >> cluster_terminate_task
)

s3_to_hudi_sensor_33 >> create_view_on_presto_33


s3_to_hudi_task_34 = EmrAddStepsOperator(
    task_id="s3_to_hudi_seller_recall_request",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="seller.recall_request.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_34 = EmrStateSensorAsync(
    task_id="load_to_lake_seller_recall_request_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_seller_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_seller_recall_request', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_34 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_seller_recall_request",
    topic_name="postgres.seller.public.recall_request",
    database="seller",
    lake_schemaname="lake_seller",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_34
    >> s3_to_hudi_sensor_34
    >> cluster_terminate_task
)

s3_to_hudi_sensor_34 >> create_view_on_presto_34


if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = dt.timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
