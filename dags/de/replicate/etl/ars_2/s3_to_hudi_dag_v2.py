# -*- coding: utf-8 -*-

import datetime as dt
import logging

from airflow import DAG
from airflow.operators.dagrun_operator import TriggerDagRunOperator
from kubernetes.client import models as k8s
from metrics_plugin import (
    EmrStateSensorAsync,
    SRPClusterMappingOperatorV2,
    SRPEmrBulkAddStepsOperator,
    PrestoCreateViewOperator,
)

import pencilbox as pb


branch_op_flows = {
    "ars.backend_facility_transfer_attributes.v3": {
        "kafka_topic": "mysql.ars.ars.backend_facility_transfer_attributes",
        "downstream_task_id": "ars_backend_facility_transfer_attributes_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.backend_facility_transfer_attributes.v3",
        ),
    },
    "ars.backend_frontend_transfer_attributes.v2": {
        "kafka_topic": "mysql.ars.ars.backend_frontend_transfer_attributes",
        "downstream_task_id": "ars_backend_frontend_transfer_attributes_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.backend_frontend_transfer_attributes.v2",
        ),
    },
    "ars.bulk_facility_transfer_days.v2": {
        "kafka_topic": "mysql.ars.ars.bulk_facility_transfer_days",
        "downstream_task_id": "ars_bulk_facility_transfer_days_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.bulk_facility_transfer_days.v2",
        ),
    },
    "ars.bulk_process_bump_tracker.v1": {
        "kafka_topic": "mysql.ars.ars.bulk_process_bump_tracker",
        "downstream_task_id": "ars_bulk_process_bump_tracker_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.bulk_process_bump_tracker.v1",
        ),
    },
    "ars.case_incorporation_result.v3": {
        "kafka_topic": "mysql.ars.ars.case_incorporation_result",
        "downstream_task_id": "ars_case_incorporation_result_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.case_incorporation_result.v3",
        ),
    },
    "ars.final_indent.v3": {
        "kafka_topic": "mysql.ars.ars.final_indent",
        "downstream_task_id": "ars_final_indent_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.final_indent.v3",
        ),
        "spark_application_jar": "s3://prod-dse-srp-configs/jars/nessie/Nessie-assembly-2.1.2-high-resources.jar",
        "spark_submit_options": {
            "--conf": {
                "spark.serializer": "org.apache.spark.serializer.KryoSerializer",
                "spark.kryo.registrator": "org.apache.spark.HoodieSparkKryoRegistrar",
                "spark.sql.catalog.spark_catalog": "org.apache.spark.sql.hudi.catalog.HoodieCatalog",
                "spark.sql.extensions": "org.apache.spark.sql.hudi.HoodieSparkSessionExtension",
                "spark.yarn.heterogeneousExecutors.enabled": "false",
                "spark.sql.hive.convertMetastoreParquet": "false",
            },
        },
    },
    "ars.fixed_indent.v1": {
        "kafka_topic": "mysql.ars.ars.fixed_indent",
        "downstream_task_id": "ars_fixed_indent_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.fixed_indent.v1",
        ),
    },
    "ars.frontend_inward_capacity.v1": {
        "kafka_topic": "mysql.ars.ars.frontend_inward_capacity",
        "downstream_task_id": "ars_frontend_inward_capacity_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.frontend_inward_capacity.v1",
        ),
    },
    "ars.growth_factor.v1": {
        "kafka_topic": "mysql.ars.ars.growth_factor",
        "downstream_task_id": "ars_growth_factor_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.growth_factor.v1",
        ),
    },
    "ars.inter_facility_transfer_days.v1": {
        "kafka_topic": "mysql.ars.ars.inter_facility_transfer_days",
        "downstream_task_id": "ars_inter_facility_transfer_days_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.inter_facility_transfer_days.v1",
        ),
    },
    "ars.inter_facility_transfer_days_log.v1": {
        "kafka_topic": "mysql.ars.ars.inter_facility_transfer_days_log",
        "downstream_task_id": "ars_inter_facility_transfer_days_log_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.inter_facility_transfer_days_log.v1",
        ),
    },
    "ars.item_cpd_replication.v1": {
        "kafka_topic": "mysql.ars.ars.item_cpd_replication",
        "downstream_task_id": "ars_item_cpd_replication_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.item_cpd_replication.v1",
        ),
    },
    "ars.job_run.v3": {
        "kafka_topic": "mysql.ars.ars.job_run",
        "downstream_task_id": "ars_job_run_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.job_run.v3",
        ),
    },
    "ars.min_bump_algo_result.v3": {
        "kafka_topic": "mysql.ars.ars.min_bump_algo_result",
        "downstream_task_id": "ars_min_bump_algo_result_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.min_bump_algo_result.v3",
        ),
    },
    "ars.outlet.v3": {
        "kafka_topic": "mysql.ars.ars.outlet",
        "downstream_task_id": "ars_outlet_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.outlet.v3",
        ),
    },
    "ars.outlet_item_aps_derived_cpd.v1": {
        "kafka_topic": "mysql.ars.ars.outlet_item_aps_derived_cpd",
        "downstream_task_id": "ars_outlet_item_aps_derived_cpd_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.outlet_item_aps_derived_cpd.v1",
        ),
    },
    "ars.outlet_item_group_mapping.v1": {
        "kafka_topic": "mysql.ars.ars.outlet_item_group_mapping",
        "downstream_task_id": "ars_outlet_item_group_mapping_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.outlet_item_group_mapping.v1",
        ),
    },
    "ars.outlet_item_open_sto.v2": {
        "kafka_topic": "mysql.ars.ars.outlet_item_open_sto",
        "downstream_task_id": "ars_outlet_item_open_sto_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.outlet_item_open_sto.v2",
        ),
    },
    "ars.outlet_vendor_item_tat_days.v3": {
        "kafka_topic": "mysql.ars.ars.outlet_vendor_item_tat_days",
        "downstream_task_id": "ars_outlet_vendor_item_tat_days_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.outlet_vendor_item_tat_days.v3",
        ),
    },
    "ars.reasons.v2": {
        "kafka_topic": "mysql.ars.ars.reasons",
        "downstream_task_id": "ars_reasons_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.reasons.v2",
        ),
    },
    "ars.slot_flushing.v1": {
        "kafka_topic": "mysql.ars.ars.slot_flushing",
        "downstream_task_id": "ars_slot_flushing_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.slot_flushing.v1",
        ),
    },
    "ars.sto_dispatch_time.v2": {
        "kafka_topic": "mysql.ars.ars.sto_dispatch_time",
        "downstream_task_id": "ars_sto_dispatch_time_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.sto_dispatch_time.v2",
        ),
    },
    "ars.table_updates.v3": {
        "kafka_topic": "mysql.ars.ars.table_updates",
        "downstream_task_id": "ars_table_updates_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.table_updates.v3",
        ),
    },
    "ars.transfer_case_size.v2": {
        "kafka_topic": "mysql.ars.ars.transfer_case_size",
        "downstream_task_id": "ars_transfer_case_size_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.transfer_case_size.v2",
        ),
    },
}

# Default values for all flow ids, to override pass `spark_application_jar` in `branch_op_flows` for respective flow id
spark_application_jar = "s3://prod-dse-srp-configs/jars/nessie/Nessie-assembly-2.1.2.jar"

# Default values for all flow ids, to override any value pass `spark_submit_options` in `branch_op_flows` for respective flow id
spark_submit_options = {
    "--deploy-mode": "cluster",
    "--master": "yarn",
    "--conf": {
        "spark.serializer": "org.apache.spark.serializer.KryoSerializer",
        "spark.kryo.registrator": "org.apache.spark.HoodieSparkKryoRegistrar",
        "spark.sql.catalog.spark_catalog": "org.apache.spark.sql.hudi.catalog.HoodieCatalog",
        "spark.sql.extensions": "org.apache.spark.sql.hudi.HoodieSparkSessionExtension",
        "spark.yarn.heterogeneousExecutors.enabled": "false",
        "spark.sql.hive.convertMetastoreParquet": "false",
    },
    "--class": "com.grofers.nessie.Nessie",
}


def slack_failure_alert(context):
    dag_id = context.get("task_instance").dag_id
    run_id = context.get("run_id")
    task_id = context.get("task_instance").task_id
    log_url = context.get("task_instance").log_url.replace("http://", "https://")
    exception = str(context.get("exception"))
    override_channel_mapping = {
        "terminated_with_errors": "bl-data-spot-interruptions",
        "spot": "bl-data-spot-interruptions",
    }

    slack_failure_msg = """
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner*: <!subteam^S089D0GQRCJ>
        *Log Url*: {log_url}
        {exception}
    """.format(
        dag_id=dag_id,
        run_id=run_id,
        task_id=task_id,
        log_url=log_url,
        exception=exception,
    )

    slack_alert_configs = [
        {"channel": "bl-data-alerts-p1"},
    ]

    # Check if exception exists and contains any key from override_channel_mapping
    if exception and override_channel_mapping:
        for error_keyword, override_channel in override_channel_mapping.items():
            if error_keyword.lower() in exception.lower():
                # Override all channels with the mapped channel
                slack_alert_configs = [{"channel": override_channel}]
                break

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(channel=channel, text=slack_failure_msg, source_info=False)
        except Exception as e:
            logging.warning(e)
            message = (
                f"<!subteam^S089D0GQRCJ> `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(channel="bl-data-alerts-p1", text=message, source_info=False)


def failure_alerts(context):
    slack_failure_alert(context)


args = {
    "owner": "<EMAIL>",
    "on_failure_callback": failure_alerts,
    "start_date": dt.datetime.strptime("2021-12-20T09:30:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": dt.datetime.strptime("2026-09-15T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "slack_id": "S089D0GQRCJ",
}

dag = DAG(
    dag_id="de_replicate_etl_ars_2_s3_to_hudi_v2",
    default_args=args,
    schedule_interval="13,43 * * * *",
    tags=["de", "replicate", "ars_2"],
    catchup=False,
    concurrency=15,
    max_active_runs=1,
    render_template_as_native_obj=True,
)


s3_hudi_cluster_allocation_task = SRPClusterMappingOperatorV2(
    task_id="cluster_allocation_ars_2_s3_to_hudi",
    dag=dag,
    connection_alias="nessie",
    flows_to_run=branch_op_flows,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


s3_hudi_bulk_submit_emr_steps_task = SRPEmrBulkAddStepsOperator(
    task_id="bulk_step_submission_ars_2_s3_to_hudi",
    dag=dag,
    aws_conn_id="aws_default",
    srp_flows_info="{{ task_instance.xcom_pull('cluster_allocation_ars_2_s3_to_hudi', key='flows_info') }}",
    spark_application_jar=spark_application_jar,
    spark_submit_options=spark_submit_options,
    srp_task_id_cluster_mapping="{{ task_instance.xcom_pull('cluster_allocation_ars_2_s3_to_hudi', key='return_value') }}",
    queue="celery",
)


s3_to_hudi_emr_sensor_1 = EmrStateSensorAsync(
    task_id="ars_backend_facility_transfer_attributes_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.backend_facility_transfer_attributes.v3', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.backend_facility_transfer_attributes.v3', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_1 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_backend_facility_transfer_attributes",
    topic_name="mysql.ars.ars.backend_facility_transfer_attributes",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_1
    >> create_view_on_presto_1
)


s3_to_hudi_emr_sensor_2 = EmrStateSensorAsync(
    task_id="ars_backend_frontend_transfer_attributes_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.backend_frontend_transfer_attributes.v2', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.backend_frontend_transfer_attributes.v2', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_2 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_backend_frontend_transfer_attributes",
    topic_name="mysql.ars.ars.backend_frontend_transfer_attributes",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_2
    >> create_view_on_presto_2
)


s3_to_hudi_emr_sensor_3 = EmrStateSensorAsync(
    task_id="ars_bulk_facility_transfer_days_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.bulk_facility_transfer_days.v2', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.bulk_facility_transfer_days.v2', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_3 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_bulk_facility_transfer_days",
    topic_name="mysql.ars.ars.bulk_facility_transfer_days",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_3
    >> create_view_on_presto_3
)


s3_to_hudi_emr_sensor_4 = EmrStateSensorAsync(
    task_id="ars_bulk_process_bump_tracker_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.bulk_process_bump_tracker.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.bulk_process_bump_tracker.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_4 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_bulk_process_bump_tracker",
    topic_name="mysql.ars.ars.bulk_process_bump_tracker",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_4
    >> create_view_on_presto_4
)


s3_to_hudi_emr_sensor_5 = EmrStateSensorAsync(
    task_id="ars_case_incorporation_result_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.case_incorporation_result.v3', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.case_incorporation_result.v3', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_5 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_case_incorporation_result",
    topic_name="mysql.ars.ars.case_incorporation_result",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_5
    >> create_view_on_presto_5
)


s3_to_hudi_emr_sensor_6 = EmrStateSensorAsync(
    task_id="ars_final_indent_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.final_indent.v3', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.final_indent.v3', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_6 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_final_indent",
    topic_name="mysql.ars.ars.final_indent",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_6
    >> create_view_on_presto_6
)


s3_to_hudi_emr_sensor_7 = EmrStateSensorAsync(
    task_id="ars_fixed_indent_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.fixed_indent.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.fixed_indent.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_7 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_fixed_indent",
    topic_name="mysql.ars.ars.fixed_indent",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_7
    >> create_view_on_presto_7
)


s3_to_hudi_emr_sensor_8 = EmrStateSensorAsync(
    task_id="ars_frontend_inward_capacity_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.frontend_inward_capacity.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.frontend_inward_capacity.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_8 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_frontend_inward_capacity",
    topic_name="mysql.ars.ars.frontend_inward_capacity",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_8
    >> create_view_on_presto_8
)


s3_to_hudi_emr_sensor_9 = EmrStateSensorAsync(
    task_id="ars_growth_factor_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.growth_factor.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.growth_factor.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_9 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_growth_factor",
    topic_name="mysql.ars.ars.growth_factor",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_9
    >> create_view_on_presto_9
)


s3_to_hudi_emr_sensor_10 = EmrStateSensorAsync(
    task_id="ars_inter_facility_transfer_days_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.inter_facility_transfer_days.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.inter_facility_transfer_days.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_10 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_inter_facility_transfer_days",
    topic_name="mysql.ars.ars.inter_facility_transfer_days",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_10
    >> create_view_on_presto_10
)


s3_to_hudi_emr_sensor_11 = EmrStateSensorAsync(
    task_id="ars_inter_facility_transfer_days_log_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.inter_facility_transfer_days_log.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.inter_facility_transfer_days_log.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_11 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_inter_facility_transfer_days_log",
    topic_name="mysql.ars.ars.inter_facility_transfer_days_log",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_11
    >> create_view_on_presto_11
)


s3_to_hudi_emr_sensor_12 = EmrStateSensorAsync(
    task_id="ars_item_cpd_replication_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.item_cpd_replication.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.item_cpd_replication.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_12 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_item_cpd_replication",
    topic_name="mysql.ars.ars.item_cpd_replication",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_12
    >> create_view_on_presto_12
)


s3_to_hudi_emr_sensor_13 = EmrStateSensorAsync(
    task_id="ars_job_run_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.job_run.v3', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.job_run.v3', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_13 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_job_run",
    topic_name="mysql.ars.ars.job_run",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_13
    >> create_view_on_presto_13
)


s3_to_hudi_emr_sensor_14 = EmrStateSensorAsync(
    task_id="ars_min_bump_algo_result_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.min_bump_algo_result.v3', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.min_bump_algo_result.v3', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_14 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_min_bump_algo_result",
    topic_name="mysql.ars.ars.min_bump_algo_result",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_14
    >> create_view_on_presto_14
)


s3_to_hudi_emr_sensor_15 = EmrStateSensorAsync(
    task_id="ars_outlet_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.outlet.v3', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.outlet.v3', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_15 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_outlet",
    topic_name="mysql.ars.ars.outlet",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_15
    >> create_view_on_presto_15
)


s3_to_hudi_emr_sensor_16 = EmrStateSensorAsync(
    task_id="ars_outlet_item_aps_derived_cpd_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.outlet_item_aps_derived_cpd.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.outlet_item_aps_derived_cpd.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_16 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_outlet_item_aps_derived_cpd",
    topic_name="mysql.ars.ars.outlet_item_aps_derived_cpd",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_16
    >> create_view_on_presto_16
)


s3_to_hudi_emr_sensor_17 = EmrStateSensorAsync(
    task_id="ars_outlet_item_group_mapping_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.outlet_item_group_mapping.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.outlet_item_group_mapping.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_17 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_outlet_item_group_mapping",
    topic_name="mysql.ars.ars.outlet_item_group_mapping",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_17
    >> create_view_on_presto_17
)


s3_to_hudi_emr_sensor_18 = EmrStateSensorAsync(
    task_id="ars_outlet_item_open_sto_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.outlet_item_open_sto.v2', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.outlet_item_open_sto.v2', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_18 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_outlet_item_open_sto",
    topic_name="mysql.ars.ars.outlet_item_open_sto",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_18
    >> create_view_on_presto_18
)


s3_to_hudi_emr_sensor_19 = EmrStateSensorAsync(
    task_id="ars_outlet_vendor_item_tat_days_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.outlet_vendor_item_tat_days.v3', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.outlet_vendor_item_tat_days.v3', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_19 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_outlet_vendor_item_tat_days",
    topic_name="mysql.ars.ars.outlet_vendor_item_tat_days",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_19
    >> create_view_on_presto_19
)


s3_to_hudi_emr_sensor_20 = EmrStateSensorAsync(
    task_id="ars_reasons_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.reasons.v2', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.reasons.v2', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_20 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_reasons",
    topic_name="mysql.ars.ars.reasons",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_20
    >> create_view_on_presto_20
)


s3_to_hudi_emr_sensor_21 = EmrStateSensorAsync(
    task_id="ars_slot_flushing_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.slot_flushing.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.slot_flushing.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_21 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_slot_flushing",
    topic_name="mysql.ars.ars.slot_flushing",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_21
    >> create_view_on_presto_21
)


s3_to_hudi_emr_sensor_22 = EmrStateSensorAsync(
    task_id="ars_sto_dispatch_time_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.sto_dispatch_time.v2', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.sto_dispatch_time.v2', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_22 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_sto_dispatch_time",
    topic_name="mysql.ars.ars.sto_dispatch_time",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_22
    >> create_view_on_presto_22
)


s3_to_hudi_emr_sensor_23 = EmrStateSensorAsync(
    task_id="ars_table_updates_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.table_updates.v3', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.table_updates.v3', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_23 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_table_updates",
    topic_name="mysql.ars.ars.table_updates",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_23
    >> create_view_on_presto_23
)


s3_to_hudi_emr_sensor_24 = EmrStateSensorAsync(
    task_id="ars_transfer_case_size_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.transfer_case_size.v2', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_2_s3_to_hudi', key='flow_id_job_mapping').get('ars.transfer_case_size.v2', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_24 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_transfer_case_size",
    topic_name="mysql.ars.ars.transfer_case_size",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_24
    >> create_view_on_presto_24
)


if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = dt.timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
