# -*- coding: utf-8 -*-

import datetime as dt
import logging

from airflow import DAG
from airflow.operators.dagrun_operator import TriggerDagRunOperator
from kubernetes.client import models as k8s
from metrics_plugin import (
    EmrStateSensorAsync,
    SRPClusterMappingOperatorV2,
    SRPEmrBulkAddStepsOperator,
    PrestoCreateViewOperator,
)

import pencilbox as pb


branch_op_flows = {
    "ars.b2b_item_outlet_mapping.v1": {
        "kafka_topic": "mysql.ars.ars.b2b_item_outlet_mapping",
        "downstream_task_id": "ars_b2b_item_outlet_mapping_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.b2b_item_outlet_mapping.v1",
        ),
    },
    "ars.b2b_item_outlet_mapping_log.v1": {
        "kafka_topic": "mysql.ars.ars.b2b_item_outlet_mapping_log",
        "downstream_task_id": "ars_b2b_item_outlet_mapping_log_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.b2b_item_outlet_mapping_log.v1",
        ),
    },
    "ars.b2b_transfer_rule_attributes.v1": {
        "kafka_topic": "mysql.ars.ars.b2b_transfer_rule_attributes",
        "downstream_task_id": "ars_b2b_transfer_rule_attributes_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.b2b_transfer_rule_attributes.v1",
        ),
    },
    "ars.b2b_transfer_rules.v1": {
        "kafka_topic": "mysql.ars.ars.b2b_transfer_rules",
        "downstream_task_id": "ars_b2b_transfer_rules_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.b2b_transfer_rules.v1",
        ),
    },
    "ars.bulk_process_event_planner.v1": {
        "kafka_topic": "mysql.ars.ars.bulk_process_event_planner",
        "downstream_task_id": "ars_bulk_process_event_planner_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.bulk_process_event_planner.v1",
        ),
    },
    "ars.distributed_cart_penetration.v1": {
        "kafka_topic": "mysql.ars.ars.distributed_cart_penetration",
        "downstream_task_id": "ars_distributed_cart_penetration_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.distributed_cart_penetration.v1",
        ),
    },
    "ars.event_outlet_item_distribution.v1": {
        "kafka_topic": "mysql.ars.ars.event_outlet_item_distribution",
        "downstream_task_id": "ars_event_outlet_item_distribution_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.event_outlet_item_distribution.v1",
        ),
    },
    "ars.facility_group_flushing_custom_ratio.v1": {
        "kafka_topic": "mysql.ars.ars.facility_group_flushing_custom_ratio",
        "downstream_task_id": "ars_facility_group_flushing_custom_ratio_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.facility_group_flushing_custom_ratio.v1",
        ),
    },
    "ars.facility_item_flushing_custom_ratio.v1": {
        "kafka_topic": "mysql.ars.ars.facility_item_flushing_custom_ratio",
        "downstream_task_id": "ars_facility_item_flushing_custom_ratio_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.facility_item_flushing_custom_ratio.v1",
        ),
    },
    "ars.flushing_ptype_custom_ratio.v1": {
        "kafka_topic": "mysql.ars.ars.flushing_ptype_custom_ratio",
        "downstream_task_id": "ars_flushing_ptype_custom_ratio_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.flushing_ptype_custom_ratio.v1",
        ),
    },
    "ars.item_group_mapping.v3": {
        "kafka_topic": "mysql.ars.ars.item_group_mapping",
        "downstream_task_id": "ars_item_group_mapping_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.item_group_mapping.v3",
        ),
    },
    "ars.item_location_exclude_sales.v1": {
        "kafka_topic": "mysql.ars.ars.item_location_exclude_sales",
        "downstream_task_id": "ars_item_location_exclude_sales_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.item_location_exclude_sales.v1",
        ),
    },
    "ars.outlet_group_cpd.v1": {
        "kafka_topic": "mysql.ars.ars.outlet_group_cpd",
        "downstream_task_id": "ars_outlet_group_cpd_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.outlet_group_cpd.v1",
        ),
    },
    "ars.store_cluster_distribution.v1": {
        "kafka_topic": "mysql.ars.ars.store_cluster_distribution",
        "downstream_task_id": "ars_store_cluster_distribution_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.store_cluster_distribution.v1",
        ),
    },
    "ars.transfer_case_size_log.v1": {
        "kafka_topic": "mysql.ars.ars.transfer_case_size_log",
        "downstream_task_id": "ars_transfer_case_size_log_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.transfer_case_size_log.v1",
        ),
    },
    "ars.ars_job_run.v1": {
        "kafka_topic": "mysql.ars.ars.ars_job_run",
        "downstream_task_id": "ars_ars_job_run_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.ars_job_run.v1",
        ),
    },
    "ars.job_run_location.v1": {
        "kafka_topic": "mysql.ars.ars.job_run_location",
        "downstream_task_id": "ars_job_run_location_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.job_run_location.v1",
        ),
    },
    "ars.job_run_snapshot.v1": {
        "kafka_topic": "mysql.ars.ars.job_run_snapshot",
        "downstream_task_id": "ars_job_run_snapshot_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "ars.job_run_snapshot.v1",
        ),
    },
}

# Default values for all flow ids, to override pass `spark_application_jar` in `branch_op_flows` for respective flow id
spark_application_jar = "s3://prod-dse-srp-configs/jars/nessie/Nessie-assembly-2.1.2.jar"

# Default values for all flow ids, to override any value pass `spark_submit_options` in `branch_op_flows` for respective flow id
spark_submit_options = {
    "--deploy-mode": "cluster",
    "--master": "yarn",
    "--conf": {
        "spark.serializer": "org.apache.spark.serializer.KryoSerializer",
        "spark.kryo.registrator": "org.apache.spark.HoodieSparkKryoRegistrar",
        "spark.sql.catalog.spark_catalog": "org.apache.spark.sql.hudi.catalog.HoodieCatalog",
        "spark.sql.extensions": "org.apache.spark.sql.hudi.HoodieSparkSessionExtension",
        "spark.yarn.heterogeneousExecutors.enabled": "false",
        "spark.sql.hive.convertMetastoreParquet": "false",
    },
    "--class": "com.grofers.nessie.Nessie",
}


def slack_failure_alert(context):
    dag_id = context.get("task_instance").dag_id
    run_id = context.get("run_id")
    task_id = context.get("task_instance").task_id
    log_url = context.get("task_instance").log_url.replace("http://", "https://")
    exception = str(context.get("exception"))
    override_channel_mapping = {
        "terminated_with_errors": "bl-data-spot-interruptions",
        "spot": "bl-data-spot-interruptions",
    }

    slack_failure_msg = """
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner*: <!subteam^S089D0GQRCJ>
        *Log Url*: {log_url}
        {exception}
    """.format(
        dag_id=dag_id,
        run_id=run_id,
        task_id=task_id,
        log_url=log_url,
        exception=exception,
    )

    slack_alert_configs = [
        {"channel": "bl-data-alerts-p1"},
    ]

    # Check if exception exists and contains any key from override_channel_mapping
    if exception and override_channel_mapping:
        for error_keyword, override_channel in override_channel_mapping.items():
            if error_keyword.lower() in exception.lower():
                # Override all channels with the mapped channel
                slack_alert_configs = [{"channel": override_channel}]
                break

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(channel=channel, text=slack_failure_msg, source_info=False)
        except Exception as e:
            logging.warning(e)
            message = (
                f"<!subteam^S089D0GQRCJ> `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(channel="bl-data-alerts-p1", text=message, source_info=False)


def failure_alerts(context):
    slack_failure_alert(context)


args = {
    "owner": "<EMAIL>",
    "on_failure_callback": failure_alerts,
    "start_date": dt.datetime.strptime("2024-09-23T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": dt.datetime.strptime("2026-09-15T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "slack_id": "S089D0GQRCJ",
}

dag = DAG(
    dag_id="de_replicate_etl_ars_5_s3_to_hudi_v1",
    default_args=args,
    schedule_interval="22,52 * * * *",
    tags=["de", "replicate", "ars_5"],
    catchup=False,
    concurrency=12,
    max_active_runs=1,
    render_template_as_native_obj=True,
)


s3_hudi_cluster_allocation_task = SRPClusterMappingOperatorV2(
    task_id="cluster_allocation_ars_5_s3_to_hudi",
    dag=dag,
    connection_alias="nessie",
    flows_to_run=branch_op_flows,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


s3_hudi_bulk_submit_emr_steps_task = SRPEmrBulkAddStepsOperator(
    task_id="bulk_step_submission_ars_5_s3_to_hudi",
    dag=dag,
    aws_conn_id="aws_default",
    srp_flows_info="{{ task_instance.xcom_pull('cluster_allocation_ars_5_s3_to_hudi', key='flows_info') }}",
    spark_application_jar=spark_application_jar,
    spark_submit_options=spark_submit_options,
    srp_task_id_cluster_mapping="{{ task_instance.xcom_pull('cluster_allocation_ars_5_s3_to_hudi', key='return_value') }}",
    queue="celery",
)


s3_to_hudi_emr_sensor_1 = EmrStateSensorAsync(
    task_id="ars_b2b_item_outlet_mapping_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_5_s3_to_hudi', key='flow_id_job_mapping').get('ars.b2b_item_outlet_mapping.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_5_s3_to_hudi', key='flow_id_job_mapping').get('ars.b2b_item_outlet_mapping.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_1 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_b2b_item_outlet_mapping",
    topic_name="mysql.ars.ars.b2b_item_outlet_mapping",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_1
    >> create_view_on_presto_1
)


s3_to_hudi_emr_sensor_2 = EmrStateSensorAsync(
    task_id="ars_b2b_item_outlet_mapping_log_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_5_s3_to_hudi', key='flow_id_job_mapping').get('ars.b2b_item_outlet_mapping_log.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_5_s3_to_hudi', key='flow_id_job_mapping').get('ars.b2b_item_outlet_mapping_log.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_2 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_b2b_item_outlet_mapping_log",
    topic_name="mysql.ars.ars.b2b_item_outlet_mapping_log",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_2
    >> create_view_on_presto_2
)


s3_to_hudi_emr_sensor_3 = EmrStateSensorAsync(
    task_id="ars_b2b_transfer_rule_attributes_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_5_s3_to_hudi', key='flow_id_job_mapping').get('ars.b2b_transfer_rule_attributes.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_5_s3_to_hudi', key='flow_id_job_mapping').get('ars.b2b_transfer_rule_attributes.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_3 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_b2b_transfer_rule_attributes",
    topic_name="mysql.ars.ars.b2b_transfer_rule_attributes",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_3
    >> create_view_on_presto_3
)


s3_to_hudi_emr_sensor_4 = EmrStateSensorAsync(
    task_id="ars_b2b_transfer_rules_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_5_s3_to_hudi', key='flow_id_job_mapping').get('ars.b2b_transfer_rules.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_5_s3_to_hudi', key='flow_id_job_mapping').get('ars.b2b_transfer_rules.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_4 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_b2b_transfer_rules",
    topic_name="mysql.ars.ars.b2b_transfer_rules",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_4
    >> create_view_on_presto_4
)


s3_to_hudi_emr_sensor_5 = EmrStateSensorAsync(
    task_id="ars_bulk_process_event_planner_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_5_s3_to_hudi', key='flow_id_job_mapping').get('ars.bulk_process_event_planner.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_5_s3_to_hudi', key='flow_id_job_mapping').get('ars.bulk_process_event_planner.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_5 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_bulk_process_event_planner",
    topic_name="mysql.ars.ars.bulk_process_event_planner",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_5
    >> create_view_on_presto_5
)


s3_to_hudi_emr_sensor_6 = EmrStateSensorAsync(
    task_id="ars_distributed_cart_penetration_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_5_s3_to_hudi', key='flow_id_job_mapping').get('ars.distributed_cart_penetration.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_5_s3_to_hudi', key='flow_id_job_mapping').get('ars.distributed_cart_penetration.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_6 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_distributed_cart_penetration",
    topic_name="mysql.ars.ars.distributed_cart_penetration",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_6
    >> create_view_on_presto_6
)


s3_to_hudi_emr_sensor_7 = EmrStateSensorAsync(
    task_id="ars_event_outlet_item_distribution_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_5_s3_to_hudi', key='flow_id_job_mapping').get('ars.event_outlet_item_distribution.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_5_s3_to_hudi', key='flow_id_job_mapping').get('ars.event_outlet_item_distribution.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_7 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_event_outlet_item_distribution",
    topic_name="mysql.ars.ars.event_outlet_item_distribution",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_7
    >> create_view_on_presto_7
)


s3_to_hudi_emr_sensor_8 = EmrStateSensorAsync(
    task_id="ars_facility_group_flushing_custom_ratio_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_5_s3_to_hudi', key='flow_id_job_mapping').get('ars.facility_group_flushing_custom_ratio.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_5_s3_to_hudi', key='flow_id_job_mapping').get('ars.facility_group_flushing_custom_ratio.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_8 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_facility_group_flushing_custom_ratio",
    topic_name="mysql.ars.ars.facility_group_flushing_custom_ratio",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_8
    >> create_view_on_presto_8
)


s3_to_hudi_emr_sensor_9 = EmrStateSensorAsync(
    task_id="ars_facility_item_flushing_custom_ratio_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_5_s3_to_hudi', key='flow_id_job_mapping').get('ars.facility_item_flushing_custom_ratio.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_5_s3_to_hudi', key='flow_id_job_mapping').get('ars.facility_item_flushing_custom_ratio.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_9 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_facility_item_flushing_custom_ratio",
    topic_name="mysql.ars.ars.facility_item_flushing_custom_ratio",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_9
    >> create_view_on_presto_9
)


s3_to_hudi_emr_sensor_10 = EmrStateSensorAsync(
    task_id="ars_flushing_ptype_custom_ratio_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_5_s3_to_hudi', key='flow_id_job_mapping').get('ars.flushing_ptype_custom_ratio.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_5_s3_to_hudi', key='flow_id_job_mapping').get('ars.flushing_ptype_custom_ratio.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_10 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_flushing_ptype_custom_ratio",
    topic_name="mysql.ars.ars.flushing_ptype_custom_ratio",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_10
    >> create_view_on_presto_10
)


s3_to_hudi_emr_sensor_11 = EmrStateSensorAsync(
    task_id="ars_item_group_mapping_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_5_s3_to_hudi', key='flow_id_job_mapping').get('ars.item_group_mapping.v3', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_5_s3_to_hudi', key='flow_id_job_mapping').get('ars.item_group_mapping.v3', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_11 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_item_group_mapping",
    topic_name="mysql.ars.ars.item_group_mapping",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_11
    >> create_view_on_presto_11
)


s3_to_hudi_emr_sensor_12 = EmrStateSensorAsync(
    task_id="ars_item_location_exclude_sales_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_5_s3_to_hudi', key='flow_id_job_mapping').get('ars.item_location_exclude_sales.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_5_s3_to_hudi', key='flow_id_job_mapping').get('ars.item_location_exclude_sales.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_12 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_item_location_exclude_sales",
    topic_name="mysql.ars.ars.item_location_exclude_sales",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_12
    >> create_view_on_presto_12
)


s3_to_hudi_emr_sensor_13 = EmrStateSensorAsync(
    task_id="ars_outlet_group_cpd_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_5_s3_to_hudi', key='flow_id_job_mapping').get('ars.outlet_group_cpd.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_5_s3_to_hudi', key='flow_id_job_mapping').get('ars.outlet_group_cpd.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_13 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_outlet_group_cpd",
    topic_name="mysql.ars.ars.outlet_group_cpd",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_13
    >> create_view_on_presto_13
)


s3_to_hudi_emr_sensor_14 = EmrStateSensorAsync(
    task_id="ars_store_cluster_distribution_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_5_s3_to_hudi', key='flow_id_job_mapping').get('ars.store_cluster_distribution.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_5_s3_to_hudi', key='flow_id_job_mapping').get('ars.store_cluster_distribution.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_14 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_store_cluster_distribution",
    topic_name="mysql.ars.ars.store_cluster_distribution",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_14
    >> create_view_on_presto_14
)


s3_to_hudi_emr_sensor_15 = EmrStateSensorAsync(
    task_id="ars_transfer_case_size_log_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_5_s3_to_hudi', key='flow_id_job_mapping').get('ars.transfer_case_size_log.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_5_s3_to_hudi', key='flow_id_job_mapping').get('ars.transfer_case_size_log.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_15 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_transfer_case_size_log",
    topic_name="mysql.ars.ars.transfer_case_size_log",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_15
    >> create_view_on_presto_15
)


s3_to_hudi_emr_sensor_16 = EmrStateSensorAsync(
    task_id="ars_ars_job_run_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_5_s3_to_hudi', key='flow_id_job_mapping').get('ars.ars_job_run.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_5_s3_to_hudi', key='flow_id_job_mapping').get('ars.ars_job_run.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_16 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_ars_job_run",
    topic_name="mysql.ars.ars.ars_job_run",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_16
    >> create_view_on_presto_16
)


s3_to_hudi_emr_sensor_17 = EmrStateSensorAsync(
    task_id="ars_job_run_location_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_5_s3_to_hudi', key='flow_id_job_mapping').get('ars.job_run_location.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_5_s3_to_hudi', key='flow_id_job_mapping').get('ars.job_run_location.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_17 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_job_run_location",
    topic_name="mysql.ars.ars.job_run_location",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_17
    >> create_view_on_presto_17
)


s3_to_hudi_emr_sensor_18 = EmrStateSensorAsync(
    task_id="ars_job_run_snapshot_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_5_s3_to_hudi', key='flow_id_job_mapping').get('ars.job_run_snapshot.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_ars_5_s3_to_hudi', key='flow_id_job_mapping').get('ars.job_run_snapshot.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_18 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_ars_job_run_snapshot",
    topic_name="mysql.ars.ars.job_run_snapshot",
    database="ars",
    lake_schemaname="lake_ars",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_18
    >> create_view_on_presto_18
)


if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = dt.timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
