# -*- coding: utf-8 -*-

import json
import datetime as dt
import ast

import requests
import logging

import airflow
from airflow import DAG
from airflow.hooks.base_hook import BaseHook
from airflow.exceptions import AirflowException
from airflow.providers.amazon.aws.hooks.emr import EmrHook
from airflow.operators.python_operator import PythonOperator, ShortCircuitOperator
from airflow.providers.postgres.operators.postgres import PostgresOperator
from airflow.providers.amazon.aws.operators.emr_add_steps import EmrAddStepsOperator
from airflow.providers.amazon.aws.operators.emr_create_job_flow import EmrCreateJobFlowOperator
from airflow.providers.amazon.aws.operators.emr_terminate_job_flow import (
    EmrTerminateJobFlowOperator,
)
from metrics_plugin import (
    S3ToRedshiftOperator,
    BranchSRPOperator,
    DynamicClusterConfigOperator,
    PrestoCreateViewOperator,
    EmrStateSensorAsync,
)
from kubernetes.client import models as k8s

import pencilbox as pb


branch_op_flows = {
    "pms.paas_ticketing_system.v1": {
        "kafka_topic": "postgres.pms.public.paas_ticketing_system",
        "downstream_task_id": "s3_to_hudi_pms_paas_ticketing_system",
    },
    "pms.printing_events.v1": {
        "kafka_topic": "postgres.pms.public.printing_events",
        "downstream_task_id": "s3_to_hudi_pms_printing_events",
    },
    "pms.printing_serviceability.v1": {
        "kafka_topic": "postgres.pms.public.printing_serviceability",
        "downstream_task_id": "s3_to_hudi_pms_printing_serviceability",
    },
    "pms.printings.v1": {
        "kafka_topic": "postgres.pms.public.printings",
        "downstream_task_id": "s3_to_hudi_pms_printings",
    },
}


def slack_failure_alert(context):
    dag_id = context.get("task_instance").dag_id
    run_id = context.get("run_id")
    task_id = context.get("task_instance").task_id
    log_url = context.get("task_instance").log_url.replace("http://", "https://")
    exception = str(context.get("exception"))
    override_channel_mapping = {
        "terminated_with_errors": "bl-data-spot-interruptions",
        "spot": "bl-data-spot-interruptions",
    }

    slack_failure_msg = """
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner*: <!subteam^S089D0GQRCJ>
        *Log Url*: {log_url}
        {exception}
    """.format(
        dag_id=dag_id,
        run_id=run_id,
        task_id=task_id,
        log_url=log_url,
        exception=exception,
    )

    slack_alert_configs = [
        {"channel": "bl-data-alerts-p1"},
    ]

    # Check if exception exists and contains any key from override_channel_mapping
    if exception and override_channel_mapping:
        for error_keyword, override_channel in override_channel_mapping.items():
            if error_keyword.lower() in exception.lower():
                # Override all channels with the mapped channel
                slack_alert_configs = [{"channel": override_channel}]
                break

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(channel=channel, text=slack_failure_msg, source_info=False)
        except Exception as e:
            logging.warning(e)
            message = (
                f"<!subteam^S089D0GQRCJ> `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(channel="bl-data-alerts-p1", text=message, source_info=False)


EMR_SETTINGS_OVERRIDE = {
    # "Name": "emr_launch_airflow_test",
    "LogUri": "s3n://grofers-prod-dse-sgp/elasticmapreduce/",
    "ReleaseLabel": "emr-6.11.1",
    "Applications": [{"Name": "Hadoop"}, {"Name": "Spark"}],
    "Instances": {
        "KeepJobFlowAliveWhenNoSteps": True,
        "Ec2KeyName": "dse-emr",
        "ServiceAccessSecurityGroup": "sg-64858200",
        "Ec2SubnetIds": [
            "subnet-09e5bab68a3640d38",
            "subnet-fe54ac88",
            "subnet-e00fc184",
        ],
        "EmrManagedSlaveSecurityGroup": "sg-587afc3c",
        "EmrManagedMasterSecurityGroup": "sg-587afc3c",
        "InstanceFleets": [],
    },
    "Configurations": [
        {
            "Classification": "spark-hive-site",
            "Properties": {
                "hive.metastore.uris": "thrift://datalake-hive-metastore-service-data.prod-sgp-k8s.grofer.io:9083"
            },
        },
        {
            "Classification": "emrfs-site",
            "Properties": {"fs.s3.maxRetries": "20"},
        },
    ],
    "VisibleToAllUsers": True,
    "JobFlowRole": "EMR_EC2_DefaultRole",
    "ServiceRole": "EMR_DefaultRole",
    "Tags": [
        {"Key": "Role", "Value": "source-replication"},
        {"Key": "Environment", "Value": "prod"},
        {"Key": "Product", "Value": "dse"},
        {"Key": "Service", "Value": "dse-emr-nessie"},
        {"Key": "Dag", "Value": "pms_1"},
        {"Key": "Database", "Value": "pms"},
        {"Key": "Name", "Value": "dse_pms_1_nessie"},
        {"Key": "grofers.io/service", "Value": "source-replication"},
        {"Key": "grofers.io/component", "Value": "source-replication-spark-cluster"},
        {"Key": "grofers.io/component-role", "Value": "data-processing"},
        {"Key": "grofers.io/business-service", "Value": "data-platform"},
        {"Key": "grofers.io/team", "Value": "data-engineering"},
        {"Key": "grofers.io/tribe", "Value": "data"},
        {"Key": "cost:namespace", "Value": "data"},
        {"Key": "cost:application", "Value": "emr"},
    ],
}


def get_emr_settings_override(**kwargs):
    EMR_SETTINGS_OVERRIDE["Name"] = "dse_{table}_nessie".format(table=kwargs["table"])
    # string -> dictionary
    kwargs["emr_settings_override"] = ast.literal_eval(kwargs["emr_settings_override"])
    EMR_SETTINGS_OVERRIDE["Instances"]["InstanceFleets"] = kwargs["emr_settings_override"][
        "emr_instance_fleets_config"
    ]
    EMR_SETTINGS_OVERRIDE["StepConcurrencyLevel"] = kwargs["emr_settings_override"][
        "step_concurrency_level"
    ]
    return EMR_SETTINGS_OVERRIDE


def config_validator(**kwargs):
    try:
        config = ast.literal_eval(kwargs["config"])
        logging.info("Validating the config string", config)
        if config["step_concurrency_level"]:
            return True
        else:
            return False
    except Exception as e:
        logging.exception("Error: ", e)
        return False


def fetch_spark_steps(flow_id, flow_type, nessie_version, spark_submit_mode="cluster", **kwargs):

    logging.info(f"Creating Spark Submit Step for flow_id {flow_id} in {spark_submit_mode} mode")

    if spark_submit_mode == "client":
        spark_submit = [
            "spark-submit",
            "--deploy-mode",
            f"{spark_submit_mode}",
            "--master",
            "yarn",
            "--conf",
            "spark.serializer=org.apache.spark.serializer.KryoSerializer",
            "--conf",
            "spark.sql.hive.convertMetastoreParquet=false",
            "--conf",
            "spark.driver.memory=3g",
            "--conf",
            "spark.driver.extraJavaOptions=-XX:NewSize=1g -XX:SurvivorRatio=2 -XX:+UseCompressedOops -XX:+UseConcMarkSweepGC -XX:+UseParNewGC -XX:CMSInitiatingOccupancyFraction=70 -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -XX:+PrintGCDateStamps -XX:+PrintGCApplicationStoppedTime -XX:+PrintGCApplicationConcurrentTime -XX:+PrintTenuringDistribution -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/tmp/hoodie-heapdump.hprof",
            "--conf",
            "spark.yarn.heterogeneousExecutors.enabled=false",
            "--conf",
            "spark.kryo.registrator=org.apache.spark.HoodieSparkKryoRegistrar",
            "--conf",
            "spark.sql.catalog.spark_catalog=org.apache.spark.sql.hudi.catalog.HoodieCatalog",
            "--conf",
            "spark.sql.extensions=org.apache.spark.sql.hudi.HoodieSparkSessionExtension",
            "--class",
            "com.grofers.nessie.Nessie",
            f"s3://prod-dse-srp-configs/jars/nessie/Nessie-assembly-{nessie_version}.jar",
            f"{flow_type}",
            "--flowId",
            f"{flow_id}",
        ]

    elif spark_submit_mode == "cluster":
        spark_submit = (
            "spark-submit",
            "--name",
            f"{flow_type}-{flow_id}",
            "--deploy-mode",
            f"{spark_submit_mode}",
            "--master",
            "yarn",
            "--conf",
            "spark.serializer=org.apache.spark.serializer.KryoSerializer",
            "--conf",
            "spark.sql.hive.convertMetastoreParquet=false",
            "--conf",
            "spark.yarn.heterogeneousExecutors.enabled=false",
            "--conf",
            "spark.kryo.registrator=org.apache.spark.HoodieSparkKryoRegistrar",
            "--conf",
            "spark.sql.catalog.spark_catalog=org.apache.spark.sql.hudi.catalog.HoodieCatalog",
            "--conf",
            "spark.sql.extensions=org.apache.spark.sql.hudi.HoodieSparkSessionExtension",
            "--class",
            "com.grofers.nessie.Nessie",
            f"s3://prod-dse-srp-configs/jars/nessie/Nessie-assembly-{nessie_version}.jar",
            f"{flow_type}",
            "--flowId",
            f"{flow_id}",
        )

    else:
        raise NotImplementedError("Incorrect spark submit mode provided")

    return [
        {
            "Name": f"""{flow_id}-{flow_type}-{dt.datetime.now().timestamp()}""",
            "ActionOnFailure": "CONTINUE",
            "HadoopJarStep": {
                "Jar": "command-runner.jar",
                "Args": spark_submit,
            },
        }
    ]


def failure_alerts(context):
    slack_failure_alert(context)


args = {
    "owner": "<EMAIL>",
    "on_failure_callback": failure_alerts,
    "start_date": dt.datetime.strptime("2024-09-24T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": dt.datetime.strptime("2025-12-16T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "slack_id": "S089D0GQRCJ",
}

dag = DAG(
    dag_id="de_replicate_etl_pms_1_v1",
    default_args=args,
    schedule_interval="0 */3 * * *",
    tags=["de", "replicate", "pms_1", "de", "replicate", "etl"],
    catchup=False,
    concurrency=4,
    max_active_runs=1,
)

cluster_config_task = DynamicClusterConfigOperator(
    task_id="cluster_config_task_pms_1",
    dag=dag,
    flows_to_run=branch_op_flows,
    connection_alias="nessie",
    queue="celery",
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
)


short_circuit_task = ShortCircuitOperator(
    task_id="short_circuit_pms_1",
    op_kwargs={
        "config": "{{ task_instance.xcom_pull('cluster_config_task_pms_1', key='return_value') }}"
    },
    python_callable=config_validator,
    dag=dag,
    queue="celery",
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
)


emr_cluster_config_task = PythonOperator(
    task_id="emr_cluster_config_task_pms_1",
    op_kwargs={
        "table": "pms_1",
        "emr_settings_override": "{{ task_instance.xcom_pull('cluster_config_task_pms_1', key='return_value') }}",
    },
    python_callable=get_emr_settings_override,
    dag=dag,
    queue="celery",
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
)


cluster_create_task = EmrCreateJobFlowOperator(
    task_id="create_emr_cluster_pms_1",
    job_flow_overrides="{{ task_instance.xcom_pull('emr_cluster_config_task_pms_1', key='return_value') }}",
    aws_conn_id="aws_default",
    emr_conn_id="emr_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


cluster_terminate_task = EmrTerminateJobFlowOperator(
    task_id="terminate_emr_cluster_pms_1",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_pms_1', key='return_value') }}",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    trigger_rule="all_done",
    priority_weight=3,
)


flows_branching_task = BranchSRPOperator(
    task_id="flows_branching_pms_1",
    dag=dag,
    flows_to_run=branch_op_flows,
    connection_alias="nessie",
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


s3_to_hudi_task_1 = EmrAddStepsOperator(
    task_id="s3_to_hudi_pms_paas_ticketing_system",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_pms_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="pms.paas_ticketing_system.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_1 = EmrStateSensorAsync(
    task_id="load_to_lake_pms_paas_ticketing_system_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_pms_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_pms_paas_ticketing_system', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_1 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pms_paas_ticketing_system",
    topic_name="postgres.pms.public.paas_ticketing_system",
    database="pms",
    lake_schemaname="lake_pms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_1
    >> s3_to_hudi_sensor_1
    >> cluster_terminate_task
)

s3_to_hudi_sensor_1 >> create_view_on_presto_1


s3_to_hudi_task_2 = EmrAddStepsOperator(
    task_id="s3_to_hudi_pms_printing_events",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_pms_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="pms.printing_events.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_2 = EmrStateSensorAsync(
    task_id="load_to_lake_pms_printing_events_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_pms_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_pms_printing_events', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_2 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pms_printing_events",
    topic_name="postgres.pms.public.printing_events",
    database="pms",
    lake_schemaname="lake_pms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_2
    >> s3_to_hudi_sensor_2
    >> cluster_terminate_task
)

s3_to_hudi_sensor_2 >> create_view_on_presto_2


s3_to_hudi_task_3 = EmrAddStepsOperator(
    task_id="s3_to_hudi_pms_printing_serviceability",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_pms_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="pms.printing_serviceability.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_3 = EmrStateSensorAsync(
    task_id="load_to_lake_pms_printing_serviceability_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_pms_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_pms_printing_serviceability', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_3 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pms_printing_serviceability",
    topic_name="postgres.pms.public.printing_serviceability",
    database="pms",
    lake_schemaname="lake_pms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_3
    >> s3_to_hudi_sensor_3
    >> cluster_terminate_task
)

s3_to_hudi_sensor_3 >> create_view_on_presto_3


s3_to_hudi_task_4 = EmrAddStepsOperator(
    task_id="s3_to_hudi_pms_printings",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_pms_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="pms.printings.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_4 = EmrStateSensorAsync(
    task_id="load_to_lake_pms_printings_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_pms_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_pms_printings', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_4 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pms_printings",
    topic_name="postgres.pms.public.printings",
    database="pms",
    lake_schemaname="lake_pms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_4
    >> s3_to_hudi_sensor_4
    >> cluster_terminate_task
)

s3_to_hudi_sensor_4 >> create_view_on_presto_4


if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = dt.timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
