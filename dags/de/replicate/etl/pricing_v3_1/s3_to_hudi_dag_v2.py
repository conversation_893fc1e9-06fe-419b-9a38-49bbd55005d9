# -*- coding: utf-8 -*-

import datetime as dt
import logging

from airflow import DAG
from airflow.operators.dagrun_operator import TriggerDagRunOperator
from kubernetes.client import models as k8s
from metrics_plugin import (
    EmrStateSensorAsync,
    SRPClusterMappingOperatorV2,
    SRPEmrBulkAddStepsOperator,
    PrestoCreateViewOperator,
)

import pencilbox as pb


branch_op_flows = {
    "pricing_v3.approval_domain_rulesapproval.v1": {
        "kafka_topic": "postgres.pricing_v3.public.approval_domain_rulesapproval",
        "downstream_task_id": "pricing_v3_approval_domain_rulesapproval_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.approval_domain_rulesapproval.v1",
        ),
    },
    "pricing_v3.approval_domain_sheetfile.v1": {
        "kafka_topic": "postgres.pricing_v3.public.approval_domain_sheetfile",
        "downstream_task_id": "pricing_v3_approval_domain_sheetfile_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.approval_domain_sheetfile.v1",
        ),
    },
    "pricing_v3.attribute_management_allocationgrouprulemap.v1": {
        "kafka_topic": "postgres.pricing_v3.public.attribute_management_allocationgrouprulemap",
        "downstream_task_id": "pricing_v3_attribute_management_allocationgrouprulemap_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.attribute_management_allocationgrouprulemap.v1",
        ),
    },
    "pricing_v3.attribute_management_attributemasterrule.v1": {
        "kafka_topic": "postgres.pricing_v3.public.attribute_management_attributemasterrule",
        "downstream_task_id": "pricing_v3_attribute_management_attributemasterrule_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.attribute_management_attributemasterrule.v1",
        ),
    },
    "pricing_v3.attribute_management_brandsfundallocationgroup.v1": {
        "kafka_topic": "postgres.pricing_v3.public.attribute_management_brandsfundallocationgroup",
        "downstream_task_id": "pricing_v3_attribute_management_brandsfundallocationgroup_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.attribute_management_brandsfundallocationgroup.v1",
        ),
    },
    "pricing_v3.attribute_management_brandsfundapproval.v1": {
        "kafka_topic": "postgres.pricing_v3.public.attribute_management_brandsfundapproval",
        "downstream_task_id": "pricing_v3_attribute_management_brandsfundapproval_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.attribute_management_brandsfundapproval.v1",
        ),
        "spark_application_jar": "s3://prod-dse-srp-configs/jars/nessie/Nessie-assembly-2.1.2-high-resources.jar",
        "spark_submit_options": {
            "--conf": {
                "spark.serializer": "org.apache.spark.serializer.KryoSerializer",
                "spark.kryo.registrator": "org.apache.spark.HoodieSparkKryoRegistrar",
                "spark.sql.catalog.spark_catalog": "org.apache.spark.sql.hudi.catalog.HoodieCatalog",
                "spark.sql.extensions": "org.apache.spark.sql.hudi.HoodieSparkSessionExtension",
                "spark.yarn.heterogeneousExecutors.enabled": "false",
                "spark.sql.hive.convertMetastoreParquet": "false",
            },
        },
    },
    "pricing_v3.attribute_management_brandsfundcityapproval.v1": {
        "kafka_topic": "postgres.pricing_v3.public.attribute_management_brandsfundcityapproval",
        "downstream_task_id": "pricing_v3_attribute_management_brandsfundcityapproval_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.attribute_management_brandsfundcityapproval.v1",
        ),
    },
    "pricing_v3.attribute_management_brandsfundsummary.v1": {
        "kafka_topic": "postgres.pricing_v3.public.attribute_management_brandsfundsummary",
        "downstream_task_id": "pricing_v3_attribute_management_brandsfundsummary_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.attribute_management_brandsfundsummary.v1",
        ),
    },
    "pricing_v3.attribute_management_brandssheetfile.v1": {
        "kafka_topic": "postgres.pricing_v3.public.attribute_management_brandssheetfile",
        "downstream_task_id": "pricing_v3_attribute_management_brandssheetfile_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.attribute_management_brandssheetfile.v1",
        ),
    },
    "pricing_v3.attribute_management_itembrandfundagendaattribute.v1": {
        "kafka_topic": "postgres.pricing_v3.public.attribute_management_itembrandfundagendaattribute",
        "downstream_task_id": "pricing_v3_attribute_management_itembrandfundagendaattribute_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.attribute_management_itembrandfundagendaattribute.v1",
        ),
    },
    "pricing_v3.attribute_management_kvibrandfundagendaattribute.v3": {
        "kafka_topic": "postgres.pricing_v3.public.attribute_management_kvibrandfundagendaattribute",
        "downstream_task_id": "pricing_v3_attribute_management_kvibrandfundagendaattribute_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.attribute_management_kvibrandfundagendaattribute.v3",
        ),
    },
    "pricing_v3.attribute_management_manufactureremail.v1": {
        "kafka_topic": "postgres.pricing_v3.public.attribute_management_manufactureremail",
        "downstream_task_id": "pricing_v3_attribute_management_manufactureremail_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.attribute_management_manufactureremail.v1",
        ),
    },
    "pricing_v3.bundles_and_combos_approval.v1": {
        "kafka_topic": "postgres.pricing_v3.public.bundles_and_combos_approval",
        "downstream_task_id": "pricing_v3_bundles_and_combos_approval_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.bundles_and_combos_approval.v1",
        ),
    },
    "pricing_v3.bundles_and_combos_city_approval.v1": {
        "kafka_topic": "postgres.pricing_v3.public.bundles_and_combos_city_approval",
        "downstream_task_id": "pricing_v3_bundles_and_combos_city_approval_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.bundles_and_combos_city_approval.v1",
        ),
    },
    "pricing_v3.bundles_and_combos_domain_bundlebrandfundsheetfile.v1": {
        "kafka_topic": "postgres.pricing_v3.public.bundles_and_combos_domain_bundlebrandfundsheetfile",
        "downstream_task_id": "pricing_v3_bundles_and_combos_domain_bundlebrandfundsheetfile_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.bundles_and_combos_domain_bundlebrandfundsheetfile.v1",
        ),
    },
    "pricing_v3.catalog_domain_kvi_item.v1": {
        "kafka_topic": "postgres.pricing_v3.public.catalog_domain_kvi_item",
        "downstream_task_id": "pricing_v3_catalog_domain_kvi_item_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.catalog_domain_kvi_item.v1",
        ),
    },
    "pricing_v3.catalog_domain_kvitagging.v1": {
        "kafka_topic": "postgres.pricing_v3.public.catalog_domain_kvitagging",
        "downstream_task_id": "pricing_v3_catalog_domain_kvitagging_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.catalog_domain_kvitagging.v1",
        ),
    },
    "pricing_v3.competitor_domain_competitorconfiguration.v1": {
        "kafka_topic": "postgres.pricing_v3.public.competitor_domain_competitorconfiguration",
        "downstream_task_id": "pricing_v3_competitor_domain_competitorconfiguration_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.competitor_domain_competitorconfiguration.v1",
        ),
    },
    "pricing_v3.competitor_domain_competitormaster.v1": {
        "kafka_topic": "postgres.pricing_v3.public.competitor_domain_competitormaster",
        "downstream_task_id": "pricing_v3_competitor_domain_competitormaster_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.competitor_domain_competitormaster.v1",
        ),
    },
    "pricing_v3.cron_tasks.v1": {
        "kafka_topic": "postgres.pricing_v3.public.cron_tasks",
        "downstream_task_id": "pricing_v3_cron_tasks_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.cron_tasks.v1",
        ),
    },
    "pricing_v3.event_management_domain_pricingeventerrors.v2": {
        "kafka_topic": "postgres.pricing_v3.public.event_management_domain_pricingeventerrors",
        "downstream_task_id": "pricing_v3_event_management_domain_pricingeventerrors_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.event_management_domain_pricingeventerrors.v2",
        ),
    },
    "pricing_v3.pricing_domain_city.v3": {
        "kafka_topic": "postgres.pricing_v3.public.pricing_domain_city",
        "downstream_task_id": "pricing_v3_pricing_domain_city_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.pricing_domain_city.v3",
        ),
    },
    "pricing_v3.pricing_domain_citycluster.v1": {
        "kafka_topic": "postgres.pricing_v3.public.pricing_domain_citycluster",
        "downstream_task_id": "pricing_v3_pricing_domain_citycluster_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.pricing_domain_citycluster.v1",
        ),
    },
    "pricing_v3.pricing_domain_cmscity.v3": {
        "kafka_topic": "postgres.pricing_v3.public.pricing_domain_cmscity",
        "downstream_task_id": "pricing_v3_pricing_domain_cmscity_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.pricing_domain_cmscity.v3",
        ),
    },
    "pricing_v3.pricing_domain_outlet.v3": {
        "kafka_topic": "postgres.pricing_v3.public.pricing_domain_outlet",
        "downstream_task_id": "pricing_v3_pricing_domain_outlet_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.pricing_domain_outlet.v3",
        ),
    },
    "pricing_v3.pricing_domain_pricerecommendation.v1": {
        "kafka_topic": "postgres.pricing_v3.public.pricing_domain_pricerecommendation",
        "downstream_task_id": "pricing_v3_pricing_domain_pricerecommendation_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.pricing_domain_pricerecommendation.v1",
        ),
    },
    "pricing_v3.pricing_domain_prices.v3": {
        "kafka_topic": "postgres.pricing_v3.public.pricing_domain_prices",
        "downstream_task_id": "pricing_v3_pricing_domain_prices_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.pricing_domain_prices.v3",
        ),
    },
    "pricing_v3.pricing_domain_product.v4": {
        "kafka_topic": "postgres.pricing_v3.public.pricing_domain_product",
        "downstream_task_id": "pricing_v3_pricing_domain_product_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.pricing_domain_product.v4",
        ),
    },
    "pricing_v3.pricing_domain_productchangelog.v3": {
        "kafka_topic": "postgres.pricing_v3.public.pricing_domain_productchangelog",
        "downstream_task_id": "pricing_v3_pricing_domain_productchangelog_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.pricing_domain_productchangelog.v3",
        ),
        "spark_application_jar": "s3://prod-dse-srp-configs/jars/nessie/Nessie-assembly-2.1.2-high-resources.jar",
        "spark_submit_options": {
            "--conf": {
                "spark.serializer": "org.apache.spark.serializer.KryoSerializer",
                "spark.kryo.registrator": "org.apache.spark.HoodieSparkKryoRegistrar",
                "spark.sql.catalog.spark_catalog": "org.apache.spark.sql.hudi.catalog.HoodieCatalog",
                "spark.sql.extensions": "org.apache.spark.sql.hudi.HoodieSparkSessionExtension",
                "spark.yarn.heterogeneousExecutors.enabled": "false",
                "spark.sql.hive.convertMetastoreParquet": "false",
            },
        },
    },
    "pricing_v3.pricing_domain_superstore.v3": {
        "kafka_topic": "postgres.pricing_v3.public.pricing_domain_superstore",
        "downstream_task_id": "pricing_v3_pricing_domain_superstore_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.pricing_domain_superstore.v3",
        ),
    },
    "pricing_v3.replication_domain_itemcityreplicationchangelog.v1": {
        "kafka_topic": "postgres.pricing_v3.public.replication_domain_itemcityreplicationchangelog",
        "downstream_task_id": "pricing_v3_replication_domain_itemcityreplicationchangelog_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.replication_domain_itemcityreplicationchangelog.v1",
        ),
    },
    "pricing_v3.rule_management_agendarulekvialignedrm.v1": {
        "kafka_topic": "postgres.pricing_v3.public.rule_management_agendarulekvialignedrm",
        "downstream_task_id": "pricing_v3_rule_management_agendarulekvialignedrm_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.rule_management_agendarulekvialignedrm.v1",
        ),
    },
    "pricing_v3.rule_management_agendarulekviinternal.v1": {
        "kafka_topic": "postgres.pricing_v3.public.rule_management_agendarulekviinternal",
        "downstream_task_id": "pricing_v3_rule_management_agendarulekviinternal_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.rule_management_agendarulekviinternal.v1",
        ),
    },
    "pricing_v3.rule_management_agendarulekvirm.v1": {
        "kafka_topic": "postgres.pricing_v3.public.rule_management_agendarulekvirm",
        "downstream_task_id": "pricing_v3_rule_management_agendarulekvirm_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.rule_management_agendarulekvirm.v1",
        ),
    },
    "pricing_v3.rule_management_agendarulerm.v1": {
        "kafka_topic": "postgres.pricing_v3.public.rule_management_agendarulerm",
        "downstream_task_id": "pricing_v3_rule_management_agendarulerm_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.rule_management_agendarulerm.v1",
        ),
    },
    "pricing_v3.rule_management_agendarulermitemid.v1": {
        "kafka_topic": "postgres.pricing_v3.public.rule_management_agendarulermitemid",
        "downstream_task_id": "pricing_v3_rule_management_agendarulermitemid_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.rule_management_agendarulermitemid.v1",
        ),
    },
    "pricing_v3.rule_management_agendarulesku.v1": {
        "kafka_topic": "postgres.pricing_v3.public.rule_management_agendarulesku",
        "downstream_task_id": "pricing_v3_rule_management_agendarulesku_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.rule_management_agendarulesku.v1",
        ),
    },
    "pricing_v3.rule_management_kviinternalbenchmarkingladder.v1": {
        "kafka_topic": "postgres.pricing_v3.public.rule_management_kviinternalbenchmarkingladder",
        "downstream_task_id": "pricing_v3_rule_management_kviinternalbenchmarkingladder_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.rule_management_kviinternalbenchmarkingladder.v1",
        ),
    },
    "pricing_v3.rule_management_masterrule.v3": {
        "kafka_topic": "postgres.pricing_v3.public.rule_management_masterrule",
        "downstream_task_id": "pricing_v3_rule_management_masterrule_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.rule_management_masterrule.v3",
        ),
    },
    "pricing_v3.rule_management_pricingconfiguration.v1": {
        "kafka_topic": "postgres.pricing_v3.public.rule_management_pricingconfiguration",
        "downstream_task_id": "pricing_v3_rule_management_pricingconfiguration_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.rule_management_pricingconfiguration.v1",
        ),
    },
    "pricing_v3.rule_management_pricingconfigurationsheetfile.v1": {
        "kafka_topic": "postgres.pricing_v3.public.rule_management_pricingconfigurationsheetfile",
        "downstream_task_id": "pricing_v3_rule_management_pricingconfigurationsheetfile_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.rule_management_pricingconfigurationsheetfile.v1",
        ),
    },
    "pricing_v3.rule_management_promotiontags.v1": {
        "kafka_topic": "postgres.pricing_v3.public.rule_management_promotiontags",
        "downstream_task_id": "pricing_v3_rule_management_promotiontags_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.rule_management_promotiontags.v1",
        ),
    },
    "pricing_v3.rule_management_sheetfile.v3": {
        "kafka_topic": "postgres.pricing_v3.public.rule_management_sheetfile",
        "downstream_task_id": "pricing_v3_rule_management_sheetfile_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "pricing_v3.rule_management_sheetfile.v3",
        ),
    },
}

# Default values for all flow ids, to override pass `spark_application_jar` in `branch_op_flows` for respective flow id
spark_application_jar = "s3://prod-dse-srp-configs/jars/nessie/Nessie-assembly-2.1.2.jar"

# Default values for all flow ids, to override any value pass `spark_submit_options` in `branch_op_flows` for respective flow id
spark_submit_options = {
    "--deploy-mode": "cluster",
    "--master": "yarn",
    "--conf": {
        "spark.serializer": "org.apache.spark.serializer.KryoSerializer",
        "spark.kryo.registrator": "org.apache.spark.HoodieSparkKryoRegistrar",
        "spark.sql.catalog.spark_catalog": "org.apache.spark.sql.hudi.catalog.HoodieCatalog",
        "spark.sql.extensions": "org.apache.spark.sql.hudi.HoodieSparkSessionExtension",
        "spark.yarn.heterogeneousExecutors.enabled": "false",
        "spark.sql.hive.convertMetastoreParquet": "false",
    },
    "--class": "com.grofers.nessie.Nessie",
}


def slack_failure_alert(context):
    dag_id = context.get("task_instance").dag_id
    run_id = context.get("run_id")
    task_id = context.get("task_instance").task_id
    log_url = context.get("task_instance").log_url.replace("http://", "https://")
    exception = str(context.get("exception"))
    override_channel_mapping = {
        "terminated_with_errors": "bl-data-spot-interruptions",
        "spot": "bl-data-spot-interruptions",
    }

    slack_failure_msg = """
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner*: <!subteam^S089D0GQRCJ>
        *Log Url*: {log_url}
        {exception}
    """.format(
        dag_id=dag_id,
        run_id=run_id,
        task_id=task_id,
        log_url=log_url,
        exception=exception,
    )

    slack_alert_configs = [
        {"channel": "bl-data-alerts-p1"},
    ]

    # Check if exception exists and contains any key from override_channel_mapping
    if exception and override_channel_mapping:
        for error_keyword, override_channel in override_channel_mapping.items():
            if error_keyword.lower() in exception.lower():
                # Override all channels with the mapped channel
                slack_alert_configs = [{"channel": override_channel}]
                break

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(channel=channel, text=slack_failure_msg, source_info=False)
        except Exception as e:
            logging.warning(e)
            message = (
                f"<!subteam^S089D0GQRCJ> `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(channel="bl-data-alerts-p1", text=message, source_info=False)


def failure_alerts(context):
    slack_failure_alert(context)


args = {
    "owner": "<EMAIL>",
    "on_failure_callback": failure_alerts,
    "start_date": dt.datetime.strptime("2021-08-18T10:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": dt.datetime.strptime("2026-08-15T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "slack_id": "S089D0GQRCJ",
}

dag = DAG(
    dag_id="de_replicate_etl_pricing_v3_1_s3_to_hudi_v2",
    default_args=args,
    schedule_interval="18,48 * * * *",
    tags=["de", "replicate", "pricing_v3_1"],
    catchup=False,
    concurrency=15,
    max_active_runs=1,
    render_template_as_native_obj=True,
)


s3_hudi_cluster_allocation_task = SRPClusterMappingOperatorV2(
    task_id="cluster_allocation_pricing_v3_1_s3_to_hudi",
    dag=dag,
    connection_alias="nessie",
    flows_to_run=branch_op_flows,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


s3_hudi_bulk_submit_emr_steps_task = SRPEmrBulkAddStepsOperator(
    task_id="bulk_step_submission_pricing_v3_1_s3_to_hudi",
    dag=dag,
    aws_conn_id="aws_default",
    srp_flows_info="{{ task_instance.xcom_pull('cluster_allocation_pricing_v3_1_s3_to_hudi', key='flows_info') }}",
    spark_application_jar=spark_application_jar,
    spark_submit_options=spark_submit_options,
    srp_task_id_cluster_mapping="{{ task_instance.xcom_pull('cluster_allocation_pricing_v3_1_s3_to_hudi', key='return_value') }}",
    queue="celery",
)


s3_to_hudi_emr_sensor_1 = EmrStateSensorAsync(
    task_id="pricing_v3_approval_domain_rulesapproval_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.approval_domain_rulesapproval.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.approval_domain_rulesapproval.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_1 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_approval_domain_rulesapproval",
    topic_name="postgres.pricing_v3.public.approval_domain_rulesapproval",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_1
    >> create_view_on_presto_1
)


s3_to_hudi_emr_sensor_2 = EmrStateSensorAsync(
    task_id="pricing_v3_approval_domain_sheetfile_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.approval_domain_sheetfile.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.approval_domain_sheetfile.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_2 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_approval_domain_sheetfile",
    topic_name="postgres.pricing_v3.public.approval_domain_sheetfile",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_2
    >> create_view_on_presto_2
)


s3_to_hudi_emr_sensor_3 = EmrStateSensorAsync(
    task_id="pricing_v3_attribute_management_allocationgrouprulemap_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.attribute_management_allocationgrouprulemap.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.attribute_management_allocationgrouprulemap.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_3 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_attribute_management_allocationgrouprulemap",
    topic_name="postgres.pricing_v3.public.attribute_management_allocationgrouprulemap",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_3
    >> create_view_on_presto_3
)


s3_to_hudi_emr_sensor_4 = EmrStateSensorAsync(
    task_id="pricing_v3_attribute_management_attributemasterrule_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.attribute_management_attributemasterrule.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.attribute_management_attributemasterrule.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_4 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_attribute_management_attributemasterrule",
    topic_name="postgres.pricing_v3.public.attribute_management_attributemasterrule",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_4
    >> create_view_on_presto_4
)


s3_to_hudi_emr_sensor_5 = EmrStateSensorAsync(
    task_id="pricing_v3_attribute_management_brandsfundallocationgroup_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.attribute_management_brandsfundallocationgroup.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.attribute_management_brandsfundallocationgroup.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_5 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_attribute_management_brandsfundallocationgroup",
    topic_name="postgres.pricing_v3.public.attribute_management_brandsfundallocationgroup",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_5
    >> create_view_on_presto_5
)


s3_to_hudi_emr_sensor_6 = EmrStateSensorAsync(
    task_id="pricing_v3_attribute_management_brandsfundapproval_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.attribute_management_brandsfundapproval.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.attribute_management_brandsfundapproval.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_6 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_attribute_management_brandsfundapproval",
    topic_name="postgres.pricing_v3.public.attribute_management_brandsfundapproval",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_6
    >> create_view_on_presto_6
)


s3_to_hudi_emr_sensor_7 = EmrStateSensorAsync(
    task_id="pricing_v3_attribute_management_brandsfundcityapproval_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.attribute_management_brandsfundcityapproval.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.attribute_management_brandsfundcityapproval.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_7 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_attribute_management_brandsfundcityapproval",
    topic_name="postgres.pricing_v3.public.attribute_management_brandsfundcityapproval",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_7
    >> create_view_on_presto_7
)


s3_to_hudi_emr_sensor_8 = EmrStateSensorAsync(
    task_id="pricing_v3_attribute_management_brandsfundsummary_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.attribute_management_brandsfundsummary.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.attribute_management_brandsfundsummary.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_8 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_attribute_management_brandsfundsummary",
    topic_name="postgres.pricing_v3.public.attribute_management_brandsfundsummary",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_8
    >> create_view_on_presto_8
)


s3_to_hudi_emr_sensor_9 = EmrStateSensorAsync(
    task_id="pricing_v3_attribute_management_brandssheetfile_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.attribute_management_brandssheetfile.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.attribute_management_brandssheetfile.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_9 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_attribute_management_brandssheetfile",
    topic_name="postgres.pricing_v3.public.attribute_management_brandssheetfile",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_9
    >> create_view_on_presto_9
)


s3_to_hudi_emr_sensor_10 = EmrStateSensorAsync(
    task_id="pricing_v3_attribute_management_itembrandfundagendaattribute_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.attribute_management_itembrandfundagendaattribute.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.attribute_management_itembrandfundagendaattribute.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_10 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_attribute_management_itembrandfundagendaattribute",
    topic_name="postgres.pricing_v3.public.attribute_management_itembrandfundagendaattribute",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_10
    >> create_view_on_presto_10
)


s3_to_hudi_emr_sensor_11 = EmrStateSensorAsync(
    task_id="pricing_v3_attribute_management_kvibrandfundagendaattribute_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.attribute_management_kvibrandfundagendaattribute.v3', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.attribute_management_kvibrandfundagendaattribute.v3', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_11 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_attribute_management_kvibrandfundagendaattribute",
    topic_name="postgres.pricing_v3.public.attribute_management_kvibrandfundagendaattribute",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_11
    >> create_view_on_presto_11
)


s3_to_hudi_emr_sensor_12 = EmrStateSensorAsync(
    task_id="pricing_v3_attribute_management_manufactureremail_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.attribute_management_manufactureremail.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.attribute_management_manufactureremail.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_12 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_attribute_management_manufactureremail",
    topic_name="postgres.pricing_v3.public.attribute_management_manufactureremail",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_12
    >> create_view_on_presto_12
)


s3_to_hudi_emr_sensor_13 = EmrStateSensorAsync(
    task_id="pricing_v3_bundles_and_combos_approval_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.bundles_and_combos_approval.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.bundles_and_combos_approval.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_13 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_bundles_and_combos_approval",
    topic_name="postgres.pricing_v3.public.bundles_and_combos_approval",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_13
    >> create_view_on_presto_13
)


s3_to_hudi_emr_sensor_14 = EmrStateSensorAsync(
    task_id="pricing_v3_bundles_and_combos_city_approval_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.bundles_and_combos_city_approval.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.bundles_and_combos_city_approval.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_14 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_bundles_and_combos_city_approval",
    topic_name="postgres.pricing_v3.public.bundles_and_combos_city_approval",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_14
    >> create_view_on_presto_14
)


s3_to_hudi_emr_sensor_15 = EmrStateSensorAsync(
    task_id="pricing_v3_bundles_and_combos_domain_bundlebrandfundsheetfile_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.bundles_and_combos_domain_bundlebrandfundsheetfile.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.bundles_and_combos_domain_bundlebrandfundsheetfile.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_15 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_bundles_and_combos_domain_bundlebrandfundsheetfile",
    topic_name="postgres.pricing_v3.public.bundles_and_combos_domain_bundlebrandfundsheetfile",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_15
    >> create_view_on_presto_15
)


s3_to_hudi_emr_sensor_16 = EmrStateSensorAsync(
    task_id="pricing_v3_catalog_domain_kvi_item_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.catalog_domain_kvi_item.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.catalog_domain_kvi_item.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_16 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_catalog_domain_kvi_item",
    topic_name="postgres.pricing_v3.public.catalog_domain_kvi_item",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_16
    >> create_view_on_presto_16
)


s3_to_hudi_emr_sensor_17 = EmrStateSensorAsync(
    task_id="pricing_v3_catalog_domain_kvitagging_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.catalog_domain_kvitagging.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.catalog_domain_kvitagging.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_17 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_catalog_domain_kvitagging",
    topic_name="postgres.pricing_v3.public.catalog_domain_kvitagging",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_17
    >> create_view_on_presto_17
)


s3_to_hudi_emr_sensor_18 = EmrStateSensorAsync(
    task_id="pricing_v3_competitor_domain_competitorconfiguration_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.competitor_domain_competitorconfiguration.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.competitor_domain_competitorconfiguration.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_18 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_competitor_domain_competitorconfiguration",
    topic_name="postgres.pricing_v3.public.competitor_domain_competitorconfiguration",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_18
    >> create_view_on_presto_18
)


s3_to_hudi_emr_sensor_19 = EmrStateSensorAsync(
    task_id="pricing_v3_competitor_domain_competitormaster_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.competitor_domain_competitormaster.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.competitor_domain_competitormaster.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_19 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_competitor_domain_competitormaster",
    topic_name="postgres.pricing_v3.public.competitor_domain_competitormaster",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_19
    >> create_view_on_presto_19
)


s3_to_hudi_emr_sensor_20 = EmrStateSensorAsync(
    task_id="pricing_v3_cron_tasks_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.cron_tasks.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.cron_tasks.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_20 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_cron_tasks",
    topic_name="postgres.pricing_v3.public.cron_tasks",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_20
    >> create_view_on_presto_20
)


s3_to_hudi_emr_sensor_21 = EmrStateSensorAsync(
    task_id="pricing_v3_event_management_domain_pricingeventerrors_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.event_management_domain_pricingeventerrors.v2', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.event_management_domain_pricingeventerrors.v2', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_21 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_event_management_domain_pricingeventerrors",
    topic_name="postgres.pricing_v3.public.event_management_domain_pricingeventerrors",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_21
    >> create_view_on_presto_21
)


s3_to_hudi_emr_sensor_22 = EmrStateSensorAsync(
    task_id="pricing_v3_pricing_domain_city_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.pricing_domain_city.v3', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.pricing_domain_city.v3', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_22 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_pricing_domain_city",
    topic_name="postgres.pricing_v3.public.pricing_domain_city",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_22
    >> create_view_on_presto_22
)


s3_to_hudi_emr_sensor_23 = EmrStateSensorAsync(
    task_id="pricing_v3_pricing_domain_citycluster_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.pricing_domain_citycluster.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.pricing_domain_citycluster.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_23 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_pricing_domain_citycluster",
    topic_name="postgres.pricing_v3.public.pricing_domain_citycluster",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_23
    >> create_view_on_presto_23
)


s3_to_hudi_emr_sensor_24 = EmrStateSensorAsync(
    task_id="pricing_v3_pricing_domain_cmscity_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.pricing_domain_cmscity.v3', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.pricing_domain_cmscity.v3', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_24 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_pricing_domain_cmscity",
    topic_name="postgres.pricing_v3.public.pricing_domain_cmscity",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_24
    >> create_view_on_presto_24
)


s3_to_hudi_emr_sensor_25 = EmrStateSensorAsync(
    task_id="pricing_v3_pricing_domain_outlet_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.pricing_domain_outlet.v3', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.pricing_domain_outlet.v3', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_25 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_pricing_domain_outlet",
    topic_name="postgres.pricing_v3.public.pricing_domain_outlet",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_25
    >> create_view_on_presto_25
)


s3_to_hudi_emr_sensor_26 = EmrStateSensorAsync(
    task_id="pricing_v3_pricing_domain_pricerecommendation_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.pricing_domain_pricerecommendation.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.pricing_domain_pricerecommendation.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_26 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_pricing_domain_pricerecommendation",
    topic_name="postgres.pricing_v3.public.pricing_domain_pricerecommendation",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_26
    >> create_view_on_presto_26
)


s3_to_hudi_emr_sensor_27 = EmrStateSensorAsync(
    task_id="pricing_v3_pricing_domain_prices_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.pricing_domain_prices.v3', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.pricing_domain_prices.v3', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_27 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_pricing_domain_prices",
    topic_name="postgres.pricing_v3.public.pricing_domain_prices",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_27
    >> create_view_on_presto_27
)


s3_to_hudi_emr_sensor_28 = EmrStateSensorAsync(
    task_id="pricing_v3_pricing_domain_product_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.pricing_domain_product.v4', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.pricing_domain_product.v4', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_28 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_pricing_domain_product",
    topic_name="postgres.pricing_v3.public.pricing_domain_product",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_28
    >> create_view_on_presto_28
)


s3_to_hudi_emr_sensor_29 = EmrStateSensorAsync(
    task_id="pricing_v3_pricing_domain_productchangelog_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.pricing_domain_productchangelog.v3', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.pricing_domain_productchangelog.v3', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_29 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_pricing_domain_productchangelog",
    topic_name="postgres.pricing_v3.public.pricing_domain_productchangelog",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_29
    >> create_view_on_presto_29
)


s3_to_hudi_emr_sensor_30 = EmrStateSensorAsync(
    task_id="pricing_v3_pricing_domain_superstore_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.pricing_domain_superstore.v3', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.pricing_domain_superstore.v3', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_30 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_pricing_domain_superstore",
    topic_name="postgres.pricing_v3.public.pricing_domain_superstore",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_30
    >> create_view_on_presto_30
)


s3_to_hudi_emr_sensor_31 = EmrStateSensorAsync(
    task_id="pricing_v3_replication_domain_itemcityreplicationchangelog_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.replication_domain_itemcityreplicationchangelog.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.replication_domain_itemcityreplicationchangelog.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_31 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_replication_domain_itemcityreplicationchangelog",
    topic_name="postgres.pricing_v3.public.replication_domain_itemcityreplicationchangelog",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_31
    >> create_view_on_presto_31
)


s3_to_hudi_emr_sensor_32 = EmrStateSensorAsync(
    task_id="pricing_v3_rule_management_agendarulekvialignedrm_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.rule_management_agendarulekvialignedrm.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.rule_management_agendarulekvialignedrm.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_32 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_rule_management_agendarulekvialignedrm",
    topic_name="postgres.pricing_v3.public.rule_management_agendarulekvialignedrm",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_32
    >> create_view_on_presto_32
)


s3_to_hudi_emr_sensor_33 = EmrStateSensorAsync(
    task_id="pricing_v3_rule_management_agendarulekviinternal_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.rule_management_agendarulekviinternal.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.rule_management_agendarulekviinternal.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_33 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_rule_management_agendarulekviinternal",
    topic_name="postgres.pricing_v3.public.rule_management_agendarulekviinternal",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_33
    >> create_view_on_presto_33
)


s3_to_hudi_emr_sensor_34 = EmrStateSensorAsync(
    task_id="pricing_v3_rule_management_agendarulekvirm_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.rule_management_agendarulekvirm.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.rule_management_agendarulekvirm.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_34 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_rule_management_agendarulekvirm",
    topic_name="postgres.pricing_v3.public.rule_management_agendarulekvirm",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_34
    >> create_view_on_presto_34
)


s3_to_hudi_emr_sensor_35 = EmrStateSensorAsync(
    task_id="pricing_v3_rule_management_agendarulerm_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.rule_management_agendarulerm.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.rule_management_agendarulerm.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_35 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_rule_management_agendarulerm",
    topic_name="postgres.pricing_v3.public.rule_management_agendarulerm",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_35
    >> create_view_on_presto_35
)


s3_to_hudi_emr_sensor_36 = EmrStateSensorAsync(
    task_id="pricing_v3_rule_management_agendarulermitemid_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.rule_management_agendarulermitemid.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.rule_management_agendarulermitemid.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_36 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_rule_management_agendarulermitemid",
    topic_name="postgres.pricing_v3.public.rule_management_agendarulermitemid",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_36
    >> create_view_on_presto_36
)


s3_to_hudi_emr_sensor_37 = EmrStateSensorAsync(
    task_id="pricing_v3_rule_management_agendarulesku_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.rule_management_agendarulesku.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.rule_management_agendarulesku.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_37 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_rule_management_agendarulesku",
    topic_name="postgres.pricing_v3.public.rule_management_agendarulesku",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_37
    >> create_view_on_presto_37
)


s3_to_hudi_emr_sensor_38 = EmrStateSensorAsync(
    task_id="pricing_v3_rule_management_kviinternalbenchmarkingladder_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.rule_management_kviinternalbenchmarkingladder.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.rule_management_kviinternalbenchmarkingladder.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_38 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_rule_management_kviinternalbenchmarkingladder",
    topic_name="postgres.pricing_v3.public.rule_management_kviinternalbenchmarkingladder",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_38
    >> create_view_on_presto_38
)


s3_to_hudi_emr_sensor_39 = EmrStateSensorAsync(
    task_id="pricing_v3_rule_management_masterrule_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.rule_management_masterrule.v3', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.rule_management_masterrule.v3', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_39 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_rule_management_masterrule",
    topic_name="postgres.pricing_v3.public.rule_management_masterrule",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_39
    >> create_view_on_presto_39
)


s3_to_hudi_emr_sensor_40 = EmrStateSensorAsync(
    task_id="pricing_v3_rule_management_pricingconfiguration_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.rule_management_pricingconfiguration.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.rule_management_pricingconfiguration.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_40 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_rule_management_pricingconfiguration",
    topic_name="postgres.pricing_v3.public.rule_management_pricingconfiguration",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_40
    >> create_view_on_presto_40
)


s3_to_hudi_emr_sensor_41 = EmrStateSensorAsync(
    task_id="pricing_v3_rule_management_pricingconfigurationsheetfile_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.rule_management_pricingconfigurationsheetfile.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.rule_management_pricingconfigurationsheetfile.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_41 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_rule_management_pricingconfigurationsheetfile",
    topic_name="postgres.pricing_v3.public.rule_management_pricingconfigurationsheetfile",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_41
    >> create_view_on_presto_41
)


s3_to_hudi_emr_sensor_42 = EmrStateSensorAsync(
    task_id="pricing_v3_rule_management_promotiontags_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.rule_management_promotiontags.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.rule_management_promotiontags.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_42 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_rule_management_promotiontags",
    topic_name="postgres.pricing_v3.public.rule_management_promotiontags",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_42
    >> create_view_on_presto_42
)


s3_to_hudi_emr_sensor_43 = EmrStateSensorAsync(
    task_id="pricing_v3_rule_management_sheetfile_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.rule_management_sheetfile.v3', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_pricing_v3_1_s3_to_hudi', key='flow_id_job_mapping').get('pricing_v3.rule_management_sheetfile.v3', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_43 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_pricing_v3_rule_management_sheetfile",
    topic_name="postgres.pricing_v3.public.rule_management_sheetfile",
    database="pricing_v3",
    lake_schemaname="lake_pricing_v3",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_43
    >> create_view_on_presto_43
)


if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = dt.timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
