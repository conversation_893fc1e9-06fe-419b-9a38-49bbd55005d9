# -*- coding: utf-8 -*-

import json
import datetime as dt
import ast

import requests
import logging

import airflow
from airflow import DAG
from airflow.hooks.base_hook import BaseHook
from airflow.exceptions import AirflowException
from airflow.providers.amazon.aws.hooks.emr import EmrHook
from airflow.operators.python_operator import PythonOperator, ShortCircuitOperator
from airflow.providers.postgres.operators.postgres import PostgresOperator
from airflow.providers.amazon.aws.operators.emr_add_steps import EmrAddStepsOperator
from airflow.providers.amazon.aws.operators.emr_create_job_flow import EmrCreateJobFlowOperator
from airflow.providers.amazon.aws.operators.emr_terminate_job_flow import (
    EmrTerminateJobFlowOperator,
)
from metrics_plugin import (
    S3ToRedshiftOperator,
    BranchSRPOperator,
    DynamicClusterConfigOperator,
    PrestoCreateViewOperator,
    EmrStateSensorAsync,
)
from kubernetes.client import models as k8s

import pencilbox as pb


branch_op_flows = {
    "vms.contact_model.v2": {
        "kafka_topic": "mysql.vms.vms.contact_model",
        "downstream_task_id": "s3_to_hudi_vms_contact_model",
    },
    "vms.contact_purpose.v2": {
        "kafka_topic": "mysql.vms.vms.contact_purpose",
        "downstream_task_id": "s3_to_hudi_vms_contact_purpose",
    },
    "vms.contact_type.v2": {
        "kafka_topic": "mysql.vms.vms.contact_type",
        "downstream_task_id": "s3_to_hudi_vms_contact_type",
    },
    "vms.delivery_type.v2": {
        "kafka_topic": "mysql.vms.vms.delivery_type",
        "downstream_task_id": "s3_to_hudi_vms_delivery_type",
    },
    "vms.external_api_call_logs.v1": {
        "kafka_topic": "mysql.vms.vms.external_api_call_logs",
        "downstream_task_id": "s3_to_hudi_vms_external_api_call_logs",
    },
    "vms.facility_vendor_address.v1": {
        "kafka_topic": "mysql.vms.vms.facility_vendor_address",
        "downstream_task_id": "s3_to_hudi_vms_facility_vendor_address",
    },
    "vms.io_max_doi_log.v2": {
        "kafka_topic": "mysql.vms.vms.io_max_doi_log",
        "downstream_task_id": "s3_to_hudi_vms_io_max_doi_log",
    },
    "vms.iov_fill_rate_buffer_doi.v2": {
        "kafka_topic": "mysql.vms.vms.iov_fill_rate_buffer_doi",
        "downstream_task_id": "s3_to_hudi_vms_iov_fill_rate_buffer_doi",
    },
    "vms.po_cycle.v2": {
        "kafka_topic": "mysql.vms.vms.po_cycle",
        "downstream_task_id": "s3_to_hudi_vms_po_cycle",
    },
    "vms.po_cycle_types.v2": {
        "kafka_topic": "mysql.vms.vms.po_cycle_types",
        "downstream_task_id": "s3_to_hudi_vms_po_cycle_types",
    },
    "vms.vendor_approval.v1": {
        "kafka_topic": "mysql.vms.vms.vendor_approval",
        "downstream_task_id": "s3_to_hudi_vms_vendor_approval",
    },
    "vms.vendor_config.v1": {
        "kafka_topic": "mysql.vms.vms.vendor_config",
        "downstream_task_id": "s3_to_hudi_vms_vendor_config",
    },
    "vms.vendor_config_log.v1": {
        "kafka_topic": "mysql.vms.vms.vendor_config_log",
        "downstream_task_id": "s3_to_hudi_vms_vendor_config_log",
    },
    "vms.vendor_contact_address.v1": {
        "kafka_topic": "mysql.vms.vms.vendor_contact_address",
        "downstream_task_id": "s3_to_hudi_vms_vendor_contact_address",
    },
    "vms.vendor_item_physical_facility_attributes_log.v2": {
        "kafka_topic": "mysql.vms.vms.vendor_item_physical_facility_attributes_log",
        "downstream_task_id": "s3_to_hudi_vms_vendor_item_physical_facility_attributes_log",
    },
    "vms.vms_company.v1": {
        "kafka_topic": "mysql.vms.vms.vms_company",
        "downstream_task_id": "s3_to_hudi_vms_vms_company",
    },
    "vms.vms_company_details.v1": {
        "kafka_topic": "mysql.vms.vms.vms_company_details",
        "downstream_task_id": "s3_to_hudi_vms_vms_company_details",
    },
    "vms.vms_document_type.v1": {
        "kafka_topic": "mysql.vms.vms.vms_document_type",
        "downstream_task_id": "s3_to_hudi_vms_vms_document_type",
    },
    "vms.vms_line_of_business.v2": {
        "kafka_topic": "mysql.vms.vms.vms_line_of_business",
        "downstream_task_id": "s3_to_hudi_vms_vms_line_of_business",
    },
    "vms.vms_manufacturer_contact_details.v2": {
        "kafka_topic": "mysql.vms.vms.vms_manufacturer_contact_details",
        "downstream_task_id": "s3_to_hudi_vms_vms_manufacturer_contact_details",
    },
    "vms.vms_ownership_types.v2": {
        "kafka_topic": "mysql.vms.vms.vms_ownership_types",
        "downstream_task_id": "s3_to_hudi_vms_vms_ownership_types",
    },
    "vms.vms_vendor_alignment_state.v2": {
        "kafka_topic": "mysql.vms.vms.vms_vendor_alignment_state",
        "downstream_task_id": "s3_to_hudi_vms_vms_vendor_alignment_state",
    },
    "vms.vms_vendor_city_documents.v1": {
        "kafka_topic": "mysql.vms.vms.vms_vendor_city_documents",
        "downstream_task_id": "s3_to_hudi_vms_vms_vendor_city_documents",
    },
    "vms.vms_vendor_city_product_details.v2": {
        "kafka_topic": "mysql.vms.vms.vms_vendor_city_product_details",
        "downstream_task_id": "s3_to_hudi_vms_vms_vendor_city_product_details",
    },
    "vms.vms_vendor_company_mapping.v1": {
        "kafka_topic": "mysql.vms.vms.vms_vendor_company_mapping",
        "downstream_task_id": "s3_to_hudi_vms_vms_vendor_company_mapping",
    },
    "vms.vms_vendor_facility_alignment_log.v1": {
        "kafka_topic": "mysql.vms.vms.vms_vendor_facility_alignment_log",
        "downstream_task_id": "s3_to_hudi_vms_vms_vendor_facility_alignment_log",
    },
    "vms.vms_vendor_group_mapping.v1": {
        "kafka_topic": "mysql.vms.vms.vms_vendor_group_mapping",
        "downstream_task_id": "s3_to_hudi_vms_vms_vendor_group_mapping",
    },
    "vms.vms_vendor_new_pi_logic.v1": {
        "kafka_topic": "mysql.vms.vms.vms_vendor_new_pi_logic",
        "downstream_task_id": "s3_to_hudi_vms_vms_vendor_new_pi_logic",
    },
    "vms.vms_vendor_new_pi_logic_log.v1": {
        "kafka_topic": "mysql.vms.vms.vms_vendor_new_pi_logic_log",
        "downstream_task_id": "s3_to_hudi_vms_vms_vendor_new_pi_logic_log",
    },
}


def slack_failure_alert(context):
    dag_id = context.get("task_instance").dag_id
    run_id = context.get("run_id")
    task_id = context.get("task_instance").task_id
    log_url = context.get("task_instance").log_url.replace("http://", "https://")
    exception = str(context.get("exception"))
    override_channel_mapping = {
        "terminated_with_errors": "bl-data-spot-interruptions",
        "spot": "bl-data-spot-interruptions",
    }

    slack_failure_msg = """
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner*: <!subteam^S089D0GQRCJ>
        *Log Url*: {log_url}
        {exception}
    """.format(
        dag_id=dag_id,
        run_id=run_id,
        task_id=task_id,
        log_url=log_url,
        exception=exception,
    )

    slack_alert_configs = [
        {"channel": "bl-data-alerts-p1"},
    ]

    # Check if exception exists and contains any key from override_channel_mapping
    if exception and override_channel_mapping:
        for error_keyword, override_channel in override_channel_mapping.items():
            if error_keyword.lower() in exception.lower():
                # Override all channels with the mapped channel
                slack_alert_configs = [{"channel": override_channel}]
                break

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(channel=channel, text=slack_failure_msg, source_info=False)
        except Exception as e:
            logging.warning(e)
            message = (
                f"<!subteam^S089D0GQRCJ> `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(channel="bl-data-alerts-p1", text=message, source_info=False)


EMR_SETTINGS_OVERRIDE = {
    # "Name": "emr_launch_airflow_test",
    "LogUri": "s3n://grofers-prod-dse-sgp/elasticmapreduce/",
    "ReleaseLabel": "emr-6.11.1",
    "Applications": [{"Name": "Hadoop"}, {"Name": "Spark"}],
    "Instances": {
        "KeepJobFlowAliveWhenNoSteps": True,
        "Ec2KeyName": "dse-emr",
        "ServiceAccessSecurityGroup": "sg-64858200",
        "Ec2SubnetIds": [
            "subnet-09e5bab68a3640d38",
            "subnet-fe54ac88",
            "subnet-e00fc184",
        ],
        "EmrManagedSlaveSecurityGroup": "sg-587afc3c",
        "EmrManagedMasterSecurityGroup": "sg-587afc3c",
        "InstanceFleets": [],
    },
    "Configurations": [
        {
            "Classification": "spark-hive-site",
            "Properties": {
                "hive.metastore.uris": "thrift://datalake-hive-metastore-service-data.prod-sgp-k8s.grofer.io:9083"
            },
        },
        {
            "Classification": "emrfs-site",
            "Properties": {"fs.s3.maxRetries": "20"},
        },
    ],
    "VisibleToAllUsers": True,
    "JobFlowRole": "EMR_EC2_DefaultRole",
    "ServiceRole": "EMR_DefaultRole",
    "Tags": [
        {"Key": "Role", "Value": "source-replication"},
        {"Key": "Environment", "Value": "prod"},
        {"Key": "Product", "Value": "dse"},
        {"Key": "Service", "Value": "dse-emr-nessie"},
        {"Key": "Dag", "Value": "vms_2"},
        {"Key": "Database", "Value": "vms"},
        {"Key": "Name", "Value": "dse_vms_2_nessie"},
        {"Key": "grofers.io/service", "Value": "source-replication"},
        {"Key": "grofers.io/component", "Value": "source-replication-spark-cluster"},
        {"Key": "grofers.io/component-role", "Value": "data-processing"},
        {"Key": "grofers.io/business-service", "Value": "data-platform"},
        {"Key": "grofers.io/team", "Value": "data-engineering"},
        {"Key": "grofers.io/tribe", "Value": "data"},
        {"Key": "cost:namespace", "Value": "data"},
        {"Key": "cost:application", "Value": "emr"},
    ],
}


def get_emr_settings_override(**kwargs):
    EMR_SETTINGS_OVERRIDE["Name"] = "dse_{table}_nessie".format(table=kwargs["table"])
    # string -> dictionary
    kwargs["emr_settings_override"] = ast.literal_eval(kwargs["emr_settings_override"])
    EMR_SETTINGS_OVERRIDE["Instances"]["InstanceFleets"] = kwargs["emr_settings_override"][
        "emr_instance_fleets_config"
    ]
    EMR_SETTINGS_OVERRIDE["StepConcurrencyLevel"] = kwargs["emr_settings_override"][
        "step_concurrency_level"
    ]
    return EMR_SETTINGS_OVERRIDE


def config_validator(**kwargs):
    try:
        config = ast.literal_eval(kwargs["config"])
        logging.info("Validating the config string", config)
        if config["step_concurrency_level"]:
            return True
        else:
            return False
    except Exception as e:
        logging.exception("Error: ", e)
        return False


def fetch_spark_steps(flow_id, flow_type, nessie_version, spark_submit_mode="cluster", **kwargs):

    logging.info(f"Creating Spark Submit Step for flow_id {flow_id} in {spark_submit_mode} mode")

    if spark_submit_mode == "client":
        spark_submit = [
            "spark-submit",
            "--deploy-mode",
            f"{spark_submit_mode}",
            "--master",
            "yarn",
            "--conf",
            "spark.serializer=org.apache.spark.serializer.KryoSerializer",
            "--conf",
            "spark.sql.hive.convertMetastoreParquet=false",
            "--conf",
            "spark.driver.memory=3g",
            "--conf",
            "spark.driver.extraJavaOptions=-XX:NewSize=1g -XX:SurvivorRatio=2 -XX:+UseCompressedOops -XX:+UseConcMarkSweepGC -XX:+UseParNewGC -XX:CMSInitiatingOccupancyFraction=70 -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -XX:+PrintGCDateStamps -XX:+PrintGCApplicationStoppedTime -XX:+PrintGCApplicationConcurrentTime -XX:+PrintTenuringDistribution -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/tmp/hoodie-heapdump.hprof",
            "--conf",
            "spark.yarn.heterogeneousExecutors.enabled=false",
            "--conf",
            "spark.kryo.registrator=org.apache.spark.HoodieSparkKryoRegistrar",
            "--conf",
            "spark.sql.catalog.spark_catalog=org.apache.spark.sql.hudi.catalog.HoodieCatalog",
            "--conf",
            "spark.sql.extensions=org.apache.spark.sql.hudi.HoodieSparkSessionExtension",
            "--class",
            "com.grofers.nessie.Nessie",
            f"s3://prod-dse-srp-configs/jars/nessie/Nessie-assembly-{nessie_version}.jar",
            f"{flow_type}",
            "--flowId",
            f"{flow_id}",
        ]

    elif spark_submit_mode == "cluster":
        spark_submit = (
            "spark-submit",
            "--name",
            f"{flow_type}-{flow_id}",
            "--deploy-mode",
            f"{spark_submit_mode}",
            "--master",
            "yarn",
            "--conf",
            "spark.serializer=org.apache.spark.serializer.KryoSerializer",
            "--conf",
            "spark.sql.hive.convertMetastoreParquet=false",
            "--conf",
            "spark.yarn.heterogeneousExecutors.enabled=false",
            "--conf",
            "spark.kryo.registrator=org.apache.spark.HoodieSparkKryoRegistrar",
            "--conf",
            "spark.sql.catalog.spark_catalog=org.apache.spark.sql.hudi.catalog.HoodieCatalog",
            "--conf",
            "spark.sql.extensions=org.apache.spark.sql.hudi.HoodieSparkSessionExtension",
            "--class",
            "com.grofers.nessie.Nessie",
            f"s3://prod-dse-srp-configs/jars/nessie/Nessie-assembly-{nessie_version}.jar",
            f"{flow_type}",
            "--flowId",
            f"{flow_id}",
        )

    else:
        raise NotImplementedError("Incorrect spark submit mode provided")

    return [
        {
            "Name": f"""{flow_id}-{flow_type}-{dt.datetime.now().timestamp()}""",
            "ActionOnFailure": "CONTINUE",
            "HadoopJarStep": {
                "Jar": "command-runner.jar",
                "Args": spark_submit,
            },
        }
    ]


def failure_alerts(context):
    slack_failure_alert(context)


args = {
    "owner": "<EMAIL>",
    "on_failure_callback": failure_alerts,
    "start_date": dt.datetime.strptime("2021-09-20T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": dt.datetime.strptime("2026-08-15T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "slack_id": "S089D0GQRCJ",
}

dag = DAG(
    dag_id="de_replicate_etl_vms_2_v1",
    default_args=args,
    schedule_interval="23 */3 * * *",
    tags=["de", "replicate", "vms_2", "de", "replicate", "etl"],
    catchup=False,
    concurrency=15,
    max_active_runs=1,
)

cluster_config_task = DynamicClusterConfigOperator(
    task_id="cluster_config_task_vms_2",
    dag=dag,
    flows_to_run=branch_op_flows,
    connection_alias="nessie",
    queue="celery",
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
)


short_circuit_task = ShortCircuitOperator(
    task_id="short_circuit_vms_2",
    op_kwargs={
        "config": "{{ task_instance.xcom_pull('cluster_config_task_vms_2', key='return_value') }}"
    },
    python_callable=config_validator,
    dag=dag,
    queue="celery",
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
)


emr_cluster_config_task = PythonOperator(
    task_id="emr_cluster_config_task_vms_2",
    op_kwargs={
        "table": "vms_2",
        "emr_settings_override": "{{ task_instance.xcom_pull('cluster_config_task_vms_2', key='return_value') }}",
    },
    python_callable=get_emr_settings_override,
    dag=dag,
    queue="celery",
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
)


cluster_create_task = EmrCreateJobFlowOperator(
    task_id="create_emr_cluster_vms_2",
    job_flow_overrides="{{ task_instance.xcom_pull('emr_cluster_config_task_vms_2', key='return_value') }}",
    aws_conn_id="aws_default",
    emr_conn_id="emr_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


cluster_terminate_task = EmrTerminateJobFlowOperator(
    task_id="terminate_emr_cluster_vms_2",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    trigger_rule="all_done",
    priority_weight=3,
)


flows_branching_task = BranchSRPOperator(
    task_id="flows_branching_vms_2",
    dag=dag,
    flows_to_run=branch_op_flows,
    connection_alias="nessie",
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


s3_to_hudi_task_1 = EmrAddStepsOperator(
    task_id="s3_to_hudi_vms_contact_model",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="vms.contact_model.v2",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_1 = EmrStateSensorAsync(
    task_id="load_to_lake_vms_contact_model_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_vms_contact_model', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_1 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vms_contact_model",
    topic_name="mysql.vms.vms.contact_model",
    database="vms",
    lake_schemaname="lake_vms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_1
    >> s3_to_hudi_sensor_1
    >> cluster_terminate_task
)

s3_to_hudi_sensor_1 >> create_view_on_presto_1


s3_to_hudi_task_2 = EmrAddStepsOperator(
    task_id="s3_to_hudi_vms_contact_purpose",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="vms.contact_purpose.v2",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_2 = EmrStateSensorAsync(
    task_id="load_to_lake_vms_contact_purpose_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_vms_contact_purpose', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_2 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vms_contact_purpose",
    topic_name="mysql.vms.vms.contact_purpose",
    database="vms",
    lake_schemaname="lake_vms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_2
    >> s3_to_hudi_sensor_2
    >> cluster_terminate_task
)

s3_to_hudi_sensor_2 >> create_view_on_presto_2


s3_to_hudi_task_3 = EmrAddStepsOperator(
    task_id="s3_to_hudi_vms_contact_type",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="vms.contact_type.v2",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_3 = EmrStateSensorAsync(
    task_id="load_to_lake_vms_contact_type_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_vms_contact_type', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_3 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vms_contact_type",
    topic_name="mysql.vms.vms.contact_type",
    database="vms",
    lake_schemaname="lake_vms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_3
    >> s3_to_hudi_sensor_3
    >> cluster_terminate_task
)

s3_to_hudi_sensor_3 >> create_view_on_presto_3


s3_to_hudi_task_4 = EmrAddStepsOperator(
    task_id="s3_to_hudi_vms_delivery_type",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="vms.delivery_type.v2",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_4 = EmrStateSensorAsync(
    task_id="load_to_lake_vms_delivery_type_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_vms_delivery_type', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_4 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vms_delivery_type",
    topic_name="mysql.vms.vms.delivery_type",
    database="vms",
    lake_schemaname="lake_vms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_4
    >> s3_to_hudi_sensor_4
    >> cluster_terminate_task
)

s3_to_hudi_sensor_4 >> create_view_on_presto_4


s3_to_hudi_task_5 = EmrAddStepsOperator(
    task_id="s3_to_hudi_vms_external_api_call_logs",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="vms.external_api_call_logs.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_5 = EmrStateSensorAsync(
    task_id="load_to_lake_vms_external_api_call_logs_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_vms_external_api_call_logs', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_5 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vms_external_api_call_logs",
    topic_name="mysql.vms.vms.external_api_call_logs",
    database="vms",
    lake_schemaname="lake_vms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_5
    >> s3_to_hudi_sensor_5
    >> cluster_terminate_task
)

s3_to_hudi_sensor_5 >> create_view_on_presto_5


s3_to_hudi_task_6 = EmrAddStepsOperator(
    task_id="s3_to_hudi_vms_facility_vendor_address",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="vms.facility_vendor_address.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_6 = EmrStateSensorAsync(
    task_id="load_to_lake_vms_facility_vendor_address_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_vms_facility_vendor_address', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_6 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vms_facility_vendor_address",
    topic_name="mysql.vms.vms.facility_vendor_address",
    database="vms",
    lake_schemaname="lake_vms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_6
    >> s3_to_hudi_sensor_6
    >> cluster_terminate_task
)

s3_to_hudi_sensor_6 >> create_view_on_presto_6


s3_to_hudi_task_7 = EmrAddStepsOperator(
    task_id="s3_to_hudi_vms_io_max_doi_log",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="vms.io_max_doi_log.v2",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_7 = EmrStateSensorAsync(
    task_id="load_to_lake_vms_io_max_doi_log_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_vms_io_max_doi_log', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_7 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vms_io_max_doi_log",
    topic_name="mysql.vms.vms.io_max_doi_log",
    database="vms",
    lake_schemaname="lake_vms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_7
    >> s3_to_hudi_sensor_7
    >> cluster_terminate_task
)

s3_to_hudi_sensor_7 >> create_view_on_presto_7


s3_to_hudi_task_8 = EmrAddStepsOperator(
    task_id="s3_to_hudi_vms_iov_fill_rate_buffer_doi",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="vms.iov_fill_rate_buffer_doi.v2",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_8 = EmrStateSensorAsync(
    task_id="load_to_lake_vms_iov_fill_rate_buffer_doi_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_vms_iov_fill_rate_buffer_doi', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_8 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vms_iov_fill_rate_buffer_doi",
    topic_name="mysql.vms.vms.iov_fill_rate_buffer_doi",
    database="vms",
    lake_schemaname="lake_vms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_8
    >> s3_to_hudi_sensor_8
    >> cluster_terminate_task
)

s3_to_hudi_sensor_8 >> create_view_on_presto_8


s3_to_hudi_task_9 = EmrAddStepsOperator(
    task_id="s3_to_hudi_vms_po_cycle",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="vms.po_cycle.v2",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_9 = EmrStateSensorAsync(
    task_id="load_to_lake_vms_po_cycle_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_vms_po_cycle', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_9 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vms_po_cycle",
    topic_name="mysql.vms.vms.po_cycle",
    database="vms",
    lake_schemaname="lake_vms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_9
    >> s3_to_hudi_sensor_9
    >> cluster_terminate_task
)

s3_to_hudi_sensor_9 >> create_view_on_presto_9


s3_to_hudi_task_10 = EmrAddStepsOperator(
    task_id="s3_to_hudi_vms_po_cycle_types",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="vms.po_cycle_types.v2",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_10 = EmrStateSensorAsync(
    task_id="load_to_lake_vms_po_cycle_types_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_vms_po_cycle_types', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_10 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vms_po_cycle_types",
    topic_name="mysql.vms.vms.po_cycle_types",
    database="vms",
    lake_schemaname="lake_vms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_10
    >> s3_to_hudi_sensor_10
    >> cluster_terminate_task
)

s3_to_hudi_sensor_10 >> create_view_on_presto_10


s3_to_hudi_task_11 = EmrAddStepsOperator(
    task_id="s3_to_hudi_vms_vendor_approval",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="vms.vendor_approval.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_11 = EmrStateSensorAsync(
    task_id="load_to_lake_vms_vendor_approval_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_vms_vendor_approval', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_11 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vms_vendor_approval",
    topic_name="mysql.vms.vms.vendor_approval",
    database="vms",
    lake_schemaname="lake_vms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_11
    >> s3_to_hudi_sensor_11
    >> cluster_terminate_task
)

s3_to_hudi_sensor_11 >> create_view_on_presto_11


s3_to_hudi_task_12 = EmrAddStepsOperator(
    task_id="s3_to_hudi_vms_vendor_config",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="vms.vendor_config.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_12 = EmrStateSensorAsync(
    task_id="load_to_lake_vms_vendor_config_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_vms_vendor_config', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_12 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vms_vendor_config",
    topic_name="mysql.vms.vms.vendor_config",
    database="vms",
    lake_schemaname="lake_vms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_12
    >> s3_to_hudi_sensor_12
    >> cluster_terminate_task
)

s3_to_hudi_sensor_12 >> create_view_on_presto_12


s3_to_hudi_task_13 = EmrAddStepsOperator(
    task_id="s3_to_hudi_vms_vendor_config_log",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="vms.vendor_config_log.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_13 = EmrStateSensorAsync(
    task_id="load_to_lake_vms_vendor_config_log_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_vms_vendor_config_log', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_13 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vms_vendor_config_log",
    topic_name="mysql.vms.vms.vendor_config_log",
    database="vms",
    lake_schemaname="lake_vms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_13
    >> s3_to_hudi_sensor_13
    >> cluster_terminate_task
)

s3_to_hudi_sensor_13 >> create_view_on_presto_13


s3_to_hudi_task_14 = EmrAddStepsOperator(
    task_id="s3_to_hudi_vms_vendor_contact_address",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="vms.vendor_contact_address.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_14 = EmrStateSensorAsync(
    task_id="load_to_lake_vms_vendor_contact_address_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_vms_vendor_contact_address', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_14 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vms_vendor_contact_address",
    topic_name="mysql.vms.vms.vendor_contact_address",
    database="vms",
    lake_schemaname="lake_vms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_14
    >> s3_to_hudi_sensor_14
    >> cluster_terminate_task
)

s3_to_hudi_sensor_14 >> create_view_on_presto_14


s3_to_hudi_task_15 = EmrAddStepsOperator(
    task_id="s3_to_hudi_vms_vendor_item_physical_facility_attributes_log",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="vms.vendor_item_physical_facility_attributes_log.v2",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_15 = EmrStateSensorAsync(
    task_id="load_to_lake_vms_vendor_item_physical_facility_attributes_log_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_vms_vendor_item_physical_facility_attributes_log', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_15 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vms_vendor_item_physical_facility_attributes_log",
    topic_name="mysql.vms.vms.vendor_item_physical_facility_attributes_log",
    database="vms",
    lake_schemaname="lake_vms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_15
    >> s3_to_hudi_sensor_15
    >> cluster_terminate_task
)

s3_to_hudi_sensor_15 >> create_view_on_presto_15


s3_to_hudi_task_16 = EmrAddStepsOperator(
    task_id="s3_to_hudi_vms_vms_company",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="vms.vms_company.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_16 = EmrStateSensorAsync(
    task_id="load_to_lake_vms_vms_company_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_vms_vms_company', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_16 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vms_vms_company",
    topic_name="mysql.vms.vms.vms_company",
    database="vms",
    lake_schemaname="lake_vms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_16
    >> s3_to_hudi_sensor_16
    >> cluster_terminate_task
)

s3_to_hudi_sensor_16 >> create_view_on_presto_16


s3_to_hudi_task_17 = EmrAddStepsOperator(
    task_id="s3_to_hudi_vms_vms_company_details",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="vms.vms_company_details.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_17 = EmrStateSensorAsync(
    task_id="load_to_lake_vms_vms_company_details_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_vms_vms_company_details', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_17 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vms_vms_company_details",
    topic_name="mysql.vms.vms.vms_company_details",
    database="vms",
    lake_schemaname="lake_vms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_17
    >> s3_to_hudi_sensor_17
    >> cluster_terminate_task
)

s3_to_hudi_sensor_17 >> create_view_on_presto_17


s3_to_hudi_task_18 = EmrAddStepsOperator(
    task_id="s3_to_hudi_vms_vms_document_type",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="vms.vms_document_type.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_18 = EmrStateSensorAsync(
    task_id="load_to_lake_vms_vms_document_type_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_vms_vms_document_type', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_18 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vms_vms_document_type",
    topic_name="mysql.vms.vms.vms_document_type",
    database="vms",
    lake_schemaname="lake_vms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_18
    >> s3_to_hudi_sensor_18
    >> cluster_terminate_task
)

s3_to_hudi_sensor_18 >> create_view_on_presto_18


s3_to_hudi_task_19 = EmrAddStepsOperator(
    task_id="s3_to_hudi_vms_vms_line_of_business",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="vms.vms_line_of_business.v2",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_19 = EmrStateSensorAsync(
    task_id="load_to_lake_vms_vms_line_of_business_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_vms_vms_line_of_business', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_19 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vms_vms_line_of_business",
    topic_name="mysql.vms.vms.vms_line_of_business",
    database="vms",
    lake_schemaname="lake_vms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_19
    >> s3_to_hudi_sensor_19
    >> cluster_terminate_task
)

s3_to_hudi_sensor_19 >> create_view_on_presto_19


s3_to_hudi_task_20 = EmrAddStepsOperator(
    task_id="s3_to_hudi_vms_vms_manufacturer_contact_details",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="vms.vms_manufacturer_contact_details.v2",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_20 = EmrStateSensorAsync(
    task_id="load_to_lake_vms_vms_manufacturer_contact_details_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_vms_vms_manufacturer_contact_details', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_20 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vms_vms_manufacturer_contact_details",
    topic_name="mysql.vms.vms.vms_manufacturer_contact_details",
    database="vms",
    lake_schemaname="lake_vms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_20
    >> s3_to_hudi_sensor_20
    >> cluster_terminate_task
)

s3_to_hudi_sensor_20 >> create_view_on_presto_20


s3_to_hudi_task_21 = EmrAddStepsOperator(
    task_id="s3_to_hudi_vms_vms_ownership_types",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="vms.vms_ownership_types.v2",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_21 = EmrStateSensorAsync(
    task_id="load_to_lake_vms_vms_ownership_types_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_vms_vms_ownership_types', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_21 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vms_vms_ownership_types",
    topic_name="mysql.vms.vms.vms_ownership_types",
    database="vms",
    lake_schemaname="lake_vms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_21
    >> s3_to_hudi_sensor_21
    >> cluster_terminate_task
)

s3_to_hudi_sensor_21 >> create_view_on_presto_21


s3_to_hudi_task_22 = EmrAddStepsOperator(
    task_id="s3_to_hudi_vms_vms_vendor_alignment_state",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="vms.vms_vendor_alignment_state.v2",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_22 = EmrStateSensorAsync(
    task_id="load_to_lake_vms_vms_vendor_alignment_state_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_vms_vms_vendor_alignment_state', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_22 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vms_vms_vendor_alignment_state",
    topic_name="mysql.vms.vms.vms_vendor_alignment_state",
    database="vms",
    lake_schemaname="lake_vms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_22
    >> s3_to_hudi_sensor_22
    >> cluster_terminate_task
)

s3_to_hudi_sensor_22 >> create_view_on_presto_22


s3_to_hudi_task_23 = EmrAddStepsOperator(
    task_id="s3_to_hudi_vms_vms_vendor_city_documents",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="vms.vms_vendor_city_documents.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_23 = EmrStateSensorAsync(
    task_id="load_to_lake_vms_vms_vendor_city_documents_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_vms_vms_vendor_city_documents', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_23 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vms_vms_vendor_city_documents",
    topic_name="mysql.vms.vms.vms_vendor_city_documents",
    database="vms",
    lake_schemaname="lake_vms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_23
    >> s3_to_hudi_sensor_23
    >> cluster_terminate_task
)

s3_to_hudi_sensor_23 >> create_view_on_presto_23


s3_to_hudi_task_24 = EmrAddStepsOperator(
    task_id="s3_to_hudi_vms_vms_vendor_city_product_details",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="vms.vms_vendor_city_product_details.v2",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_24 = EmrStateSensorAsync(
    task_id="load_to_lake_vms_vms_vendor_city_product_details_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_vms_vms_vendor_city_product_details', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_24 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vms_vms_vendor_city_product_details",
    topic_name="mysql.vms.vms.vms_vendor_city_product_details",
    database="vms",
    lake_schemaname="lake_vms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_24
    >> s3_to_hudi_sensor_24
    >> cluster_terminate_task
)

s3_to_hudi_sensor_24 >> create_view_on_presto_24


s3_to_hudi_task_25 = EmrAddStepsOperator(
    task_id="s3_to_hudi_vms_vms_vendor_company_mapping",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="vms.vms_vendor_company_mapping.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_25 = EmrStateSensorAsync(
    task_id="load_to_lake_vms_vms_vendor_company_mapping_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_vms_vms_vendor_company_mapping', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_25 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vms_vms_vendor_company_mapping",
    topic_name="mysql.vms.vms.vms_vendor_company_mapping",
    database="vms",
    lake_schemaname="lake_vms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_25
    >> s3_to_hudi_sensor_25
    >> cluster_terminate_task
)

s3_to_hudi_sensor_25 >> create_view_on_presto_25


s3_to_hudi_task_26 = EmrAddStepsOperator(
    task_id="s3_to_hudi_vms_vms_vendor_facility_alignment_log",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="vms.vms_vendor_facility_alignment_log.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_26 = EmrStateSensorAsync(
    task_id="load_to_lake_vms_vms_vendor_facility_alignment_log_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_vms_vms_vendor_facility_alignment_log', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_26 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vms_vms_vendor_facility_alignment_log",
    topic_name="mysql.vms.vms.vms_vendor_facility_alignment_log",
    database="vms",
    lake_schemaname="lake_vms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_26
    >> s3_to_hudi_sensor_26
    >> cluster_terminate_task
)

s3_to_hudi_sensor_26 >> create_view_on_presto_26


s3_to_hudi_task_27 = EmrAddStepsOperator(
    task_id="s3_to_hudi_vms_vms_vendor_group_mapping",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="vms.vms_vendor_group_mapping.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_27 = EmrStateSensorAsync(
    task_id="load_to_lake_vms_vms_vendor_group_mapping_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_vms_vms_vendor_group_mapping', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_27 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vms_vms_vendor_group_mapping",
    topic_name="mysql.vms.vms.vms_vendor_group_mapping",
    database="vms",
    lake_schemaname="lake_vms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_27
    >> s3_to_hudi_sensor_27
    >> cluster_terminate_task
)

s3_to_hudi_sensor_27 >> create_view_on_presto_27


s3_to_hudi_task_28 = EmrAddStepsOperator(
    task_id="s3_to_hudi_vms_vms_vendor_new_pi_logic",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="vms.vms_vendor_new_pi_logic.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_28 = EmrStateSensorAsync(
    task_id="load_to_lake_vms_vms_vendor_new_pi_logic_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_vms_vms_vendor_new_pi_logic', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_28 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vms_vms_vendor_new_pi_logic",
    topic_name="mysql.vms.vms.vms_vendor_new_pi_logic",
    database="vms",
    lake_schemaname="lake_vms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_28
    >> s3_to_hudi_sensor_28
    >> cluster_terminate_task
)

s3_to_hudi_sensor_28 >> create_view_on_presto_28


s3_to_hudi_task_29 = EmrAddStepsOperator(
    task_id="s3_to_hudi_vms_vms_vendor_new_pi_logic_log",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="vms.vms_vendor_new_pi_logic_log.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_29 = EmrStateSensorAsync(
    task_id="load_to_lake_vms_vms_vendor_new_pi_logic_log_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_vms_2', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_vms_vms_vendor_new_pi_logic_log', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_29 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_vms_vms_vendor_new_pi_logic_log",
    topic_name="mysql.vms.vms.vms_vendor_new_pi_logic_log",
    database="vms",
    lake_schemaname="lake_vms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_29
    >> s3_to_hudi_sensor_29
    >> cluster_terminate_task
)

s3_to_hudi_sensor_29 >> create_view_on_presto_29


if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = dt.timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
