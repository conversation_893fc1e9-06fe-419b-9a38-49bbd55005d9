# -*- coding: utf-8 -*-

import datetime as dt
import logging

from airflow import DAG
from airflow.operators.dagrun_operator import TriggerDagRunOperator
from kubernetes.client import models as k8s
from metrics_plugin import (
    EmrStateSensorAsync,
    SRPClusterMappingOperatorV2,
    SRPEmrBulkAddStepsOperator,
    PrestoCreateViewOperator,
)

import pencilbox as pb


branch_op_flows = {
    "promo_service.campaign_conditions.v1": {
        "kafka_topic": "mysql.promo_service.promo_service.campaign_conditions",
        "downstream_task_id": "promo_service_campaign_conditions_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "promo_service.campaign_conditions.v1",
        ),
    },
    "promo_service.campaign_expressions.v1": {
        "kafka_topic": "mysql.promo_service.promo_service.campaign_expressions",
        "downstream_task_id": "promo_service_campaign_expressions_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "promo_service.campaign_expressions.v1",
        ),
    },
    "promo_service.campaign_history.v1": {
        "kafka_topic": "mysql.promo_service.promo_service.campaign_history",
        "downstream_task_id": "promo_service_campaign_history_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "promo_service.campaign_history.v1",
        ),
    },
    "promo_service.campaigns.v1": {
        "kafka_topic": "mysql.promo_service.promo_service.campaigns",
        "downstream_task_id": "promo_service_campaigns_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "promo_service.campaigns.v1",
        ),
    },
    "promo_service.conditions.v1": {
        "kafka_topic": "mysql.promo_service.promo_service.conditions",
        "downstream_task_id": "promo_service_conditions_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "promo_service.conditions.v1",
        ),
    },
    "promo_service.expressions.v1": {
        "kafka_topic": "mysql.promo_service.promo_service.expressions",
        "downstream_task_id": "promo_service_expressions_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "promo_service.expressions.v1",
        ),
    },
    "promo_service.offer_budget.v1": {
        "kafka_topic": "mysql.promo_service.promo_service.offer_budget",
        "downstream_task_id": "promo_service_offer_budget_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "promo_service.offer_budget.v1",
        ),
    },
    "promo_service.offers.v1": {
        "kafka_topic": "mysql.promo_service.promo_service.offers",
        "downstream_task_id": "promo_service_offers_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "promo_service.offers.v1",
        ),
    },
    "promo_service.promo_codes.v1": {
        "kafka_topic": "mysql.promo_service.promo_service.promo_codes",
        "downstream_task_id": "promo_service_promo_codes_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "promo_service.promo_codes.v1",
        ),
    },
    "promo_service.promos.v1": {
        "kafka_topic": "mysql.promo_service.promo_service.promos",
        "downstream_task_id": "promo_service_promos_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "promo_service.promos.v1",
        ),
    },
    "promo_service.salts.v1": {
        "kafka_topic": "mysql.promo_service.promo_service.salts",
        "downstream_task_id": "promo_service_salts_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "promo_service.salts.v1",
        ),
    },
    "promo_service.user.v1": {
        "kafka_topic": "mysql.promo_service.promo_service.user",
        "downstream_task_id": "promo_service_user_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "promo_service.user.v1",
        ),
    },
}

# Default values for all flow ids, to override pass `spark_application_jar` in `branch_op_flows` for respective flow id
spark_application_jar = "s3://prod-dse-srp-configs/jars/nessie/Nessie-assembly-2.1.2.jar"

# Default values for all flow ids, to override any value pass `spark_submit_options` in `branch_op_flows` for respective flow id
spark_submit_options = {
    "--deploy-mode": "cluster",
    "--master": "yarn",
    "--conf": {
        "spark.serializer": "org.apache.spark.serializer.KryoSerializer",
        "spark.kryo.registrator": "org.apache.spark.HoodieSparkKryoRegistrar",
        "spark.sql.catalog.spark_catalog": "org.apache.spark.sql.hudi.catalog.HoodieCatalog",
        "spark.sql.extensions": "org.apache.spark.sql.hudi.HoodieSparkSessionExtension",
        "spark.yarn.heterogeneousExecutors.enabled": "false",
        "spark.sql.hive.convertMetastoreParquet": "false",
    },
    "--class": "com.grofers.nessie.Nessie",
}


def slack_failure_alert(context):
    dag_id = context.get("task_instance").dag_id
    run_id = context.get("run_id")
    task_id = context.get("task_instance").task_id
    log_url = context.get("task_instance").log_url.replace("http://", "https://")
    exception = str(context.get("exception"))
    override_channel_mapping = {
        "terminated_with_errors": "bl-data-spot-interruptions",
        "spot": "bl-data-spot-interruptions",
    }

    slack_failure_msg = """
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner*: <!subteam^S089D0GQRCJ>
        *Log Url*: {log_url}
        {exception}
    """.format(
        dag_id=dag_id,
        run_id=run_id,
        task_id=task_id,
        log_url=log_url,
        exception=exception,
    )

    slack_alert_configs = [
        {"channel": "bl-data-alerts-p1"},
    ]

    # Check if exception exists and contains any key from override_channel_mapping
    if exception and override_channel_mapping:
        for error_keyword, override_channel in override_channel_mapping.items():
            if error_keyword.lower() in exception.lower():
                # Override all channels with the mapped channel
                slack_alert_configs = [{"channel": override_channel}]
                break

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(channel=channel, text=slack_failure_msg, source_info=False)
        except Exception as e:
            logging.warning(e)
            message = (
                f"<!subteam^S089D0GQRCJ> `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(channel="bl-data-alerts-p1", text=message, source_info=False)


def failure_alerts(context):
    slack_failure_alert(context)


args = {
    "owner": "<EMAIL>",
    "on_failure_callback": failure_alerts,
    "start_date": dt.datetime.strptime("2023-01-16T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": dt.datetime.strptime("2026-08-15T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "slack_id": "S089D0GQRCJ",
}

dag = DAG(
    dag_id="de_replicate_etl_promo_service_1_s3_to_hudi_v1",
    default_args=args,
    schedule_interval="29,59 0-19,22 * * *",
    tags=["de", "replicate", "promo_service_1"],
    catchup=False,
    concurrency=9,
    max_active_runs=1,
    render_template_as_native_obj=True,
)


s3_hudi_cluster_allocation_task = SRPClusterMappingOperatorV2(
    task_id="cluster_allocation_promo_service_1_s3_to_hudi",
    dag=dag,
    connection_alias="nessie",
    flows_to_run=branch_op_flows,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


s3_hudi_bulk_submit_emr_steps_task = SRPEmrBulkAddStepsOperator(
    task_id="bulk_step_submission_promo_service_1_s3_to_hudi",
    dag=dag,
    aws_conn_id="aws_default",
    srp_flows_info="{{ task_instance.xcom_pull('cluster_allocation_promo_service_1_s3_to_hudi', key='flows_info') }}",
    spark_application_jar=spark_application_jar,
    spark_submit_options=spark_submit_options,
    srp_task_id_cluster_mapping="{{ task_instance.xcom_pull('cluster_allocation_promo_service_1_s3_to_hudi', key='return_value') }}",
    queue="celery",
)


s3_to_hudi_emr_sensor_1 = EmrStateSensorAsync(
    task_id="promo_service_campaign_conditions_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_promo_service_1_s3_to_hudi', key='flow_id_job_mapping').get('promo_service.campaign_conditions.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_promo_service_1_s3_to_hudi', key='flow_id_job_mapping').get('promo_service.campaign_conditions.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_application_dse_airflow",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_1 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_promo_service_campaign_conditions",
    topic_name="mysql.promo_service.promo_service.campaign_conditions",
    database="promo_service",
    lake_schemaname="lake_promo_service",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_1
    >> create_view_on_presto_1
)


s3_to_hudi_emr_sensor_2 = EmrStateSensorAsync(
    task_id="promo_service_campaign_expressions_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_promo_service_1_s3_to_hudi', key='flow_id_job_mapping').get('promo_service.campaign_expressions.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_promo_service_1_s3_to_hudi', key='flow_id_job_mapping').get('promo_service.campaign_expressions.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_application_dse_airflow",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_2 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_promo_service_campaign_expressions",
    topic_name="mysql.promo_service.promo_service.campaign_expressions",
    database="promo_service",
    lake_schemaname="lake_promo_service",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_2
    >> create_view_on_presto_2
)


s3_to_hudi_emr_sensor_3 = EmrStateSensorAsync(
    task_id="promo_service_campaign_history_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_promo_service_1_s3_to_hudi', key='flow_id_job_mapping').get('promo_service.campaign_history.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_promo_service_1_s3_to_hudi', key='flow_id_job_mapping').get('promo_service.campaign_history.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_application_dse_airflow",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_3 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_promo_service_campaign_history",
    topic_name="mysql.promo_service.promo_service.campaign_history",
    database="promo_service",
    lake_schemaname="lake_promo_service",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_3
    >> create_view_on_presto_3
)


s3_to_hudi_emr_sensor_4 = EmrStateSensorAsync(
    task_id="promo_service_campaigns_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_promo_service_1_s3_to_hudi', key='flow_id_job_mapping').get('promo_service.campaigns.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_promo_service_1_s3_to_hudi', key='flow_id_job_mapping').get('promo_service.campaigns.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_application_dse_airflow",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_4 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_promo_service_campaigns",
    topic_name="mysql.promo_service.promo_service.campaigns",
    database="promo_service",
    lake_schemaname="lake_promo_service",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_4
    >> create_view_on_presto_4
)


s3_to_hudi_emr_sensor_5 = EmrStateSensorAsync(
    task_id="promo_service_conditions_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_promo_service_1_s3_to_hudi', key='flow_id_job_mapping').get('promo_service.conditions.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_promo_service_1_s3_to_hudi', key='flow_id_job_mapping').get('promo_service.conditions.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_application_dse_airflow",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_5 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_promo_service_conditions",
    topic_name="mysql.promo_service.promo_service.conditions",
    database="promo_service",
    lake_schemaname="lake_promo_service",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_5
    >> create_view_on_presto_5
)


s3_to_hudi_emr_sensor_6 = EmrStateSensorAsync(
    task_id="promo_service_expressions_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_promo_service_1_s3_to_hudi', key='flow_id_job_mapping').get('promo_service.expressions.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_promo_service_1_s3_to_hudi', key='flow_id_job_mapping').get('promo_service.expressions.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_application_dse_airflow",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_6 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_promo_service_expressions",
    topic_name="mysql.promo_service.promo_service.expressions",
    database="promo_service",
    lake_schemaname="lake_promo_service",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_6
    >> create_view_on_presto_6
)


s3_to_hudi_emr_sensor_7 = EmrStateSensorAsync(
    task_id="promo_service_offer_budget_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_promo_service_1_s3_to_hudi', key='flow_id_job_mapping').get('promo_service.offer_budget.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_promo_service_1_s3_to_hudi', key='flow_id_job_mapping').get('promo_service.offer_budget.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_application_dse_airflow",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_7 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_promo_service_offer_budget",
    topic_name="mysql.promo_service.promo_service.offer_budget",
    database="promo_service",
    lake_schemaname="lake_promo_service",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_7
    >> create_view_on_presto_7
)


s3_to_hudi_emr_sensor_8 = EmrStateSensorAsync(
    task_id="promo_service_offers_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_promo_service_1_s3_to_hudi', key='flow_id_job_mapping').get('promo_service.offers.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_promo_service_1_s3_to_hudi', key='flow_id_job_mapping').get('promo_service.offers.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_application_dse_airflow",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_8 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_promo_service_offers",
    topic_name="mysql.promo_service.promo_service.offers",
    database="promo_service",
    lake_schemaname="lake_promo_service",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_8
    >> create_view_on_presto_8
)


s3_to_hudi_emr_sensor_9 = EmrStateSensorAsync(
    task_id="promo_service_promo_codes_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_promo_service_1_s3_to_hudi', key='flow_id_job_mapping').get('promo_service.promo_codes.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_promo_service_1_s3_to_hudi', key='flow_id_job_mapping').get('promo_service.promo_codes.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_application_dse_airflow",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_9 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_promo_service_promo_codes",
    topic_name="mysql.promo_service.promo_service.promo_codes",
    database="promo_service",
    lake_schemaname="lake_promo_service",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_9
    >> create_view_on_presto_9
)


s3_to_hudi_emr_sensor_10 = EmrStateSensorAsync(
    task_id="promo_service_promos_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_promo_service_1_s3_to_hudi', key='flow_id_job_mapping').get('promo_service.promos.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_promo_service_1_s3_to_hudi', key='flow_id_job_mapping').get('promo_service.promos.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_application_dse_airflow",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_10 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_promo_service_promos",
    topic_name="mysql.promo_service.promo_service.promos",
    database="promo_service",
    lake_schemaname="lake_promo_service",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_10
    >> create_view_on_presto_10
)


s3_to_hudi_emr_sensor_11 = EmrStateSensorAsync(
    task_id="promo_service_salts_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_promo_service_1_s3_to_hudi', key='flow_id_job_mapping').get('promo_service.salts.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_promo_service_1_s3_to_hudi', key='flow_id_job_mapping').get('promo_service.salts.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_application_dse_airflow",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_11 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_promo_service_salts",
    topic_name="mysql.promo_service.promo_service.salts",
    database="promo_service",
    lake_schemaname="lake_promo_service",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_11
    >> create_view_on_presto_11
)


s3_to_hudi_emr_sensor_12 = EmrStateSensorAsync(
    task_id="promo_service_user_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_promo_service_1_s3_to_hudi', key='flow_id_job_mapping').get('promo_service.user.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_promo_service_1_s3_to_hudi', key='flow_id_job_mapping').get('promo_service.user.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_application_dse_airflow",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_12 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_promo_service_user",
    topic_name="mysql.promo_service.promo_service.user",
    database="promo_service",
    lake_schemaname="lake_promo_service",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_12
    >> create_view_on_presto_12
)


if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = dt.timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
