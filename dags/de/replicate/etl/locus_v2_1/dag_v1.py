# -*- coding: utf-8 -*-

import json
import datetime as dt
import ast

import requests
import logging

import airflow
from airflow import DAG
from airflow.hooks.base_hook import BaseHook
from airflow.exceptions import AirflowException
from airflow.providers.amazon.aws.hooks.emr import EmrHook
from airflow.operators.python_operator import PythonOperator, ShortCircuitOperator
from airflow.providers.postgres.operators.postgres import PostgresOperator
from airflow.providers.amazon.aws.operators.emr_add_steps import EmrAddStepsOperator
from airflow.providers.amazon.aws.operators.emr_create_job_flow import EmrCreateJobFlowOperator
from airflow.providers.amazon.aws.operators.emr_terminate_job_flow import (
    EmrTerminateJobFlowOperator,
)
from metrics_plugin import (
    S3ToRedshiftOperator,
    BranchSRPOperator,
    DynamicClusterConfigOperator,
    PrestoCreateViewOperator,
    EmrStateSensorAsync,
)
from kubernetes.client import models as k8s

import pencilbox as pb


branch_op_flows = {
    "locus_v2.document_classifications.v1": {
        "kafka_topic": "postgres.locus_v2.public.document_classifications",
        "downstream_task_id": "s3_to_hudi_locus_v2_document_classifications",
    },
    "locus_v2.document_form_mappings.v1": {
        "kafka_topic": "postgres.locus_v2.public.document_form_mappings",
        "downstream_task_id": "s3_to_hudi_locus_v2_document_form_mappings",
    },
    "locus_v2.document_records.v1": {
        "kafka_topic": "postgres.locus_v2.public.document_records",
        "downstream_task_id": "s3_to_hudi_locus_v2_document_records",
    },
    "locus_v2.form.v1": {
        "kafka_topic": "postgres.locus_v2.public.form",
        "downstream_task_id": "s3_to_hudi_locus_v2_form",
    },
    "locus_v2.form_final_status.v1": {
        "kafka_topic": "postgres.locus_v2.public.form_final_status",
        "downstream_task_id": "s3_to_hudi_locus_v2_form_final_status",
    },
    "locus_v2.form_final_status_lookup.v1": {
        "kafka_topic": "postgres.locus_v2.public.form_final_status_lookup",
        "downstream_task_id": "s3_to_hudi_locus_v2_form_final_status_lookup",
    },
    "locus_v2.form_team_status_mapping.v1": {
        "kafka_topic": "postgres.locus_v2.public.form_team_status_mapping",
        "downstream_task_id": "s3_to_hudi_locus_v2_form_team_status_mapping",
    },
    "locus_v2.network_team.v1": {
        "kafka_topic": "postgres.locus_v2.public.network_team",
        "downstream_task_id": "s3_to_hudi_locus_v2_network_team",
    },
    "locus_v2.project.v1": {
        "kafka_topic": "postgres.locus_v2.public.project",
        "downstream_task_id": "s3_to_hudi_locus_v2_project",
    },
    "locus_v2.project_final_status_lookup.v1": {
        "kafka_topic": "postgres.locus_v2.public.project_final_status_lookup",
        "downstream_task_id": "s3_to_hudi_locus_v2_project_final_status_lookup",
    },
    "locus_v2.status_type.v1": {
        "kafka_topic": "postgres.locus_v2.public.status_type",
        "downstream_task_id": "s3_to_hudi_locus_v2_status_type",
    },
    "locus_v2.sub_forms.v1": {
        "kafka_topic": "postgres.locus_v2.public.sub_forms",
        "downstream_task_id": "s3_to_hudi_locus_v2_sub_forms",
    },
    "locus_v2.team_final_status.v1": {
        "kafka_topic": "postgres.locus_v2.public.team_final_status",
        "downstream_task_id": "s3_to_hudi_locus_v2_team_final_status",
    },
    "locus_v2.user_data.v2": {
        "kafka_topic": "postgres.locus_v2.public.user_data",
        "downstream_task_id": "s3_to_hudi_locus_v2_user_data",
    },
    "locus_v2.user_ops_form_mapping.v1": {
        "kafka_topic": "postgres.locus_v2.public.user_ops_form_mapping",
        "downstream_task_id": "s3_to_hudi_locus_v2_user_ops_form_mapping",
    },
    "locus_v2.user_re_project_mapping.v1": {
        "kafka_topic": "postgres.locus_v2.public.user_re_project_mapping",
        "downstream_task_id": "s3_to_hudi_locus_v2_user_re_project_mapping",
    },
    "locus_v2.user_role_mapping.v1": {
        "kafka_topic": "postgres.locus_v2.public.user_role_mapping",
        "downstream_task_id": "s3_to_hudi_locus_v2_user_role_mapping",
    },
    "locus_v2.user_roles.v1": {
        "kafka_topic": "postgres.locus_v2.public.user_roles",
        "downstream_task_id": "s3_to_hudi_locus_v2_user_roles",
    },
    "locus_v2.user_zone_mapping.v1": {
        "kafka_topic": "postgres.locus_v2.public.user_zone_mapping",
        "downstream_task_id": "s3_to_hudi_locus_v2_user_zone_mapping",
    },
    "locus_v2.user_zones.v1": {
        "kafka_topic": "postgres.locus_v2.public.user_zones",
        "downstream_task_id": "s3_to_hudi_locus_v2_user_zones",
    },
    "locus_v2.project_business_network.v1": {
        "kafka_topic": "postgres.locus_v2.public.project_business_network",
        "downstream_task_id": "s3_to_hudi_locus_v2_project_business_network",
    },
}


def slack_failure_alert(context):
    dag_id = context.get("task_instance").dag_id
    run_id = context.get("run_id")
    task_id = context.get("task_instance").task_id
    log_url = context.get("task_instance").log_url.replace("http://", "https://")
    exception = str(context.get("exception"))
    override_channel_mapping = {
        "terminated_with_errors": "bl-data-spot-interruptions",
        "spot": "bl-data-spot-interruptions",
    }

    slack_failure_msg = """
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner*: <!subteam^S089D0GQRCJ>
        *Log Url*: {log_url}
        {exception}
    """.format(
        dag_id=dag_id,
        run_id=run_id,
        task_id=task_id,
        log_url=log_url,
        exception=exception,
    )

    slack_alert_configs = [
        {"channel": "bl-data-alerts-p1"},
    ]

    # Check if exception exists and contains any key from override_channel_mapping
    if exception and override_channel_mapping:
        for error_keyword, override_channel in override_channel_mapping.items():
            if error_keyword.lower() in exception.lower():
                # Override all channels with the mapped channel
                slack_alert_configs = [{"channel": override_channel}]
                break

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(channel=channel, text=slack_failure_msg, source_info=False)
        except Exception as e:
            logging.warning(e)
            message = (
                f"<!subteam^S089D0GQRCJ> `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(channel="bl-data-alerts-p1", text=message, source_info=False)


EMR_SETTINGS_OVERRIDE = {
    # "Name": "emr_launch_airflow_test",
    "LogUri": "s3n://grofers-prod-dse-sgp/elasticmapreduce/",
    "ReleaseLabel": "emr-6.11.1",
    "Applications": [{"Name": "Hadoop"}, {"Name": "Spark"}],
    "Instances": {
        "KeepJobFlowAliveWhenNoSteps": True,
        "Ec2KeyName": "dse-emr",
        "ServiceAccessSecurityGroup": "sg-64858200",
        "Ec2SubnetIds": [
            "subnet-09e5bab68a3640d38",
            "subnet-fe54ac88",
            "subnet-e00fc184",
        ],
        "EmrManagedSlaveSecurityGroup": "sg-587afc3c",
        "EmrManagedMasterSecurityGroup": "sg-587afc3c",
        "InstanceFleets": [],
    },
    "Configurations": [
        {
            "Classification": "spark-hive-site",
            "Properties": {
                "hive.metastore.uris": "thrift://datalake-hive-metastore-service-data.prod-sgp-k8s.grofer.io:9083"
            },
        },
        {
            "Classification": "emrfs-site",
            "Properties": {"fs.s3.maxRetries": "20"},
        },
    ],
    "VisibleToAllUsers": True,
    "JobFlowRole": "EMR_EC2_DefaultRole",
    "ServiceRole": "EMR_DefaultRole",
    "Tags": [
        {"Key": "Role", "Value": "source-replication"},
        {"Key": "Environment", "Value": "prod"},
        {"Key": "Product", "Value": "dse"},
        {"Key": "Service", "Value": "dse-emr-nessie"},
        {"Key": "Dag", "Value": "locus_v2_1"},
        {"Key": "Database", "Value": "locus_v2"},
        {"Key": "Name", "Value": "dse_locus_v2_1_nessie"},
        {"Key": "grofers.io/service", "Value": "source-replication"},
        {"Key": "grofers.io/component", "Value": "source-replication-spark-cluster"},
        {"Key": "grofers.io/component-role", "Value": "data-processing"},
        {"Key": "grofers.io/business-service", "Value": "data-platform"},
        {"Key": "grofers.io/team", "Value": "data-engineering"},
        {"Key": "grofers.io/tribe", "Value": "data"},
        {"Key": "cost:namespace", "Value": "data"},
        {"Key": "cost:application", "Value": "emr"},
    ],
}


def get_emr_settings_override(**kwargs):
    EMR_SETTINGS_OVERRIDE["Name"] = "dse_{table}_nessie".format(table=kwargs["table"])
    # string -> dictionary
    kwargs["emr_settings_override"] = ast.literal_eval(kwargs["emr_settings_override"])
    EMR_SETTINGS_OVERRIDE["Instances"]["InstanceFleets"] = kwargs["emr_settings_override"][
        "emr_instance_fleets_config"
    ]
    EMR_SETTINGS_OVERRIDE["StepConcurrencyLevel"] = kwargs["emr_settings_override"][
        "step_concurrency_level"
    ]
    return EMR_SETTINGS_OVERRIDE


def config_validator(**kwargs):
    try:
        config = ast.literal_eval(kwargs["config"])
        logging.info("Validating the config string", config)
        if config["step_concurrency_level"]:
            return True
        else:
            return False
    except Exception as e:
        logging.exception("Error: ", e)
        return False


def fetch_spark_steps(flow_id, flow_type, nessie_version, spark_submit_mode="cluster", **kwargs):

    logging.info(f"Creating Spark Submit Step for flow_id {flow_id} in {spark_submit_mode} mode")

    if spark_submit_mode == "client":
        spark_submit = [
            "spark-submit",
            "--deploy-mode",
            f"{spark_submit_mode}",
            "--master",
            "yarn",
            "--conf",
            "spark.serializer=org.apache.spark.serializer.KryoSerializer",
            "--conf",
            "spark.sql.hive.convertMetastoreParquet=false",
            "--conf",
            "spark.driver.memory=3g",
            "--conf",
            "spark.driver.extraJavaOptions=-XX:NewSize=1g -XX:SurvivorRatio=2 -XX:+UseCompressedOops -XX:+UseConcMarkSweepGC -XX:+UseParNewGC -XX:CMSInitiatingOccupancyFraction=70 -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -XX:+PrintGCDateStamps -XX:+PrintGCApplicationStoppedTime -XX:+PrintGCApplicationConcurrentTime -XX:+PrintTenuringDistribution -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/tmp/hoodie-heapdump.hprof",
            "--conf",
            "spark.yarn.heterogeneousExecutors.enabled=false",
            "--conf",
            "spark.kryo.registrator=org.apache.spark.HoodieSparkKryoRegistrar",
            "--conf",
            "spark.sql.catalog.spark_catalog=org.apache.spark.sql.hudi.catalog.HoodieCatalog",
            "--conf",
            "spark.sql.extensions=org.apache.spark.sql.hudi.HoodieSparkSessionExtension",
            "--class",
            "com.grofers.nessie.Nessie",
            f"s3://prod-dse-srp-configs/jars/nessie/Nessie-assembly-{nessie_version}.jar",
            f"{flow_type}",
            "--flowId",
            f"{flow_id}",
        ]

    elif spark_submit_mode == "cluster":
        spark_submit = (
            "spark-submit",
            "--name",
            f"{flow_type}-{flow_id}",
            "--deploy-mode",
            f"{spark_submit_mode}",
            "--master",
            "yarn",
            "--conf",
            "spark.serializer=org.apache.spark.serializer.KryoSerializer",
            "--conf",
            "spark.sql.hive.convertMetastoreParquet=false",
            "--conf",
            "spark.yarn.heterogeneousExecutors.enabled=false",
            "--conf",
            "spark.kryo.registrator=org.apache.spark.HoodieSparkKryoRegistrar",
            "--conf",
            "spark.sql.catalog.spark_catalog=org.apache.spark.sql.hudi.catalog.HoodieCatalog",
            "--conf",
            "spark.sql.extensions=org.apache.spark.sql.hudi.HoodieSparkSessionExtension",
            "--class",
            "com.grofers.nessie.Nessie",
            f"s3://prod-dse-srp-configs/jars/nessie/Nessie-assembly-{nessie_version}.jar",
            f"{flow_type}",
            "--flowId",
            f"{flow_id}",
        )

    else:
        raise NotImplementedError("Incorrect spark submit mode provided")

    return [
        {
            "Name": f"""{flow_id}-{flow_type}-{dt.datetime.now().timestamp()}""",
            "ActionOnFailure": "CONTINUE",
            "HadoopJarStep": {
                "Jar": "command-runner.jar",
                "Args": spark_submit,
            },
        }
    ]


def failure_alerts(context):
    slack_failure_alert(context)


args = {
    "owner": "<EMAIL>",
    "on_failure_callback": failure_alerts,
    "start_date": dt.datetime.strptime("2024-08-20T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": dt.datetime.strptime("2026-01-07T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "slack_id": "S089D0GQRCJ",
}

dag = DAG(
    dag_id="de_replicate_etl_locus_v2_1_v1",
    default_args=args,
    schedule_interval="0 */1 * * *",
    tags=["de", "replicate", "locus_v2_1", "de", "replicate", "etl"],
    catchup=False,
    concurrency=9,
    max_active_runs=1,
)

cluster_config_task = DynamicClusterConfigOperator(
    task_id="cluster_config_task_locus_v2_1",
    dag=dag,
    flows_to_run=branch_op_flows,
    connection_alias="nessie",
    queue="celery",
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
)


short_circuit_task = ShortCircuitOperator(
    task_id="short_circuit_locus_v2_1",
    op_kwargs={
        "config": "{{ task_instance.xcom_pull('cluster_config_task_locus_v2_1', key='return_value') }}"
    },
    python_callable=config_validator,
    dag=dag,
    queue="celery",
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
)


emr_cluster_config_task = PythonOperator(
    task_id="emr_cluster_config_task_locus_v2_1",
    op_kwargs={
        "table": "locus_v2_1",
        "emr_settings_override": "{{ task_instance.xcom_pull('cluster_config_task_locus_v2_1', key='return_value') }}",
    },
    python_callable=get_emr_settings_override,
    dag=dag,
    queue="celery",
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
)


cluster_create_task = EmrCreateJobFlowOperator(
    task_id="create_emr_cluster_locus_v2_1",
    job_flow_overrides="{{ task_instance.xcom_pull('emr_cluster_config_task_locus_v2_1', key='return_value') }}",
    aws_conn_id="aws_default",
    emr_conn_id="emr_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


cluster_terminate_task = EmrTerminateJobFlowOperator(
    task_id="terminate_emr_cluster_locus_v2_1",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    trigger_rule="all_done",
    priority_weight=3,
)


flows_branching_task = BranchSRPOperator(
    task_id="flows_branching_locus_v2_1",
    dag=dag,
    flows_to_run=branch_op_flows,
    connection_alias="nessie",
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


s3_to_hudi_task_1 = EmrAddStepsOperator(
    task_id="s3_to_hudi_locus_v2_document_classifications",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="locus_v2.document_classifications.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_1 = EmrStateSensorAsync(
    task_id="load_to_lake_locus_v2_document_classifications_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_locus_v2_document_classifications', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_1 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_locus_v2_document_classifications",
    topic_name="postgres.locus_v2.public.document_classifications",
    database="locus_v2",
    lake_schemaname="lake_locus_v2",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_1
    >> s3_to_hudi_sensor_1
    >> cluster_terminate_task
)

s3_to_hudi_sensor_1 >> create_view_on_presto_1


s3_to_hudi_task_2 = EmrAddStepsOperator(
    task_id="s3_to_hudi_locus_v2_document_form_mappings",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="locus_v2.document_form_mappings.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_2 = EmrStateSensorAsync(
    task_id="load_to_lake_locus_v2_document_form_mappings_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_locus_v2_document_form_mappings', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_2 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_locus_v2_document_form_mappings",
    topic_name="postgres.locus_v2.public.document_form_mappings",
    database="locus_v2",
    lake_schemaname="lake_locus_v2",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_2
    >> s3_to_hudi_sensor_2
    >> cluster_terminate_task
)

s3_to_hudi_sensor_2 >> create_view_on_presto_2


s3_to_hudi_task_3 = EmrAddStepsOperator(
    task_id="s3_to_hudi_locus_v2_document_records",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="locus_v2.document_records.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_3 = EmrStateSensorAsync(
    task_id="load_to_lake_locus_v2_document_records_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_locus_v2_document_records', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_3 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_locus_v2_document_records",
    topic_name="postgres.locus_v2.public.document_records",
    database="locus_v2",
    lake_schemaname="lake_locus_v2",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_3
    >> s3_to_hudi_sensor_3
    >> cluster_terminate_task
)

s3_to_hudi_sensor_3 >> create_view_on_presto_3


s3_to_hudi_task_4 = EmrAddStepsOperator(
    task_id="s3_to_hudi_locus_v2_form",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="locus_v2.form.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_4 = EmrStateSensorAsync(
    task_id="load_to_lake_locus_v2_form_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_locus_v2_form', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_4 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_locus_v2_form",
    topic_name="postgres.locus_v2.public.form",
    database="locus_v2",
    lake_schemaname="lake_locus_v2",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_4
    >> s3_to_hudi_sensor_4
    >> cluster_terminate_task
)

s3_to_hudi_sensor_4 >> create_view_on_presto_4


s3_to_hudi_task_5 = EmrAddStepsOperator(
    task_id="s3_to_hudi_locus_v2_form_final_status",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="locus_v2.form_final_status.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_5 = EmrStateSensorAsync(
    task_id="load_to_lake_locus_v2_form_final_status_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_locus_v2_form_final_status', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_5 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_locus_v2_form_final_status",
    topic_name="postgres.locus_v2.public.form_final_status",
    database="locus_v2",
    lake_schemaname="lake_locus_v2",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_5
    >> s3_to_hudi_sensor_5
    >> cluster_terminate_task
)

s3_to_hudi_sensor_5 >> create_view_on_presto_5


s3_to_hudi_task_6 = EmrAddStepsOperator(
    task_id="s3_to_hudi_locus_v2_form_final_status_lookup",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="locus_v2.form_final_status_lookup.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_6 = EmrStateSensorAsync(
    task_id="load_to_lake_locus_v2_form_final_status_lookup_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_locus_v2_form_final_status_lookup', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_6 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_locus_v2_form_final_status_lookup",
    topic_name="postgres.locus_v2.public.form_final_status_lookup",
    database="locus_v2",
    lake_schemaname="lake_locus_v2",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_6
    >> s3_to_hudi_sensor_6
    >> cluster_terminate_task
)

s3_to_hudi_sensor_6 >> create_view_on_presto_6


s3_to_hudi_task_7 = EmrAddStepsOperator(
    task_id="s3_to_hudi_locus_v2_form_team_status_mapping",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="locus_v2.form_team_status_mapping.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_7 = EmrStateSensorAsync(
    task_id="load_to_lake_locus_v2_form_team_status_mapping_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_locus_v2_form_team_status_mapping', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_7 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_locus_v2_form_team_status_mapping",
    topic_name="postgres.locus_v2.public.form_team_status_mapping",
    database="locus_v2",
    lake_schemaname="lake_locus_v2",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_7
    >> s3_to_hudi_sensor_7
    >> cluster_terminate_task
)

s3_to_hudi_sensor_7 >> create_view_on_presto_7


s3_to_hudi_task_8 = EmrAddStepsOperator(
    task_id="s3_to_hudi_locus_v2_network_team",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="locus_v2.network_team.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_8 = EmrStateSensorAsync(
    task_id="load_to_lake_locus_v2_network_team_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_locus_v2_network_team', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_8 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_locus_v2_network_team",
    topic_name="postgres.locus_v2.public.network_team",
    database="locus_v2",
    lake_schemaname="lake_locus_v2",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_8
    >> s3_to_hudi_sensor_8
    >> cluster_terminate_task
)

s3_to_hudi_sensor_8 >> create_view_on_presto_8


s3_to_hudi_task_9 = EmrAddStepsOperator(
    task_id="s3_to_hudi_locus_v2_project",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="locus_v2.project.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_9 = EmrStateSensorAsync(
    task_id="load_to_lake_locus_v2_project_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_locus_v2_project', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_9 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_locus_v2_project",
    topic_name="postgres.locus_v2.public.project",
    database="locus_v2",
    lake_schemaname="lake_locus_v2",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_9
    >> s3_to_hudi_sensor_9
    >> cluster_terminate_task
)

s3_to_hudi_sensor_9 >> create_view_on_presto_9


s3_to_hudi_task_10 = EmrAddStepsOperator(
    task_id="s3_to_hudi_locus_v2_project_final_status_lookup",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="locus_v2.project_final_status_lookup.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_10 = EmrStateSensorAsync(
    task_id="load_to_lake_locus_v2_project_final_status_lookup_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_locus_v2_project_final_status_lookup', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_10 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_locus_v2_project_final_status_lookup",
    topic_name="postgres.locus_v2.public.project_final_status_lookup",
    database="locus_v2",
    lake_schemaname="lake_locus_v2",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_10
    >> s3_to_hudi_sensor_10
    >> cluster_terminate_task
)

s3_to_hudi_sensor_10 >> create_view_on_presto_10


s3_to_hudi_task_11 = EmrAddStepsOperator(
    task_id="s3_to_hudi_locus_v2_status_type",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="locus_v2.status_type.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_11 = EmrStateSensorAsync(
    task_id="load_to_lake_locus_v2_status_type_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_locus_v2_status_type', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_11 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_locus_v2_status_type",
    topic_name="postgres.locus_v2.public.status_type",
    database="locus_v2",
    lake_schemaname="lake_locus_v2",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_11
    >> s3_to_hudi_sensor_11
    >> cluster_terminate_task
)

s3_to_hudi_sensor_11 >> create_view_on_presto_11


s3_to_hudi_task_12 = EmrAddStepsOperator(
    task_id="s3_to_hudi_locus_v2_sub_forms",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="locus_v2.sub_forms.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_12 = EmrStateSensorAsync(
    task_id="load_to_lake_locus_v2_sub_forms_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_locus_v2_sub_forms', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_12 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_locus_v2_sub_forms",
    topic_name="postgres.locus_v2.public.sub_forms",
    database="locus_v2",
    lake_schemaname="lake_locus_v2",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_12
    >> s3_to_hudi_sensor_12
    >> cluster_terminate_task
)

s3_to_hudi_sensor_12 >> create_view_on_presto_12


s3_to_hudi_task_13 = EmrAddStepsOperator(
    task_id="s3_to_hudi_locus_v2_team_final_status",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="locus_v2.team_final_status.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_13 = EmrStateSensorAsync(
    task_id="load_to_lake_locus_v2_team_final_status_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_locus_v2_team_final_status', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_13 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_locus_v2_team_final_status",
    topic_name="postgres.locus_v2.public.team_final_status",
    database="locus_v2",
    lake_schemaname="lake_locus_v2",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_13
    >> s3_to_hudi_sensor_13
    >> cluster_terminate_task
)

s3_to_hudi_sensor_13 >> create_view_on_presto_13


s3_to_hudi_task_14 = EmrAddStepsOperator(
    task_id="s3_to_hudi_locus_v2_user_data",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="locus_v2.user_data.v2",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_14 = EmrStateSensorAsync(
    task_id="load_to_lake_locus_v2_user_data_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_locus_v2_user_data', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_14 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_locus_v2_user_data",
    topic_name="postgres.locus_v2.public.user_data",
    database="locus_v2",
    lake_schemaname="lake_locus_v2",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_14
    >> s3_to_hudi_sensor_14
    >> cluster_terminate_task
)

s3_to_hudi_sensor_14 >> create_view_on_presto_14


s3_to_hudi_task_15 = EmrAddStepsOperator(
    task_id="s3_to_hudi_locus_v2_user_ops_form_mapping",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="locus_v2.user_ops_form_mapping.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_15 = EmrStateSensorAsync(
    task_id="load_to_lake_locus_v2_user_ops_form_mapping_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_locus_v2_user_ops_form_mapping', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_15 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_locus_v2_user_ops_form_mapping",
    topic_name="postgres.locus_v2.public.user_ops_form_mapping",
    database="locus_v2",
    lake_schemaname="lake_locus_v2",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_15
    >> s3_to_hudi_sensor_15
    >> cluster_terminate_task
)

s3_to_hudi_sensor_15 >> create_view_on_presto_15


s3_to_hudi_task_16 = EmrAddStepsOperator(
    task_id="s3_to_hudi_locus_v2_user_re_project_mapping",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="locus_v2.user_re_project_mapping.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_16 = EmrStateSensorAsync(
    task_id="load_to_lake_locus_v2_user_re_project_mapping_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_locus_v2_user_re_project_mapping', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_16 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_locus_v2_user_re_project_mapping",
    topic_name="postgres.locus_v2.public.user_re_project_mapping",
    database="locus_v2",
    lake_schemaname="lake_locus_v2",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_16
    >> s3_to_hudi_sensor_16
    >> cluster_terminate_task
)

s3_to_hudi_sensor_16 >> create_view_on_presto_16


s3_to_hudi_task_17 = EmrAddStepsOperator(
    task_id="s3_to_hudi_locus_v2_user_role_mapping",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="locus_v2.user_role_mapping.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_17 = EmrStateSensorAsync(
    task_id="load_to_lake_locus_v2_user_role_mapping_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_locus_v2_user_role_mapping', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_17 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_locus_v2_user_role_mapping",
    topic_name="postgres.locus_v2.public.user_role_mapping",
    database="locus_v2",
    lake_schemaname="lake_locus_v2",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_17
    >> s3_to_hudi_sensor_17
    >> cluster_terminate_task
)

s3_to_hudi_sensor_17 >> create_view_on_presto_17


s3_to_hudi_task_18 = EmrAddStepsOperator(
    task_id="s3_to_hudi_locus_v2_user_roles",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="locus_v2.user_roles.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_18 = EmrStateSensorAsync(
    task_id="load_to_lake_locus_v2_user_roles_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_locus_v2_user_roles', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_18 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_locus_v2_user_roles",
    topic_name="postgres.locus_v2.public.user_roles",
    database="locus_v2",
    lake_schemaname="lake_locus_v2",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_18
    >> s3_to_hudi_sensor_18
    >> cluster_terminate_task
)

s3_to_hudi_sensor_18 >> create_view_on_presto_18


s3_to_hudi_task_19 = EmrAddStepsOperator(
    task_id="s3_to_hudi_locus_v2_user_zone_mapping",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="locus_v2.user_zone_mapping.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_19 = EmrStateSensorAsync(
    task_id="load_to_lake_locus_v2_user_zone_mapping_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_locus_v2_user_zone_mapping', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_19 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_locus_v2_user_zone_mapping",
    topic_name="postgres.locus_v2.public.user_zone_mapping",
    database="locus_v2",
    lake_schemaname="lake_locus_v2",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_19
    >> s3_to_hudi_sensor_19
    >> cluster_terminate_task
)

s3_to_hudi_sensor_19 >> create_view_on_presto_19


s3_to_hudi_task_20 = EmrAddStepsOperator(
    task_id="s3_to_hudi_locus_v2_user_zones",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="locus_v2.user_zones.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_20 = EmrStateSensorAsync(
    task_id="load_to_lake_locus_v2_user_zones_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_locus_v2_user_zones', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_20 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_locus_v2_user_zones",
    topic_name="postgres.locus_v2.public.user_zones",
    database="locus_v2",
    lake_schemaname="lake_locus_v2",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_20
    >> s3_to_hudi_sensor_20
    >> cluster_terminate_task
)

s3_to_hudi_sensor_20 >> create_view_on_presto_20


s3_to_hudi_task_21 = EmrAddStepsOperator(
    task_id="s3_to_hudi_locus_v2_project_business_network",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
    aws_conn_id="aws_default",
    steps=fetch_spark_steps(
        flow_id="locus_v2.project_business_network.v1",
        flow_type="s3Hudi",
        nessie_version="2.1.2",
        spark_submit_mode="cluster",
    ),
)

s3_to_hudi_sensor_21 = EmrStateSensorAsync(
    task_id="load_to_lake_locus_v2_project_business_network_sensor",
    cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_locus_v2_1', key='return_value') }}",
    step_id="{{ task_instance.xcom_pull('s3_to_hudi_locus_v2_project_business_network', key='return_value')[0] }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    timeout=10800,
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


create_view_on_presto_21 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_locus_v2_project_business_network",
    topic_name="postgres.locus_v2.public.project_business_network",
    database="locus_v2",
    lake_schemaname="lake_locus_v2",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


(
    cluster_config_task
    >> short_circuit_task
    >> emr_cluster_config_task
    >> cluster_create_task
    >> flows_branching_task
    >> s3_to_hudi_task_21
    >> s3_to_hudi_sensor_21
    >> cluster_terminate_task
)

s3_to_hudi_sensor_21 >> create_view_on_presto_21


if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = dt.timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
