# -*- coding: utf-8 -*-

import datetime as dt
import logging

from airflow import DAG
from airflow.operators.dagrun_operator import TriggerDagRunOperator
from kubernetes.client import models as k8s
from metrics_plugin import (
    EmrStateSensorAsync,
    SRPClusterMappingOperatorV2,
    SRPEmrBulkAddStepsOperator,
    PrestoCreateViewOperator,
)

import pencilbox as pb


branch_op_flows = {
    "transit_server.shipments_shipmentallocationstatelog.v1": {
        "kafka_topic": "postgres.transit_server.public.shipments_shipmentallocationstatelog",
        "downstream_task_id": "transit_server_shipments_shipmentallocationstatelog_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.shipments_shipmentallocationstatelog.v1",
        ),
    },
    "transit_server.shipments_shipmentstatelog.v1": {
        "kafka_topic": "postgres.transit_server.public.shipments_shipmentstatelog",
        "downstream_task_id": "transit_server_shipments_shipmentstatelog_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.shipments_shipmentstatelog.v1",
        ),
    },
    "transit_server.task_management_taskstatelog.v1": {
        "kafka_topic": "postgres.transit_server.public.task_management_taskstatelog",
        "downstream_task_id": "transit_server_task_management_taskstatelog_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.task_management_taskstatelog.v1",
        ),
    },
    "transit_server.transit_address.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_address",
        "downstream_task_id": "transit_server_transit_address_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_address.v1",
        ),
    },
    "transit_server.transit_allocation_batch.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_allocation_batch",
        "downstream_task_id": "transit_server_transit_allocation_batch_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_allocation_batch.v1",
        ),
    },
    "transit_server.transit_allocation_request.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_allocation_request",
        "downstream_task_id": "transit_server_transit_allocation_request_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_allocation_request.v1",
        ),
    },
    "transit_server.transit_allocation_vehicle.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_allocation_vehicle",
        "downstream_task_id": "transit_server_transit_allocation_vehicle_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_allocation_vehicle.v1",
        ),
    },
    "transit_server.transit_allocation_vehicle_label.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_allocation_vehicle_label",
        "downstream_task_id": "transit_server_transit_allocation_vehicle_label_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_allocation_vehicle_label.v1",
        ),
    },
    "transit_server.transit_consignment_container.v2": {
        "kafka_topic": "postgres.transit_server.public.transit_consignment_container",
        "downstream_task_id": "transit_server_transit_consignment_container_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_consignment_container.v2",
        ),
    },
    "transit_server.transit_consignment_item.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_consignment_item",
        "downstream_task_id": "transit_server_transit_consignment_item_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_consignment_item.v1",
        ),
    },
    "transit_server.transit_discrepancy.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_discrepancy",
        "downstream_task_id": "transit_server_transit_discrepancy_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_discrepancy.v1",
        ),
    },
    "transit_server.transit_discrepancy_rca.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_discrepancy_rca",
        "downstream_task_id": "transit_server_transit_discrepancy_rca_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_discrepancy_rca.v1",
        ),
    },
    "transit_server.transit_discrepancy_reason_type.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_discrepancy_reason_type",
        "downstream_task_id": "transit_server_transit_discrepancy_reason_type_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_discrepancy_reason_type.v1",
        ),
    },
    "transit_server.transit_discrepancy_resolution_action_type.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_discrepancy_resolution_action_type",
        "downstream_task_id": "transit_server_transit_discrepancy_resolution_action_type_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_discrepancy_resolution_action_type.v1",
        ),
    },
    "transit_server.transit_express_allocation_field_executive_queue.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_express_allocation_field_executive_queue",
        "downstream_task_id": "transit_server_transit_express_allocation_field_executive_queue_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_express_allocation_field_executive_queue.v1",
        ),
    },
    "transit_server.transit_file_storage.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_file_storage",
        "downstream_task_id": "transit_server_transit_file_storage_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_file_storage.v1",
        ),
    },
    "transit_server.transit_fleet_plan.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_fleet_plan",
        "downstream_task_id": "transit_server_transit_fleet_plan_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_fleet_plan.v1",
        ),
    },
    "transit_server.transit_fleet_plan_dispatch_slot.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_fleet_plan_dispatch_slot",
        "downstream_task_id": "transit_server_transit_fleet_plan_dispatch_slot_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_fleet_plan_dispatch_slot.v1",
        ),
    },
    "transit_server.transit_fleet_plan_slot_assigned_vehicle.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_fleet_plan_slot_assigned_vehicle",
        "downstream_task_id": "transit_server_transit_fleet_plan_slot_assigned_vehicle_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_fleet_plan_slot_assigned_vehicle.v1",
        ),
    },
    "transit_server.transit_location.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_location",
        "downstream_task_id": "transit_server_transit_location_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_location.v1",
        ),
    },
    "transit_server.transit_location_rule_trigger.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_location_rule_trigger",
        "downstream_task_id": "transit_server_transit_location_rule_trigger_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_location_rule_trigger.v1",
        ),
    },
    "transit_server.transit_location_rule_trigger_log.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_location_rule_trigger_log",
        "downstream_task_id": "transit_server_transit_location_rule_trigger_log_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_location_rule_trigger_log.v1",
        ),
    },
    "transit_server.transit_node.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_node",
        "downstream_task_id": "transit_server_transit_node_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_node.v1",
        ),
    },
    "transit_server.transit_node_address.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_node_address",
        "downstream_task_id": "transit_server_transit_node_address_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_node_address.v1",
        ),
    },
    "transit_server.transit_projection_consignment.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_projection_consignment",
        "downstream_task_id": "transit_server_transit_projection_consignment_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_projection_consignment.v1",
        ),
    },
    "transit_server.transit_projection_trip.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_projection_trip",
        "downstream_task_id": "transit_server_transit_projection_trip_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_projection_trip.v1",
        ),
    },
    "transit_server.transit_projection_trip_event_timeline.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_projection_trip_event_timeline",
        "downstream_task_id": "transit_server_transit_projection_trip_event_timeline_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_projection_trip_event_timeline.v1",
        ),
    },
    "transit_server.transit_shipment.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_shipment",
        "downstream_task_id": "transit_server_transit_shipment_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_shipment.v1",
        ),
    },
    "transit_server.transit_shipment_allocation.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_shipment_allocation",
        "downstream_task_id": "transit_server_transit_shipment_allocation_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_shipment_allocation.v1",
        ),
    },
    "transit_server.transit_shipment_label.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_shipment_label",
        "downstream_task_id": "transit_server_transit_shipment_label_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_shipment_label.v1",
        ),
    },
    "transit_server.transit_task.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_task",
        "downstream_task_id": "transit_server_transit_task_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_task.v1",
        ),
    },
    "transit_server.transit_trip_error_log.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_trip_error_log",
        "downstream_task_id": "transit_server_transit_trip_error_log_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_trip_error_log.v1",
        ),
    },
    "transit_server.transit_trip_metadata.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_trip_metadata",
        "downstream_task_id": "transit_server_transit_trip_metadata_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_trip_metadata.v1",
        ),
    },
    "transit_server.transit_user.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_user",
        "downstream_task_id": "transit_server_transit_user_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_user.v1",
        ),
    },
    "transit_server.transit_user_profile.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_user_profile",
        "downstream_task_id": "transit_server_transit_user_profile_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_user_profile.v1",
        ),
    },
    "transit_server.transit_workflow.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_workflow",
        "downstream_task_id": "transit_server_transit_workflow_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_workflow.v1",
        ),
    },
    "transit_server.transit_workflow_step.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_workflow_step",
        "downstream_task_id": "transit_server_transit_workflow_step_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_workflow_step.v1",
        ),
    },
    "transit_server.transit_workflow_type.v1": {
        "kafka_topic": "postgres.transit_server.public.transit_workflow_type",
        "downstream_task_id": "transit_server_transit_workflow_type_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "transit_server.transit_workflow_type.v1",
        ),
    },
}

# Default values for all flow ids, to override pass `spark_application_jar` in `branch_op_flows` for respective flow id
spark_application_jar = "s3://prod-dse-srp-configs/jars/nessie/Nessie-assembly-2.1.2.jar"

# Default values for all flow ids, to override any value pass `spark_submit_options` in `branch_op_flows` for respective flow id
spark_submit_options = {
    "--deploy-mode": "cluster",
    "--master": "yarn",
    "--conf": {
        "spark.serializer": "org.apache.spark.serializer.KryoSerializer",
        "spark.kryo.registrator": "org.apache.spark.HoodieSparkKryoRegistrar",
        "spark.sql.catalog.spark_catalog": "org.apache.spark.sql.hudi.catalog.HoodieCatalog",
        "spark.sql.extensions": "org.apache.spark.sql.hudi.HoodieSparkSessionExtension",
        "spark.yarn.heterogeneousExecutors.enabled": "false",
        "spark.sql.hive.convertMetastoreParquet": "false",
    },
    "--class": "com.grofers.nessie.Nessie",
}


def slack_failure_alert(context):
    dag_id = context.get("task_instance").dag_id
    run_id = context.get("run_id")
    task_id = context.get("task_instance").task_id
    log_url = context.get("task_instance").log_url.replace("http://", "https://")
    exception = str(context.get("exception"))
    override_channel_mapping = {
        "terminated_with_errors": "bl-data-spot-interruptions",
        "user_request": "bl-data-spot-interruptions",
        "spot": "bl-data-spot-interruptions",
    }

    slack_failure_msg = """
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner*: <!subteam^S089D0GQRCJ>
        *Log Url*: {log_url}
        {exception}
    """.format(
        dag_id=dag_id,
        run_id=run_id,
        task_id=task_id,
        log_url=log_url,
        exception=exception,
    )

    slack_alert_configs = [
        {"channel": "bl-data-alerts-p1"},
    ]

    # Check if exception exists and contains any key from override_channel_mapping
    if exception and override_channel_mapping:
        for error_keyword, override_channel in override_channel_mapping.items():
            if error_keyword.lower() in exception.lower():
                # Override all channels with the mapped channel
                slack_alert_configs = [{"channel": override_channel}]
                break

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(channel=channel, text=slack_failure_msg, source_info=False)
        except Exception as e:
            logging.warning(e)
            message = (
                f"<!subteam^S089D0GQRCJ> `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(channel="bl-data-alerts-p1", text=message, source_info=False)


def failure_alerts(context):
    slack_failure_alert(context)


args = {
    "owner": "<EMAIL>",
    "on_failure_callback": failure_alerts,
    "start_date": dt.datetime.strptime("2022-10-26T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": dt.datetime.strptime("2026-08-15T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "slack_id": "S089D0GQRCJ",
}

dag = DAG(
    dag_id="de_replicate_etl_transit_server_1_s3_to_hudi_v1",
    default_args=args,
    schedule_interval="27,57 0-19,22 * * *",
    tags=["de", "replicate", "transit_server_1"],
    catchup=False,
    concurrency=15,
    max_active_runs=1,
    render_template_as_native_obj=True,
)


s3_hudi_cluster_allocation_task = SRPClusterMappingOperatorV2(
    task_id="cluster_allocation_transit_server_1_s3_to_hudi",
    dag=dag,
    connection_alias="nessie",
    flows_to_run=branch_op_flows,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


s3_hudi_bulk_submit_emr_steps_task = SRPEmrBulkAddStepsOperator(
    task_id="bulk_step_submission_transit_server_1_s3_to_hudi",
    dag=dag,
    aws_conn_id="aws_default",
    srp_flows_info="{{ task_instance.xcom_pull('cluster_allocation_transit_server_1_s3_to_hudi', key='flows_info') }}",
    spark_application_jar=spark_application_jar,
    spark_submit_options=spark_submit_options,
    srp_task_id_cluster_mapping="{{ task_instance.xcom_pull('cluster_allocation_transit_server_1_s3_to_hudi', key='return_value') }}",
    queue="celery",
)


s3_to_hudi_emr_sensor_1 = EmrStateSensorAsync(
    task_id="transit_server_shipments_shipmentallocationstatelog_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.shipments_shipmentallocationstatelog.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.shipments_shipmentallocationstatelog.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_1 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_shipments_shipmentallocationstatelog",
    topic_name="postgres.transit_server.public.shipments_shipmentallocationstatelog",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_1
    >> create_view_on_presto_1
)


s3_to_hudi_emr_sensor_2 = EmrStateSensorAsync(
    task_id="transit_server_shipments_shipmentstatelog_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.shipments_shipmentstatelog.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.shipments_shipmentstatelog.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_2 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_shipments_shipmentstatelog",
    topic_name="postgres.transit_server.public.shipments_shipmentstatelog",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_2
    >> create_view_on_presto_2
)


s3_to_hudi_emr_sensor_3 = EmrStateSensorAsync(
    task_id="transit_server_task_management_taskstatelog_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.task_management_taskstatelog.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.task_management_taskstatelog.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_3 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_task_management_taskstatelog",
    topic_name="postgres.transit_server.public.task_management_taskstatelog",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_3
    >> create_view_on_presto_3
)


s3_to_hudi_emr_sensor_4 = EmrStateSensorAsync(
    task_id="transit_server_transit_address_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_address.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_address.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_4 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_address",
    topic_name="postgres.transit_server.public.transit_address",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_4
    >> create_view_on_presto_4
)


s3_to_hudi_emr_sensor_5 = EmrStateSensorAsync(
    task_id="transit_server_transit_allocation_batch_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_allocation_batch.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_allocation_batch.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_5 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_allocation_batch",
    topic_name="postgres.transit_server.public.transit_allocation_batch",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_5
    >> create_view_on_presto_5
)


s3_to_hudi_emr_sensor_6 = EmrStateSensorAsync(
    task_id="transit_server_transit_allocation_request_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_allocation_request.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_allocation_request.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_6 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_allocation_request",
    topic_name="postgres.transit_server.public.transit_allocation_request",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_6
    >> create_view_on_presto_6
)


s3_to_hudi_emr_sensor_7 = EmrStateSensorAsync(
    task_id="transit_server_transit_allocation_vehicle_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_allocation_vehicle.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_allocation_vehicle.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_7 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_allocation_vehicle",
    topic_name="postgres.transit_server.public.transit_allocation_vehicle",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_7
    >> create_view_on_presto_7
)


s3_to_hudi_emr_sensor_8 = EmrStateSensorAsync(
    task_id="transit_server_transit_allocation_vehicle_label_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_allocation_vehicle_label.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_allocation_vehicle_label.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_8 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_allocation_vehicle_label",
    topic_name="postgres.transit_server.public.transit_allocation_vehicle_label",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_8
    >> create_view_on_presto_8
)


s3_to_hudi_emr_sensor_9 = EmrStateSensorAsync(
    task_id="transit_server_transit_consignment_container_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_consignment_container.v2', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_consignment_container.v2', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_9 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_consignment_container",
    topic_name="postgres.transit_server.public.transit_consignment_container",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_9
    >> create_view_on_presto_9
)


s3_to_hudi_emr_sensor_10 = EmrStateSensorAsync(
    task_id="transit_server_transit_consignment_item_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_consignment_item.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_consignment_item.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_10 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_consignment_item",
    topic_name="postgres.transit_server.public.transit_consignment_item",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_10
    >> create_view_on_presto_10
)


s3_to_hudi_emr_sensor_11 = EmrStateSensorAsync(
    task_id="transit_server_transit_discrepancy_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_discrepancy.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_discrepancy.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_11 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_discrepancy",
    topic_name="postgres.transit_server.public.transit_discrepancy",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_11
    >> create_view_on_presto_11
)


s3_to_hudi_emr_sensor_12 = EmrStateSensorAsync(
    task_id="transit_server_transit_discrepancy_rca_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_discrepancy_rca.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_discrepancy_rca.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_12 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_discrepancy_rca",
    topic_name="postgres.transit_server.public.transit_discrepancy_rca",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_12
    >> create_view_on_presto_12
)


s3_to_hudi_emr_sensor_13 = EmrStateSensorAsync(
    task_id="transit_server_transit_discrepancy_reason_type_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_discrepancy_reason_type.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_discrepancy_reason_type.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_13 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_discrepancy_reason_type",
    topic_name="postgres.transit_server.public.transit_discrepancy_reason_type",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_13
    >> create_view_on_presto_13
)


s3_to_hudi_emr_sensor_14 = EmrStateSensorAsync(
    task_id="transit_server_transit_discrepancy_resolution_action_type_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_discrepancy_resolution_action_type.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_discrepancy_resolution_action_type.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_14 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_discrepancy_resolution_action_type",
    topic_name="postgres.transit_server.public.transit_discrepancy_resolution_action_type",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_14
    >> create_view_on_presto_14
)


s3_to_hudi_emr_sensor_15 = EmrStateSensorAsync(
    task_id="transit_server_transit_express_allocation_field_executive_queue_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_express_allocation_field_executive_queue.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_express_allocation_field_executive_queue.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_15 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_express_allocation_field_executive_queue",
    topic_name="postgres.transit_server.public.transit_express_allocation_field_executive_queue",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_15
    >> create_view_on_presto_15
)


s3_to_hudi_emr_sensor_16 = EmrStateSensorAsync(
    task_id="transit_server_transit_file_storage_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_file_storage.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_file_storage.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_16 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_file_storage",
    topic_name="postgres.transit_server.public.transit_file_storage",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_16
    >> create_view_on_presto_16
)


s3_to_hudi_emr_sensor_17 = EmrStateSensorAsync(
    task_id="transit_server_transit_fleet_plan_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_fleet_plan.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_fleet_plan.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_17 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_fleet_plan",
    topic_name="postgres.transit_server.public.transit_fleet_plan",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_17
    >> create_view_on_presto_17
)


s3_to_hudi_emr_sensor_18 = EmrStateSensorAsync(
    task_id="transit_server_transit_fleet_plan_dispatch_slot_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_fleet_plan_dispatch_slot.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_fleet_plan_dispatch_slot.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_18 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_fleet_plan_dispatch_slot",
    topic_name="postgres.transit_server.public.transit_fleet_plan_dispatch_slot",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_18
    >> create_view_on_presto_18
)


s3_to_hudi_emr_sensor_19 = EmrStateSensorAsync(
    task_id="transit_server_transit_fleet_plan_slot_assigned_vehicle_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_fleet_plan_slot_assigned_vehicle.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_fleet_plan_slot_assigned_vehicle.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_19 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_fleet_plan_slot_assigned_vehicle",
    topic_name="postgres.transit_server.public.transit_fleet_plan_slot_assigned_vehicle",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_19
    >> create_view_on_presto_19
)


s3_to_hudi_emr_sensor_20 = EmrStateSensorAsync(
    task_id="transit_server_transit_location_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_location.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_location.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_20 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_location",
    topic_name="postgres.transit_server.public.transit_location",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_20
    >> create_view_on_presto_20
)


s3_to_hudi_emr_sensor_21 = EmrStateSensorAsync(
    task_id="transit_server_transit_location_rule_trigger_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_location_rule_trigger.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_location_rule_trigger.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_21 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_location_rule_trigger",
    topic_name="postgres.transit_server.public.transit_location_rule_trigger",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_21
    >> create_view_on_presto_21
)


s3_to_hudi_emr_sensor_22 = EmrStateSensorAsync(
    task_id="transit_server_transit_location_rule_trigger_log_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_location_rule_trigger_log.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_location_rule_trigger_log.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_22 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_location_rule_trigger_log",
    topic_name="postgres.transit_server.public.transit_location_rule_trigger_log",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_22
    >> create_view_on_presto_22
)


s3_to_hudi_emr_sensor_23 = EmrStateSensorAsync(
    task_id="transit_server_transit_node_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_node.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_node.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_23 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_node",
    topic_name="postgres.transit_server.public.transit_node",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_23
    >> create_view_on_presto_23
)


s3_to_hudi_emr_sensor_24 = EmrStateSensorAsync(
    task_id="transit_server_transit_node_address_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_node_address.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_node_address.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_24 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_node_address",
    topic_name="postgres.transit_server.public.transit_node_address",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_24
    >> create_view_on_presto_24
)


s3_to_hudi_emr_sensor_25 = EmrStateSensorAsync(
    task_id="transit_server_transit_projection_consignment_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_projection_consignment.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_projection_consignment.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_25 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_projection_consignment",
    topic_name="postgres.transit_server.public.transit_projection_consignment",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_25
    >> create_view_on_presto_25
)


s3_to_hudi_emr_sensor_26 = EmrStateSensorAsync(
    task_id="transit_server_transit_projection_trip_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_projection_trip.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_projection_trip.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_26 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_projection_trip",
    topic_name="postgres.transit_server.public.transit_projection_trip",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_26
    >> create_view_on_presto_26
)


s3_to_hudi_emr_sensor_27 = EmrStateSensorAsync(
    task_id="transit_server_transit_projection_trip_event_timeline_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_projection_trip_event_timeline.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_projection_trip_event_timeline.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_27 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_projection_trip_event_timeline",
    topic_name="postgres.transit_server.public.transit_projection_trip_event_timeline",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_27
    >> create_view_on_presto_27
)


s3_to_hudi_emr_sensor_28 = EmrStateSensorAsync(
    task_id="transit_server_transit_shipment_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_shipment.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_shipment.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_28 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_shipment",
    topic_name="postgres.transit_server.public.transit_shipment",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_28
    >> create_view_on_presto_28
)


s3_to_hudi_emr_sensor_29 = EmrStateSensorAsync(
    task_id="transit_server_transit_shipment_allocation_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_shipment_allocation.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_shipment_allocation.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_29 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_shipment_allocation",
    topic_name="postgres.transit_server.public.transit_shipment_allocation",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_29
    >> create_view_on_presto_29
)


s3_to_hudi_emr_sensor_30 = EmrStateSensorAsync(
    task_id="transit_server_transit_shipment_label_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_shipment_label.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_shipment_label.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_30 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_shipment_label",
    topic_name="postgres.transit_server.public.transit_shipment_label",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_30
    >> create_view_on_presto_30
)


s3_to_hudi_emr_sensor_31 = EmrStateSensorAsync(
    task_id="transit_server_transit_task_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_task.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_task.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_31 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_task",
    topic_name="postgres.transit_server.public.transit_task",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_31
    >> create_view_on_presto_31
)


s3_to_hudi_emr_sensor_32 = EmrStateSensorAsync(
    task_id="transit_server_transit_trip_error_log_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_trip_error_log.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_trip_error_log.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_32 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_trip_error_log",
    topic_name="postgres.transit_server.public.transit_trip_error_log",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_32
    >> create_view_on_presto_32
)


s3_to_hudi_emr_sensor_33 = EmrStateSensorAsync(
    task_id="transit_server_transit_trip_metadata_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_trip_metadata.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_trip_metadata.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_33 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_trip_metadata",
    topic_name="postgres.transit_server.public.transit_trip_metadata",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_33
    >> create_view_on_presto_33
)


s3_to_hudi_emr_sensor_34 = EmrStateSensorAsync(
    task_id="transit_server_transit_user_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_user.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_user.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_34 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_user",
    topic_name="postgres.transit_server.public.transit_user",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_34
    >> create_view_on_presto_34
)


s3_to_hudi_emr_sensor_35 = EmrStateSensorAsync(
    task_id="transit_server_transit_user_profile_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_user_profile.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_user_profile.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_35 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_user_profile",
    topic_name="postgres.transit_server.public.transit_user_profile",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_35
    >> create_view_on_presto_35
)


s3_to_hudi_emr_sensor_36 = EmrStateSensorAsync(
    task_id="transit_server_transit_workflow_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_workflow.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_workflow.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_36 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_workflow",
    topic_name="postgres.transit_server.public.transit_workflow",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_36
    >> create_view_on_presto_36
)


s3_to_hudi_emr_sensor_37 = EmrStateSensorAsync(
    task_id="transit_server_transit_workflow_step_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_workflow_step.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_workflow_step.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_37 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_workflow_step",
    topic_name="postgres.transit_server.public.transit_workflow_step",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_37
    >> create_view_on_presto_37
)


s3_to_hudi_emr_sensor_38 = EmrStateSensorAsync(
    task_id="transit_server_transit_workflow_type_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_workflow_type.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_transit_server_1_s3_to_hudi', key='flow_id_job_mapping').get('transit_server.transit_workflow_type.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_38 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_transit_server_transit_workflow_type",
    topic_name="postgres.transit_server.public.transit_workflow_type",
    database="transit_server",
    lake_schemaname="lake_transit_server",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_38
    >> create_view_on_presto_38
)


if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = dt.timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
