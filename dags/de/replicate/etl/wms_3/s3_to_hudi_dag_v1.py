# -*- coding: utf-8 -*-

import datetime as dt
import logging

from airflow import DAG
from airflow.operators.dagrun_operator import TriggerDagRunOperator
from kubernetes.client import models as k8s
from metrics_plugin import (
    EmrStateSensorAsync,
    SRPClusterMappingOperatorV2,
    SRPEmrBulkAddStepsOperator,
    PrestoCreateViewOperator,
)

import pencilbox as pb


branch_op_flows = {
    "wms.app_layout.v1": {
        "kafka_topic": "mysql.wms.wms.app_layout",
        "downstream_task_id": "wms_app_layout_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "wms.app_layout.v1",
        ),
    },
    "wms.bad_stock_container.v1": {
        "kafka_topic": "mysql.wms.wms.bad_stock_container",
        "downstream_task_id": "wms_bad_stock_container_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "wms.bad_stock_container.v1",
        ),
    },
    "wms.bad_stock_item.v1": {
        "kafka_topic": "mysql.wms.wms.bad_stock_item",
        "downstream_task_id": "wms_bad_stock_item_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "wms.bad_stock_item.v1",
        ),
    },
    "wms.dispatch_consignment_state_log.v1": {
        "kafka_topic": "mysql.wms.wms.dispatch_consignment_state_log",
        "downstream_task_id": "wms_dispatch_consignment_state_log_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "wms.dispatch_consignment_state_log.v1",
        ),
    },
    "wms.dispatch_trip_activity.v1": {
        "kafka_topic": "mysql.wms.wms.dispatch_trip_activity",
        "downstream_task_id": "wms_dispatch_trip_activity_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "wms.dispatch_trip_activity.v1",
        ),
    },
    "wms.dispatch_trip_state_log.v1": {
        "kafka_topic": "mysql.wms.wms.dispatch_trip_state_log",
        "downstream_task_id": "wms_dispatch_trip_state_log_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "wms.dispatch_trip_state_log.v1",
        ),
    },
    "wms.entity_config_mappings.v1": {
        "kafka_topic": "mysql.wms.wms.entity_config_mappings",
        "downstream_task_id": "wms_entity_config_mappings_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "wms.entity_config_mappings.v1",
        ),
    },
    "wms.external_order_activity_invoice_tracker.v1": {
        "kafka_topic": "mysql.wms.wms.external_order_activity_invoice_tracker",
        "downstream_task_id": "wms_external_order_activity_invoice_tracker_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "wms.external_order_activity_invoice_tracker.v1",
        ),
    },
    "wms.handover_qc_activity.v1": {
        "kafka_topic": "mysql.wms.wms.handover_qc_activity",
        "downstream_task_id": "wms_handover_qc_activity_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "wms.handover_qc_activity.v1",
        ),
    },
    "wms.handover_qc_activity_container.v1": {
        "kafka_topic": "mysql.wms.wms.handover_qc_activity_container",
        "downstream_task_id": "wms_handover_qc_activity_container_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "wms.handover_qc_activity_container.v1",
        ),
    },
    "wms.handover_qc_activity_item.v1": {
        "kafka_topic": "mysql.wms.wms.handover_qc_activity_item",
        "downstream_task_id": "wms_handover_qc_activity_item_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "wms.handover_qc_activity_item.v1",
        ),
    },
    "wms.inbound_job_request.v1": {
        "kafka_topic": "mysql.wms.wms.inbound_job_request",
        "downstream_task_id": "wms_inbound_job_request_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "wms.inbound_job_request.v1",
        ),
    },
    "wms.internal_movement_container.v1": {
        "kafka_topic": "mysql.wms.wms.internal_movement_container",
        "downstream_task_id": "wms_internal_movement_container_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "wms.internal_movement_container.v1",
        ),
    },
    "wms.internal_movement_container_item.v1": {
        "kafka_topic": "mysql.wms.wms.internal_movement_container_item",
        "downstream_task_id": "wms_internal_movement_container_item_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "wms.internal_movement_container_item.v1",
        ),
    },
    "wms.ob_pkg_external_order_activity.v1": {
        "kafka_topic": "mysql.wms.wms.ob_pkg_external_order_activity",
        "downstream_task_id": "wms_ob_pkg_external_order_activity_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "wms.ob_pkg_external_order_activity.v1",
        ),
    },
    "wms.outbound_config.v1": {
        "kafka_topic": "mysql.wms.wms.outbound_config",
        "downstream_task_id": "wms_outbound_config_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "wms.outbound_config.v1",
        ),
    },
    "wms.outbound_config_log.v1": {
        "kafka_topic": "mysql.wms.wms.outbound_config_log",
        "downstream_task_id": "wms_outbound_config_log_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "wms.outbound_config_log.v1",
        ),
    },
    "wms.outlet_vehicle_entry.v1": {
        "kafka_topic": "mysql.wms.wms.outlet_vehicle_entry",
        "downstream_task_id": "wms_outlet_vehicle_entry_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "wms.outlet_vehicle_entry.v1",
        ),
    },
    "wms.pick_list.v1": {
        "kafka_topic": "mysql.wms.wms.pick_list",
        "downstream_task_id": "wms_pick_list_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "wms.pick_list.v1",
        ),
    },
    "wms.pick_list_item.v1": {
        "kafka_topic": "mysql.wms.wms.pick_list_item",
        "downstream_task_id": "wms_pick_list_item_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "wms.pick_list_item.v1",
        ),
    },
    "wms.pick_list_item_order_mapping.v1": {
        "kafka_topic": "mysql.wms.wms.pick_list_item_order_mapping",
        "downstream_task_id": "wms_pick_list_item_order_mapping_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "wms.pick_list_item_order_mapping.v1",
        ),
    },
    "wms.pick_list_log.v1": {
        "kafka_topic": "mysql.wms.wms.pick_list_log",
        "downstream_task_id": "wms_pick_list_log_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "wms.pick_list_log.v1",
        ),
    },
    "wms.picking_slot_demand_details.v1": {
        "kafka_topic": "mysql.wms.wms.picking_slot_demand_details",
        "downstream_task_id": "wms_picking_slot_demand_details_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "wms.picking_slot_demand_details.v1",
        ),
    },
    "wms.picking_slot_demand_details_log.v1": {
        "kafka_topic": "mysql.wms.wms.picking_slot_demand_details_log",
        "downstream_task_id": "wms_picking_slot_demand_details_log_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "wms.picking_slot_demand_details_log.v1",
        ),
    },
    "wms.picking_slot_info.v1": {
        "kafka_topic": "mysql.wms.wms.picking_slot_info",
        "downstream_task_id": "wms_picking_slot_info_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "wms.picking_slot_info.v1",
        ),
    },
    "wms.unloading_proof_of_delivery.v1": {
        "kafka_topic": "mysql.wms.wms.unloading_proof_of_delivery",
        "downstream_task_id": "wms_unloading_proof_of_delivery_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "wms.unloading_proof_of_delivery.v1",
        ),
    },
    "wms.unloading_task_po_invoice_mapping.v1": {
        "kafka_topic": "mysql.wms.wms.unloading_task_po_invoice_mapping",
        "downstream_task_id": "wms_unloading_task_po_invoice_mapping_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "wms.unloading_task_po_invoice_mapping.v1",
        ),
    },
    "wms.user_attendance.v2": {
        "kafka_topic": "mysql.wms.wms.user_attendance",
        "downstream_task_id": "wms_user_attendance_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "wms.user_attendance.v2",
        ),
    },
    "wms.vehicle_registry.v1": {
        "kafka_topic": "mysql.wms.wms.vehicle_registry",
        "downstream_task_id": "wms_vehicle_registry_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "wms.vehicle_registry.v1",
        ),
    },
    "wms.vehicle_registry_log.v1": {
        "kafka_topic": "mysql.wms.wms.vehicle_registry_log",
        "downstream_task_id": "wms_vehicle_registry_log_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "wms.vehicle_registry_log.v1",
        ),
    },
    "wms.warehouse_dock.v1": {
        "kafka_topic": "mysql.wms.wms.warehouse_dock",
        "downstream_task_id": "wms_warehouse_dock_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "wms.warehouse_dock.v1",
        ),
    },
    "wms.warehouse_dock_activity.v1": {
        "kafka_topic": "mysql.wms.wms.warehouse_dock_activity",
        "downstream_task_id": "wms_warehouse_dock_activity_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "wms.warehouse_dock_activity.v1",
        ),
    },
    "wms.wave.v1": {
        "kafka_topic": "mysql.wms.wms.wave",
        "downstream_task_id": "wms_wave_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "wms.wave.v1",
        ),
    },
    "wms.entity_group.v1": {
        "kafka_topic": "mysql.wms.wms.entity_group",
        "downstream_task_id": "wms_entity_group_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "wms.entity_group.v1",
        ),
    },
    "wms.outbound_dispatch_config.v1": {
        "kafka_topic": "mysql.wms.wms.outbound_dispatch_config",
        "downstream_task_id": "wms_outbound_dispatch_config_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "wms.outbound_dispatch_config.v1",
        ),
    },
}

# Default values for all flow ids, to override pass `spark_application_jar` in `branch_op_flows` for respective flow id
spark_application_jar = "s3://prod-dse-srp-configs/jars/nessie/Nessie-assembly-2.1.2.jar"

# Default values for all flow ids, to override any value pass `spark_submit_options` in `branch_op_flows` for respective flow id
spark_submit_options = {
    "--deploy-mode": "cluster",
    "--master": "yarn",
    "--conf": {
        "spark.serializer": "org.apache.spark.serializer.KryoSerializer",
        "spark.kryo.registrator": "org.apache.spark.HoodieSparkKryoRegistrar",
        "spark.sql.catalog.spark_catalog": "org.apache.spark.sql.hudi.catalog.HoodieCatalog",
        "spark.sql.extensions": "org.apache.spark.sql.hudi.HoodieSparkSessionExtension",
        "spark.yarn.heterogeneousExecutors.enabled": "false",
        "spark.sql.hive.convertMetastoreParquet": "false",
    },
    "--class": "com.grofers.nessie.Nessie",
}


def slack_failure_alert(context):
    dag_id = context.get("task_instance").dag_id
    run_id = context.get("run_id")
    task_id = context.get("task_instance").task_id
    log_url = context.get("task_instance").log_url.replace("http://", "https://")
    exception = str(context.get("exception"))
    override_channel_mapping = {
        "terminated_with_errors": "bl-data-spot-interruptions",
        "spot": "bl-data-spot-interruptions",
    }

    slack_failure_msg = """
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner*: <!subteam^S089D0GQRCJ>
        *Log Url*: {log_url}
        {exception}
    """.format(
        dag_id=dag_id,
        run_id=run_id,
        task_id=task_id,
        log_url=log_url,
        exception=exception,
    )

    slack_alert_configs = [
        {"channel": "bl-data-alerts-p1"},
    ]

    # Check if exception exists and contains any key from override_channel_mapping
    if exception and override_channel_mapping:
        for error_keyword, override_channel in override_channel_mapping.items():
            if error_keyword.lower() in exception.lower():
                # Override all channels with the mapped channel
                slack_alert_configs = [{"channel": override_channel}]
                break

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(channel=channel, text=slack_failure_msg, source_info=False)
        except Exception as e:
            logging.warning(e)
            message = (
                f"<!subteam^S089D0GQRCJ> `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(channel="bl-data-alerts-p1", text=message, source_info=False)


def failure_alerts(context):
    slack_failure_alert(context)


args = {
    "owner": "<EMAIL>",
    "on_failure_callback": failure_alerts,
    "start_date": dt.datetime.strptime("2024-04-15T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": dt.datetime.strptime("2026-08-15T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "slack_id": "S089D0GQRCJ",
}

dag = DAG(
    dag_id="de_replicate_etl_wms_3_s3_to_hudi_v1",
    default_args=args,
    schedule_interval="12,27,42,57 * * * *",
    tags=["de", "replicate", "wms_3"],
    catchup=False,
    concurrency=15,
    max_active_runs=1,
    render_template_as_native_obj=True,
)


s3_hudi_cluster_allocation_task = SRPClusterMappingOperatorV2(
    task_id="cluster_allocation_wms_3_s3_to_hudi",
    dag=dag,
    connection_alias="nessie",
    flows_to_run=branch_op_flows,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


s3_hudi_bulk_submit_emr_steps_task = SRPEmrBulkAddStepsOperator(
    task_id="bulk_step_submission_wms_3_s3_to_hudi",
    dag=dag,
    aws_conn_id="aws_default",
    srp_flows_info="{{ task_instance.xcom_pull('cluster_allocation_wms_3_s3_to_hudi', key='flows_info') }}",
    spark_application_jar=spark_application_jar,
    spark_submit_options=spark_submit_options,
    srp_task_id_cluster_mapping="{{ task_instance.xcom_pull('cluster_allocation_wms_3_s3_to_hudi', key='return_value') }}",
    queue="celery",
)


s3_to_hudi_emr_sensor_1 = EmrStateSensorAsync(
    task_id="wms_app_layout_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.app_layout.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.app_layout.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_1 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_wms_app_layout",
    topic_name="mysql.wms.wms.app_layout",
    database="wms",
    lake_schemaname="lake_wms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_1
    >> create_view_on_presto_1
)


s3_to_hudi_emr_sensor_2 = EmrStateSensorAsync(
    task_id="wms_bad_stock_container_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.bad_stock_container.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.bad_stock_container.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_2 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_wms_bad_stock_container",
    topic_name="mysql.wms.wms.bad_stock_container",
    database="wms",
    lake_schemaname="lake_wms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_2
    >> create_view_on_presto_2
)


s3_to_hudi_emr_sensor_3 = EmrStateSensorAsync(
    task_id="wms_bad_stock_item_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.bad_stock_item.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.bad_stock_item.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_3 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_wms_bad_stock_item",
    topic_name="mysql.wms.wms.bad_stock_item",
    database="wms",
    lake_schemaname="lake_wms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_3
    >> create_view_on_presto_3
)


s3_to_hudi_emr_sensor_4 = EmrStateSensorAsync(
    task_id="wms_dispatch_consignment_state_log_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.dispatch_consignment_state_log.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.dispatch_consignment_state_log.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_4 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_wms_dispatch_consignment_state_log",
    topic_name="mysql.wms.wms.dispatch_consignment_state_log",
    database="wms",
    lake_schemaname="lake_wms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_4
    >> create_view_on_presto_4
)


s3_to_hudi_emr_sensor_5 = EmrStateSensorAsync(
    task_id="wms_dispatch_trip_activity_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.dispatch_trip_activity.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.dispatch_trip_activity.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_5 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_wms_dispatch_trip_activity",
    topic_name="mysql.wms.wms.dispatch_trip_activity",
    database="wms",
    lake_schemaname="lake_wms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_5
    >> create_view_on_presto_5
)


s3_to_hudi_emr_sensor_6 = EmrStateSensorAsync(
    task_id="wms_dispatch_trip_state_log_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.dispatch_trip_state_log.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.dispatch_trip_state_log.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_6 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_wms_dispatch_trip_state_log",
    topic_name="mysql.wms.wms.dispatch_trip_state_log",
    database="wms",
    lake_schemaname="lake_wms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_6
    >> create_view_on_presto_6
)


s3_to_hudi_emr_sensor_7 = EmrStateSensorAsync(
    task_id="wms_entity_config_mappings_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.entity_config_mappings.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.entity_config_mappings.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_7 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_wms_entity_config_mappings",
    topic_name="mysql.wms.wms.entity_config_mappings",
    database="wms",
    lake_schemaname="lake_wms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_7
    >> create_view_on_presto_7
)


s3_to_hudi_emr_sensor_8 = EmrStateSensorAsync(
    task_id="wms_external_order_activity_invoice_tracker_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.external_order_activity_invoice_tracker.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.external_order_activity_invoice_tracker.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_8 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_wms_external_order_activity_invoice_tracker",
    topic_name="mysql.wms.wms.external_order_activity_invoice_tracker",
    database="wms",
    lake_schemaname="lake_wms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_8
    >> create_view_on_presto_8
)


s3_to_hudi_emr_sensor_9 = EmrStateSensorAsync(
    task_id="wms_handover_qc_activity_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.handover_qc_activity.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.handover_qc_activity.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_9 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_wms_handover_qc_activity",
    topic_name="mysql.wms.wms.handover_qc_activity",
    database="wms",
    lake_schemaname="lake_wms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_9
    >> create_view_on_presto_9
)


s3_to_hudi_emr_sensor_10 = EmrStateSensorAsync(
    task_id="wms_handover_qc_activity_container_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.handover_qc_activity_container.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.handover_qc_activity_container.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_10 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_wms_handover_qc_activity_container",
    topic_name="mysql.wms.wms.handover_qc_activity_container",
    database="wms",
    lake_schemaname="lake_wms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_10
    >> create_view_on_presto_10
)


s3_to_hudi_emr_sensor_11 = EmrStateSensorAsync(
    task_id="wms_handover_qc_activity_item_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.handover_qc_activity_item.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.handover_qc_activity_item.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_11 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_wms_handover_qc_activity_item",
    topic_name="mysql.wms.wms.handover_qc_activity_item",
    database="wms",
    lake_schemaname="lake_wms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_11
    >> create_view_on_presto_11
)


s3_to_hudi_emr_sensor_12 = EmrStateSensorAsync(
    task_id="wms_inbound_job_request_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.inbound_job_request.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.inbound_job_request.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_12 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_wms_inbound_job_request",
    topic_name="mysql.wms.wms.inbound_job_request",
    database="wms",
    lake_schemaname="lake_wms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_12
    >> create_view_on_presto_12
)


s3_to_hudi_emr_sensor_13 = EmrStateSensorAsync(
    task_id="wms_internal_movement_container_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.internal_movement_container.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.internal_movement_container.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_13 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_wms_internal_movement_container",
    topic_name="mysql.wms.wms.internal_movement_container",
    database="wms",
    lake_schemaname="lake_wms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_13
    >> create_view_on_presto_13
)


s3_to_hudi_emr_sensor_14 = EmrStateSensorAsync(
    task_id="wms_internal_movement_container_item_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.internal_movement_container_item.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.internal_movement_container_item.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_14 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_wms_internal_movement_container_item",
    topic_name="mysql.wms.wms.internal_movement_container_item",
    database="wms",
    lake_schemaname="lake_wms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_14
    >> create_view_on_presto_14
)


s3_to_hudi_emr_sensor_15 = EmrStateSensorAsync(
    task_id="wms_ob_pkg_external_order_activity_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.ob_pkg_external_order_activity.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.ob_pkg_external_order_activity.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_15 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_wms_ob_pkg_external_order_activity",
    topic_name="mysql.wms.wms.ob_pkg_external_order_activity",
    database="wms",
    lake_schemaname="lake_wms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_15
    >> create_view_on_presto_15
)


s3_to_hudi_emr_sensor_16 = EmrStateSensorAsync(
    task_id="wms_outbound_config_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.outbound_config.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.outbound_config.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_16 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_wms_outbound_config",
    topic_name="mysql.wms.wms.outbound_config",
    database="wms",
    lake_schemaname="lake_wms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_16
    >> create_view_on_presto_16
)


s3_to_hudi_emr_sensor_17 = EmrStateSensorAsync(
    task_id="wms_outbound_config_log_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.outbound_config_log.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.outbound_config_log.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_17 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_wms_outbound_config_log",
    topic_name="mysql.wms.wms.outbound_config_log",
    database="wms",
    lake_schemaname="lake_wms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_17
    >> create_view_on_presto_17
)


s3_to_hudi_emr_sensor_18 = EmrStateSensorAsync(
    task_id="wms_outlet_vehicle_entry_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.outlet_vehicle_entry.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.outlet_vehicle_entry.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_18 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_wms_outlet_vehicle_entry",
    topic_name="mysql.wms.wms.outlet_vehicle_entry",
    database="wms",
    lake_schemaname="lake_wms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_18
    >> create_view_on_presto_18
)


s3_to_hudi_emr_sensor_19 = EmrStateSensorAsync(
    task_id="wms_pick_list_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.pick_list.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.pick_list.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_19 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_wms_pick_list",
    topic_name="mysql.wms.wms.pick_list",
    database="wms",
    lake_schemaname="lake_wms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_19
    >> create_view_on_presto_19
)


s3_to_hudi_emr_sensor_20 = EmrStateSensorAsync(
    task_id="wms_pick_list_item_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.pick_list_item.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.pick_list_item.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_20 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_wms_pick_list_item",
    topic_name="mysql.wms.wms.pick_list_item",
    database="wms",
    lake_schemaname="lake_wms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_20
    >> create_view_on_presto_20
)


s3_to_hudi_emr_sensor_21 = EmrStateSensorAsync(
    task_id="wms_pick_list_item_order_mapping_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.pick_list_item_order_mapping.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.pick_list_item_order_mapping.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_21 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_wms_pick_list_item_order_mapping",
    topic_name="mysql.wms.wms.pick_list_item_order_mapping",
    database="wms",
    lake_schemaname="lake_wms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_21
    >> create_view_on_presto_21
)


s3_to_hudi_emr_sensor_22 = EmrStateSensorAsync(
    task_id="wms_pick_list_log_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.pick_list_log.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.pick_list_log.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_22 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_wms_pick_list_log",
    topic_name="mysql.wms.wms.pick_list_log",
    database="wms",
    lake_schemaname="lake_wms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_22
    >> create_view_on_presto_22
)


s3_to_hudi_emr_sensor_23 = EmrStateSensorAsync(
    task_id="wms_picking_slot_demand_details_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.picking_slot_demand_details.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.picking_slot_demand_details.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_23 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_wms_picking_slot_demand_details",
    topic_name="mysql.wms.wms.picking_slot_demand_details",
    database="wms",
    lake_schemaname="lake_wms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_23
    >> create_view_on_presto_23
)


s3_to_hudi_emr_sensor_24 = EmrStateSensorAsync(
    task_id="wms_picking_slot_demand_details_log_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.picking_slot_demand_details_log.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.picking_slot_demand_details_log.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_24 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_wms_picking_slot_demand_details_log",
    topic_name="mysql.wms.wms.picking_slot_demand_details_log",
    database="wms",
    lake_schemaname="lake_wms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_24
    >> create_view_on_presto_24
)


s3_to_hudi_emr_sensor_25 = EmrStateSensorAsync(
    task_id="wms_picking_slot_info_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.picking_slot_info.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.picking_slot_info.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_25 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_wms_picking_slot_info",
    topic_name="mysql.wms.wms.picking_slot_info",
    database="wms",
    lake_schemaname="lake_wms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_25
    >> create_view_on_presto_25
)


s3_to_hudi_emr_sensor_26 = EmrStateSensorAsync(
    task_id="wms_unloading_proof_of_delivery_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.unloading_proof_of_delivery.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.unloading_proof_of_delivery.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_26 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_wms_unloading_proof_of_delivery",
    topic_name="mysql.wms.wms.unloading_proof_of_delivery",
    database="wms",
    lake_schemaname="lake_wms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_26
    >> create_view_on_presto_26
)


s3_to_hudi_emr_sensor_27 = EmrStateSensorAsync(
    task_id="wms_unloading_task_po_invoice_mapping_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.unloading_task_po_invoice_mapping.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.unloading_task_po_invoice_mapping.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_27 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_wms_unloading_task_po_invoice_mapping",
    topic_name="mysql.wms.wms.unloading_task_po_invoice_mapping",
    database="wms",
    lake_schemaname="lake_wms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_27
    >> create_view_on_presto_27
)


s3_to_hudi_emr_sensor_28 = EmrStateSensorAsync(
    task_id="wms_user_attendance_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.user_attendance.v2', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.user_attendance.v2', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_28 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_wms_user_attendance",
    topic_name="mysql.wms.wms.user_attendance",
    database="wms",
    lake_schemaname="lake_wms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_28
    >> create_view_on_presto_28
)


s3_to_hudi_emr_sensor_29 = EmrStateSensorAsync(
    task_id="wms_vehicle_registry_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.vehicle_registry.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.vehicle_registry.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_29 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_wms_vehicle_registry",
    topic_name="mysql.wms.wms.vehicle_registry",
    database="wms",
    lake_schemaname="lake_wms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_29
    >> create_view_on_presto_29
)


s3_to_hudi_emr_sensor_30 = EmrStateSensorAsync(
    task_id="wms_vehicle_registry_log_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.vehicle_registry_log.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.vehicle_registry_log.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_30 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_wms_vehicle_registry_log",
    topic_name="mysql.wms.wms.vehicle_registry_log",
    database="wms",
    lake_schemaname="lake_wms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_30
    >> create_view_on_presto_30
)


s3_to_hudi_emr_sensor_31 = EmrStateSensorAsync(
    task_id="wms_warehouse_dock_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.warehouse_dock.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.warehouse_dock.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_31 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_wms_warehouse_dock",
    topic_name="mysql.wms.wms.warehouse_dock",
    database="wms",
    lake_schemaname="lake_wms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_31
    >> create_view_on_presto_31
)


s3_to_hudi_emr_sensor_32 = EmrStateSensorAsync(
    task_id="wms_warehouse_dock_activity_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.warehouse_dock_activity.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.warehouse_dock_activity.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_32 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_wms_warehouse_dock_activity",
    topic_name="mysql.wms.wms.warehouse_dock_activity",
    database="wms",
    lake_schemaname="lake_wms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_32
    >> create_view_on_presto_32
)


s3_to_hudi_emr_sensor_33 = EmrStateSensorAsync(
    task_id="wms_wave_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.wave.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.wave.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_33 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_wms_wave",
    topic_name="mysql.wms.wms.wave",
    database="wms",
    lake_schemaname="lake_wms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_33
    >> create_view_on_presto_33
)


s3_to_hudi_emr_sensor_34 = EmrStateSensorAsync(
    task_id="wms_entity_group_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.entity_group.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.entity_group.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_34 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_wms_entity_group",
    topic_name="mysql.wms.wms.entity_group",
    database="wms",
    lake_schemaname="lake_wms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_34
    >> create_view_on_presto_34
)


s3_to_hudi_emr_sensor_35 = EmrStateSensorAsync(
    task_id="wms_outbound_dispatch_config_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.outbound_dispatch_config.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_wms_3_s3_to_hudi', key='flow_id_job_mapping').get('wms.outbound_dispatch_config.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_35 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_wms_outbound_dispatch_config",
    topic_name="mysql.wms.wms.outbound_dispatch_config",
    database="wms",
    lake_schemaname="lake_wms",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_35
    >> create_view_on_presto_35
)


if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = dt.timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
