# -*- coding: utf-8 -*-

import datetime as dt
import logging

from airflow import DAG
from airflow.operators.dagrun_operator import TriggerDagRunOperator
from kubernetes.client import models as k8s
from metrics_plugin import (
    EmrStateSensorAsync,
    SRPClusterMappingOperatorV2,
    SRPEmrBulkAddStepsOperator,
    PrestoCreateViewOperator,
)

import pencilbox as pb


branch_op_flows = {
    "rpc.product_tax_revision.v1": {
        "kafka_topic": "mysql.rpc.rpc.product_tax_revision",
        "downstream_task_id": "rpc_product_tax_revision_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "rpc.product_tax_revision.v1",
        ),
    },
    "rpc.ams_city_cluster_mapping.v1": {
        "kafka_topic": "mysql.rpc.rpc.ams_city_cluster_mapping",
        "downstream_task_id": "rpc_ams_city_cluster_mapping_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "rpc.ams_city_cluster_mapping.v1",
        ),
    },
    "rpc.ams_cluster.v1": {
        "kafka_topic": "mysql.rpc.rpc.ams_cluster",
        "downstream_task_id": "rpc_ams_cluster_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "rpc.ams_cluster.v1",
        ),
    },
    "rpc.attribute_outlet_entity_vendor.v1": {
        "kafka_topic": "mysql.rpc.rpc.attribute_outlet_entity_vendor",
        "downstream_task_id": "rpc_attribute_outlet_entity_vendor_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "rpc.attribute_outlet_entity_vendor.v1",
        ),
    },
    "rpc.dated_tot_margin.v1": {
        "kafka_topic": "mysql.rpc.rpc.dated_tot_margin",
        "downstream_task_id": "rpc_dated_tot_margin_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "rpc.dated_tot_margin.v1",
        ),
    },
    "rpc.facility_adjacency.v1": {
        "kafka_topic": "mysql.rpc.rpc.facility_adjacency",
        "downstream_task_id": "rpc_facility_adjacency_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "rpc.facility_adjacency.v1",
        ),
    },
    "rpc.facility_adjacency_log.v1": {
        "kafka_topic": "mysql.rpc.rpc.facility_adjacency_log",
        "downstream_task_id": "rpc_facility_adjacency_log_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "rpc.facility_adjacency_log.v1",
        ),
    },
    "rpc.facility_polygon.v1": {
        "kafka_topic": "mysql.rpc.rpc.facility_polygon",
        "downstream_task_id": "rpc_facility_polygon_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "rpc.facility_polygon.v1",
        ),
    },
    "rpc.facility_polygon_log.v1": {
        "kafka_topic": "mysql.rpc.rpc.facility_polygon_log",
        "downstream_task_id": "rpc_facility_polygon_log_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "rpc.facility_polygon_log.v1",
        ),
    },
    "rpc.item_tag_type.v1": {
        "kafka_topic": "mysql.rpc.rpc.item_tag_type",
        "downstream_task_id": "rpc_item_tag_type_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "rpc.item_tag_type.v1",
        ),
    },
    "rpc.item_tag_type_value_mapping.v1": {
        "kafka_topic": "mysql.rpc.rpc.item_tag_type_value_mapping",
        "downstream_task_id": "rpc_item_tag_type_value_mapping_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "rpc.item_tag_type_value_mapping.v1",
        ),
    },
    "rpc.new_store_assortment_request.v1": {
        "kafka_topic": "mysql.rpc.rpc.new_store_assortment_request",
        "downstream_task_id": "rpc_new_store_assortment_request_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "rpc.new_store_assortment_request.v1",
        ),
    },
    "rpc.new_store_assortment_request_log.v1": {
        "kafka_topic": "mysql.rpc.rpc.new_store_assortment_request_log",
        "downstream_task_id": "rpc_new_store_assortment_request_log_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "rpc.new_store_assortment_request_log.v1",
        ),
    },
    "rpc.off_invoice_rule.v1": {
        "kafka_topic": "mysql.rpc.rpc.off_invoice_rule",
        "downstream_task_id": "rpc_off_invoice_rule_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "rpc.off_invoice_rule.v1",
        ),
    },
    "rpc.off_invoice_rule_instance.v1": {
        "kafka_topic": "mysql.rpc.rpc.off_invoice_rule_instance",
        "downstream_task_id": "rpc_off_invoice_rule_instance_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "rpc.off_invoice_rule_instance.v1",
        ),
    },
    "rpc.off_invoice_rule_update_request.v1": {
        "kafka_topic": "mysql.rpc.rpc.off_invoice_rule_update_request",
        "downstream_task_id": "rpc_off_invoice_rule_update_request_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "rpc.off_invoice_rule_update_request.v1",
        ),
    },
    "rpc.product_product_log.v1": {
        "kafka_topic": "mysql.rpc.rpc.product_product_log",
        "downstream_task_id": "rpc_product_product_log_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "rpc.product_product_log.v1",
        ),
    },
    "rpc.return_policy.v1": {
        "kafka_topic": "mysql.rpc.rpc.return_policy",
        "downstream_task_id": "rpc_return_policy_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "rpc.return_policy.v1",
        ),
    },
    "rpc.return_policy_bucket_attribute.v1": {
        "kafka_topic": "mysql.rpc.rpc.return_policy_bucket_attribute",
        "downstream_task_id": "rpc_return_policy_bucket_attribute_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "rpc.return_policy_bucket_attribute.v1",
        ),
    },
    "rpc.substitutable_group.v1": {
        "kafka_topic": "mysql.rpc.rpc.substitutable_group",
        "downstream_task_id": "rpc_substitutable_group_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "rpc.substitutable_group.v1",
        ),
    },
    "rpc.substitutable_item_group.v1": {
        "kafka_topic": "mysql.rpc.rpc.substitutable_item_group",
        "downstream_task_id": "rpc_substitutable_item_group_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "rpc.substitutable_item_group.v1",
        ),
    },
    "rpc.supply_event.v1": {
        "kafka_topic": "mysql.rpc.rpc.supply_event",
        "downstream_task_id": "rpc_supply_event_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "rpc.supply_event.v1",
        ),
    },
    "rpc.supply_event_info.v1": {
        "kafka_topic": "mysql.rpc.rpc.supply_event_info",
        "downstream_task_id": "rpc_supply_event_info_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "rpc.supply_event_info.v1",
        ),
    },
    "rpc.temporary_stock_details.v1": {
        "kafka_topic": "mysql.rpc.rpc.temporary_stock_details",
        "downstream_task_id": "rpc_temporary_stock_details_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "rpc.temporary_stock_details.v1",
        ),
    },
    "rpc.temporary_stock_details_log.v1": {
        "kafka_topic": "mysql.rpc.rpc.temporary_stock_details_log",
        "downstream_task_id": "rpc_temporary_stock_details_log_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "rpc.temporary_stock_details_log.v1",
        ),
    },
    "rpc.transfer_case_size.v1": {
        "kafka_topic": "mysql.rpc.rpc.transfer_case_size",
        "downstream_task_id": "rpc_transfer_case_size_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "rpc.transfer_case_size.v1",
        ),
    },
    "rpc.transfer_case_size_log.v1": {
        "kafka_topic": "mysql.rpc.rpc.transfer_case_size_log",
        "downstream_task_id": "rpc_transfer_case_size_log_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "rpc.transfer_case_size_log.v1",
        ),
    },
    "rpc.transfer_tag_rules.v1": {
        "kafka_topic": "mysql.rpc.rpc.transfer_tag_rules",
        "downstream_task_id": "rpc_transfer_tag_rules_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "rpc.transfer_tag_rules.v1",
        ),
    },
    "rpc.transfer_tag_rules_log.v1": {
        "kafka_topic": "mysql.rpc.rpc.transfer_tag_rules_log",
        "downstream_task_id": "rpc_transfer_tag_rules_log_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "rpc.transfer_tag_rules_log.v1",
        ),
    },
    "rpc.warehouse_transition.v1": {
        "kafka_topic": "mysql.rpc.rpc.warehouse_transition",
        "downstream_task_id": "rpc_warehouse_transition_s3_to_hudi_emr_sensor",
        "spark_application_arguments": (
            "s3Hudi",
            "--flowId",
            "rpc.warehouse_transition.v1",
        ),
    },
}

# Default values for all flow ids, to override pass `spark_application_jar` in `branch_op_flows` for respective flow id
spark_application_jar = "s3://prod-dse-srp-configs/jars/nessie/Nessie-assembly-2.1.2.jar"

# Default values for all flow ids, to override any value pass `spark_submit_options` in `branch_op_flows` for respective flow id
spark_submit_options = {
    "--deploy-mode": "cluster",
    "--master": "yarn",
    "--conf": {
        "spark.serializer": "org.apache.spark.serializer.KryoSerializer",
        "spark.kryo.registrator": "org.apache.spark.HoodieSparkKryoRegistrar",
        "spark.sql.catalog.spark_catalog": "org.apache.spark.sql.hudi.catalog.HoodieCatalog",
        "spark.sql.extensions": "org.apache.spark.sql.hudi.HoodieSparkSessionExtension",
        "spark.yarn.heterogeneousExecutors.enabled": "false",
        "spark.sql.hive.convertMetastoreParquet": "false",
    },
    "--class": "com.grofers.nessie.Nessie",
}


def slack_failure_alert(context):
    dag_id = context.get("task_instance").dag_id
    run_id = context.get("run_id")
    task_id = context.get("task_instance").task_id
    log_url = context.get("task_instance").log_url.replace("http://", "https://")
    exception = str(context.get("exception"))
    override_channel_mapping = {
        "terminated_with_errors": "bl-data-spot-interruptions",
        "spot": "bl-data-spot-interruptions",
    }

    slack_failure_msg = """
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner*: <!subteam^S089D0GQRCJ>
        *Log Url*: {log_url}
        {exception}
    """.format(
        dag_id=dag_id,
        run_id=run_id,
        task_id=task_id,
        log_url=log_url,
        exception=exception,
    )

    slack_alert_configs = [
        {"channel": "bl-data-alerts-p1"},
    ]

    # Check if exception exists and contains any key from override_channel_mapping
    if exception and override_channel_mapping:
        for error_keyword, override_channel in override_channel_mapping.items():
            if error_keyword.lower() in exception.lower():
                # Override all channels with the mapped channel
                slack_alert_configs = [{"channel": override_channel}]
                break

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(channel=channel, text=slack_failure_msg, source_info=False)
        except Exception as e:
            logging.warning(e)
            message = (
                f"<!subteam^S089D0GQRCJ> `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(channel="bl-data-alerts-p1", text=message, source_info=False)


def failure_alerts(context):
    slack_failure_alert(context)


args = {
    "owner": "<EMAIL>",
    "on_failure_callback": failure_alerts,
    "start_date": dt.datetime.strptime("2022-09-01T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": dt.datetime.strptime("2025-12-14T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "slack_id": "S089D0GQRCJ",
}

dag = DAG(
    dag_id="de_replicate_etl_rpc_2_s3_to_hudi_v1",
    default_args=args,
    schedule_interval="21,51 * * * *",
    tags=["de", "replicate", "rpc_2"],
    catchup=False,
    concurrency=15,
    max_active_runs=1,
    render_template_as_native_obj=True,
)


s3_hudi_cluster_allocation_task = SRPClusterMappingOperatorV2(
    task_id="cluster_allocation_rpc_2_s3_to_hudi",
    dag=dag,
    connection_alias="nessie",
    flows_to_run=branch_op_flows,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)


s3_hudi_bulk_submit_emr_steps_task = SRPEmrBulkAddStepsOperator(
    task_id="bulk_step_submission_rpc_2_s3_to_hudi",
    dag=dag,
    aws_conn_id="aws_default",
    srp_flows_info="{{ task_instance.xcom_pull('cluster_allocation_rpc_2_s3_to_hudi', key='flows_info') }}",
    spark_application_jar=spark_application_jar,
    spark_submit_options=spark_submit_options,
    srp_task_id_cluster_mapping="{{ task_instance.xcom_pull('cluster_allocation_rpc_2_s3_to_hudi', key='return_value') }}",
    queue="celery",
)


s3_to_hudi_emr_sensor_1 = EmrStateSensorAsync(
    task_id="rpc_product_tax_revision_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.product_tax_revision.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.product_tax_revision.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_1 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_rpc_product_tax_revision",
    topic_name="mysql.rpc.rpc.product_tax_revision",
    database="rpc",
    lake_schemaname="lake_rpc",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_1
    >> create_view_on_presto_1
)


s3_to_hudi_emr_sensor_2 = EmrStateSensorAsync(
    task_id="rpc_ams_city_cluster_mapping_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.ams_city_cluster_mapping.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.ams_city_cluster_mapping.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_2 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_rpc_ams_city_cluster_mapping",
    topic_name="mysql.rpc.rpc.ams_city_cluster_mapping",
    database="rpc",
    lake_schemaname="lake_rpc",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_2
    >> create_view_on_presto_2
)


s3_to_hudi_emr_sensor_3 = EmrStateSensorAsync(
    task_id="rpc_ams_cluster_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.ams_cluster.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.ams_cluster.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_3 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_rpc_ams_cluster",
    topic_name="mysql.rpc.rpc.ams_cluster",
    database="rpc",
    lake_schemaname="lake_rpc",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_3
    >> create_view_on_presto_3
)


s3_to_hudi_emr_sensor_4 = EmrStateSensorAsync(
    task_id="rpc_attribute_outlet_entity_vendor_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.attribute_outlet_entity_vendor.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.attribute_outlet_entity_vendor.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_4 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_rpc_attribute_outlet_entity_vendor",
    topic_name="mysql.rpc.rpc.attribute_outlet_entity_vendor",
    database="rpc",
    lake_schemaname="lake_rpc",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_4
    >> create_view_on_presto_4
)


s3_to_hudi_emr_sensor_5 = EmrStateSensorAsync(
    task_id="rpc_dated_tot_margin_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.dated_tot_margin.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.dated_tot_margin.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_5 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_rpc_dated_tot_margin",
    topic_name="mysql.rpc.rpc.dated_tot_margin",
    database="rpc",
    lake_schemaname="lake_rpc",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_5
    >> create_view_on_presto_5
)


s3_to_hudi_emr_sensor_6 = EmrStateSensorAsync(
    task_id="rpc_facility_adjacency_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.facility_adjacency.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.facility_adjacency.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_6 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_rpc_facility_adjacency",
    topic_name="mysql.rpc.rpc.facility_adjacency",
    database="rpc",
    lake_schemaname="lake_rpc",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_6
    >> create_view_on_presto_6
)


s3_to_hudi_emr_sensor_7 = EmrStateSensorAsync(
    task_id="rpc_facility_adjacency_log_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.facility_adjacency_log.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.facility_adjacency_log.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_7 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_rpc_facility_adjacency_log",
    topic_name="mysql.rpc.rpc.facility_adjacency_log",
    database="rpc",
    lake_schemaname="lake_rpc",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_7
    >> create_view_on_presto_7
)


s3_to_hudi_emr_sensor_8 = EmrStateSensorAsync(
    task_id="rpc_facility_polygon_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.facility_polygon.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.facility_polygon.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_8 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_rpc_facility_polygon",
    topic_name="mysql.rpc.rpc.facility_polygon",
    database="rpc",
    lake_schemaname="lake_rpc",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_8
    >> create_view_on_presto_8
)


s3_to_hudi_emr_sensor_9 = EmrStateSensorAsync(
    task_id="rpc_facility_polygon_log_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.facility_polygon_log.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.facility_polygon_log.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_9 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_rpc_facility_polygon_log",
    topic_name="mysql.rpc.rpc.facility_polygon_log",
    database="rpc",
    lake_schemaname="lake_rpc",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_9
    >> create_view_on_presto_9
)


s3_to_hudi_emr_sensor_10 = EmrStateSensorAsync(
    task_id="rpc_item_tag_type_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.item_tag_type.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.item_tag_type.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_10 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_rpc_item_tag_type",
    topic_name="mysql.rpc.rpc.item_tag_type",
    database="rpc",
    lake_schemaname="lake_rpc",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_10
    >> create_view_on_presto_10
)


s3_to_hudi_emr_sensor_11 = EmrStateSensorAsync(
    task_id="rpc_item_tag_type_value_mapping_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.item_tag_type_value_mapping.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.item_tag_type_value_mapping.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_11 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_rpc_item_tag_type_value_mapping",
    topic_name="mysql.rpc.rpc.item_tag_type_value_mapping",
    database="rpc",
    lake_schemaname="lake_rpc",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_11
    >> create_view_on_presto_11
)


s3_to_hudi_emr_sensor_12 = EmrStateSensorAsync(
    task_id="rpc_new_store_assortment_request_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.new_store_assortment_request.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.new_store_assortment_request.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_12 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_rpc_new_store_assortment_request",
    topic_name="mysql.rpc.rpc.new_store_assortment_request",
    database="rpc",
    lake_schemaname="lake_rpc",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_12
    >> create_view_on_presto_12
)


s3_to_hudi_emr_sensor_13 = EmrStateSensorAsync(
    task_id="rpc_new_store_assortment_request_log_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.new_store_assortment_request_log.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.new_store_assortment_request_log.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_13 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_rpc_new_store_assortment_request_log",
    topic_name="mysql.rpc.rpc.new_store_assortment_request_log",
    database="rpc",
    lake_schemaname="lake_rpc",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_13
    >> create_view_on_presto_13
)


s3_to_hudi_emr_sensor_14 = EmrStateSensorAsync(
    task_id="rpc_off_invoice_rule_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.off_invoice_rule.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.off_invoice_rule.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_14 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_rpc_off_invoice_rule",
    topic_name="mysql.rpc.rpc.off_invoice_rule",
    database="rpc",
    lake_schemaname="lake_rpc",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_14
    >> create_view_on_presto_14
)


s3_to_hudi_emr_sensor_15 = EmrStateSensorAsync(
    task_id="rpc_off_invoice_rule_instance_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.off_invoice_rule_instance.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.off_invoice_rule_instance.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_15 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_rpc_off_invoice_rule_instance",
    topic_name="mysql.rpc.rpc.off_invoice_rule_instance",
    database="rpc",
    lake_schemaname="lake_rpc",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_15
    >> create_view_on_presto_15
)


s3_to_hudi_emr_sensor_16 = EmrStateSensorAsync(
    task_id="rpc_off_invoice_rule_update_request_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.off_invoice_rule_update_request.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.off_invoice_rule_update_request.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_16 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_rpc_off_invoice_rule_update_request",
    topic_name="mysql.rpc.rpc.off_invoice_rule_update_request",
    database="rpc",
    lake_schemaname="lake_rpc",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_16
    >> create_view_on_presto_16
)


s3_to_hudi_emr_sensor_17 = EmrStateSensorAsync(
    task_id="rpc_product_product_log_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.product_product_log.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.product_product_log.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_17 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_rpc_product_product_log",
    topic_name="mysql.rpc.rpc.product_product_log",
    database="rpc",
    lake_schemaname="lake_rpc",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_17
    >> create_view_on_presto_17
)


s3_to_hudi_emr_sensor_18 = EmrStateSensorAsync(
    task_id="rpc_return_policy_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.return_policy.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.return_policy.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_18 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_rpc_return_policy",
    topic_name="mysql.rpc.rpc.return_policy",
    database="rpc",
    lake_schemaname="lake_rpc",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_18
    >> create_view_on_presto_18
)


s3_to_hudi_emr_sensor_19 = EmrStateSensorAsync(
    task_id="rpc_return_policy_bucket_attribute_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.return_policy_bucket_attribute.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.return_policy_bucket_attribute.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_19 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_rpc_return_policy_bucket_attribute",
    topic_name="mysql.rpc.rpc.return_policy_bucket_attribute",
    database="rpc",
    lake_schemaname="lake_rpc",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_19
    >> create_view_on_presto_19
)


s3_to_hudi_emr_sensor_20 = EmrStateSensorAsync(
    task_id="rpc_substitutable_group_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.substitutable_group.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.substitutable_group.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_20 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_rpc_substitutable_group",
    topic_name="mysql.rpc.rpc.substitutable_group",
    database="rpc",
    lake_schemaname="lake_rpc",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_20
    >> create_view_on_presto_20
)


s3_to_hudi_emr_sensor_21 = EmrStateSensorAsync(
    task_id="rpc_substitutable_item_group_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.substitutable_item_group.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.substitutable_item_group.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_21 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_rpc_substitutable_item_group",
    topic_name="mysql.rpc.rpc.substitutable_item_group",
    database="rpc",
    lake_schemaname="lake_rpc",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_21
    >> create_view_on_presto_21
)


s3_to_hudi_emr_sensor_22 = EmrStateSensorAsync(
    task_id="rpc_supply_event_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.supply_event.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.supply_event.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_22 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_rpc_supply_event",
    topic_name="mysql.rpc.rpc.supply_event",
    database="rpc",
    lake_schemaname="lake_rpc",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_22
    >> create_view_on_presto_22
)


s3_to_hudi_emr_sensor_23 = EmrStateSensorAsync(
    task_id="rpc_supply_event_info_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.supply_event_info.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.supply_event_info.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_23 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_rpc_supply_event_info",
    topic_name="mysql.rpc.rpc.supply_event_info",
    database="rpc",
    lake_schemaname="lake_rpc",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_23
    >> create_view_on_presto_23
)


s3_to_hudi_emr_sensor_24 = EmrStateSensorAsync(
    task_id="rpc_temporary_stock_details_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.temporary_stock_details.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.temporary_stock_details.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_24 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_rpc_temporary_stock_details",
    topic_name="mysql.rpc.rpc.temporary_stock_details",
    database="rpc",
    lake_schemaname="lake_rpc",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_24
    >> create_view_on_presto_24
)


s3_to_hudi_emr_sensor_25 = EmrStateSensorAsync(
    task_id="rpc_temporary_stock_details_log_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.temporary_stock_details_log.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.temporary_stock_details_log.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_25 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_rpc_temporary_stock_details_log",
    topic_name="mysql.rpc.rpc.temporary_stock_details_log",
    database="rpc",
    lake_schemaname="lake_rpc",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_25
    >> create_view_on_presto_25
)


s3_to_hudi_emr_sensor_26 = EmrStateSensorAsync(
    task_id="rpc_transfer_case_size_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.transfer_case_size.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.transfer_case_size.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_26 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_rpc_transfer_case_size",
    topic_name="mysql.rpc.rpc.transfer_case_size",
    database="rpc",
    lake_schemaname="lake_rpc",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_26
    >> create_view_on_presto_26
)


s3_to_hudi_emr_sensor_27 = EmrStateSensorAsync(
    task_id="rpc_transfer_case_size_log_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.transfer_case_size_log.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.transfer_case_size_log.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_27 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_rpc_transfer_case_size_log",
    topic_name="mysql.rpc.rpc.transfer_case_size_log",
    database="rpc",
    lake_schemaname="lake_rpc",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_27
    >> create_view_on_presto_27
)


s3_to_hudi_emr_sensor_28 = EmrStateSensorAsync(
    task_id="rpc_transfer_tag_rules_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.transfer_tag_rules.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.transfer_tag_rules.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_28 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_rpc_transfer_tag_rules",
    topic_name="mysql.rpc.rpc.transfer_tag_rules",
    database="rpc",
    lake_schemaname="lake_rpc",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_28
    >> create_view_on_presto_28
)


s3_to_hudi_emr_sensor_29 = EmrStateSensorAsync(
    task_id="rpc_transfer_tag_rules_log_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.transfer_tag_rules_log.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.transfer_tag_rules_log.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_29 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_rpc_transfer_tag_rules_log",
    topic_name="mysql.rpc.rpc.transfer_tag_rules_log",
    database="rpc",
    lake_schemaname="lake_rpc",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_29
    >> create_view_on_presto_29
)


s3_to_hudi_emr_sensor_30 = EmrStateSensorAsync(
    task_id="rpc_warehouse_transition_s3_to_hudi_emr_sensor",
    step_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.warehouse_transition.v1', {}).get('step_id') }}",
    cluster_id="{{ task_instance.xcom_pull('bulk_step_submission_rpc_2_s3_to_hudi', key='flow_id_job_mapping').get('rpc.warehouse_transition.v1', {}).get('cluster_id') }}",
    db_conn_id="nessie_db",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

create_view_on_presto_30 = PrestoCreateViewOperator(
    task_id="create_view_on_presto_lake_rpc_warehouse_transition",
    topic_name="mysql.rpc.rpc.warehouse_transition",
    database="rpc",
    lake_schemaname="lake_rpc",
    dag=dag,
    retries=3,
    retry_delay=dt.timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

(
    s3_hudi_cluster_allocation_task
    >> s3_hudi_bulk_submit_emr_steps_task
    >> s3_to_hudi_emr_sensor_30
    >> create_view_on_presto_30
)


if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = dt.timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
