{"cells": [{"cell_type": "code", "execution_count": null, "id": "78e5d6d4-543b-4468-88ee-37b144262e78", "metadata": {}, "outputs": [], "source": ["import random\n", "import string\n", "import datetime\n", "import pencilbox as pb\n", "import pandas as pd\n", "import os"]}, {"cell_type": "code", "execution_count": null, "id": "be69343c-755b-40d8-ac43-b18cd7c0fd33", "metadata": {}, "outputs": [], "source": ["SCHEMA_NAME = \"consumer_etls\"\n", "TABLE_NAME = \"grofers_app_device_uuid\"\n", "TABLE_DESCRIPTION = \"Log of first_seen and last seen on our mobile platforms for a unique combination of user_id, device_uuid, advertising_id and platform\""]}, {"cell_type": "code", "execution_count": null, "id": "d7eafa84-2dd9-4166-8437-f509b2aa7045", "metadata": {}, "outputs": [], "source": ["INTERIM_SCHEMA_NAME = \"interim\"\n", "INTERIM_TABLE_NAME = \"grofers_app_device_uuid_logging\"\n", "INTERIM_TABLE_DESCRIPTION = (\n", "    \"Log of records being inserted in grofers_app_device_uuid on hourly level\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a784d6c7-2cf7-4750-b0bc-532c2d730f4f", "metadata": {}, "outputs": [], "source": ["COLUMN_DTYPES = [\n", "    {\n", "        \"name\": \"device_uuid\",\n", "        \"type\": \"VARCHAR\",\n", "        \"description\": \"Device UUID for the device which triggered the event. device id for Android and advertising_id for iOS\",\n", "    },\n", "    {\n", "        \"name\": \"platform\",\n", "        \"type\": \"VARCHAR\",\n", "        \"description\": \"Platform for the event. e.g. - ios, android\",\n", "    },\n", "    {\n", "        \"name\": \"advertising_id\",\n", "        \"type\": \"VARCHAR\",\n", "        \"description\": \"Advertising id for the device\",\n", "    },\n", "    {\n", "        \"name\": \"user_id\",\n", "        \"type\": \"INTEGER\",\n", "        \"description\": \"Grofers user_id of the user\",\n", "    },\n", "    {\n", "        \"name\": \"first_seen_timestamp_ist\",\n", "        \"type\": \"TIMESTAMP(6)\",\n", "        \"description\": \"First timestamp among events from the device, platform, advertising_id, user_id combination\",\n", "    },\n", "    {\n", "        \"name\": \"last_seen_timestamp_ist\",\n", "        \"type\": \"TIMESTAMP(6)\",\n", "        \"description\": \"Latest timestamp among events from the device, platform, advertising_id, user_id combination\",\n", "    },\n", "    {\n", "        \"name\": \"etl_snapshot_ts_ist\",\n", "        \"type\": \"TIMESTAMP(6)\",\n", "        \"description\": \"current timestamp when dag runs, same for every row\",\n", "    },\n", "]\n", "\n", "INTERIM_COLUMN_DTYPES = COLUMN_DTYPES + [\n", "    {\"name\": \"dt\", \"type\": \"date\", \"description\": \"current date\"},\n", "    {\"name\": \"current_hour\", \"type\": \"VARCHAR\", \"description\": \"current hour\"},\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "05018f05-904a-408c-a83e-ca740f1da6b8", "metadata": {}, "outputs": [], "source": ["trino_kwargs = {\n", "    \"schema_name\": SCHEMA_NAME,\n", "    \"table_name\": TABLE_NAME,\n", "    \"column_dtypes\": COLUMN_DTYPES,\n", "    \"primary_key\": [\"device_uuid\", \"platform\", \"advertising_id\", \"user_id\"],\n", "    \"incremental_key\": \"device_uuid\",\n", "    \"load_type\": \"upsert\",\n", "    \"table_description\": TABLE_DESCRIPTION,\n", "}\n", "\n", "interim_trino_kwargs = {\n", "    \"schema_name\": INTERIM_SCHEMA_NAME,\n", "    \"table_name\": INTERIM_TABLE_NAME,\n", "    \"column_dtypes\": INTERIM_COLUMN_DTYPES,\n", "    \"load_type\": \"append\",\n", "    \"partition_key\": [\"dt\"],\n", "    \"table_description\": INTERIM_TABLE_DESCRIPTION,\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "652347a9-b3ea-4b8a-ba1f-3ba7a38bc9ff", "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "import pytz\n", "\n", "ist_timezone = pytz.timezone(\"Asia/Kolkata\")\n", "current_time = datetime.now(ist_timezone)\n", "\n", "print(current_time)\n", "\n", "# If current hour is 0, we need to include all hours from previous day\n", "if current_time.hour == 0:\n", "    # Get the start of previous day\n", "    prev_day = (current_time - timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)\n", "    # Generate hours for previous day (0-23)\n", "    partition_conditions = [\n", "        f\"(dt='{prev_day.date().strftime('%Y/%m/%d')}' and hour='{hour:02d}')\" for hour in range(24)\n", "    ]\n", "    # Add current hour (which is 0)\n", "    partition_conditions.append(f\"(dt='{current_time.date().strftime('%Y/%m/%d')}' and hour='00')\")\n", "else:\n", "    # Original logic for other hours\n", "    start_date = current_time - <PERSON><PERSON><PERSON>(hours=5)\n", "    temp_time = start_date\n", "    partition_conditions = []\n", "    while temp_time <= current_time:\n", "        temp_time += <PERSON><PERSON>ta(hours=1)\n", "        partition_conditions.append(\n", "            f\"(dt='{temp_time.date().strftime('%Y/%m/%d')}' and hour='{temp_time.hour:02d}')\"\n", "        )\n", "\n", "final_partition_condition = \" OR \".join(partition_conditions)\n", "print(final_partition_condition)"]}, {"cell_type": "code", "execution_count": null, "id": "c8d37c85-3a0f-4879-8d8c-55bd602edbe4", "metadata": {}, "outputs": [], "source": ["trino_sql_template = f\"\"\"\n", "WITH temp_table AS\n", "  ( SELECT case source\n", "               when 'ios' then coalesce(traits.device_uuid, device_id)\n", "               else traits.device_uuid\n", "           end as device_uuid,\n", "           source as platform,\n", "           lower(device_id) as advertising_id,\n", "           cast(user_id as integer) as user_id,\n", "           min(cast(from_unixtime(\"time\") as timestamp)) AS \"first_seen_timestamp_ist\",\n", "           max(cast(from_unixtime(\"time\") as timestamp)) AS \"last_seen_timestamp_ist\"\n", "   FROM de_etls.blinkit_click_events_json_dump\n", "   WHERE \n", "       ({final_partition_condition})\n", "        AND try_cast(user_id as integer) is not null \n", "        AND try_cast(user_id as integer) != -1\n", "        AND \"time\" is not null\n", "        AND source in ('android', 'ios')\n", "   GROUP BY 1,\n", "            2,\n", "            3,\n", "            4)\n", "SELECT coalesce(a.device_uuid, '') as device_uuid,\n", "       coalesce(a.platform, 'unknown') as platform,\n", "       coalesce(a.advertising_id, '') as advertising_id,\n", "       a.user_id as user_id,\n", "       (CASE\n", "            WHEN b.first_seen_timestamp_ist < cast(a.first_seen_timestamp_ist AS TIMESTAMP(6)) THEN b.first_seen_timestamp_ist\n", "            ELSE cast(a.first_seen_timestamp_ist AS TIMESTAMP(6))\n", "        END) AS first_seen_timestamp_ist,\n", "       (CASE\n", "            WHEN b.last_seen_timestamp_ist > cast(a.last_seen_timestamp_ist AS TIMESTAMP(6)) THEN b.last_seen_timestamp_ist\n", "            ELSE cast(a.last_seen_timestamp_ist AS TIMESTAMP(6))\n", "        END) AS last_seen_timestamp_ist,\n", "       CAST(CURRENT_TIMESTAMP AS TIMESTAMP(6)) AS etl_snapshot_ts_ist\n", "       {{current_hour_sql}}\n", "FROM temp_table a\n", "LEFT JOIN {SCHEMA_NAME}.{TABLE_NAME} b ON a.device_uuid = b.device_uuid\n", "AND a.platform = b.platform\n", "AND a.advertising_id = b.advertising_id\n", "AND a.user_id = b.user_id\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "b716c17d-41d9-48b9-b7ee-dc28f3e3bb42", "metadata": {}, "outputs": [], "source": ["main_trino_sql = trino_sql_template.format(current_hour_sql=\"\")"]}, {"cell_type": "code", "execution_count": null, "id": "561ce941-6d9d-4ee7-8ede-fdc15b824f78", "metadata": {}, "outputs": [], "source": ["print(main_trino_sql)"]}, {"cell_type": "code", "execution_count": null, "id": "675d809b-81ca-4e28-a69e-0959fdbab5a5", "metadata": {}, "outputs": [], "source": ["pb.to_trino(main_trino_sql, **trino_kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "a8e07d74-41e8-491d-b83a-ea4b9997c135", "metadata": {}, "outputs": [], "source": ["interim_trino_sql = trino_sql_template.format(\n", "    current_hour_sql=\"\"\"\n", "    , current_date as dt\n", "    ,cast(hour(current_timestamp) as var<PERSON><PERSON>) as current_hour\n", "    \"\"\"\n", ")\n", "print(interim_trino_sql)"]}, {"cell_type": "code", "execution_count": null, "id": "579efee6-d507-4608-877a-e1d487507d18", "metadata": {}, "outputs": [], "source": ["pb.to_trino(interim_trino_sql, **interim_trino_kwargs)"]}, {"cell_type": "code", "execution_count": null, "id": "b6939aed-e717-4914-9af1-7547e4065be5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 5}