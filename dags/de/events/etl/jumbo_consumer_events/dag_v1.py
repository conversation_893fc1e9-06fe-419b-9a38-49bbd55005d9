import os
import json
import yaml
import time
import requests
import dateutil
import logging
from datetime import datetime, timedelta
import pandas as pd
import tempfile
from typing import List, Tuple, Dict
from concurrent.futures import ThreadPoolExecutor

from airflow import DAG, models, settings
from airflow.hooks.base_hook import BaseHook
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from airflow.providers.postgres.hooks.postgres import PostgresHook
from airflow.operators.python_operator import PythonOperator
from airflow.providers.postgres.operators.postgres import PostgresOperator
from airflow.providers.amazon.aws.operators.emr_add_steps import EmrAddStepsOperator
from airflow.providers.amazon.aws.operators.emr_create_job_flow import EmrCreateJobFlowOperator
from airflow.providers.amazon.aws.operators.emr_terminate_job_flow import EmrTerminateJobFlowOperator
from airflow.providers.opsgenie.hooks.opsgenie_alert import Opsgenie<PERSON>lertHook
from airflow.models import Variable
from metrics_plugin import PapermillOperator, EmrStateSensorAsync
from kubernetes.client import models as k8s

import pencilbox as pb

cwd = os.path.dirname(os.path.realpath(__file__))

k8s_executor_config = {
    "pod_override": k8s.V1Pod(
        spec=k8s.V1PodSpec(
            service_account_name="blinkit-prod-airflow-de-primary-eks-role",
            node_selector={"nodetype": "airflow-spot-general"},
            tolerations=[
                k8s.V1Toleration(
                    effect="NoSchedule",
                    key="service",
                    operator="Equal",
                    value="airflow-spot-general",
                ),
            ],
            containers=[
                k8s.V1Container(
                    image=Variable.get("************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable", "************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable"),
                    name="base",
                    volume_mounts=[
                        k8s.V1VolumeMount(
                            mount_path="/usr/local/airflow/plugins",
                            name="airflow-dags",
                            sub_path="airflow-de/plugins",
                        ),
                    ],
                    resources=k8s.V1ResourceRequirements(
                        limits={"cpu": 1, "memory": "4G"},
                        requests={"cpu": 0.5, "memory": "500M"},
                    ),
                )
            ],
        )
    )
}


def pagerduty_failure_alert(context):
    dag_id = context.get("task_instance").dag_id
    run_id = context.get("run_id")
    task_id = context.get("task_instance").task_id
    header = {"Content-Type": "application/json"}
    owner = {"email": "<EMAIL>", "slack_id": "S089D0GQRCJ"}

    def get_payload(service_name):
        payload = {
            "event_action": "trigger",
            "links": [
                {
                    "href": context.get("task_instance").log_url.replace("http://", "https://"),
                    "text": "Logs URL",
                },
                {
                    "href": f"https://dse-commuter.grofer.io/view/{dag_id}/{run_id}/{task_id}.ipynb",
                    "text": "Notebook URL",
                },
            ],
            "client": "Airflow",
            "client_url": context.get("task_instance").log_url.replace("http://", "https://"),
            "payload": {
                "summary": f"DAG {dag_id} failed",
                "source": f"Airflow:{dag_id}:{run_id}:{task_id}",
                "custom_details": {
                    "Dag ID": dag_id,
                    "Run ID": run_id,
                    "Task ID": task_id,
                    "Owner": owner["email"],
                    "service_name": service_name,
                },
                "severity": "critical",
                "service_name": service_name,
            },
        }
        return payload

    alerts = [{"name": "data", "type": "service"}]
    pd_service_names = [alert["name"] for alert in alerts if alert["type"] == "service"]
    for service_name in pd_service_names:
        payload = get_payload(service_name)
        payload["routing_key"] = pb.get_secret("dse/services/pagerduty/routing_keys")["global"]
        response = requests.post(
            "https://events.pagerduty.com/v2/enqueue",
            data=json.dumps(payload),
            headers=header,
        )
        if response.json()["status"] != "success":
            logging.warning(f"Could not send Pagerduty Alert: {response.text}")
            slack_id = owner["slack_id"]
            _owner = {
                "s": "s" if len(slack_id.split(" ")) > 1 else "",
                "slack_id": "<@{}>".format(slack_id),
            }
            message = (
                f"{_owner['slack_id']} `{dag_id}` failed but cannot send pagerduty alert to `{service_name}`."
                f"\n```{response.text}```"
            )
            pb.send_slack_message(channel="bl-data-airflow-alerts", text=message, source_info=False)


def slack_failure_alert(context):
    task_instance = context.get("task_instance")
    dag_id = task_instance.dag_id
    run_id = context.get("run_id")
    task_id = task_instance.task_id
    log_url = task_instance.log_url.replace("http://", "https://")
    notebook_url = (
        f"https://dse-commuter.grofer.io/view/{dag_id}/{run_id}/{task_id}.ipynb"
        if task_instance.operator == "PapermillOperator"
        else None
    )

    owner = {"email": "<EMAIL>", "slack_id": "S089D0GQRCJ"}
    slack_id = owner["slack_id"]
    _owner = {
        "s": "s" if len(slack_id.split(" ")) > 1 else "",
        "slack_id": "<!subteam^{}>".format(slack_id),
    }

    slack_failure_msg = f"""
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner{_owner["s"]}*: {_owner["slack_id"]}
        *Log Url*: {log_url}
        {f"*Notebook Url*: {notebook_url}" if notebook_url is not None else ""}
    """

    slack_alert_configs = [
        {"channel": "bl-data-alerts-p0"},
    ]

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(channel=channel, text=slack_failure_msg, source_info=False)
        except Exception as e:
            logging.warning(e)
            message = (
                f"{_owner['slack_id']} `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(channel="bl-data-airflow-alerts", text=message, source_info=False)


def failure_alerts(context):
    slack_failure_alert(context)
    pagerduty_failure_alert(context)

def multi_upload_to_s3(object_list: List[List[str]]) -> None:
    for filename, key, bucket_name in object_list:
        pb.to_s3(filename, bucket_name, key)


TRINO_TO_SPARK_TYPE_MAPPING_READ = {
    "BOOLEAN": "string",
    "INTEGER": "string",
    "BIGINT": "string",
    "REAL": "string",
    "DOUBLE": "string",
    "DATE": "string",
    "TIMESTAMP(6)": "string",
    "VARCHAR": "string",
    "VARBINARY": "string",
    "DECIMAL": "string",
}


TRINO_TO_SPARK_TYPE_MAPPING_WRITE = {
    "BOOLEAN": "boolean",
    "INTEGER": "int",
    "BIGINT": "long",
    "REAL": "float",
    "DOUBLE": "double",
    "DATE": "date",
    "TIMESTAMP(6)": "timestamp",
    "VARCHAR": "string",
    "VARBINARY": "binary",
    "DECIMAL": "decimal",
}


def get_fields(data_type_str):
    level = 0
    field = ""
    fields = []
    for index, value in enumerate(data_type_str):
        if value == "(":
            level += 1
        if value == ")":
            level -= 1
        if level >= 1:
            field += value
        if (level == 1 and value == ",") or (index == len(data_type_str) - 1):
            fields.append(field.removeprefix("(").removesuffix(",").strip())
            field = ""
    return fields


def get_row_fields(data_type):
    fields = []
    fields_strings = get_fields(data_type)
    for field in fields_strings:
        if len(field.split(" ", 1)) < 2:
            raise Exception(f"Improper named field {field} specified")
        fields.append(
            f'{field.split(" ", 1)[0]} {field.split(" ", 1)[1]}'
        )
    return fields


def convert_trino_to_spark(data_type, trino_spark_type_mapping):
    # Convert any extra space, newline, tab to single space
    if data_type.upper() in trino_spark_type_mapping:
        return trino_spark_type_mapping[data_type.upper()]
    elif data_type.upper()[:5] == "ARRAY":
        return f"array<{convert_trino_to_spark(data_type[6:-1], trino_spark_type_mapping)}>"
    elif data_type.upper()[:3] == "ROW":
        fields_strings = get_row_fields(data_type)
        fields = []
        for field in fields_strings:
            fields.append(
                f'{field.split(" ", 1)[0]} {convert_trino_to_spark(field.split(" ", 1)[1], trino_spark_type_mapping)}'
            )
        return f"struct<{', '.join(fields)}>"
    elif data_type[:3] == "MAP":
        fields_strings = get_fields(data_type)
        return f"map<{convert_trino_to_spark(fields_strings[0], trino_spark_type_mapping)}, {convert_trino_to_spark(fields_strings[1], trino_spark_type_mapping)}>"
    else:
        raise Exception(f"Unsupported data type {data_type} was passed.")


def upload_spark_read_schema():
    trino_con = pb.get_connection("[Warehouse] Trino")
    product_image_schema_df = pd.read_sql_query(sql="DESC zomato.blinkit_jumbo2.product_image_shown_events",
                                                con=trino_con)
    click_schema_df = pd.read_sql_query(sql="DESC zomato.blinkit_jumbo2.click_events", con=trino_con)
    impression_schema_df = pd.read_sql_query(sql="DESC zomato.blinkit_jumbo2.impression_events", con=trino_con)

    product_image_prop = set(
        get_row_fields(product_image_schema_df[product_image_schema_df["Column"] == "properties"]["Type"].to_list()[0]))
    impression_prop = set(
        get_row_fields(impression_schema_df[impression_schema_df["Column"] == "properties"]["Type"].to_list()[0]))

    click_properties = click_schema_df[click_schema_df["Column"] == "properties"]["Type"].to_list()[0]
    impression_properties = f"ROW({', '.join(set.union(product_image_prop, impression_prop))})"

    traits = impression_schema_df[impression_schema_df["Column"] == "traits"]["Type"].to_list()[0]

    all_properties = {
        "click_events": click_properties,
        "impression_events": impression_properties
    }

    for table_name, properties in all_properties.items():
        read_properties_str = convert_trino_to_spark(properties, TRINO_TO_SPARK_TYPE_MAPPING_READ)
        read_traits_str = convert_trino_to_spark(traits, TRINO_TO_SPARK_TYPE_MAPPING_READ)
        write_properties_str = convert_trino_to_spark(properties, TRINO_TO_SPARK_TYPE_MAPPING_WRITE)
        write_traits_str = convert_trino_to_spark(traits, TRINO_TO_SPARK_TYPE_MAPPING_WRITE)

        spark_string_schema = """event_name string not null,
        user_id string,
        source string,
        time long,
        session_id string,
        location int,
        ip_address string,
        device_id string,
        user_agent string,
        ingestion_timestamp long,
        location_info struct<user_defined_latitude double, user_defined_longitude double, current_latitude double, current_longitude double>,
        app_info struct<app_appearance string, device_performance string, system_theme string, theme string>,
        traits {traits_str},
        properties {properties_str}
        """

        local_filename = tempfile.NamedTemporaryFile().name
        with open(local_filename, "w") as fo:
            fo.write(spark_string_schema.format(traits_str=read_traits_str, properties_str=read_properties_str))
        s3_bucket = "prod-dse-srp-configs"
        s3_fullpath = f"schemas/jumbo/spark/{table_name}_read_schema.txt"
        pb.to_s3(local_filename, s3_bucket, s3_fullpath)

        local_filename = tempfile.NamedTemporaryFile().name
        with open(local_filename, "w") as fw:
            fw.write(spark_string_schema.format(traits_str=write_traits_str, properties_str=write_properties_str))
        s3_bucket = "prod-dse-srp-configs"
        s3_fullpath = f"schemas/jumbo/spark/{table_name}_write_schema.txt"
        pb.to_s3(local_filename, s3_bucket, s3_fullpath)


def s3_sync(aws_conn_id="aws_default", **kwargs):
    s3_hook = S3Hook(aws_conn_id=aws_conn_id)
    keys = s3_hook.list_keys(
        bucket_name=kwargs['source_bucket'],
        prefix=kwargs['source_prefix'],
        delimiter=''
    )

    print((f"{len(keys)} keys will be synced from "
        f"s3://{kwargs['source_bucket']}/{kwargs['source_prefix']} "
        f"to s3://{kwargs['destination_bucket']}/{kwargs['destination_prefix']}"))

    for idx, key in enumerate(keys):
        if kwargs.get('extension') and key.rsplit('.', 1)[-1] != kwargs['extension']:
            continue
        destination_key = key.replace(kwargs['source_prefix'], kwargs['destination_prefix'])
        source_bucket_key = f"s3://{kwargs['source_bucket']}/{key}"
        destination_bucket_key = f"s3://{kwargs['destination_bucket']}/{destination_key}"
        print(f"Syncing : #{idx} {source_bucket_key} -> {destination_bucket_key}")
        s3_hook.copy_object(
            source_bucket_key=source_bucket_key,
            dest_bucket_key=destination_bucket_key,
        )
    return len(keys)

def s3_sync_parallel(aws_conn_id="aws_default", **kwargs) -> Dict:
    """S3Hook parallel copy with ThreadPool."""
    
    s3_hook = S3Hook(aws_conn_id=aws_conn_id)
    keys = s3_hook.list_keys(
        bucket_name=kwargs['source_bucket'],
        prefix=kwargs['source_prefix'],
        delimiter=''
    )
    
    print((f"{len(keys)} keys will be synced from "
        f"s3://{kwargs['source_bucket']}/{kwargs['source_prefix']} "
        f"to s3://{kwargs['destination_bucket']}/{kwargs['destination_prefix']}"))
    
    # Create a counter using itertools.count to track file numbers
    from itertools import count
    counter = count(1)
    
    # Thread-local storage for worker ID
    import threading
    thread_local = threading.local()
    
    def get_worker_id():
        if not hasattr(thread_local, 'worker_id'):
            thread_local.worker_id = threading.get_ident()
        return thread_local.worker_id
    
    def copy_file(key: str) -> Tuple[str, bool, str]:
        try:
            if kwargs.get('extension') and key.rsplit('.', 1)[-1] != kwargs['extension']:
                return key, False, f"File extension does not match. Skipping file {key}"
            file_num = next(counter)
            worker_id = get_worker_id()
            dest_key = key.replace(kwargs['source_prefix'], kwargs['destination_prefix'])
            source_bucket_key = f"s3://{kwargs['source_bucket']}/{key}"
            destination_bucket_key = f"s3://{kwargs['destination_bucket']}/{dest_key}"
            print(f"Syncing: File #{file_num} (Worker {worker_id}): {source_bucket_key} -> {destination_bucket_key}")
            s3_hook.copy_object(
                source_bucket_key=source_bucket_key,
                dest_bucket_key=destination_bucket_key,
            )
            return key, True, ""
        except Exception as e:
            print(f"Error in worker {get_worker_id()} processing {key}: {str(e)}")
            return key, False, str(e)
    
    results = []
    with ThreadPoolExecutor(max_workers=kwargs['max_workers']) as executor:
        future_to_key = {executor.submit(copy_file, key): key for key in keys}
        for future in future_to_key:
            results.append(future.result())
    
    return results


args = {
    "owner": "<EMAIL>",
    "on_failure_callback": failure_alerts,
    "start_date": datetime.strptime("2024-03-06T11:40:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": datetime.strptime("2026-08-15T23:00:00", "%Y-%m-%dT%H:%M:%S"),
    "slack_id": "S089D0GQRCJ",
}

dag = DAG(
    dag_id="de_events_jumbo_consumer_hourly_v1",
    default_args=args,
    schedule_interval="40 * * * *",
)

env = {}
default_env = {
    "AIRFLOW_DAG_OWNER": "<EMAIL>",
}
env.update(default_env)


EMR_SETTINGS_OVERRIDE = {
    "Name": "jumbo_consumer_events_v1",
    "LogUri": "s3://grofers-prod-dse-sgp/elasticmapreduce/",
    "ReleaseLabel": "emr-6.11.1",
    "Applications": [{"Name": "Hadoop"}, {"Name": "Spark"}, {"Name": "Ganglia"}],
    "Instances": {
        "KeepJobFlowAliveWhenNoSteps": True,
        "Ec2KeyName": "dse-emr",
        "ServiceAccessSecurityGroup": "sg-64858200",
        "Ec2SubnetIds": [
            "subnet-09e5bab68a3640d38",
            "subnet-fe54ac88",
            "subnet-e00fc184",
        ],
        "EmrManagedSlaveSecurityGroup": "sg-587afc3c",
        "EmrManagedMasterSecurityGroup": "sg-587afc3c",
        "InstanceFleets": [
            {
                "Name": "MasterFleet",
                "InstanceFleetType": "MASTER",
                "TargetOnDemandCapacity": 1,
                "TargetSpotCapacity": 0,
                "InstanceTypeConfigs": [
                    {
                        "InstanceType": "m6g.xlarge",
                        "WeightedCapacity": 1,
                        "BidPriceAsPercentageOfOnDemandPrice": 100,
                        "EbsConfiguration": {
                            "EbsBlockDeviceConfigs": [
                                {
                                    "VolumeSpecification": {
                                        "VolumeType": "gp3",
                                        "Iops": 3000,
                                        "SizeInGB": 32
                                    }
                                }
                            ]
                        },
                    },
                    {
                        "InstanceType": "m7g.xlarge",
                        "WeightedCapacity": 1,
                        "BidPriceAsPercentageOfOnDemandPrice": 100,
                        "EbsConfiguration": {
                            "EbsBlockDeviceConfigs": [
                                {
                                    "VolumeSpecification": {
                                        "VolumeType": "gp3",
                                        "Iops": 3000,
                                        "SizeInGB": 32
                                    }
                                }
                            ]
                        },
                    },
                ],
                "LaunchSpecifications": {
                    "OnDemandSpecification": {"AllocationStrategy": "lowest-price"}
                },
            },
            {
                "Name": "CoreFleet",
                "InstanceFleetType": "CORE",
                "TargetOnDemandCapacity": 2,
                "TargetSpotCapacity": 0,
                "InstanceTypeConfigs": [
                    {
                        "InstanceType": "r6g.2xlarge",
                        "EbsConfiguration": {
                            "EbsBlockDeviceConfigs": [
                                {
                                    "VolumeSpecification": {
                                        "VolumeType": "gp3",
                                        "Iops": 3000,
                                        "SizeInGB": 50
                                    }
                                }
                            ]
                        },
                    },
                    {
                        "InstanceType": "r7g.2xlarge",
                        "EbsConfiguration": {
                            "EbsBlockDeviceConfigs": [
                                {
                                    "VolumeSpecification": {
                                        "VolumeType": "gp3",
                                        "Iops": 3000,
                                        "SizeInGB": 50
                                    }
                                }
                            ]
                        },
                    },
                ],
                "LaunchSpecifications": {
                    "OnDemandSpecification": {"AllocationStrategy": "lowest-price"}
                },
            },
            {
                "Name": "TaskFleet",
                "InstanceFleetType": "TASK",
                "TargetSpotCapacity": 45,
                "InstanceTypeConfigs": [
                    {
                        "InstanceType": "r6g.2xlarge",
                        "EbsConfiguration": {
                            "EbsBlockDeviceConfigs": [
                                {
                                    "VolumeSpecification": {
                                        "VolumeType": "gp3",
                                        "Iops": 3000,
                                        "SizeInGB": 50
                                    }
                                }
                            ]
                        },
                    },
                    {
                        "InstanceType": "r7g.2xlarge",
                        "EbsConfiguration": {
                            "EbsBlockDeviceConfigs": [
                                {
                                    "VolumeSpecification": {
                                        "VolumeType": "gp3",
                                        "Iops": 3000,
                                        "SizeInGB": 50
                                    }
                                }
                            ]
                        },
                    },
                    {
                        "InstanceType": "m6g.4xlarge",
                        "EbsConfiguration": {
                            "EbsBlockDeviceConfigs": [
                                {
                                    "VolumeSpecification": {
                                        "VolumeType": "gp3",
                                        "Iops": 3000,
                                        "SizeInGB": 50
                                    }
                                }
                            ]
                        },
                    },
                    {
                        "InstanceType": "m7g.4xlarge",
                        "EbsConfiguration": {
                            "EbsBlockDeviceConfigs": [
                                {
                                    "VolumeSpecification": {
                                        "VolumeType": "gp3",
                                        "Iops": 3000,
                                        "SizeInGB": 50
                                    }
                                }
                            ]
                        },
                    },
                    {
                        "InstanceType": "r6gd.2xlarge",
                        "EbsConfiguration": {},
                    },
                    {
                        "InstanceType": "m6gd.4xlarge",
                        "EbsConfiguration": {},
                    },
                    {
                        "InstanceType": "r7gd.2xlarge",
                        "EbsConfiguration": {},
                    },
                ],
                "LaunchSpecifications": {
                    "SpotSpecification": {
                        "TimeoutDurationMinutes": 10,
                        "TimeoutAction": "SWITCH_TO_ON_DEMAND",
                        "AllocationStrategy": "capacity-optimized",
                    }
                },
            },
        ],
    },
    "ManagedScalingPolicy": {
        'ComputeLimits': {
            'UnitType': 'InstanceFleetUnits',
            'MinimumCapacityUnits': 1,
            'MaximumCapacityUnits': 70,
            'MaximumOnDemandCapacityUnits': 2,
            'MaximumCoreCapacityUnits': 2
        }
    },
    "StepConcurrencyLevel": 10,
    "Configurations": [
        {
            "Classification": "spark-hive-site",
            "Properties": {
                "hive.exec.dynamic.partition.mode": "nonstrict",
                "hive.metastore.uris": "thrift://datalake-hive-metastore-service-data.prod-sgp-k8s.grofer.io:9083"
            }
        },
        {
            "Classification": "hive-site",
            "Properties": {
                "hive.execution.engine": "tez"
            }
        },
        {
            "Classification": "hiveserver2-site",
            "Properties": {
                "hive.compute.query.using.stats": "false",
                "hive.exec.dynamic.partition.mode": "nonstrict",
                "hive.exec.max.dynamic.partitions": "100000",
                "hive.exec.max.dynamic.partitions.pernode": "100000",
                "hive.execution.engine": "tez",
                "hive.groupby.orderby.position.alias": "true"
            }
        }
    ],
    "BootstrapActions": [
        {
            "Name": "python-libs",
            "ScriptBootstrapAction": {
                "Path": "s3://prod-data-feature-store/config/emr/python_bootstrap_packages.sh"
            },
        },
        {
            "Name": "kafka-libs",
            "ScriptBootstrapAction": {
                "Path": "s3://prod-data-feature-store/config/emr/bootstrap_spark_packages.sh"
            },
        },
        {
            "Name": "mysql-libs",
            "ScriptBootstrapAction": {
                "Path": "s3://prod-dse-srp-configs/scripts/mysql_connector_install.sh"
            },
        },
    ],
    "VisibleToAllUsers": True,
    "JobFlowRole": "EMR_EC2_DefaultRole",
    "ServiceRole": "EMR_DefaultRole",
    "Tags": [
        {"Key": "Dag", "Value": "de_events_jumbo_consumer_hourly_v1"},
        {"Key": "Name", "Value": "de_events_jumbo_consumer_hourly_v1"},
        {"Key": "grofers.io/service", "Value": "consumer-events"},
        {"Key": "grofers.io/component", "Value": "consumer-events-spark-cluster"},
        {"Key": "grofers.io/component-role", "Value": "data-processing"},
        {"Key": "grofers.io/business-service", "Value": "data-platform"},
        {"Key": "grofers.io/team", "Value": "data-engineering"},
        {"Key": "grofers.io/tribe", "Value": "data"},
        {"Key": "cost:namespace", "Value": "data"},
        {"Key": "cost:application", "Value": "emr"},
    ],
}


def fetch_spark_steps(etl_name, executor_config, arguments=[]):
    source_python_base_s3_path = "s3://prod-dse-spark-etl-processed-sgp/config/spark/de_events_jumbo_consumer_hourly"
    spark_submit = (
        "spark-submit --deploy-mode cluster --master yarn"
        " --conf spark.driver.memory=8g"
        " --conf spark.driver.maxResultSize=2g"
        f" --conf spark.executor.cores={executor_config['cores']}"
        f" --conf spark.executor.memory={executor_config['memory']}g"
        " --conf spark.sql.caseSensitive=true"
        " --conf spark.dynamicAllocation.enabled=true"
        " --conf spark.sql.mapKeyDedupPolicy=LAST_WIN"
        f" --conf spark.dynamicAllocation.initialExecutors={executor_config['min_instances']}"
        f" --conf spark.dynamicAllocation.minExecutors={executor_config['min_instances']}"
        f" --conf spark.dynamicAllocation.maxExecutors={executor_config['max_instances']}"
        " --conf spark.yarn.am.waitTime=1800s"
        " --conf spark.yarn.heterogeneousExecutors.enabled=false"
        " --conf spark.serializer=org.apache.spark.serializer.KryoSerializer"
        " --conf spark.kryo.registrator=org.apache.spark.HoodieSparkKryoRegistrar"
        " --conf spark.sql.catalog.spark_catalog=org.apache.spark.sql.hudi.catalog.HoodieCatalog"
        " --conf spark.sql.extensions=org.apache.spark.sql.hudi.HoodieSparkSessionExtension"
        " --jars s3://prod-dse-srp-configs/jars/spark/hudi/hudi-spark3.3-bundle_2.12-0.13.1.jar"
        f" --py-files {source_python_base_s3_path}/utils.py"
        f" {source_python_base_s3_path}/s3_to_parquet_jumbo_events_hourly_v1.py"
        f" --max_records_per_file {executor_config['maxRecordsPerFile']}"
        f" {' '.join(arguments)}"
    )
    return [
        {
            "Name": etl_name,
            "ActionOnFailure": "CONTINUE",
            "HadoopJarStep": {
                "Jar": "command-runner.jar",
                "Args": spark_submit.split(" "),
            },
        }
    ]


task_upload_spark_source_s3 = PythonOperator(
    task_id='upload_spark_source_to_s3',
    python_callable=multi_upload_to_s3,
    op_kwargs={'object_list': [
        [
            "/usr/local/airflow/dags/repo/dags/de/events/etl/jumbo_consumer_events/s3_to_parquet_jumbo_events_hourly_v1.py",
            "config/spark/de_events_jumbo_consumer_hourly/s3_to_parquet_jumbo_events_hourly_v1.py",
            "prod-dse-spark-etl-processed-sgp"
        ],
        [
            "/usr/local/airflow/dags/repo/dags/de/events/etl/jumbo_consumer_events/utils.py",
            "config/spark/de_events_jumbo_consumer_hourly/utils.py",
            "prod-dse-spark-etl-processed-sgp"
        ]
    ]},
    dag=dag,
    retries=2,
    executor_config=k8s_executor_config,
)

task_upload_read_schema_to_s3 = PythonOperator(
    task_id='update_read_schema_in_s3',
    python_callable=upload_spark_read_schema,
    dag=dag,
    retries=2,
    executor_config=k8s_executor_config,
)

cluster_create_task = EmrCreateJobFlowOperator(
    task_id="create_emr_cluster_consumer_events",
    job_flow_overrides=EMR_SETTINGS_OVERRIDE,
    aws_conn_id="aws_default",
    emr_conn_id="emr_default",
    dag=dag,
    retries=3,
    retry_delay=timedelta(seconds=15),
    retry_exponential_backoff=True,
    queue="celery",
)

cluster_terminate_task = EmrTerminateJobFlowOperator(
    task_id="terminate_emr_cluster",
    job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_consumer_events', key='return_value') }}",
    aws_conn_id="aws_default",
    dag=dag,
    retries=3,
    retry_delay=timedelta(seconds=15),
    retry_exponential_backoff=True,
    trigger_rule="all_done",
    queue="celery",
)

catalog_task = PapermillOperator(
    task_id="catalog_notebook",
    input_nb="{cwd}/add_catalog.ipynb".format(cwd=cwd),
    output_nb="s3://grofers-prod-dse-sgp/airflow/dag_runs/de_events_jumbo_consumer_hourly_v1/{{ run_id }}/add_catalog.ipynb",
    parameters={"cwd": "/usr/local/airflow/dags/repo/dags/de/events/etl/jumbo_consumer_events"},
    env={
        "AIRFLOW_DAG_OWNER": "<EMAIL>",
        "AIRFLOW_DAG_ID": "de_events_jumbo_consumer_hourly_v1",
    },
    dag=dag,
    retries=3,
    retry_delay=timedelta(seconds=15),
    retry_exponential_backoff=True,
    executor_config=k8s_executor_config,
)

with open('/usr/local/airflow/dags/repo/dags/de/events/etl/jumbo_consumer_events/spark_config.yml') as f:
    config = yaml.safe_load(f)
    task_instance = "{{task_instance}}"
    for etl in config.get('dags', []):

        event_routing_s3_paths = []
        for key in etl["s3"]["event_routing"]["keys"].split(','):
            event_routing_s3_paths.append(f's3://{etl["s3"]["event_routing"]["bucket"]}/{key}')
        add_emr_step = EmrAddStepsOperator(
            task_id=f'add_emr_step_{etl["name"]}',
            job_flow_id="{{ task_instance.xcom_pull('create_emr_cluster_consumer_events', key='return_value') }}",
            dag=dag,
            retries=3,
            retry_delay=timedelta(seconds=15),
            retry_exponential_backoff=True,
            aws_conn_id='aws_default',
            steps=fetch_spark_steps(
                etl_name=etl['etl_name'],
                executor_config=etl["spark-submit"],
                arguments=[
                    f"--etl_name {etl['etl_name']}",
                    "--etl_timestamp {{ts}}",
                    f"--dest_hive_table {etl['destination_table']}",
                    f"--app_type {etl.get('app_type', 'blinkit')}",
                    "--single_event" if etl["single_event"] else "",
                    f"--event_routing_s3_paths {','.join(event_routing_s3_paths)},",
                    f'--read_schema_s3_path s3://{etl["s3"]["read_schema"]["bucket"]}/{etl["s3"]["read_schema"]["key"]}',
                    f'--write_schema_s3_path s3://{etl["s3"]["write_schema"]["bucket"]}/{etl["s3"]["write_schema"]["key"]}',
                    f'--staging_s3_path s3://{etl["s3"]["staging"]["bucket"]}/{etl["s3"]["staging"]["prefix"]}',
                    f'--cdc_base_path s3://{etl["s3"]["source"]["bucket"]}/{etl["s3"]["source"]["prefix"]}',
                ]
            ),
            queue="celery",
        )

        emr_step_checker = EmrStateSensorAsync(
            task_id=f'emr_step_checker_{etl["name"]}',
            cluster_id="{{ task_instance.xcom_pull('create_emr_cluster_consumer_events', key='return_value') }}",
            step_id=f"{{{{ task_instance.xcom_pull(task_ids='add_emr_step_{etl['name']}', key='return_value')[0] }}}}",
            db_conn_id="nessie_db",
            aws_conn_id="aws_default",
            dag=dag,
            retries=1,
            retry_delay=timedelta(seconds=15),
            retry_exponential_backoff=True,
            db_poke_interval=60,
            api_poke_interval=300,
            event_sensor_timeout=1200,
            queue="celery",
        )

        # sync_data_to_archive_s3_sequential = PythonOperator(
        #     task_id=f'sync_s3_to_prod_{etl["name"]}_sequential',
        #     python_callable=s3_sync,
        #     op_kwargs={
        #         'source_bucket': etl["s3"]["staging"]["bucket"],
        #         'source_prefix': etl["s3"]["staging"]["prefix"],
        #         'destination_bucket': etl["s3"]["destination"]["bucket"],
        #         'destination_prefix': etl["s3"]["destination"]["prefix"],
        #         'aws_conn_id': etl["s3"]["aws_conn"],
        #         'extension': 'parquet'
        #     },
        #     dag=dag,
        #     retries=3,
        #     retry_delay=timedelta(seconds=15),
        #     retry_exponential_backoff=True,
        #     queue="celery",
        # )
        
        sync_data_to_archive_s3 = PythonOperator(
            task_id=f'sync_s3_to_prod_{etl["name"]}',
            python_callable=s3_sync_parallel,
            op_kwargs={
                'source_bucket': etl["s3"]["staging"]["bucket"],
                'source_prefix': etl["s3"]["staging"]["prefix"],
                'destination_bucket': etl["s3"]["destination"]["bucket"],
                'destination_prefix': etl["s3"]["destination"]["prefix"],
                'max_workers': 10,
                'aws_conn_id': etl["s3"]["aws_conn"],
                'extension': 'parquet'
            },
            dag=dag,
            retries=3,
            retry_delay=timedelta(seconds=15),
            retry_exponential_backoff=True,
            queue="celery",
        )

        update_markers = PostgresOperator(
            task_id=f'update_marker_{etl["name"]}',
            postgres_conn_id="dse_db_application_api",
            sql=("UPDATE jumbo_consumer_events_marker_logs main"
                 " SET max_kafka_offset_read=staging.max_kafka_offset_read,"
                 " max_kafka_partition_path=staging.max_kafka_partition_path,"
                 " updated_at=current_timestamp"
                 f" FROM jumbo_marker_logs_{etl['etl_name'].lower()}_staging staging"
                 " WHERE main.etl_name=staging.etl_name;"),
            dag=dag,
            retries=3,
            retry_delay=timedelta(seconds=15),
            retry_exponential_backoff=True,
            queue="celery",
        )

        cluster_create_task >> add_emr_step >> emr_step_checker >> sync_data_to_archive_s3 >> update_markers
        emr_step_checker >> cluster_terminate_task >> catalog_task

task_upload_read_schema_to_s3 >> cluster_create_task
task_upload_spark_source_s3 >> cluster_create_task

if __name__ == "__main__":
    from airflow.models import DagBag

    dagbag = DagBag()

    from airflow.utils import timezone

    dt = timezone.utcnow()
    dag._schedule_interval = timedelta(hours=1)
    done = set([])

    def run(key):
        task = dag.task_dict[key]
        for k in task._upstream_task_ids:
            run(k)
        if key not in done:
            task.run(dt, dt, ignore_ti_state=True)
            done.add(key)

    for k, _ in dag.task_dict.items():
        run(k)
