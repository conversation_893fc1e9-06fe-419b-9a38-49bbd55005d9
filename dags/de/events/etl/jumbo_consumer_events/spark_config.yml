dags:
- name: jumbo_consumer_product_shown
  etl_name: ProductShownV4Impressions
  destination_table: lake_events.mobile_impression_data
  single_event: true
  spark-submit:
    min_instances: 10
    max_instances: 65
    memory: 25
    cores: 4
    maxRecordsPerFile: 1000000
  s3:
    aws_conn: aws_default
    read_schema:
      bucket: prod-dse-srp-configs
      key: schemas/jumbo/spark/impression_events_read_schema.txt
    write_schema:
      bucket: prod-dse-srp-configs
      key: schemas/jumbo/spark/impression_events_write_schema.txt
    event_routing:
      bucket: prod-dse-srp-configs
      keys: consumer_events/android_routing_config.json,consumer_events/web_routing_config.json
    source:
      bucket: prod-dse-backend-events-raw
      prefix: warpstream-bridge-analytics-events/product_shown/jumbo_transformed.blinkit.product_image_shown_events
      region: ap-southeast-1
    staging:
      bucket: prod-dse-spark-etl-processed-sgp
      prefix: jumbo_consumer_events/ProductShownV4Impressions
      region: ap-southeast-1
    destination:
      bucket: prod-data-lake-hudi-rudder-events
      prefix: jumbo_consumer_impression_data
      region: ap-southeast-1
- name: jumbo_consumer_product_shown_other_sources
  etl_name: ProductShownV4ImpressionsOtherSources
  destination_table: lake_events.mobile_impression_data_other_sources
  single_event: true
  app_type: others
  spark-submit:
    min_instances: 10
    max_instances: 40
    memory: 21
    cores: 4
    maxRecordsPerFile: 1000000
  s3:
    aws_conn: aws_default
    read_schema:
      bucket: prod-dse-srp-configs
      key: schemas/jumbo/spark/impression_events_read_schema.txt
    write_schema:
      bucket: prod-dse-srp-configs
      key: schemas/jumbo/spark/impression_events_write_schema.txt
    event_routing:
      bucket: prod-dse-srp-configs
      keys: consumer_events/android_routing_config.json,consumer_events/web_routing_config.json
    source:
      bucket: prod-dse-backend-events-raw
      prefix: warpstream-bridge-analytics-events/product_shown/jumbo_transformed.blinkit.product_image_shown_events
      region: ap-southeast-1
    staging:
      bucket: prod-dse-spark-etl-processed-sgp
      prefix: jumbo_consumer_events/OtherSourcesProductShownV4Impressions
      region: ap-southeast-1
    destination:
      bucket: prod-data-lake-hudi-rudder-events
      prefix: jumbo_consumer_impression_data_other_sources
      region: ap-southeast-1
- name: jumbo_consumer_image_shown
  etl_name: ImageShownV4Impressions
  destination_table: lake_events.mobile_impression_data
  single_event: true
  spark-submit:
    min_instances: 10
    max_instances: 50
    memory: 25
    cores: 4
    maxRecordsPerFile: 1000000
  s3:
    aws_conn: aws_default
    read_schema:
      bucket: prod-dse-srp-configs
      key: schemas/jumbo/spark/impression_events_read_schema.txt
    write_schema:
      bucket: prod-dse-srp-configs
      key: schemas/jumbo/spark/impression_events_write_schema.txt
    event_routing:
      bucket: prod-dse-srp-configs
      keys: consumer_events/android_routing_config.json,consumer_events/web_routing_config.json
    source:
      bucket: prod-dse-backend-events-raw
      prefix: warpstream-bridge-analytics-events/image_shown/jumbo_transformed.blinkit.product_image_shown_events
      region: ap-southeast-1
    staging:
      bucket: prod-dse-spark-etl-processed-sgp
      prefix: jumbo_consumer_events/ImageShownV4Impressions
      region: ap-southeast-1
    destination:
      bucket: prod-data-lake-hudi-rudder-events
      prefix: jumbo_consumer_impression_data
      region: ap-southeast-1
- name: jumbo_consumer_image_shown_other_sources
  etl_name: ImageShownV4ImpressionsOtherSources
  destination_table: lake_events.mobile_impression_data_other_sources
  single_event: true
  app_type: others
  spark-submit:
    min_instances: 10
    max_instances: 40
    memory: 21
    cores: 4
    maxRecordsPerFile: 1000000
  s3:
    aws_conn: aws_default
    read_schema:
      bucket: prod-dse-srp-configs
      key: schemas/jumbo/spark/impression_events_read_schema.txt
    write_schema:
      bucket: prod-dse-srp-configs
      key: schemas/jumbo/spark/impression_events_write_schema.txt
    event_routing:
      bucket: prod-dse-srp-configs
      keys: consumer_events/android_routing_config.json,consumer_events/web_routing_config.json
    source:
      bucket: prod-dse-backend-events-raw
      prefix: warpstream-bridge-analytics-events/image_shown/jumbo_transformed.blinkit.product_image_shown_events
      region: ap-southeast-1
    staging:
      bucket: prod-dse-spark-etl-processed-sgp
      prefix: jumbo_consumer_events/OtherSourcesImageShownV4Impressions
      region: ap-southeast-1
    destination:
      bucket: prod-data-lake-hudi-rudder-events
      prefix: jumbo_consumer_impression_data_other_sources
      region: ap-southeast-1
- name: jumbo_consumer_all_impression
  etl_name: AllEventsV4Impressions
  destination_table: lake_events.mobile_impression_data
  single_event: false
  spark-submit:
    min_instances: 5
    max_instances: 100
    memory: 25
    cores: 4
    maxRecordsPerFile: 500000
  s3:
    aws_conn: aws_default
    read_schema:
      bucket: prod-dse-srp-configs
      key: schemas/jumbo/spark/impression_events_read_schema.txt
    write_schema:
      bucket: prod-dse-srp-configs
      key: schemas/jumbo/spark/impression_events_write_schema.txt
    event_routing:
      bucket: prod-dse-srp-configs
      keys: consumer_events/android_routing_config.json,consumer_events/web_routing_config.json
    source:
      bucket: prod-dse-backend-events-raw
      prefix: warpstream-bridge-analytics-events/impression_events/jumbo_transformed.blinkit.impression_events
      region: ap-southeast-1
    staging:
      bucket: prod-dse-spark-etl-processed-sgp
      prefix: jumbo_consumer_events/AllEventsV4Impressions
      region: ap-southeast-1
    destination:
      bucket: prod-data-lake-hudi-rudder-events
      prefix: jumbo_consumer_impression_data
      region: ap-southeast-1
- name: jumbo_consumer_all_impression_other_sources
  etl_name: AllEventsV4ImpressionsOtherSources
  destination_table: lake_events.mobile_impression_data_other_sources
  single_event: false
  app_type: others
  spark-submit:
    min_instances: 5
    max_instances: 45
    memory: 21
    cores: 4
    maxRecordsPerFile: 500000
  s3:
    aws_conn: aws_default
    read_schema:
      bucket: prod-dse-srp-configs
      key: schemas/jumbo/spark/impression_events_read_schema.txt
    write_schema:
      bucket: prod-dse-srp-configs
      key: schemas/jumbo/spark/impression_events_write_schema.txt
    event_routing:
      bucket: prod-dse-srp-configs
      keys: consumer_events/android_routing_config.json,consumer_events/web_routing_config.json
    source:
      bucket: prod-dse-backend-events-raw
      prefix: warpstream-bridge-analytics-events/impression_events/jumbo_transformed.blinkit.impression_events
      region: ap-southeast-1
    staging:
      bucket: prod-dse-spark-etl-processed-sgp
      prefix: jumbo_consumer_events/OtherSourcesAllEventsV4Impressions
      region: ap-southeast-1
    destination:
      bucket: prod-data-lake-hudi-rudder-events
      prefix: jumbo_consumer_impression_data_other_sources
      region: ap-southeast-1
- name: jumbo_consumer_all_clicks
  etl_name: AllEventsV4Clicks
  destination_table: lake_events.mobile_event_data
  single_event: false
  spark-submit:
    min_instances: 5
    max_instances: 15
    memory: 21
    cores: 4
    maxRecordsPerFile: 500000
  s3:
    aws_conn: aws_default
    read_schema:
      bucket: prod-dse-srp-configs
      key: schemas/jumbo/spark/click_events_read_schema.txt
    write_schema:
      bucket: prod-dse-srp-configs
      key: schemas/jumbo/spark/click_events_write_schema.txt
    event_routing:
      bucket: prod-dse-srp-configs
      keys: consumer_events/android_routing_config.json,consumer_events/web_routing_config.json
    source:
      bucket: prod-dse-backend-events-raw
      prefix: warpstream-bridge-analytics-events/click_web_events/jumbo_transformed.blinkit.click_events
      region: ap-southeast-1
    staging:
      bucket: prod-dse-spark-etl-processed-sgp
      prefix: jumbo_consumer_events/AllEventsV4Clicks
      region: ap-southeast-1
    destination:
      bucket: prod-data-lake-hudi-rudder-events
      prefix: jumbo_consumer_click_data
      region: ap-southeast-1
- name: jumbo_consumer_all_clicks_other_sources
  etl_name: AllEventsV4ClicksOtherSources
  destination_table: lake_events.mobile_event_data_other_sources
  single_event: false
  app_type: others
  spark-submit:
    min_instances: 5
    max_instances: 15
    memory: 21
    cores: 4
    maxRecordsPerFile: 500000
  s3:
    aws_conn: aws_default
    read_schema:
      bucket: prod-dse-srp-configs
      key: schemas/jumbo/spark/click_events_read_schema.txt
    write_schema:
      bucket: prod-dse-srp-configs
      key: schemas/jumbo/spark/click_events_write_schema.txt
    event_routing:
      bucket: prod-dse-srp-configs
      keys: consumer_events/android_routing_config.json,consumer_events/web_routing_config.json
    source:
      bucket: prod-dse-backend-events-raw
      prefix: warpstream-bridge-analytics-events/click_web_events/jumbo_transformed.blinkit.click_events
      region: ap-southeast-1
    staging:
      bucket: prod-dse-spark-etl-processed-sgp
      prefix: jumbo_consumer_events/OtherSourcesAllEventsV4Clicks
      region: ap-southeast-1
    destination:
      bucket: prod-data-lake-hudi-rudder-events
      prefix: jumbo_consumer_click_data_other_sources
      region: ap-southeast-1
- name: jumbo_consumer_web_clicks
  etl_name: WebEventsV4Clicks
  destination_table: lake_events.mobile_event_data
  single_event: false
  spark-submit:
    min_instances: 1
    max_instances: 2
    memory: 21
    cores: 4
    maxRecordsPerFile: 500000
  s3:
    aws_conn: aws_default
    read_schema:
      bucket: prod-dse-srp-configs
      key: schemas/jumbo/spark/click_events_read_schema.txt
    write_schema:
      bucket: prod-dse-srp-configs
      key: schemas/jumbo/spark/click_events_write_schema.txt
    event_routing:
      bucket: prod-dse-srp-configs
      keys: consumer_events/android_routing_config.json,consumer_events/web_routing_config.json
    source:
      bucket: prod-dse-backend-events-raw
      prefix: warpstream-bridge-analytics-events/click_web_events/jumbo_transformed.blinkit.blinkit_web_events
      region: ap-southeast-1
    staging:
      bucket: prod-dse-spark-etl-processed-sgp
      prefix: jumbo_consumer_events/WebEventsV4Clicks
      region: ap-southeast-1
    destination:
      bucket: prod-data-lake-hudi-rudder-events
      prefix: jumbo_consumer_click_data
      region: ap-southeast-1
- name: jumbo_consumer_web_impression
  etl_name: WebEventsV4Impressions
  destination_table: lake_events.mobile_impression_data
  single_event: false
  spark-submit:
    min_instances: 1
    max_instances: 2
    memory: 21
    cores: 4
    maxRecordsPerFile: 500000
  s3:
    aws_conn: aws_default
    read_schema:
      bucket: prod-dse-srp-configs
      key: schemas/jumbo/spark/impression_events_read_schema.txt
    write_schema:
      bucket: prod-dse-srp-configs
      key: schemas/jumbo/spark/impression_events_write_schema.txt
    event_routing:
      bucket: prod-dse-srp-configs
      keys: consumer_events/android_routing_config.json,consumer_events/web_routing_config.json
    source:
      bucket: prod-dse-backend-events-raw
      prefix: warpstream-bridge-analytics-events/click_web_events/jumbo_transformed.blinkit.blinkit_web_events
      region: ap-southeast-1
    staging:
      bucket: prod-dse-spark-etl-processed-sgp
      prefix: jumbo_consumer_events/WebEventsV4Impressions
      region: ap-southeast-1
    destination:
      bucket: prod-data-lake-hudi-rudder-events
      prefix: jumbo_consumer_impression_data
      region: ap-southeast-1
