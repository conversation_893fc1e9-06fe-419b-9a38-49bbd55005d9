# -*- coding: utf-8 -*-

import os
import logging
from contextlib import closing
from datetime import datetime, timedelta

import airflow
from airflow import DAG
from airflow.hooks.base_hook import BaseHook
from airflow.operators.python_operator import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models import Variable
from kubernetes.client import models as k8s

import pencilbox as pb


cwd = os.path.dirname(os.path.realpath(__file__))


def get_k8s_executor_config(executor_config={}):
    limits = {}
    requests = {}
    if executor_config.get("cpu"):
        limits["cpu"] = executor_config["cpu"]["limit"]
        requests["cpu"] = executor_config["cpu"]["request"]
    if executor_config.get("memory"):
        limits["memory"] = executor_config["memory"]["limit"]
        requests["memory"] = executor_config["memory"]["request"]

    return {
        "pod_override": k8s.V1Pod(
            spec=k8s.V1PodSpec(
                node_selector={"nodetype": executor_config.get("node_selector")},
                tolerations=[
                    k8s.V1Toleration(
                        effect="NoSchedule",
                        key="service",
                        operator="Equal",
                        value=executor_config.get("toleration"),
                    ),
                ],
                containers=[
                    k8s.V1Container(
                        image=Variable.get(
                            "************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable",
                            "************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable",
                        ),
                        name="base",
                        volume_mounts=[
                            k8s.V1VolumeMount(
                                mount_path="/usr/local/airflow/plugins",
                                name="airflow-dags",
                                sub_path="airflow/plugins",
                            ),
                        ],
                        resources=k8s.V1ResourceRequirements(limits=limits, requests=requests),
                    )
                ],
                service_account_name="blinkit-prod-airflow-primary-eks-role",
            )
        )
    }


def slack_failure_alert(context):
    task_instance = context.get("task_instance")
    dag_id = task_instance.dag_id
    run_id = context.get("run_id")
    task_id = task_instance.task_id
    log_url = task_instance.log_url.replace("http://", "https://")

    owner = {"email": "<EMAIL>", "slack_id": "U08FD0L7L5V"}
    slack_id = owner["slack_id"]
    _owner = {
        "s": "s" if len(slack_id.split(" ")) > 1 else "",
        "slack_id": "<@{}>".format(slack_id),
    }

    slack_failure_msg = f"""
        :red_circle: DagRun Failed.
        *Dag ID*: {dag_id}
        *Run ID*: {run_id}
        *Task ID*: {task_id}
        *Owner{_owner["s"]}*: {_owner["slack_id"]}
        *Log Url*: {log_url}
    """

    slack_alert_configs = [
        {"channel": "bl-data-airflow-alerts"},
    ]

    for config in slack_alert_configs:
        channel = config["channel"]
        try:
            pb.send_slack_message(channel=channel, text=slack_failure_msg, source_info=False)
        except Exception as e:
            logging.warning(e)
            message = (
                f"{_owner['slack_id']} `{dag_id}` failed but cannot send an alert to `{channel}`."
                f"\n```{e}```"
            )
            pb.send_slack_message(channel="bl-data-airflow-alerts", text=message, source_info=False)


def failure_alerts(context):
    slack_failure_alert(context)


args = {
    "owner": "<EMAIL>",
    "on_failure_callback": failure_alerts,
    "start_date": datetime.strptime("2023-01-27T02:30:00", "%Y-%m-%dT%H:%M:%S"),
    "end_date": datetime.strptime("2025-09-30T00:00:00", "%Y-%m-%dT%H:%M:%S"),
    "slack_id": "U08FD0L7L5V",
    "execution_timeout": timedelta(minutes=840),
    "pool": "de_pool",
}


def _read_sql(file_path):
    with open(file_path, "r") as f:
        return f.read()


def execute_sql_file(
    file_path,
    schema_name,
    table_name,
    table_description,
    load_type,
    primary_key,
    partition_key,
    incremental_key,
    force_upsert_without_increment_check,
    column_descriptions,
    parameters,
):
    sql = _read_sql(file_path)
    if column_descriptions:
        for col in column_descriptions:
            if not col["description"]:
                raise ValueError(f"Column description for {col['column_name']} is required")
        column_descriptions = [
            {"name": col["column_name"], "description": col["description"]}
            for col in column_descriptions
        ]
    else:
        raise ValueError("Column descriptions are required")
    if not sql:
        logging.error(f"SQL file {file_path} is empty or not found.")
        raise ValueError(f"SQL file {file_path} is empty or not found.")

    sql = sql.format(**parameters)
    logging.info(f"Executing SQL query:\n {sql}")
    try:
        pb.to_trino(
            sql=sql,
            schema_name=schema_name,
            table_name=table_name,
            table_description=table_description,
            load_type=load_type,
            primary_key=primary_key,
            partition_key=partition_key,
            incremental_key=incremental_key,
            force_upsert_without_increment_check=force_upsert_without_increment_check,
            column_descriptions=column_descriptions,
        )
    except Exception as e:
        logging.error(f"Error executing SQL file {file_path}: {e}")
        raise e


dag = DAG(
    dag_id="de_test_etl_test-1_v1",
    default_args=args,
    schedule_interval="45 22 * * *",
    tags=["de", "test", "etl"],
    concurrency=3,
)

env = {}
default_env = {
    "AIRFLOW_DAG_ID": "de_test_etl_test-1_v1",
    "AIRFLOW_DAG_OWNER": "<EMAIL>",
    "AIRFLOW_DAG_RUN_ID": "{{ run_id }}",
    "PYTHONPATH": "/usr/local/airflow/dags/repo/dag_utils",
}
env.update(default_env)

sql_task_bag = {}


sql_task_1 = PythonOperator(
    task_id="run_test_1",
    python_callable=execute_sql_file,
    op_kwargs={
        "file_path": "daggers/de/test/etl/test-1/test.sql",
        "schema_name": "de_etls",
        "table_name": "sqlflow_test_table",
        "table_description": "Test Table",
        "load_type": "upsert",
        "primary_key": ["view_name"],
        "partition_key": ["last_refresh_ts_ist"],
        "incremental_key": sql_file_path,
        "force_upsert_without_increment_check": False,
        "column_descriptions": [
            {"name": "view_name", "type": "VARCHAR", "description": "Test"},
            {"name": "sql_file_path", "type": "VARCHAR", "description": "Test"},
            {"name": "security", "type": "VARCHAR", "description": "Test"},
            {"name": "owner_email", "type": "VARCHAR", "description": "Test"},
            {"name": "slack_id", "type": "VARCHAR", "description": "Test"},
            {"name": "reviewer_email", "type": "VARCHAR", "description": "Test"},
            {"name": "version", "type": "INTEGER", "description": "Test"},
            {"name": "etl_snapshot_ts_ist", "type": "TIMESTAMP", "description": "Test"},
            {"name": "end_date", "type": "DATE", "description": "Test"},
            {"name": "partition_columns", "type": "ARRAY(VARCHAR)", "description": "Test"},
            {"name": "materialized_view", "type": "BOOLEAN", "description": "Test"},
            {"name": "last_refresh_ts_ist", "type": "TIMESTAMP", "description": "Test"},
            {"name": "refresh_interval_utc", "type": "VARCHAR", "description": "Test"},
            {"name": "rls", "type": "VARCHAR", "description": "Test"},
            {"name": "priority", "type": "VARCHAR", "description": "Test"},
        ],
        "parameters": {"cwd": "/usr/local/airflow/dags/repo/dags/de/test/etl/test-1"},
    },
    dag=dag,
    executor_config=get_k8s_executor_config(
        {
            "image": "************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common:stable",
            "load_type": "tiny",
            "node_type": "spot",
            "service_account_name": "blinkit-prod-airflow-primary-eks-role",
            "cpu": {"request": 0.5, "limit": 1},
            "memory": {"request": "1Gi", "limit": "3Gi"},
            "node_selector": "airflow-spot-general",
            "toleration": "airflow-spot-general",
            "volume_mounts": [
                {
                    "name": "airflow-dags",
                    "mountPath": "/usr/local/airflow/plugins",
                    "subPath": "airflow/plugins",
                }
            ],
        }
    ),
    priority_weight=1,
)
sql_task_bag["run_test_1"] = sql_task_1
