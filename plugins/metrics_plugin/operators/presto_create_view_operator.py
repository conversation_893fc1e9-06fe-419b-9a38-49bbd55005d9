import pandas as pd
import sqlalchemy as sqla
from sqlalchemy.sql.compiler import RESERVED_WORDS
import json

from airflow.models import BaseOperator, Variable
from airflow.utils.decorators import apply_defaults

import pencilbox as pb


HIVE_TRINO_MAP = {
    "boolean": "boolean",
    "string": "varchar",
    "int": "integer",
    "bigint": "bigint",
    "float": "real",
    "double": "double",
    "date": "date",
    "binary": "varbinary"
}

HUDI_META_COLUMNS =[
    "_hoodie_commit_seqno",
    "_hoodie_commit_time",
    "_hoodie_file_name",
    "_hoodie_incremental_key",
    "_hoodie_partition_path",
    "_hoodie_record_key",
    "lake_active_record",
    "insert_ds_ist",
    "partition_field"
]

def get_fields(data_type_str):
    level = 0
    field = ""
    fields = []
    for index, value in enumerate(data_type_str):
        if value == "<":
            level += 1
        if value == ">":
            level -= 1
        if level >= 1:
            field += value
        if (level == 1 and value == ",") or (index == len(data_type_str) - 1):
            fields.append(field.removeprefix("<").removesuffix(",").strip())
            field = ""
    return fields

def convert_hive_to_trino(data_type):
    # Convert any extra space, newline, tab to single space
    if data_type in HIVE_TRINO_MAP:
        return HIVE_TRINO_MAP[data_type]
    elif data_type[:7] == "decimal":
        return data_type
    elif data_type[:5] == "array":
        return f"array({convert_hive_to_trino(data_type[6:-1])})"
    elif data_type[:6] == "struct":
        fields = []
        fields_strings = get_fields(data_type)
        for field in fields_strings:
            if len(field.split(":", 1)) < 2:
                raise Exception(f"Improper named field {field} specified")
            fields.append(
                f'"{field.split(":", 1)[0]}" {convert_hive_to_trino(field.split(":", 1)[1])}'
            )
        return f"row({','.join(fields)})"
    elif data_type[:3] == "map":
        fields_strings = get_fields(data_type)
        return f"map({convert_hive_to_trino(fields_strings[0])},{convert_hive_to_trino(fields_strings[1])})"
    else:
        raise Exception(f"Unsupported data type {data_type} was passed.")


class PrestoCreateViewOperator(BaseOperator):
    """
    Create/Update views on trino after Hudi to Redshift Flow
    """

    template_fields = ("topic_name",)

    @apply_defaults
    def __init__(self, topic_name, database, lake_schemaname, *args, **kwargs):
        self.topic_name = topic_name
        self.database = database
        self.lake_schemaname = lake_schemaname
        self.source_vault_path_mapping = pb.get_secret(
            "dse/pencilbox/key_map_srp_replicas"
        )
        try:
            self.database_pii_tables = pb.get_secret("dse/pencilbox/pii_tables")[database]
        except KeyError:
            self.database_pii_tables = []

        super().__init__(*args, **kwargs)

    def get_columns_to_skip(self, schema_table):
        """
        Get columns to skip for a given schema.table from Airflow variable.

        Args:
            schema_table (str): The schema.table identifier

        Returns:
            list: List of column names to skip, empty list if no mapping found
        """
        try:
            # Get the mapping from Airflow variable
            skip_columns_mapping = Variable.get("presto_view_skip_columns", "{}")
            skip_columns_dict = json.loads(skip_columns_mapping)

            # Return columns to skip for this schema.table, or empty list if not found
            return skip_columns_dict.get(schema_table, [])
        except Exception as e:
            self.log.warning(f"Failed to get column skip mapping: {e}")
            return []

    def get_temp_partition_column(self, schema_table):
        """
        Get the column name to use for creating insert_ds_ist partition column.

        Args:
            schema_table (str): The schema.table identifier

        Returns:
            str: Column name to use for partition, None if no mapping found
        """
        try:
            # Get the mapping from Airflow variable
            partition_mapping = Variable.get("presto_view_temp_partition_key", "{}")
            partition_dict = json.loads(partition_mapping)

            # Return column name for this schema.table, or None if not found
            return partition_dict.get(schema_table)
        except Exception as e:
            self.log.warning(f"Failed to get temp partition column mapping: {e}")
            return None

    def execute(self, context):
        _, _, source_schemaname, tablename = self.topic_name.split(".")
        original_table_name = tablename
        # Temp: For pii table migration, if table has pii data,
        # in hive table name will be tablename_pii
        # but view name should be tablename
        if tablename in self.database_pii_tables:
            tablename = tablename + "_pii"
        database = self.database
        lake_schemaname = self.lake_schemaname
        trino_view_schemaname = f"{lake_schemaname[5:]}"

        # Get columns to skip for this schema.table
        schema_table_key = f"{trino_view_schemaname}.{original_table_name}"
        columns_to_skip = self.get_columns_to_skip(schema_table_key)
        self.log.info(f"Columns to skip for {schema_table_key}: {columns_to_skip}")

        # Get temp partition column for this schema.table
        temp_partition_column = self.get_temp_partition_column(schema_table_key)
        if temp_partition_column:
            self.log.info(f"Using temp partition column for {schema_table_key}: {temp_partition_column}")

        dtype_query = f"""
        SELECT
            table_schema as table_schema,
            table_name as table_name,
            column_name as column_name,
            data_type as data_type
        FROM
            information_schema.columns
        WHERE
            table_name = '{original_table_name}'
        AND table_schema = '{source_schemaname}'
        """

        hive_query = f"""
                WITH raw AS
                  (SELECT 
                    DBS.NAME AS table_schema,
                    TBLS.TBL_NAME AS table_name,
                    CONVERT(FROM_BASE64(trim(TRAILING ' */' FROM substring(VIEW_ORIGINAL_TEXT,16))) USING utf8) AS view_structure
                   FROM TBLS
                   LEFT JOIN DBS ON TBLS.DB_ID = DBS.DB_ID
                   WHERE 
                    DBS.NAME='{trino_view_schemaname}'
                     AND TBLS.TBL_NAME='{original_table_name}')
                SELECT 
                    table_schema,
                    table_name,
                    column_name, 
                    data_type
                FROM raw
                CROSS JOIN JSON_TABLE(json_extract(raw.view_structure, '$.columns'), '$[*]' COLUMNS (
                                    column_name varchar(767) PATH '$.name', 
                                    data_type mediumtext PATH '$.type')
                            ) col_data

                UNION ALL
                SELECT 
                    DBS.NAME AS table_schema,
                    TBLS.TBL_NAME as table_name,
                    PARTITION_KEYS.PKEY_NAME AS column_name,
                    PARTITION_KEYS.PKEY_TYPE AS data_type
                FROM DBS
                JOIN TBLS ON DBS.DB_ID = TBLS.DB_ID
                JOIN PARTITION_KEYS ON TBLS.TBL_ID = PARTITION_KEYS.TBL_ID
                WHERE TBLS.TBL_NAME='{tablename}'
                  AND DBS.NAME='{lake_schemaname}'
                UNION ALL
                SELECT
                    'trino view' AS table_schema,
                    'schema' AS table_name,
                    'already' AS column_name,
                    'exists' AS data_type
                FROM DBS
                WHERE DBS.NAME='{trino_view_schemaname}'
                """

        hive_lake_query = f"""
        SELECT * FROM (
            SELECT 
               DBS.NAME AS table_schema,
               TBLS.TBL_NAME AS table_name,
               COLUMNS_V2.COLUMN_NAME as column_name,
               COLUMNS_V2.TYPE_NAME AS data_type
            FROM DBS
            JOIN TBLS ON DBS.DB_ID = TBLS.DB_ID
            JOIN SDS ON TBLS.SD_ID = SDS.SD_ID
            JOIN COLUMNS_V2 ON COLUMNS_V2.CD_ID = SDS.CD_ID
            WHERE TBLS.TBL_NAME='{tablename}'
              AND DBS.NAME='{lake_schemaname}'
            ORDER BY INTEGER_IDX
        ) lake_table
        """

        trino_table_columns = []

        source_uri = pb.get_secret(self.source_vault_path_mapping[database])["uri"]
        source_conn = sqla.create_engine(source_uri)
        hive_conn = pb.get_connection("[Prod] Hivemetastore")

        df_dtypes_source = pd.read_sql_query(dtype_query, source_conn)
        df_dtypes_trino = pd.read_sql_query(hive_query, hive_conn)
        df_dtypes_trino_lake = pd.read_sql_query(hive_lake_query, hive_conn)

        df_dtypes_trino = pd.concat([df_dtypes_trino, df_dtypes_trino_lake], ignore_index=True)

        df_dtypes_trino_view = df_dtypes_trino[df_dtypes_trino['table_schema'] == trino_view_schemaname]
        df_dtypes_trino = df_dtypes_trino[
            (df_dtypes_trino['table_schema'] == lake_schemaname) &
            (~df_dtypes_trino['column_name'].str.endswith("_encrypted"))
            ]
        view_schema_exists = df_dtypes_trino[df_dtypes_trino['table_schema'] == 'trino view']

        trino_cols = df_dtypes_trino["column_name"].unique().tolist()
        source_cols, trino_view_cols = (
            set(df_dtypes_source["column_name"]),
            set(df_dtypes_trino_view["column_name"])
        )

        trino_cols_dict, trino_view_cols_dict = (
            dict(zip(df_dtypes_trino["column_name"],
                     [convert_hive_to_trino(row) for row in df_dtypes_trino["data_type"].to_list()])),
            dict(zip(df_dtypes_trino_view["column_name"],
                     df_dtypes_trino_view["data_type"].replace("timestamp(3)", "bigint").replace("time(3)", "bigint")))
        )

        # build expected view columns dict - remove columns_to_skip and add insert_ds_ist as required
        partition_migration_view_cols_dict = {}
        if temp_partition_column:
            partition_migration_view_cols_dict["insert_ds_ist"] = "varchar"

        expected_view_cols_dict = trino_cols_dict.copy()
        expected_view_cols_dict.update(partition_migration_view_cols_dict)

        if columns_to_skip:
            for col in columns_to_skip:
                expected_view_cols_dict.pop(col, None)

        if expected_view_cols_dict == trino_view_cols_dict:
            self.log.info("Columns in the View table are consistent with lake and migration")
        else:
            trino_admin_conn = sqla.create_engine(
                pb.get_secret("data/trino/zomato/prod-data-dwh/dwh_admin")["uri"],
                **{"connect_args": {"protocol": "https"}},
            )
            hudi_default_meta_columns_to_add = [col for col in trino_cols if col in HUDI_META_COLUMNS]
            hudi_non_meta_columns = [col for col in trino_cols if col not in HUDI_META_COLUMNS]
            hudi_derived_non_meta_columns = [col for col in hudi_non_meta_columns if col not in source_cols]
            hudi_meta_columns = hudi_derived_non_meta_columns + hudi_default_meta_columns_to_add

            for _, row in df_dtypes_source.iterrows():
                if row["column_name"] not in trino_cols:
                    continue

                # Skip columns that are in the skip list
                if row["column_name"] in columns_to_skip:
                    self.log.info(f"Skipping column: {row['column_name']}")
                    continue

                if row["data_type"] in (
                    "datetime",
                    "timestamp",
                    "timestamp with time zone",
                    "timestamp without time zone",
                ):
                    trino_table_columns.append(
                        f"cast(from_unixtime({row['column_name']}/pow(10,6), 'UTC') as timestamp) as {row['column_name']}"
                    )
                elif row["data_type"] in (
                    "time",
                    "time with time zone",
                    "time without time zone"
                ):
                    trino_table_columns.append(
                        f"CAST(concat(lpad(CAST(({row['column_name']}/1000000)/3600 AS varchar), 2, '0'),"  # hours
                        f"':', lpad(CAST((({row['column_name']}/1000000)%%3600)/60 AS varchar), 2, '0'),"    # minutes
                        f"':', lpad(CAST(({row['column_name']}/1000000)%%60 AS varchar), 2, '0'),"           # seconds
                        f"'.', lpad(CAST({row['column_name']}%%1000000 AS varchar), 6, '0')) AS time)"       # microsec
                        f" AS {row['column_name']}"
                    )
                else:
                    trino_table_columns.append(row["column_name"])

            # Add custom insert_ds_ist column if temp partition column is configured
            if temp_partition_column:
                insert_ds_ist_column = f"""CAST(
                    DATE(
                    CAST(
                        from_unixtime(({temp_partition_column} / pow(10, 6)), 'Asia/Kolkata') AS timestamp
                    )
                    ) AS varchar
                ) insert_ds_ist"""
                trino_table_columns.append(insert_ds_ist_column)
                self.log.info(f"Added custom insert_ds_ist column using {temp_partition_column}")

            trino_table_columns.extend(hudi_meta_columns)
            trino_create_schema = f"CREATE SCHEMA IF NOT EXISTS {trino_view_schemaname}"
            trino_view_query = f"""
                CREATE OR REPLACE VIEW "{trino_view_schemaname}"."{original_table_name}" SECURITY INVOKER AS
                    SELECT
                        {','.join(trino_table_columns)}
                    FROM "{lake_schemaname}"."{tablename}"
            """
            self.log.info(f"Running query: {trino_view_query}")
            try:
                if view_schema_exists.empty:
                    trino_admin_conn.execute(trino_create_schema)
                trino_admin_conn.execute(trino_view_query)
            except Exception as error:
                self.log.error(error)
